module.exports = {
    // oss 上传配置
    oss: {
        dir: "static", // 上传目录
        url: "https://dcdn.yang800.com",
        bucket: "daita-front",
        region: "oss-cn-shanghai",
    },
    webpack: {
        entry: {
            index: "./src/index",
        },
        externals: [
            {
                "react-single-app": "ReactSingleApp",
                react: "React",
                "react-dom": "ReactDOM",
                axios: "axios",
                moment: "moment",
                antd: "antd",
                "@dt/components": "dtComponents",
                "@dt/networks": "dtNetworks",
                "@dt/hooks": "dtHooks",
                "@dt/util": "dtUtil",
            },
        ],
    },
    projectName: "ccs",
    projectVersion: "1.0.1",
};
