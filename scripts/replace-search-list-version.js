const path = require("path");
const glob = require("glob");
const fs = require("fs-extra");
(async () => {
    let useSemiColon = true;
    let spacesBetweenBraces = true;
    let doubleQuotes = true;
    console.time("Execution...");
    let rootPath = path.join(__dirname, "../");
    const tmpFolder = path.resolve("src", "tmp");
    await fs.remove(tmpFolder);
    await fs.ensureDir(tmpFolder);
    const jsFiles = glob.sync(path.join(process.cwd(), "src/**/*.js"));
    const reg = /"(https:\/\/maria.yang800.com\/api\/data\/v2\/)(\d+)"/;
    const relativePath = "react-single-app";
    let fixResultContent = "";

    function createImportStatement(imp, path, endLine) {
        let formattedPath = path.replace(/\"/g, "").replace(/\'/g, "");
        let returnStr = "";
        if (doubleQuotes && spacesBetweenBraces) {
            returnStr = `import { ${imp} } from "${formattedPath}";${endLine ? "\r\n" : ""}`;
        } else if (doubleQuotes) {
            returnStr = `import {${imp}} from "${formattedPath}";${endLine ? "\r\n" : ""}`;
        } else if (spacesBetweenBraces) {
            returnStr = `import { ${imp} } from '${formattedPath}';${endLine ? "\r\n" : ""}`;
        } else {
            returnStr = `import {${imp}} from '${formattedPath}';${endLine ? "\r\n" : ""}`;
        }

        if (useSemiColon === false) {
            returnStr = returnStr.replace(";", "");
        }

        return returnStr;
    }

    function getSearchList(content, jsPath) {
        const lines = content.split(/[\n\r]/);
        const searchSearchListLine = lines.findIndex(line => {
            return line.replace(/\s/g).includes("SearchList");
        });
        const searchReactSingleAppListLine = lines.findIndex(line => {
            return line.replace(/\s/g).includes("react-single-ap");
        });
        const searchGetConfigLine = lines.findIndex(line => {
            return line.replace(/\s/g).includes("getConfig");
        });
        if (searchSearchListLine < 0 || searchGetConfigLine < 0) {
            return null;
        }
        let findMarchMariaUrl = false;
        let flatMapLines = lines.flatMap((line, index) => {
            let regExp = new RegExp(reg, "g");
            let marchMariaUrl = line.match(regExp);
            if (marchMariaUrl) {
                findMarchMariaUrl = true;
                fixResultContent += path.basename(jsPath);
                marchMariaUrl.forEach(item => {
                    let regExp = new RegExp(reg, "i");
                    let urlArr = line.match(regExp);
                    let dataID = urlArr[2];
                    fixResultContent += `【${dataID}】`;
                    line = line.replace(item, `getConfigDataUtils.getDataUrlByDataId(${dataID})`);
                });
                fixResultContent += "\n";
            }
            return line;
        });
        if (findMarchMariaUrl) {
            let replaceImportLine = searchSearchListLine;
            let searchListLine = lines[searchSearchListLine];
            let reactSingleAppListLine = lines[searchReactSingleAppListLine];
            let exp = new RegExp("(?:import {)(?:.*)(?:} from (|'|\"))(?:" + relativePath + ")(?:(|'|\");)", "g");
            let currentLine = searchListLine;
            let foundImport = currentLine.match(exp);
            if (!foundImport) {
                currentLine = reactSingleAppListLine;
                replaceImportLine = searchReactSingleAppListLine;
                foundImport = currentLine.match(exp);
            }
            let workingString = foundImport[0];
            let replaceTarget = useSemiColon === true ? /{|}|from|import|'|"| |;/gi : /{|}|from|import|'|"| |/gi;
            workingString = workingString.replace(replaceTarget, "").replace(relativePath, "");
            let importArray = workingString.split(",");
            importArray.push("getConfigDataUtils");
            let newImport = createImportStatement(importArray.join(", "), relativePath);
            currentLine = currentLine.replace(exp, newImport);
            flatMapLines[replaceImportLine] = currentLine;
        }
        return flatMapLines;
    }

    for (let i = 0; i < jsFiles.length; i += 1) {
        const jsPath = jsFiles[i];
        let s1 = path.basename(jsPath);
        let jsDirPath = path.join(jsPath, "../");
        let path2 = path.relative(rootPath, jsDirPath);
        const content = await fs.readFile(jsPath, "utf8");
        let newContent = getSearchList(content, jsPath);
        if (newContent) {
            const newJsDirPath = path.join(tmpFolder, path2);
            await fs.ensureDir(newJsDirPath);
            const tmpFile = path.join(newJsDirPath, s1);
            newContent = newContent.join("\n");
            await fs.writeFile(tmpFile, newContent, "utf8");
        }
    }
    const resultDirPath = path.join(tmpFolder, "result.txt");
    await fs.writeFile(resultDirPath, fixResultContent, "utf8");

    console.timeEnd("Execution...");
})();
