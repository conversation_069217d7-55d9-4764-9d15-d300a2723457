{"name": "new-ccs", "version": "1.0.0", "description": "", "private": true, "dependencies": {"@antv/g2": "^4.1.1", "array-move": "^4.0.0", "echarts": "^4.9.0", "echarts-for-react": "^2.0.16", "file-saver": "^2.0.5", "jszip": "^3.6.0", "mathjs": "^13.2.0", "react-copy-to-clipboard": "5.1.0", "react-sortable-hoc": "^2.0.0", "react-single-app": "7.3.0-beta.126", "speed-measure-webpack-plugin": "^1.5.0", "typescript": "^4.8.4", "webpack-bundle-analyzer": "^4.6.1", "webpack-merge": "^5.8.0", "load-script-once": "^2.0.1"}, "devDependencies": {"@babel/core": "^7.19.3", "@babel/preset-react": "^7.18.6", "@babel/preset-typescript": "^7.18.6", "@svgr/webpack": "^5.5.0", "@types/react": "^16.14.0", "@types/react-dom": "^16.9.0", "babel-loader": "^8.2.5", "file-loader": "^4.2.0", "fs-extra": "^10.0.1", "glob": "^7.2.0", "husky": "^8.0.3", "lint-staged": "^14.0.1", "prettier": "2.8.8", "url-loader": "^3.0.0", "webpack-dev-server": "^3.2.1"}, "scripts": {"maria-replace-version": "node ./scripts/replace-search-list-version", "build": "webpack --config webpack/build.js", "build-dev": "webpack --config webpack/build-dev.js", "build-test": "webpack --config webpack/build-test.js", "build-pre": "webpack --config webpack/build-pre.js", "build-jz": "webpack --config webpack/build-jz.js", "build-inte": "webpack --config webpack/build-inte.js", "build-analy": "webpack --config webpack/build-analy.js", "start": "dd-cli start --env=integration", "prettier": "prettier -c --write **/*", "prepare": "husky install", "init": "dd-cli init", "start:integration": "dd-cli start --env=integration", "start:test": "dd-cli start --env=test", "start:pre": "dd-cli start --env=pre", "start:online": "dd-cli start --env=online", "build:integration": "dd-cli build --env=integration", "build:test": "dd-cli build --env=test", "build:pre": "dd-cli build --env=pre", "build:online": "dd-cli build --env=online"}, "keywords": [], "author": "", "license": "ISC", "lint-staged": {"**/*": "prettier --write --ignore-unknown"}}