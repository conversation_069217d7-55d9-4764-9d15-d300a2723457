version: "1.1"
"on":
  push:
    branches:
      - develop
      - feature/main
stages:
  - stage:
      - git-checkout:
          alias: git-checkout
  - stage:
      - js:
          alias: js
          params:
            build_cmd: npm run ((build_env))
            container_type: spa
            dependency_cmd: npm install --registry=https://registry.npm.taobao.org
            dest_dir: build
            workdir: ${git-checkout}
  - stage:
      - release:
          alias: release
          params:
            dice_yml: ${git-checkout}/dice.yml
            image:
              app: ${js:OUTPUT:image}
  - stage:
      - dice:
          alias: dice
          params:
            release_id_path: ${release}
