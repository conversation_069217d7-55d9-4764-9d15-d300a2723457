server {
    listen       80;
    server_name  localhost;
    location /ccs {
        root   /usr/share/nginx/html;
        index  index.htm;
        try_files $uri $uri/ /ccs/index.htm;
    }

    location /cds {
            root   /usr/share/nginx/html;
            index  index.htm;
            try_files $uri $uri/ /ccs/index.htm;
    }
    location ~ .*\.(htm|html)$ {
        root   /usr/share/nginx/html;
        add_header Cache-Control no-store;
    }
    error_page   500 502 503 504  /50x.html;
    location = /50x.html {
        root   /usr/share/nginx/html;
    }
}
