const path = require("path");
const ProgressBarPlugin = require("progress-bar-webpack-plugin");
const HappyPack = require("happypack");
const HtmlWebpackPlugin = require("html-webpack-plugin");
const config = require("../site-config");
const happyThreadPool = HappyPack.ThreadPool({
    size: 5,
});

module.exports = {
    mode: "development",
    entry: {
        index: `./src/index`,
    },
    plugins: [
        new ProgressBarPlugin(),
        new HappyPack({
            id: "babel",
            loaders: [
                {
                    path: "babel-loader",
                    query: {
                        presets: ["@babel/preset-typescript", "@babel/preset-react", "@babel/preset-env"],
                        plugins: [
                            ["@babel/plugin-proposal-decorators", { legacy: true }],
                            ["@babel/plugin-proposal-class-properties", { loose: true }],
                        ],
                    },
                    cache: true,
                },
            ],
            threadPool: happyThreadPool,
            verbose: true,
        }),
        new HappyPack({
            id: "css",
            loaders: ["style-loader", "css-loader"],
            threadPool: happyThreadPool,
            verbose: true,
        }),
        new HappyPack({
            id: "less",
            loaders: [
                "style-loader",
                "css-loader",
                {
                    loader: "less-loader",
                    options: {
                        javascriptEnabled: true,
                    },
                },
            ],
            threadPool: happyThreadPool,
            verbose: true,
        }),
        new HtmlWebpackPlugin({
            filename: `index.htm`,
            template: "./src/index.htm",
            inject: true,
            title: "",
            hash: true,
            mariaServerConfig:
                config.usrVar === "true"
                    ? `${config.mariaUrl}${config.configListUrl}`
                    : `${config.defaultConfig.mariaUrl}${config.defaultConfig.configListUrl}`,
        }),
    ],
    output: {
        filename: "[name]-[chunkhash].js",
        path: path.resolve("./", "build/ccs"),
        publicPath: "/ccs",
    },
    resolve: {
        alias: {
            "@": path.resolve("src"), // 这样配置后 @ 可以指向 src 目录
        },
        extensions: [".js", ".tsx", ".ts", ".json", ".jsx"],
        modules: [path.resolve(__dirname, "../node_modules")], // 查找第三方模块只在本项目的node_modules中查找
    },
    module: {
        rules: [
            {
                test: /\.(js|jsx|ts|tsx)$/, // 匹配.ts, tsx文件
                use: ["happypack/loader?id=babel"],
                exclude: path.resolve("./", "node_modules"), //排除node_modules目录，该目录不参与编译
            },
            {
                test: /\.css$/,
                use: ["happypack/loader?id=css"],
            },
            {
                test: /\.less$/,
                use: ["happypack/loader?id=less"],
                // exclude: path.resolve('./', 'node_modules'),
            },
            {
                test: /\.svg$/i,
                issuer: /\.[jt]sx?$/,
                use: ["@svgr/webpack"],
            },
            {
                test: /\.(?:ico|gif|png|jpg|jpeg)$/i,
                use: [
                    {
                        loader: "url-loader",
                        options: {
                            outputPath: "asset/images/", // 指定图片输入的文件夹
                            publicPath: "/ccs/asset/images", // 指定获取图片的路径
                            limit: 1024,
                            name: "[name].[hash:8].[ext]",
                        },
                    },
                ],
            },
        ],
    },
    optimization: {
        splitChunks: {
            chunks: "all",
            cacheGroups: {
                vendors: {
                    test: /[\\/]node_modules[\\/]/,
                    name: "vendors",
                    priority: -10,
                    enforce: true,
                },
                default: false,
            },
        },
    },
};
