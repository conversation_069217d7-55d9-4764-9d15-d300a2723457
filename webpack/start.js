const { merge } = require("webpack-merge");
const common = require("./common.js");
const webpack = require("webpack");

module.exports = merge(common, {
    mode: "development",
    devtool: "source-map",
    plugins: [
        new webpack.DefinePlugin({
            BUILD_ENV: JSON.stringify("integration"),
        }),
    ],
    devServer: {
        contentBase: "./dist",
        host: "127.0.0.1",
        // port: 80,
        historyApiFallback: {
            rewrites: [{ from: /.*/, to: `/ccs/index.htm` }],
        },
    },
});
