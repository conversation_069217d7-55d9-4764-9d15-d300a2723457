const { merge } = require("webpack-merge");
const common = require("./common.js");
const CleanWebpackPlugin = require("clean-webpack-plugin");
const webpack = require("webpack");

module.exports = merge(common, {
    mode: "production",
    devtool: "source-map",
    plugins: [
        new CleanWebpackPlugin(),
        new webpack.DefinePlugin({
            BUILD_ENV: JSON.stringify("pre"),
        }),
    ],
});
