import React, { useState, useEffect, useImperativeHandle, useRef } from "react";
import { lib } from "react-single-app";
import {
    Space,
    Button,
    message,
    Image,
    Steps,
    Modal,
    Form,
    Input,
    Divider,
    Typography,
    Table,
    Select,
    Descriptions,
    InputNumber,
} from "antd";
import DetailList from "../components/DetailList";
import FormList from "../components/FormList";
import "./record-detail.less";
const { Step } = Steps;
const FormItem = Form.Item;
const { TextArea } = Input;
const { Title } = Typography;
const { Option } = Select;
// 基础信息配置列表
const BaseList = [
    // { label: "京东sku", labelKey: "skuId" },
    // { label: "主sku", labelKey: "mainSkuId" },
    { label: "备案类型", labelKey: "typeDesc" },
    { label: "主商品编码", labelKey: "emg" },
    { label: "原产国", labelKey: "originAreaDesc" },
    // { label: "是否保税区商品", labelKey: "depotItemFlag", render: detail => (detail?.depotItemFlag ? "是" : "否") },
    { label: "税务承担方", labelKey: "taxBear" },
    { label: "商品名称（中文）", labelKey: "itemChineseName" },
    { label: "商品名称（英文）", labelKey: "itemEnglishName" },
    { label: "型号", labelKey: "model" },
    { label: "规格", labelKey: "packSpecification" },
    { label: "申报/合同单位", labelKey: "declareAgreementUnitStr" },
    { label: "品牌（中文）", labelKey: "chineseBrand" },
    { label: "品牌（英文）", labelKey: "englishBrand" },
    { label: "商品单价（元）", labelKey: "itemSalePrice" },
    { label: "毛重（kg）", labelKey: "maoZhong" },
    { label: "净重（kg）", labelKey: "jingZhong" },
    { label: "条码", labelKey: "upc" },
    { label: "体积", labelKey: "volume" },
    { label: "保质期（天）", labelKey: "safeDays" },
    { label: "销售网址", labelKey: "saleWebsite" },

    {
        label: "商品图片",
        span: 3,
        render: detail => detail?.itemPics?.map((item, index) => <Image width={200} src={item} key={index} />),
    },
];
// 申报信息配置列表
const DeclareList = [
    // { label: "hs编码", labelKey: "hs" },
    // { label: "增值税率", labelKey: "vat" },
    // { label: "消费税率", labelKey: "consumption" },
    { label: "海关申报要素", labelKey: "declareElement" },
    { label: "成分", labelKey: "element" },
    { label: "功能", labelKey: "ability" },
    { label: "用途", labelKey: "purpose" },
    { label: "生产企业名称", labelKey: "productCompanyName" },
    { label: "生产企业地址", labelKey: "productCompanyAddress" },
    { label: "供应商", labelKey: "shangJiaContacts" },
    { label: "备注", labelKey: "remark" },
];
// 企业备案信息配置列表
const RecordList = [
    { label: "商家ID", labelKey: "shangJiaId" },
    { label: "ECLP事业部编号", labelKey: "eclpCode" },
    { label: "ECLP事业部名称", labelKey: "eclpName" },
    { label: "跨境业务模式", labelKey: "mode" },
    { label: "保税区", labelKey: "depotName" },
    { label: "关区", labelKey: "customs" },
    { label: "服务商", labelKey: "serviceId" },
    { label: "平台编码", labelKey: "platformId" },
    { label: "平台名称", labelKey: "platformName" },
];
// 商家信息配置列表
const MerchantList = [
    { label: "店铺名称", labelKey: "shopName" },
    { label: "商家联系方式", labelKey: "shangJiaPhone" },
    { label: "商家邮箱", labelKey: "shangJiaEmail" },
];
const taxFloatMap = ["不浮动", "化妆品浮动", "面膜浮动"];
// 备案完成推送配置列表
const FinishList = [
    { label: "商品货号", labelKey: "itemCode" },
    { label: "海关备案号", labelKey: "hgRecordCode" },
    { label: "海关原产国", labelKey: "hgOriginCountryStr" },
    { label: "国检备案号", labelKey: "gjRecordCode" },
    { label: "国检原产国", labelKey: "gjOriginCountryStr" },
    { label: "海关计量单位", labelKey: "hgMeteringUnitStr" },
    { label: "国检计量单位", labelKey: "gjMeteringUnitStr" },
    { label: "税率标识", labelKey: "taxFloat", render: detail => taxFloatMap[detail?.taxFloat] || "" },
];
// 弹窗表单配置列表
const ModalFormList = [
    { labelName: "商品货号", labelCol: 24, labelKey: "itemCode", type: "INPUT", required: true, maxLength: 40 },
    { labelName: "海关备案号", labelCol: 24, labelKey: "hgRecordCode", type: "INPUT", required: true, maxLength: 40 },
    {
        labelName: "海关原产国",
        labelKey: "hgOriginCountry",
        type: "SELECT",
        xhr: "/ccs/customs/listCountry",
        required: true,
        together: true,
        labelCol: 24,
    },
    { labelName: "国检备案号", labelCol: 24, labelKey: "gjRecordCode", type: "INPUT", required: true, maxLength: 100 },
    {
        labelName: "国检原产国",
        labelKey: "gjOriginCountry",
        type: "SELECT",
        xhr: "/ccs/ciq/listCiqCountry",
        list: [],
        required: true,
        together: true,
        labelCol: 24,
    },
    {
        labelName: "海关计量单位",
        labelKey: "hgMeteringUnit",
        type: "SELECT",
        labelCol: 24,
        xhr: "/ccs/customs/listUom",
        list: [],
        required: true,
        stateKey: "unitList",
        together: true,
    },
    {
        labelName: "国检计量单位",
        labelKey: "gjMeteringUnit",
        type: "SELECT",
        labelCol: 24,
        xhr: "/ccs/customs/listUom",
        list: [],
        required: true,
        together: true,
    },
    {
        labelName: "hs编码",
        labelKey: "hs",
        type: "SELECT",
        xhr: "/ccs/customs/listHs",
        together: true,
        list: [],
        required: true,
        labelCol: 24,
        needCopy: true,
    },
    {
        labelName: "增值税率",
        labelKey: "vat",
        type: "INPUT",
        rules: [
            { required: true, message: "请输入增值税率" },
            {
                validator(_, value) {
                    console.log(_, value);
                    if (!value) {
                        return Promise.resolve();
                    }
                    if (/^(\d{1,3})$/.test(value)) {
                        return Promise.resolve();
                    } else {
                        return Promise.reject("请输入3位整数");
                    }
                },
            },
        ],
    },
    {
        labelName: "从价消费税率",
        labelCol: 24,
        labelKey: "adValoremConsumption",
        type: "INPUT",
        rules: [
            { required: true, message: "请输入从价消费税率" },
            {
                validator(_, value) {
                    console.log(_, value);
                    if (!value) {
                        return Promise.resolve();
                    }
                    if (/^\d+(\.\d{1,4})?$/.test(value)) {
                        return Promise.resolve();
                    } else {
                        return Promise.reject("小数点最多4位");
                    }
                },
            },
        ],
    },
    {
        labelName: "从量消费税率",
        labelCol: 24,
        labelKey: "consumption",
        type: "INPUT",
        tooltip: "例如：从量消费税率 0.2495元/升，需填写0.2495",
        rules: [
            {
                validator(_, value) {
                    console.log(_, value);
                    if (!value) {
                        return Promise.resolve();
                    }
                    if (/^\d+(\.\d{1,4})?$/.test(value)) {
                        return Promise.resolve();
                    } else {
                        return Promise.reject("小数点最多4位");
                    }
                },
            },
        ],
    },

    {
        labelName: "税率标识",
        labelKey: "taxFloat",
        type: "SELECT",
        tooltip: (
            <>
                0-不浮动＋从价消费稅
                <br />
                1-化妆品浮动＋从价消费稅
                <br />
                2-面膜浮动＋从价消费税
                <br />
                3- 黄酒不浮动＋从量消费税
                <br />
                4- 机油不浮动＋从量消费税
                <br />
                5-酒水不浮动＋复合消费税（从价和从量都填写）
                <br />
                6-啤酒浮动＋从量消费税
                <br />
                7-手表浮动＋从价消费稅
            </>
        ),
        labelCol: 24,
        // together: true,
        list: [],
        xhr: "/ccs/jdGoodsRecord/taxFloatList",
        required: true,
    },
];

export const FormShow = React.forwardRef(({ detail, disabled }, ref) => {
    const [unitList, setUnitList] = useState([]);
    const [validateObj, setValidateObj] = useState({});
    const [form] = Form.useForm();
    // 审核通过弹窗表单列表
    const [formList, setFormList] = useState(ModalFormList);

    useEffect(() => {
        formList.map((item, index) => {
            if (item.xhr) {
                lib.request({
                    url: item.xhr,
                    success: res => {
                        setFormList(formList => {
                            formList[index].list = res;
                            if (item.stateKey) {
                                setUnitList(res);
                            }
                            return formList.slice();
                        });
                    },
                });
            }
        });
    }, []);
    useImperativeHandle(ref, () => {
        return {
            submit: fn => {
                onOk(fn);
            },
            form: form,
        };
    });
    function onOk(fn) {
        form.validateFields().then(values => {
            if (values.secondAmount && !values.legalSecondUnit) {
                setValidateObj({
                    validateStatus: "error",
                    help: "请选择法定第二单位",
                });
                return;
            } else if (!values.secondAmount && values.legalSecondUnit) {
                setValidateObj({
                    validateStatus: "error",
                    help: "请输入法定第二数量",
                });
                return;
            } else {
                setValidateObj({});
            }
            if (typeof values.legalSecondUnit === "undefined") {
                values.legalSecondUnit = "";
            }
            fn(values);
        });
    }

    useEffect(() => {
        console.log(disabled);
        formList.map(item => {
            item.disabled = disabled;
        });
        setFormList([...formList]);
    }, [disabled]);
    useEffect(() => {
        form.setFieldsValue(detail);
    }, [detail]);
    return (
        <Form form={form} initialValues={detail} layout="vertical">
            <FormList {...{ configList: formList, formItemFloat: true, form }}>
                <FormItem required style={{ width: "45%" }} label="法定第一计量数量和单位">
                    <Input.Group compact>
                        <FormItem
                            noStyle
                            name="firstAmount"
                            rules={[{ required: true, message: "请输入法定第一计量数量" }]}>
                            <InputNumber disabled={disabled} style={{ width: "50%" }} />
                        </FormItem>
                        <FormItem
                            noStyle
                            name="legalFirstUnit"
                            rules={[{ required: true, message: "请选择法定第一计量单位" }]}>
                            <Select disabled={disabled} style={{ width: "50%" }} showSearch optionFilterProp="children">
                                {unitList.map(item => (
                                    <Option value={item.id} key={item.id}>
                                        {item.id}-{item.name}
                                    </Option>
                                ))}
                            </Select>
                        </FormItem>
                    </Input.Group>
                </FormItem>
                <FormItem style={{ width: "45%" }} label="法定第二计量数量和单位" {...validateObj}>
                    <Input.Group compact>
                        <FormItem noStyle name="secondAmount">
                            <InputNumber disabled={disabled} style={{ width: "50%" }} />
                        </FormItem>
                        <FormItem noStyle name="legalSecondUnit">
                            <Select
                                disabled={disabled}
                                style={{ width: "50%" }}
                                showSearch
                                allowClear
                                optionFilterProp="children">
                                {unitList.map(item => (
                                    <Option value={item.id} key={item.id}>
                                        {item.id}-{item.name}
                                    </Option>
                                ))}
                            </Select>
                        </FormItem>
                    </Input.Group>
                </FormItem>
                <FormItem label="是否境外套盒" name="ifOverSeaBox">
                    <Select disabled={true} style={{ width: "50%" }} showSearch allowClear optionFilterProp="children">
                        {[
                            { id: 1, name: "是" },
                            { id: 0, name: "否" },
                        ].map(item => (
                            <Option value={item.id} key={item.id}>
                                {item.name}
                            </Option>
                        ))}
                    </Select>
                </FormItem>
            </FormList>
        </Form>
    );
});

// 海关审核通过按钮
function PassButtonByCustom({ refreshDetail, detail }) {
    const [visible, setVisible] = useState(false);
    const ref = useRef();

    function onOk() {
        ref.current.submit(values => {
            lib.request({
                url: "/ccs/jdGoodsRecord/approve",
                data: {
                    approveFlag: true,
                    id: lib.getParam("id"),
                    ...values,
                },
                needMask: true,
                success: res => {
                    message.success("海关审核通过");
                    setVisible(false);
                    refreshDetail();
                },
            });
        });
    }

    return (
        <React.Fragment>
            <Modal
                title="请填写以下信息，完成备案"
                open={visible}
                onOk={onOk}
                onCancel={() => setVisible(false)}
                width={800}>
                <FormShow ref={ref} detail={detail} />
            </Modal>
            <Button type="primary" onClick={() => setVisible(true)}>
                海关审核通过
            </Button>
        </React.Fragment>
    );
}

// 海关审核驳回按钮
function RejectButtonByCustom({ refreshDetail, formCheck }) {
    const [visible, setVisible] = useState(false);
    const [form] = Form.useForm();
    function onOk() {
        form.validateFields().then(values => {
            lib.request({
                url: "/ccs/jdGoodsRecord/approve",
                data: {
                    approveFlag: false,
                    id: lib.getParam("id"),
                    ...values,
                    consumption: parseFloat(values.consumption),
                    adValoremConsumption: parseFloat(values.adValoremConsumption),
                },
                needMask: true,
                success: res => {
                    message.success("海关审核驳回");
                    refreshDetail();
                    setVisible(false);
                },
            });
        });
    }

    return (
        <React.Fragment>
            <Modal title="驳回原因" open={visible} onOk={onOk} onCancel={() => setVisible(false)} destroyOnClose>
                <Form form={form} preserve={false}>
                    <FormItem
                        name="backReason"
                        rules={[
                            {
                                required: true,
                                message: "请输入驳回原因",
                            },
                        ]}
                        label="驳回原因">
                        <Input maxLength="200" />
                    </FormItem>
                </Form>
            </Modal>
            <Button
                type="danger"
                onClick={() => {
                    formCheck(() => {
                        setVisible(true);
                    });
                }}>
                海关审核驳回
            </Button>
        </React.Fragment>
    );
}

// 审核通过按钮
function PassButton({ refreshDetail }) {
    function passHandle() {
        let modal = Modal.confirm({
            title: "提示",
            content: "是否确认商品备案信息合格，审核通过?",
            onOk: () => {
                lib.request({
                    url: "/ccs/jdGoodsRecord/approveCustom",
                    data: {
                        approveFlag: true,
                        ids: [lib.getParam("id")],
                    },
                    needMask: true,
                    success: res => {
                        message.success("审核通过");
                        refreshDetail();
                        modal.destroy();
                    },
                });
            },
        });
    }
    return (
        <React.Fragment>
            <Button type="primary" onClick={passHandle}>
                审核通过
            </Button>
        </React.Fragment>
    );
}

// 审核驳回按钮
function RejectButton({ refreshDetail }) {
    const [visible, setVisible] = useState(false);
    const [form] = Form.useForm();
    function onOk() {
        form.validateFields().then(values => {
            lib.request({
                url: "/ccs/jdGoodsRecord/approveCustom",
                data: {
                    approveFlag: false,
                    ids: [lib.getParam("id")],
                    ...values,
                },
                needMask: true,
                success: res => {
                    message.success("审核驳回");
                    refreshDetail();
                    setVisible(false);
                },
            });
        });
    }

    return (
        <React.Fragment>
            <Modal title="驳回原因" open={visible} onOk={onOk} onCancel={() => setVisible(false)} destroyOnClose>
                <Form form={form} preserve={false}>
                    <FormItem name="backReason" label="驳回原因">
                        <TextArea maxLength="200" />
                    </FormItem>
                </Form>
            </Modal>
            <Button
                type="danger"
                onClick={() => {
                    setVisible(true);
                }}>
                审核驳回
            </Button>
        </React.Fragment>
    );
}

// 审核状态
function AuditStatus({ detail, refreshDetail, formCheck }) {
    let status = "process";
    if (detail?.nodeList?.length) {
        status = detail.nodeList.some(item => item.failFlag)
            ? "error"
            : detail?.nodeList?.every(item => item.active)
            ? "finish"
            : "process";
    }
    return (
        <div>
            <div className="audit-status">
                <Steps current={detail?.nodeList?.filter(item => item.active).length - 1} status={status}>
                    {detail?.nodeList?.map((item, index) => (
                        <Step title={item.text} key={index} description={item.failFlag && detail.backReason} />
                    ))}
                </Steps>
            </div>
            <div style={{ padding: "20px 10px" }}>
                <DetailList
                    configList={[
                        { labelKey: "skuId", label: "京东sku" },
                        { labelKey: "jdRecordNo", label: "JD商品备案号" },
                        // 商品编码
                        { labelKey: "jdEmg", label: "商品编码" },
                        { labelKey: "firstRecordTime", label: "备案创建时间" },
                        { labelKey: "extend", label: "发起修改的信息" },
                    ]}
                    detail={detail}></DetailList>
            </div>
        </div>
    );
}

// 商品列表
function GoodsTable({ detail }) {
    let dataSource = [{ upc: detail.upc, originArea: detail.originArea, id: 1 }];
    const columns = [
        { title: "条形码（UPC）", dataIndex: "upc" },
        { title: "原产国/地区", dataIndex: "originArea" },
    ];
    return <Table pagination={false} dataSource={dataSource} columns={columns} rowKey="id" />;
}

// 境外套盒信息
function OverSeaBoxTable({ detail }) {
    const columns = [
        { title: "货品名", dataIndex: "goodsName" },
        { title: "货品ID", dataIndex: "goodsId" },
        { title: "货品价格", dataIndex: "price" },
        { title: "货品数量", dataIndex: "quantity" },
    ];
    return (
        <Table
            pagination={false}
            dataSource={detail?.overSeaBoxDTO ? [detail?.overSeaBoxDTO] : []}
            columns={columns}
            rowKey="goodsId"
        />
    );
}

// 备案完成推送模块
function FinishCom({ detail }) {
    return (
        <DetailList {...{ configList: FinishList, detail }}>
            <Descriptions.Item label="法定第一计量数量和单位">
                {detail?.firstAmount}
                {detail?.legalFirstUnitStr}
            </Descriptions.Item>
            <Descriptions.Item label="法定第二计量数量和单位">
                {detail?.secondAmount}
                {detail?.legalSecondUnitStr}
            </Descriptions.Item>
        </DetailList>
    );
}

function FileTable({ list }) {
    const columns2 = [
        {
            title: "附件名称",
            dataIndex: "name",
        },
        {
            title: "操作",
            render: row => {
                return (
                    <React.Fragment>
                        <Space>
                            <a className="link" href={row.url} target="_blank">
                                预览
                            </a>
                            <a
                                className="link"
                                onClick={() => {
                                    window.open(row.url);
                                }}>
                                下载
                            </a>
                        </Space>
                    </React.Fragment>
                );
            },
        },
    ];
    return (
        <div>
            <Table
                dataSource={
                    list?.map(item => {
                        return { url: item, name: item };
                    }) || []
                }
                columns={columns2}
                rowKey="id"
                pagination={false}></Table>
        </div>
    );
}

// 详情模块
function DetailItem({ title, children, last }) {
    return (
        <div
            className="detail-item"
            style={
                title === ""
                    ? {
                          // position: "sticky",
                          paddingTop: "12px",
                          background: "#fff",
                          // top: 0,
                          zIndex: "200",
                      }
                    : { paddingTop: "12px" }
            }>
            <Title level={5}>{title}</Title>
            {children}
            {/* {!last && <Divider />} */}
        </div>
    );
}

export default function () {
    const [detail, setDetail] = useState({});
    const ref = useRef();
    const ref1 = useRef();
    function fetchDetail() {
        return new Promise((success, fail) => {
            lib.request({
                url: "/ccs/jdGoodsRecord/detail",
                data: {
                    id: lib.getParam("id"),
                },
                needMask: true,
                success,
                fail,
            });
        });
    }
    function refreshDetail() {
        fetchDetail().then(res => {
            res.goodsCode = res.itemCode;
            res.taxFloat = String(res.taxFloat);
            setDetail(res);
        });
    }

    const formCheck = fn => {
        console.log(ref.current);
        if (ref.current) {
            ref.current.submit(values => {
                console.log("values:", values);
                fn();
            });
        } else {
            fn();
        }
    };
    useEffect(() => {
        refreshDetail();
    }, []);

    const ComList = [
        { title: "", children: <AuditStatus {...{ detail, refreshDetail, formCheck }} /> },
        { title: "基础信息", children: <DetailList {...{ configList: BaseList, detail }} /> },
        // { title: "商品列表", children: <GoodsTable {...{ detail }} /> },
        { title: "境外套盒信息", children: <OverSeaBoxTable {...{ detail }} /> },
        { title: "申报信息", children: <DetailList {...{ configList: DeclareList, detail }} /> },
        { title: "企业备案标识", children: <DetailList {...{ configList: RecordList, detail }} /> },
        { title: "商家信息", children: <DetailList {...{ configList: MerchantList, detail }} />, last: true },
        {
            title: "附件",
            children: <FileTable list={detail?.goodsAttachList || []} />,
        },
    ];
    return (
        <div className="record-detail">
            <div style={{ width: "69%", marginTop: "20px" }}>
                {ComList.map((item, index) => (
                    <DetailItem {...item} key={index} />
                ))}
            </div>
            <div style={{ width: "31%", paddingLeft: "10px" }}>
                <div
                    style={{
                        backgroundColor: "#fff",
                        padding: "20px 0 20px 20px",
                        borderRadius: "8px",
                        marginTop: "20px",
                    }}>
                    <div style={{ display: "flex", alignItems: "center", flexDirection: "row", paddingBottom: "15px" }}>
                        <h3 style={{ marginBottom: 0, marginRight: "10px" }}>审核信息</h3>
                        {["WAIT_EXAMINE_HG", "WAIT_EXAMINE"].includes(detail?.flowState) && (
                            <Button
                                type="primary"
                                onClick={() => {
                                    ref.current.submit(values1 => {
                                        console.log("values1:", values1);
                                        lib.request({
                                            url: "/ccs/jdGoodsRecord/updateExamineInfo",
                                            data: {
                                                ...values1,
                                                consumption: parseFloat(values1.consumption),
                                                adValoremConsumption: parseFloat(values1.adValoremConsumption),

                                                id: lib.getParam("id"),
                                            },
                                            needMask: true,
                                            success: res => {
                                                message.success("保存成功");
                                                fetchDetail();
                                            },
                                        });
                                    });
                                }}>
                                保存
                            </Button>
                        )}
                    </div>
                    <FormShow
                        ref={ref}
                        detail={detail}
                        disabled={!["WAIT_EXAMINE_HG", "WAIT_EXAMINE"].includes(detail?.flowState)}
                    />
                    {detail?.flowState === "WAIT_EXAMINE_HG" && (
                        <Space>
                            {/* <PassButtonByCustom {...{ refreshDetail, detail }} /> */}
                            <Button
                                type="primary"
                                onClick={() => {
                                    ref.current.submit(values => {
                                        lib.request({
                                            url: "/ccs/jdGoodsRecord/approve",
                                            data: {
                                                approveFlag: true,
                                                id: lib.getParam("id"),
                                                ...values,
                                                consumption: parseFloat(values.consumption),
                                                adValoremConsumption: parseFloat(values.adValoremConsumption),
                                                goodsCode: values.itemCode,
                                            },
                                            needMask: true,
                                            success: res => {
                                                message.success("海关审核通过");
                                                refreshDetail();
                                            },
                                        });
                                    });
                                }}>
                                海关审核通过
                            </Button>
                            <RejectButtonByCustom {...{ refreshDetail, formCheck: formCheck }} />
                        </Space>
                    )}
                    {detail?.flowState === "WAIT_EXAMINE" && (
                        <Space>
                            <PassButton {...{ refreshDetail }} />
                            <RejectButton {...{ refreshDetail }} />
                        </Space>
                    )}
                </div>
            </div>
        </div>
    );
}
