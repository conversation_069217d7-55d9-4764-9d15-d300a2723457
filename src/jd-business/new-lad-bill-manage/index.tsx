import {
    getConfigDataUtils,
    lib,
    SearchList,
    ClassSearchListProps,
    DDYObject,
    ClassSearchListState,
} from "react-single-app";
import { NewLadBillManageState, NewLadBillManageProps } from "./type";
import axios from "axios";
import React from "react";
import { Space, Button, message, Tabs, Modal } from "antd";
import moment from "moment";
//@ts-ignore
import EditModal from "../../components/EditModal";
export default class NewLadBillManage extends SearchList<NewLadBillManageProps, NewLadBillManageState> {
    async getConfig() {
        let url = getConfigDataUtils.getDataUrlByDataId(638);
        const res = await axios.get(url);
        return res.data.data;
    }

    renderLeftOperation() {
        return (
            <>
                <Button
                    onClick={() => {
                        lib.openPage(`/create-lad-bill?page_title=新建提单&type=watch`, () => {
                            this.load();
                        });
                    }}>
                    新建
                </Button>
            </>
        );
    }

    deleteRow(id) {
        Modal.confirm({
            title: "是否删除提单",
            onOk: () => {
                lib.request({
                    url: "/ccs/jdLadNew/delete",
                    data: { id },
                    success: () => {
                        message.success("删除成功");
                        this.load();
                    },
                    fail(code, msg, data) {},
                });
            },
        });
    }

    myOperation(row) {
        return (
            <>
                <Button
                    type="link"
                    onClick={() => {
                        this.deleteRow(row.id);
                    }}>
                    删除
                </Button>
            </>
        );
    }

    renderLadNo(row: any) {
        return (
            <span
                className="link"
                onClick={() => lib.openPage(`/create-lad-bill?id=${row.id}&page_title=提单详情&type=watch`)}>
                {row.ladNo}
            </span>
        );
    }
}
