import React, { useEffect, useState } from "react";
import { Typo<PERSON>, Button, Table, Row } from "antd";
import { lib } from "react-single-app";
import { tallyingColumns } from "../config";
export default () => {
    const [data, setData] = useState([]);
    const [pagination, setPagination] = useState({
        page: 1,
        pageSize: 20,
        total: 0,
    });
    const getdatas = (page: number, pageSize: number) => {
        lib.request({
            url: "/ccs/jdPurchaseOrderNew/pagingTallyDetail",
            data: {
                pageSize,
                currentPage: page,
                id: lib.getParam("id"),
            },
            success: res => {
                setData(res.dataList);
                setPagination({
                    page: res.page.currentPage,
                    pageSize: res.page.pageSize,
                    total: res.page.totalCount,
                });
            },
        });
    };

    useEffect(() => {
        getdatas(1, 20);
    }, []);

    return (
        <>
            <Row align="middle" style={{ padding: "20px 0" }}>
                <Typography.Title level={4} style={{ margin: "10px 0" }}>
                    理货报告
                </Typography.Title>
                <Button
                    style={{ marginLeft: "30px" }}
                    onClick={() => {
                        lib.request({
                            url: "/ccs/jdPurchaseOrderNew/exportTallyReport",
                            needMask: true,
                            data: { id: lib.getParam("id") },
                            success: res => {
                                lib.openPage("/download-center?page_title=下载中心");
                            },
                        });
                    }}>
                    导出
                </Button>
            </Row>
            <Table
                columns={tallyingColumns}
                dataSource={data}
                scroll={{ x: 2000, y: 500 }}
                pagination={{
                    onChange(page, pageSize) {
                        getdatas(page, pageSize);
                    },
                    total: pagination.total,
                    showSizeChanger: true,
                    pageSize: pagination.pageSize,
                    current: pagination.page,
                }}
            />
        </>
    );
};
