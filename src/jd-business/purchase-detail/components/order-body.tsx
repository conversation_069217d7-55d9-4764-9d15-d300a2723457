import React, { useState, useRef, useEffect } from "react";
import { detailResVoList, enumEoStatus, IPoDetail, LadObj } from "../type";
import { Form, Table, Button, message, Space, Modal, Typography } from "antd";
import FormList from "./FormList";
import "./order-body.less";
//@ts-ignore
import NewModal from "../../../components/NewModal";
import CustomsInfo from "./CustomInfo";
import DeclareteInfo from "./DeclarateInfo";
import TallyingTable from "./tallying-table";
import moment from "moment";

import {
    purchaseDetailConfig,
    formItemLayout,
    goodsColumns,
    LadingInfo,
    LadingColumns,
    CustomsDeclareColumn,
    CustomsDeclareConfig,
    UploadFilesConfig,
    GoodsConfig,
    connectLadConfig,
} from "../config";
import { lib } from "react-single-app";
import Editable, { EditableRef } from "./edit-table";
import GoodsTable from "./goods-table";
export default (props: IPoDetail) => {
    const id = lib.getParam("id");

    const [ladOpen, setLadOpen] = useState<boolean>(false);
    const [uploadOpen, setUploadOpen] = useState<boolean>(false);

    const [customsEdit, setCustomsEdit] = useState<boolean>(false);
    const [dataSource, setDataSource] = useState<any[]>([]);
    const ladRef = useRef<any>();
    const updateData = (e: [any, any]) => {
        const timeBegin = e[0].hours(0).minutes(0).seconds(0).valueOf();
        const timeEnd = e[1].hours(23).minutes(59).seconds(59).valueOf();
        lib.request({
            url: "/ccs/jdLadNew/listLadNoByCreateTime",
            data: { timeBegin, timeEnd },
            success(data) {
                ladModalConfig[1].list = data;
                setLadModalConfig([...ladModalConfig]);
            },
            fail(code, msg, data) {},
        });
    };
    const [ladModalConfig, setLadModalConfig] = useState<any[]>(connectLadConfig(updateData));
    const [form] = Form.useForm();
    const [form1] = Form.useForm();

    const renderFirst = (e: LadObj) => {
        return (
            <Space>
                {e.ladNo}
                {![enumEoStatus.DECLARE_FINISH, enumEoStatus.CANCELED].includes(props.poStatus) && (
                    <Button
                        type="primary"
                        onClick={() => {
                            setLadOpen(true);
                            const nowTime = moment().hours(23).minutes(59).seconds(59);
                            const beforeTime = moment(new Date()).subtract(1, "months").hours(0).minutes(0).seconds(0);
                            ladRef?.current?.setFormValue([
                                {
                                    name: "LadCreateTime",
                                    value: [beforeTime, nowTime],
                                },
                            ]);
                            updateData([beforeTime, nowTime]);
                        }}
                        style={{ marginLeft: "10px" }}>
                        关联
                    </Button>
                )}
            </Space>
        );
    };

    const ladHandOk = (data: any) => {
        lib.request({
            url: "/ccs/jdPurchaseOrderNew/associateLad",
            data: { ...data, id },
            success(data) {
                setLadOpen(false);
                props.reload();
            },
            fail(code, msg, data) {},
        });
    };

    const ladProps = {
        visible: ladOpen,
        configList: ladModalConfig,
        title: "关联提单",
        onOk: ladHandOk,
        ref: ladRef,
        onCancel: () => {
            setLadOpen(false);
        },
    };

    const uploadHandOK = (data: any) => {
        data["attachmentName"] = data["attachmentUrl"][0].key;
        data["attachmentUrl"] = data["attachmentUrl"][0].url;
        lib.request({
            url: "/ccs/jdPurchaseOrderNew/uploadAttachment",
            data: { ...data, id },
            success(data) {
                message.success("上传附件成功");
                props.reload();
            },
            fail(code, msg, data) {},
        });
        setUploadOpen(false);
    };

    const uploadProps = {
        visible: uploadOpen,
        configList: UploadFilesConfig,
        title: "添加附件",
        onOk: uploadHandOK,
        onCancel: () => {
            setUploadOpen(false);
        },
    };

    const ladConfig = LadingInfo(renderFirst);

    const initTableData = () => {
        const arr = [];
        if (props.returnCert) {
            arr.push({
                fileType: "退运凭证",
                type: "return",
                fileName: props.returnCert,
                index: arr.length,
            });
        }
        if (props.improveCert) {
            arr.push({
                fileType: "改单凭证",
                type: "improve",
                fileName: props.improveCert,
                index: arr.length,
            });
        }
        if (props.electronicBill) {
            arr.push({
                fileType: "电子税单",
                type: "tax",
                fileName: props.electronicBill,
                index: arr.length,
            });
        }
        setDataSource(arr);
    };

    const delTableItem = (row: { fileType: string; fileName: string; index: number; type: string }) => {
        Modal.confirm({
            title: "确认删除吗",
            onOk: () => {
                lib.request({
                    url: "/ccs/jdPurchaseOrderNew/deleteAttachment",
                    data: { id: id, type: row.type },
                    success(data) {
                        message.success("删除成功");
                        props.reload();
                    },
                    fail(code, msg, data) {},
                });
            },
        });
    };

    useEffect(() => {
        initTableData();
    }, [props]);

    return (
        <div className="order-body">
            <Typography.Title level={4} style={{ margin: "10px 0" }}>
                采购单信息
            </Typography.Title>
            <FormList configList={purchaseDetailConfig} form={form} formItemFloat={true} detail={props} />

            {/**
             * 商品明细table
             */}
            <GoodsTable reload={props.reload} poStatus={props.poStatus} />

            {/**
             * 理货报告table
             */}
            <TallyingTable />

            <CustomsInfo detail={props} reload={props.reload} />
            <Typography.Title level={4} style={{ margin: "10px 0" }}>
                提单信息
            </Typography.Title>
            <FormList configList={ladConfig} form={form1} formItemFloat={true} detail={props} />
            <Table rowKey={"index"} columns={LadingColumns} dataSource={props.jdLadDetailAddParamList} />
            <DeclareteInfo detail={{ ...props }} reload={props.reload} />
            {[
                enumEoStatus.WAIT_AUDIT,
                enumEoStatus.START_PRE_DECLARE,
                enumEoStatus.WAIT_ENTER,
                enumEoStatus.WAIT_TALLY,
                enumEoStatus.WAIT_DEFINITE,
                enumEoStatus.WAIT_RETURN,
                enumEoStatus.WAIT_IMPROVE,
            ].includes(props.poStatus) && (
                <Button
                    onClick={() => {
                        setUploadOpen(true);
                    }}
                    style={{ margin: "15px 0" }}>
                    添加附件
                </Button>
            )}
            <Table
                rowKey={"index"}
                columns={CustomsDeclareColumn(row => {
                    return (
                        ![enumEoStatus.DECLARE_FINISH, enumEoStatus.CANCELED].includes(props.poStatus) && (
                            <Button
                                type="link"
                                onClick={() => {
                                    delTableItem(row);
                                }}>
                                删除
                            </Button>
                        )
                    );
                })}
                dataSource={dataSource}
                pagination={false}
            />

            <NewModal {...ladProps} />

            <NewModal {...uploadProps} />

            {/* <NewModal {...editProps} /> */}
        </div>
    );
};
