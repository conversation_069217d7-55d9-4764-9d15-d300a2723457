import React, { useState, useEffect } from "react";
import { CustomsDeclareConfig } from "../config";
import { Form, Button, message, Typography } from "antd";
import FormList from "./FormList";
import { DeclareteInfoProps, enumEoStatus } from "../type";
import { lib } from "react-single-app";
import moment from "moment";
// import useEffect from 'react';
export default (props: DeclareteInfoProps) => {
    const [infoEdit, setInfoEdit] = useState(false);
    const [config, setConfig] = useState(
        CustomsDeclareConfig(str => {
            return <div>{str}</div>;
        }),
    );
    const [form] = Form.useForm();

    const save = () => {
        form.validateFields().then(() => {
            const data = form.getFieldsValue();
            console.log(data);
            data.applyReturnTime && (data.applyReturnTime = data.applyReturnTime.valueOf());
            data.applyImproveTime && (data.applyImproveTime = data.applyImproveTime.valueOf());
            data.additionalTaxAmount && (data.additionalTaxAmount = +data.additionalTaxAmount);
            data.id = props.detail.id;
            lib.request({
                url: "/ccs/jdPurchaseOrderNew/edit",
                data: data,
                success(data) {
                    message.success("修改成功");
                    setInfoEdit(false);
                    props.reload();
                },
                fail(code, msg, data) {},
            });
        });
    };

    useEffect(() => {
        const details = { ...props.detail };
        details.applyReturnTime && (details.applyReturnTime = moment(details.applyReturnTime, "YYYY-MM-DD hh:mm:ss"));
        details.applyImproveTime &&
            (details.applyImproveTime = moment(details.applyImproveTime, "YYYY-MM-DD hh:mm:ss"));
        //@ts-ignore
        details.needAdditionalTax = details?.needAdditionalTax?.toString();
        form.setFieldsValue(details);
    }, [props.detail]);

    useEffect(() => {
        const data = config.filter(item => !!item.from);
        if (data.length > 0) {
            lib.request({
                url: data[0].from as string,
                data: {
                    type: "currency",
                },
                success(data) {
                    config[6].list = data;
                    setConfig([...config]);
                },
                fail(code, msg, data) {},
            });
        }
    }, []);
    return (
        <>
            <div className="form-title">
                <Typography.Title level={4} style={{ margin: "10px 0" }}>
                    报关指令信息
                </Typography.Title>
                {![enumEoStatus.DECLARE_FINISH, enumEoStatus.CANCELED].includes(props.detail.poStatus) &&
                    (!infoEdit ? (
                        <Button
                            onClick={() => {
                                setInfoEdit(true);
                            }}>
                            编辑
                        </Button>
                    ) : (
                        <Button
                            onClick={() => {
                                save();
                            }}>
                            保存
                        </Button>
                    ))}
            </div>
            <Form form={form}>
                <FormList configList={config} form={form} formItemFloat={true} detail={props} disableEdit={!infoEdit} />
            </Form>
        </>
    );
};
