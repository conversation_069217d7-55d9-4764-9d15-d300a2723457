import React, { ForwardRefExoticComponent, useContext, useEffect, useImperative<PERSON><PERSON>le, useState } from "react";
import { Form, Input, InputNumber, Popconfirm, Table, Typography, FormInstance } from "antd";
import { DDYObject } from "react-single-app";
const EditableContext = React.createContext(null);
interface Item {
    key: string;
    name: string;
    age: number;
    address: string;
    form: any;
}

interface EditableCellProps extends React.HTMLAttributes<HTMLElement> {
    editing: boolean;
    dataIndex: string;
    title: any;
    inputType: "number" | "text";
    record: Item;
    index: number;
    children: React.ReactNode;
    col: DDYObject;
    editable?: boolean;
    onNewChange: (value: any, form: any) => void;
}

const EditableRow = ({ index, ...props }) => {
    const [form] = Form.useForm();
    return (
        <Form form={form} component={false}>
            <EditableContext.Provider value={form}>
                <tr {...props} />
            </EditableContext.Provider>
        </Form>
    );
};

const EditableCell: React.FC<EditableCellProps> = ({
    dataIndex,
    title,
    inputType,
    record,
    editable,
    col,
    children,
    onNewChange,
    ...restProps
}) => {
    const inputNode = inputType === "number" ? <InputNumber /> : <Input />;
    const form = useContext(EditableContext);

    const save = async () => {
        try {
            const values = await form.validateFields();
        } catch (errInfo) {}
    };

    let childNode = (
        <Form.Item
            style={{
                margin: 0,
            }}
            hidden={col?.hide}
            name={dataIndex}>
            {children}
        </Form.Item>
    );
    if (editable) {
        let newRules = [];
        const requireRule = {
            required: true,
            message: `${title}是必须的`,
        };
        if (col && col.required) {
            newRules = [requireRule];
        }
        if (col && col.rules) {
            newRules = col.rules;
            if (col.required) {
                newRules[0] = requireRule;
            }
            if (newRules[0].required) {
                newRules[0] = requireRule;
            }
        }
        childNode = (
            <Form.Item
                style={{
                    margin: 0,
                }}
                name={dataIndex}
                rules={newRules}
                hidden={col?.hide}>
                <Input
                    disabled={col.disabled}
                    onPressEnter={save}
                    onBlur={save}
                    onChange={value => {
                        onNewChange(value.target.value, form);
                    }}
                />
            </Form.Item>
        );
    }
    useEffect(() => {
        record && (record.form = form);
        form.setFieldsValue(record);
    }, [record]);
    return (
        <td {...restProps} style={{ width: "200px" }}>
            {childNode}
        </td>
    );
};

interface EditableProps {
    tableColumns: any[];
    dataSource: any[];
    pagination?: {};
    scroll?: {};
}
export interface EditableRef {
    validatorAll: () => Promise<any>;
    getDatas: () => any[];
    setColumns: (columns: any[]) => void;
}
const Editable: React.ForwardRefExoticComponent<React.RefAttributes<EditableRef> & EditableProps> = React.forwardRef(
    (props: EditableProps, ref) => {
        // console.log("props:ref", props, ref)
        // const [form] = Form.useForm();
        const [data, setData] = useState([]);
        const [editingKey, setEditingKey] = useState("");
        const [columns, setColumns] = useState([]);

        useImperativeHandle(ref, () => {
            return {
                validatorAll: () => {
                    const arr = data.map(item => item.form.validateFields());
                    return Promise.all(arr);
                },
                getDatas: () => {
                    return data.map(item => {
                        const result = { ...item };
                        delete result.form;
                        return result;
                    });
                },
                setColumns: (columns: any[]) => {
                    setColumns(proxyColumns(columns));
                },
            };
        });

        const proxyColumns = (preColumns: any[]) => {
            return preColumns.map(col => {
                if (!col.editable) {
                    return col;
                }
                return {
                    ...col,
                    onCell: (record: Item, index: number) => ({
                        record,
                        editable: col.editable,
                        dataIndex: col.dataIndex,
                        title: col.title,
                        col: col,
                        cellIndex: index,
                        onNewChange: (value, form) => {
                            const item = data[index];
                            col.onChange && col.onChange(value, form.getFieldsValue(), item, form);
                            data[index] = { ...data[index], ...form.getFieldsValue() };
                            // props.onChange && props.onChange(value, dataSource)
                        },
                    }),
                };
            });
        };

        useEffect(() => {
            setColumns([...proxyColumns(props.tableColumns)]);
        }, [props.tableColumns]);

        useEffect(() => {
            setData(props.dataSource);
        }, [props.dataSource]);

        const components = {
            body: {
                row: (row, ...rest) => {
                    return <EditableRow {...row} />;
                },
                cell: (cell, ...rest) => {
                    return <EditableCell {...cell} />;
                },
            },
        };
        return (
            <Table
                components={components}
                bordered
                dataSource={data}
                columns={columns}
                rowClassName="editable-row"
                scroll={props.scroll}
                pagination={props.pagination}
            />
        );
    },
);

export default Editable;
// .editable-row .ant-form-item-explain {
//   position: absolute;
//   top: 100%;
//   font-size: 12px;
// }
