import React, { useState } from "react";
import { Steps, Button, message, Modal } from "antd";
import { enumEoStatus, IOrderHeaderStatusProps } from "../type";
import { lib } from "react-single-app";
import "./order-header-status.less";
import { ConsistConfig, ImporeConfig, improveConfig, PushInOpenConfig, returnConfig, StepsConfig } from "../config";
//@ts-ignore
import NewModal from "../../../components/NewModal";
import moment from "moment";

export default (props: IOrderHeaderStatusProps) => {
    const [imporeOpen, setImporeOpen] = useState(false);
    const [pushInOpen, setPushInOpen] = useState(false);
    const [consistOpen, setConsistOpen] = useState(false);
    const [returnOpen, setReturnOpen] = useState(false);
    const [improveOpen, setImproveOpen] = useState(false);

    const [configs, setConfigs] = useState(improveConfig);

    const accessApply = () => {
        Modal.confirm({
            title: "审核通过",
            content: "是否确认采购单信息无误，审核通过",
            onOk: () => {
                lib.request({
                    url: "/ccs/jdPurchaseOrderNew/audit",
                    data: { ids: props.id.toString(), opinion: "1" },
                    success: data => {
                        message.success("审核通过");
                        props.reload();
                    },
                    fail(code, msg, data) {},
                });
            },
            okText: "确认",
            cancelText: "取消",
            onCancel: () => {},
        });
    };

    // 驳回处理函数
    const rejectApply = () => {
        setImporeOpen(true);
    };
    const imporeOK = (data: any) => {
        lib.request({
            url: "/ccs/jdPurchaseOrderNew/audit",
            data: { reason: data.reason, ids: props.id.toString(), opinion: "0" },
            success(data) {
                message.success("驳回完成");
                props.reload();
                setImporeOpen(false);
            },
            fail(code, msg, data) {},
        });
    };
    const imporeProps = {
        visible: imporeOpen,
        configList: ImporeConfig,
        title: "驳回原因",
        onOk: imporeOK,
        editRow: {},
        onCancel: () => {
            setImporeOpen(false);
        },
    };

    // 推送预申报完成函数
    const pushNotice = () => {
        Modal.confirm({
            title: "推送预申报完成",
            content: "确定推送采购单预申报完成吗？",
            okText: "确定",
            cancelText: "取消",
            onOk: () => {
                lib.request({
                    url: "/ccs/jdPurchaseOrderNew/pushMsg",
                    data: { ids: props.id.toString(), type: "DR" },
                    success: data => {
                        message.info("推送成功");
                        props.reload();
                    },
                    fail(code, msg, data) {},
                });
            },
            onCancel: () => {},
        });
    };

    // 推送入区处理函数
    const pushInNotice = () => {
        setPushInOpen(true);
    };
    const pushInOK = (values: { actEntryTime?: moment.Moment | number; licensePlateNumber?: string }) => {
        const data = { ...values };
        data.actEntryTime = values.actEntryTime?.valueOf();
        lib.request({
            url: "/ccs/jdPurchaseOrderNew/pushMsg",
            data: { ids: props.id.toString(), type: "ET", ...data },
            success: data => {
                message.info("推送成功");
                props.reload();
                setPushInOpen(false);
            },
            fail(code, msg, data) {},
        });
    };
    const pushInProps = {
        visible: pushInOpen,
        configList: PushInOpenConfig,
        editRow: {
            licensePlateNumber: props.detail.licensePlateNumber,
            actEntryTime: props.detail.actEntryTime && moment(props.detail.actEntryTime, "YYYY-MM-DD hh:mm:ss"),
        },
        title: "推送入区放行",
        onOk: pushInOK,
        onCancel: () => {
            setPushInOpen(false);
        },
    };

    // 推送确报放行处理函数
    const pushConsist = () => {
        setConsistOpen(true);
    };
    const consistOK = (values: any) => {
        lib.request({
            url: "/ccs/jdPurchaseOrderNew/pushMsg",
            data: { ids: props.id.toString(), type: "RL", ...values },
            success: data => {
                message.info("推送成功");
                props.reload();
                setConsistOpen(false);
            },
            fail(code, msg, data) {},
        });
    };
    const consistPorps = {
        visible: consistOpen,
        configList: ConsistConfig,
        editRow: {
            apprMarkNo: props.detail.apprMarkNo,
        },
        title: "推送确报放行",
        onOk: consistOK,
        onCancel: () => {
            setConsistOpen(false);
        },
    };

    // 确认退运放行
    const pushReturn = () => {
        setReturnOpen(true);
    };
    const returnOK = (values: any) => {
        const data = { ...values };
        data.applyReturnTime = values.applyReturnTime?.valueOf();
        lib.request({
            url: "/ccs/jdPurchaseOrderNew/pushMsg",
            data: { ids: props.id.toString(), type: "RL", ...data },
            success: data => {
                message.success("推送成功");
                props.reload();
                setReturnOpen(false);
            },
            fail(code, msg, data) {},
        });
    };
    const returnPorps = {
        visible: returnOpen,
        configList: returnConfig,
        editRow: {
            returnCert: props.detail.returnCert,
            applyReturnTime:
                props.detail.applyReturnTime && moment(props.detail.applyReturnTime, "YYYY-MM-DD hh:mm:ss"),
        },
        title: "推送退运放行",
        onOk: returnOK,
        onCancel: () => {
            setReturnOpen(false);
        },
    };

    // 推送改单放行方法
    const pushImprove = () => {
        const result = improveConfig.filter(item => !!item.from);
        if (result.length > 0) {
            lib.request({
                url: result[0].from as string,
                data: { type: "currency" },
                success(data) {
                    improveConfig[4].list = data;
                    setConfigs([...improveConfig]);
                },
                fail(code, msg, data) {},
            });
        }
        setImproveOpen(true);
    };
    const improveOK = (values: any) => {
        const data = { ...values };
        data.applyImproveTime = values.applyImproveTime?.valueOf();
        data.additionalTaxAmount = +values.additionalTaxAmount;
        data.needAdditionalTax = +values.needAdditionalTax;
        lib.request({
            url: "/ccs/jdPurchaseOrderNew/pushMsg",
            data: { ids: props.id.toString(), type: "RL", ...data },
            success: data => {
                message.success("推送成功");
                setImproveOpen(false);
                props.reload();
            },
            fail(code, msg, data) {},
        });
    };
    const improveProps = {
        visible: improveOpen,
        configList: configs,
        title: "推送改单放行",
        editRow: {
            improveCert: props.detail.improveCert,
            electronicBill: props.detail.electronicBill,
            needAdditionalTax: props.detail?.needAdditionalTax?.toString(),
            additionalTaxAmount: props.detail.additionalTaxAmount,
            taxCurrency: props.detail.taxCurrency,
            applyImproveTime:
                props.detail.applyImproveTime && moment(props.detail.applyImproveTime, "YYYY-MM-DD hh:mm:ss"),
        },
        onOk: improveOK,
        onCancel: () => {
            setImproveOpen(false);
        },
    };

    return (
        <div className="order-header-status">
            <p style={{ color: "#999" }}>
                存在【采购单数量{"<"}报关数量】的商品明细、或采购单取消、或其他异常无法正常申报时，请驳回单据
            </p>
            <div className="order-header-status-content">
                <Steps
                    current={props.statusIndex + 1}
                    labelPlacement="vertical"
                    status={props?.steps?.[props.statusIndex]?.status}>
                    {props.steps.map((item, index) => {
                        return (
                            <Steps.Step
                                title={item.businessName}
                                description={
                                    <>
                                        {item.status === "error" && <div>{item.title}</div>}
                                        <div>{item.businessTime}</div>
                                    </>
                                }
                                status={item.status}
                                key={index}
                            />
                        );
                    })}
                </Steps>
                <div className="order-header-btn">
                    {props.poStatus === enumEoStatus.WAIT_AUDIT && (
                        <>
                            <Button onClick={accessApply}>审核通过</Button>
                            <Button onClick={rejectApply} style={{ marginLeft: "10px" }}>
                                审核驳回
                            </Button>
                        </>
                    )}
                    {props.poStatus === enumEoStatus.START_PRE_DECLARE && (
                        <Button onClick={pushNotice}>推送预申报完成</Button>
                    )}
                    {props.poStatus === enumEoStatus.WAIT_ENTER && <Button onClick={pushInNotice}>推送入区通知</Button>}
                    {props.poStatus === enumEoStatus.WAIT_DEFINITE && (
                        <Button onClick={pushConsist}>推送确报放行</Button>
                    )}
                    {props.poStatus === enumEoStatus.WAIT_RETURN && <Button onClick={pushReturn}>推送退运放行</Button>}
                    {props.poStatus === enumEoStatus.WAIT_IMPROVE && (
                        <Button onClick={pushImprove}>推送改单放行</Button>
                    )}
                </div>
            </div>

            <NewModal {...imporeProps} />

            <NewModal {...pushInProps} />

            <NewModal {...consistPorps} />

            <NewModal {...returnPorps} />

            <NewModal {...improveProps} />
        </div>
    );
};
