import React, { useEffect, useRef, useState } from "react";
import { lib } from "react-single-app";
import { message, Typography, Button } from "antd";
import Editable, { EditableRef } from "./edit-table";
import { GoodsConfig, goodsColumns } from "../config";
import { detailResVoList, enumEoStatus } from "../type";
//@ts-ignore
import NewModal from "../../../components/NewModal";

export default props => {
    const [editOpen, setEditOpen] = useState<boolean>(false);
    const goodsDetail = useRef<detailResVoList>();
    const EditableRef = useRef<EditableRef>();
    const [data, setData] = useState([]);
    const [pagination, setPagination] = useState({
        page: 1,
        pageSize: 20,
        total: 0,
    });

    const exportGoods = () => {
        lib.request({
            url: "/ccs/jdPurchaseOrderNew/detail/export",
            data: { id: lib.getParam("id") },
            success(data) {
                message.info("导出成功");
                lib.openPage("/download-center?page_title=下载中心");
            },
            fail(code, msg, data) {},
        });
    };

    const importData = () => {
        const importExtendParam = `orderId=${props.id}`;
        let importExtendParamBase64 = window.btoa(importExtendParam);
        let url = `/excel/import-data?page_title=${lib.getParam(
            "page_title",
        )}导入&code=IMPORT_JD_PURCHASE_DETAIL&importExtendParam=${importExtendParamBase64}`;
        lib.openPage(url, () => {
            props.reload();
        });
    };

    const saveGoodInfo = () => {
        EditableRef.current.validatorAll()?.then(() => {
            const data = EditableRef.current?.getDatas().map(item => {
                return { id: item.id, actualAmount: +item.actualAmount };
            });
            console.log(EditableRef.current?.getDatas());
            lib.request({
                url: "/ccs/jdPurchaseOrderNew/editActualAmount",
                data: {
                    batchEditList: data,
                },
                success(data) {
                    message.success("保存成功");
                    props.reload();
                },
                fail(code, msg, data) {},
            });
        });
    };

    const showEditFn = (row: detailResVoList) => {
        return (
            <Button
                type="link"
                onClick={() => {
                    goodsDetail.current = row;
                    setEditOpen(true);
                }}>
                编辑
            </Button>
        );
    };
    const editHandOK = (data: any) => {
        lib.request({
            url: "/ccs/jdPurchaseOrderNew/editDetail",
            data: { ...data, id: goodsDetail.current?.id },
            success(data) {
                message.success("编辑成功");
                setEditOpen(false);
                // goodsDetail.current = undefined;
                props.reload();
            },
            fail(code, msg, data) {},
        });
        // setUploadOpen(false);
    };

    const editProps = {
        visible: editOpen,
        configList: GoodsConfig,
        title: "编辑",
        editRow: goodsDetail.current,
        onOk: editHandOK,
        onCancel: () => {
            setEditOpen(false);
        },
    };

    const getdatas = (page: number, pageSize: number) => {
        lib.request({
            url: "/ccs/jdPurchaseOrderNew/pagingDetail",
            data: {
                pageSize,
                currentPage: page,
                id: lib.getParam("id"),
            },
            success: res => {
                setData(res.dataList);
                setPagination({
                    page: res.page.currentPage,
                    pageSize: res.page.pageSize,
                    total: res.page.totalCount,
                });
            },
        });
    };

    useEffect(() => {
        getdatas(1, 20);
    }, []);
    return (
        <>
            <div className="form-title">
                <Typography.Title level={4} style={{ margin: "10px 0" }}>
                    商品明细信息
                </Typography.Title>
                <div>
                    {[enumEoStatus.WAIT_AUDIT, enumEoStatus.START_PRE_DECLARE].includes(props.poStatus) && (
                        <>
                            {<Button onClick={saveGoodInfo}>保存</Button>}
                            <Button onClick={importData} style={{ marginLeft: "10px" }}>
                                导入
                            </Button>
                            <Button onClick={exportGoods} style={{ marginLeft: "10px" }}>
                                导出
                            </Button>
                        </>
                    )}
                </div>
            </div>
            <Editable
                tableColumns={goodsColumns(
                    showEditFn,
                    [enumEoStatus.WAIT_AUDIT, enumEoStatus.START_PRE_DECLARE].includes(props.poStatus),
                )}
                dataSource={data}
                ref={EditableRef}
                scroll={{ x: 2000, y: 500 }}
                pagination={{
                    onChange(page, pageSize) {
                        getdatas(page, pageSize);
                    },
                    total: pagination.total,
                    showSizeChanger: true,
                    pageSize: pagination.pageSize,
                    current: pagination.page,
                }}
            />
            <NewModal {...editProps} />
        </>
    );
};
