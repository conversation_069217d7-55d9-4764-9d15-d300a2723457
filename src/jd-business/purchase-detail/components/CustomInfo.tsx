import React, { useState, useRef, useEffect, useCallback } from "react";
import FormList from "./FormList";
import { Form, Table, Button, message, Typography } from "antd";
// import { customsInfo } from "../config";
import { enumEoStatus, IPoDetail } from "../type";
import { lib } from "react-single-app";
import moment from "moment";

const rulesBaseNeed = [{ required: true }];

const rulesNeed = [
    { required: true },
    {
        validator(_, value) {
            if (!/^[A-Za-z0-9\u4e00-\u9fa5]*$/.test(value))
                return Promise.reject(new Error("内容应以字母、数字、中文组成"));
            return Promise.resolve();
        },
    },
];

const rulesNoNeed = [
    {
        validator(_, value) {
            if (!value) return Promise.resolve();
            if (!/^[A-Za-z0-9\u4e00-\u9fa5]*$/.test(value))
                return Promise.reject(new Error("内容应以字母、数字、中文组成"));
            return Promise.resolve();
        },
    },
];
const rulesCNNeed = [
    { required: true },
    {
        validator(_, value) {
            if (!/^[A-Za-z0-9\u4e00-\u9fa5\\;]*$/.test(value))
                return Promise.reject(new Error("内容应以字母、数字、中文、英文分号组成"));
            return Promise.resolve();
        },
    },
];

const rulesCNNoNeed = [
    {
        validator(_, value) {
            if (!value) return Promise.resolve();
            if (!/^[A-Za-z0-9\u4e00-\u9fa5\\;]*$/.test(value))
                return Promise.reject(new Error("内容应以字母、数字、中文、英文分号组成"));
            return Promise.resolve();
        },
    },
];

const rulesNoCHNeed = [
    { required: true },
    {
        validator(_, value) {
            if (!value) return Promise.resolve();
            if (!/^[A-Za-z0-9\\;]*$/.test(value)) return Promise.reject(new Error("内容应以字母、数字、英文分号组成"));
            return Promise.resolve();
        },
    },
];
const rulesNoCHNoNeed = [
    {
        validator(_, value) {
            if (!value) return Promise.resolve();
            if (!/^[A-Za-z0-9\\;]*$/.test(value)) return Promise.reject(new Error("内容应以字母、数字、英文分号组成"));
            return Promise.resolve();
        },
    },
];

enum entryModeVal {
    FIRSTMODE = "0", // 一线入区
    SECONDMODE = "1", // 二线调拨
    INNERMODE = "2", // 内配调拨
}

// const { BGD, YSB, RQDC } = customsInfo
export default (props: { detail: IPoDetail; reload: () => void }) => {
    const [customsEdit, setCustomsEdit] = useState<boolean>(false);
    const poStatusRef = useRef(props?.detail?.poStatus);
    const customsInfo = {
        BGD: (fn: (e: string) => void, fn1: (e: string) => void) => [
            {
                labelName: "运输方式",
                labelKey: "transMode",
                type: "SELECT",
                list: [
                    {
                        id: "0",
                        name: "空运",
                    },
                    {
                        id: "1",
                        name: "海运",
                    },
                    {
                        id: "2",
                        name: "陆运",
                    },
                    {
                        id: "3",
                        name: "铁路",
                    },
                ],
                required: true,
            },
            {
                labelName: "入区方式",
                labelKey: "entryMode",
                type: "SELECT",
                required: true,
                list: [
                    {
                        id: "0",
                        name: "一线入区",
                    },
                    {
                        id: "1",
                        name: "二线调拨",
                    },
                    {
                        id: "2",
                        name: "内配调拨",
                    },
                ],
                onChange: fn,
                rules: [{ required: true }],
            },
            {
                labelName: "报关方式",
                labelKey: "declareMode",
                type: "SELECT",
                required: true,
                onChange: fn1,
                list: [
                    {
                        id: "0",
                        name: "一次申报",
                    },
                    {
                        id: "1",
                        name: "两步申报",
                    },
                    {
                        id: "2",
                        name: "二线转关申报",
                    },
                ],
            },
            {
                labelName: "起运港",
                labelKey: "departPort",
                type: "INPUT",
                maxLength: 16,
            },
            {
                labelName: "目的港",
                labelKey: "destPort",
                type: "INPUT",
                maxLength: 16,
            },
            {
                labelName: "预计到港时间",
                labelKey: "estArriveTime",
                type: "DATE",
            },
            {
                labelName: "实际到港时间",
                labelKey: "actArriveTime",
                type: "DATE",
            },
            {
                labelName: "转出关区",
                labelKey: "transOutRegion",
                type: "INPUT",
                maxLength: 16,
            },
            {
                labelName: "转入关区",
                labelKey: "transInRegion",
                type: "INPUT",
                maxLength: 16,
            },
            {
                labelName: "港到仓履约主体",
                labelKey: "promiseType",
                type: "SELECT",
                list: [],
                required: true,
            },
        ],
        YSB: [
            {
                labelName: "报关单号",
                labelKey: "corrDocNo",
                type: "TEXTAREA",
                maxLength: 255,
                rules: rulesNoCHNoNeed,
            },
            {
                labelName: "报关时间",
                labelKey: "corrDocTime",
                type: "DATE",
            },
            {
                labelName: "报检单号",
                labelKey: "declNo",
                type: "TEXTAREA",
                maxLength: 255,
                rules: rulesNoCHNoNeed,
            },
            {
                labelName: "报检时间",
                labelKey: "declTime",
                type: "DATE",
            },
            {
                labelName: "核注清单编号",
                labelKey: "apprMarkNo",
                type: "TEXTAREA",
                maxLength: 255,
                rules: rulesNoCHNoNeed,
            },

            {
                labelName: "查验状态",
                labelKey: "inspectState",
                type: "SELECT",
                // required: true,
                list: [
                    {
                        id: "0",
                        name: "无查验",
                    },
                    {
                        id: "1",
                        name: "口岸查验",
                    },
                    {
                        id: "2",
                        name: "目的地查验",
                    },
                ],
            },
            {
                labelName: "预申报时间",
                labelKey: "preDeclareTime",
                type: "DATE",
            },
            {
                labelName: "预申报放行时间",
                labelKey: "releaseTime",
                type: "DATE",
            },
            {
                labelName: "预申报备注",
                labelKey: "preDeclareRemark",
                type: "INPUT",
                maxLength: 256,
            },
            {
                labelName: "查验备注",
                labelKey: "inspectRemark",
                type: "INPUT",
                maxLength: 256,
            },
        ],
        RQDC: [
            {
                labelName: "车牌号",
                labelKey: "licensePlateNumber",
                type: "TEXTAREA",
                maxLength: 255,
            },
            {
                labelName: "实际入区时间",
                labelKey: "actEntryTime",
                type: "DATE",
            },
        ],
    };
    let { BGD, YSB, RQDC } = customsInfo;
    const selectChangeRequire = useCallback((e: string) => {
        const val1 = form1.getFieldsValue();
        const val2 = form2.getFieldsValue();
        const val3 = form3.getFieldsValue();
        BGDConfig.map(item => {
            switch (e) {
                case entryModeVal.FIRSTMODE:
                    if (["departPort", "destPort"].includes(item.labelKey)) {
                        item.rules = rulesNeed;
                    }
                    if (["transOutRegion", "transInRegion"].includes(item.labelKey)) {
                        item.rules = rulesNoNeed;
                    }
                    if (["actArriveTime", "estArriveTime"].includes(item.labelKey)) {
                        item.rules = rulesBaseNeed;
                    }
                    if (["promiseType"].includes(item.labelKey)) {
                        item.required = true;
                    }
                    break;
                case entryModeVal.SECONDMODE:
                    if (["departPort", "destPort"].includes(item.labelKey)) {
                        item.rules = rulesNoNeed;
                    }
                    if (["transOutRegion", "transInRegion"].includes(item.labelKey)) {
                        item.rules = rulesNeed;
                    }
                    if (["actArriveTime", "estArriveTime"].includes(item.labelKey)) {
                        item.rules = [];
                    }
                    if (["promiseType"].includes(item.labelKey)) {
                        item.required = false;
                    }
                    break;
                case entryModeVal.INNERMODE:
                    if (["departPort", "destPort"].includes(item.labelKey)) {
                        item.rules = rulesNoNeed;
                    }
                    if (["transOutRegion", "transInRegion"].includes(item.labelKey)) {
                        item.rules = rulesNoNeed;
                    }
                    if (["actArriveTime", "estArriveTime"].includes(item.labelKey)) {
                        item.rules = [];
                    }
                    if (["promiseType"].includes(item.labelKey)) {
                        item.required = false;
                    }
                    break;
                default:
                    break;
            }
        });
        switch (poStatusRef.current) {
            // 推送预申报完成
            case enumEoStatus.START_PRE_DECLARE:
            // 推送入区通知
            case enumEoStatus.WAIT_ENTER:
            case enumEoStatus.WAIT_TALLY:
            // 推送确保放行
            case enumEoStatus.WAIT_DEFINITE:
            // 推送改单放行
            case enumEoStatus.WAIT_IMPROVE:
            // 推送退运放行
            case enumEoStatus.WAIT_RETURN:
                YSBConfig.map(item => {
                    if (["preDeclareTime", "releaseTime"].includes(item.labelKey)) {
                        item.rules = rulesBaseNeed;
                        return;
                    }
                    switch (e) {
                        case entryModeVal.FIRSTMODE:
                            if (["corrDocNo"].includes(item.labelKey)) {
                                item.rules = rulesNoCHNeed;
                            }
                            if (["corrDocTime", "inspectState"].includes(item.labelKey)) {
                                item.rules = rulesBaseNeed;
                            }
                            break;
                        case entryModeVal.SECONDMODE:
                            if (["corrDocNo"].includes(item.labelKey)) {
                                item.rules = rulesNoCHNoNeed;
                            }
                            if (["corrDocTime", "inspectState"].includes(item.labelKey)) {
                                item.rules = [];
                            }
                            break;
                        case entryModeVal.INNERMODE:
                            if (["corrDocNo"].includes(item.labelKey)) {
                                item.rules = rulesNoCHNoNeed;
                            }
                            if (["corrDocTime", "inspectState"].includes(item.labelKey)) {
                                item.rules = [];
                            }
                            break;
                        default:
                            break;
                    }
                });
                break;
            default:
                break;
        }
        switch (poStatusRef.current) {
            // 推送入区通知
            case enumEoStatus.WAIT_ENTER:
            case enumEoStatus.WAIT_TALLY:
            // 推送确保放行
            case enumEoStatus.WAIT_DEFINITE:
            // 推送改单放行
            case enumEoStatus.WAIT_IMPROVE:
            // 推送退运放行
            case enumEoStatus.WAIT_RETURN:
                RQDCConfig.map(item => {
                    if (item.labelKey === "licensePlateNumber") {
                        item.rules = rulesCNNeed;
                    }
                    if (item.labelKey === "actEntryTime") {
                        item.rules = rulesBaseNeed;
                    }
                });
                break;
            default:
                RQDCConfig.map(item => {
                    item = { ...item };
                    if (item.labelKey === "licensePlateNumber") {
                        item.rules = rulesCNNoNeed;
                    }
                    if (item.labelKey === "actEntryTime") {
                        item.rules = [];
                    }
                });
                break;
        }
        setRQDCConfig([...RQDCConfig]);
        setYSBConfig([...YSBConfig]);
        setBGDConfig([...BGDConfig]);
        if (props.detail?.orderTagList?.includes(1)) {
            allNoRequiredChange();
        }
        form1.resetFields([
            "departPort",
            "destPort",
            "transOutRegion",
            "transInRegion",
            "actArriveTime",
            "estArriveTime",
        ]);
        form2.resetFields(["corrDocNo", "apprMarkNo", "corrDocTime", "inspectState"]);
        // 清除旧的报错信息时会把值一起清除，这时需要重新设置上
        form1.setFieldsValue(val1);
        form2.setFieldsValue(val2);
        form3.setFieldsValue(val3);
    }, []);
    const selectChangeApprMarkNo = (e: string) => {
        switch (poStatusRef.current) {
            // 推送预申报完成
            case enumEoStatus.START_PRE_DECLARE:
            // 推送入区通知
            case enumEoStatus.WAIT_ENTER:
            // 待理货waitTally
            case enumEoStatus.WAIT_TALLY:
                YSBConfig.map(item => {
                    if (["apprMarkNo"].includes(item.labelKey)) {
                        if (e === "1" || !e) {
                            item.rules = [
                                {
                                    validator(_, value) {
                                        if (!value) return Promise.resolve();
                                        if (!/^[A-Za-z0-9\\;]*$/.test(value))
                                            return Promise.reject(new Error("内容应以字母、数字、英文分号组成"));
                                        return Promise.resolve();
                                    },
                                },
                            ];
                        } else {
                            item.rules = [
                                { required: true },
                                {
                                    validator(_, value) {
                                        if (!value) return Promise.resolve();
                                        if (!/^[A-Za-z0-9\\;]*$/.test(value))
                                            return Promise.reject(new Error("内容应以字母、数字、英文分号组成"));
                                        return Promise.resolve();
                                    },
                                },
                            ];
                        }
                    }
                });
                if (poStatusRef.current === enumEoStatus.WAIT_ENTER) {
                    BGDConfig.map(item => {
                        if (["estArriveTime", "actArriveTime"].includes(item.labelKey)) {
                            item.rules = rulesBaseNeed;
                        }
                    });
                }
                break;
            // START_PRE_DECLARE="startPreDeclare",      //"开始清关预申报"
            // WAIT_ENTER="waitEnter",                   //"待入区"
            // WAIT_TALLY="waitTally",                   //"待理货"
            // WAIT_DEFINITE="waitDefinite",             //"待确报"
            // WAIT_IMPROVE="waitImprove",               //"待改单"
            // WAIT_RETURN="waitReturn",                 //"待退运"
            // DECLARE_FINISH="DeclareFinish",           //"清关完成"
            // 推送确保放行
            case enumEoStatus.WAIT_DEFINITE:
            // 推送改单放行
            case enumEoStatus.WAIT_IMPROVE:
            // 推送退运放行
            case enumEoStatus.WAIT_RETURN:
            // 推送退运放行
            case enumEoStatus.WAIT_RETURN:
                YSBConfig.map(item => {
                    if (["apprMarkNo"].includes(item.labelKey)) {
                        item.rules = [
                            { required: true },
                            {
                                validator(_, value) {
                                    if (!value) return Promise.resolve();
                                    if (!/^[A-Za-z0-9\\;]*$/.test(value))
                                        return Promise.reject(new Error("内容应以字母、数字、英文分号组成"));
                                    return Promise.resolve();
                                },
                            },
                        ];
                    }
                });
                break;
            default:
                break;
        }
        if (props.detail?.orderTagList?.includes(1)) {
            allNoRequiredChange();
        }
        setBGDConfig([...BGDConfig]);
        setYSBConfig([...YSBConfig]);
    };

    let [BGDConfig, setBGDConfig] = useState<any[]>([...BGD(selectChangeRequire, selectChangeApprMarkNo)]);
    let [YSBConfig, setYSBConfig] = useState<any[]>([...YSB]);
    let [RQDCConfig, setRQDCConfig] = useState<any[]>([...RQDC]);
    const [form1] = Form.useForm();
    const [form2] = Form.useForm();
    const [form3] = Form.useForm();

    const setCustomsInfo = () => {
        Promise.all([form1.validateFields(), form2.validateFields(), form3.validateFields()]).then(() => {
            const val1 = form1.getFieldsValue();
            const val2 = form2.getFieldsValue();
            const val3 = form3.getFieldsValue();
            const data = { ...val1, ...val2, ...val3, id: props.detail.id };
            data.estArriveTime && (data.estArriveTime = data.estArriveTime.valueOf());
            data.actArriveTime && (data.actArriveTime = data.actArriveTime.valueOf());
            data.corrDocTime && (data.corrDocTime = data.corrDocTime.valueOf());
            data.declTime && (data.declTime = data.declTime.valueOf());
            data.preDeclareTime && (data.preDeclareTime = data.preDeclareTime.valueOf());
            data.releaseTime && (data.releaseTime = data.releaseTime.valueOf());
            data.actEntryTime && (data.actEntryTime = data.actEntryTime.valueOf());
            lib.request({
                url: "/ccs/jdPurchaseOrderNew/edit",
                data: data,
                success(data) {
                    props.reload();
                    message.success("修改成功");
                    setCustomsEdit(false);
                },
                fail(code, msg, data) {},
            });
        });
    };

    // 所有表单项转化为非必填
    const allNoRequiredChange = () => {
        BGDConfig.map(item => {
            // if(item.required)
            item.required = false;
            if (item.rules && item.rules.length > 0 && item.rules[0].required === true) {
                item.rules = item.rules.filter(item => !item.required);
            }
        });
        setBGDConfig([...BGDConfig]);
        YSBConfig.map(item => {
            // if(item.required)
            item.required = false;
            if (item.rules && item.rules.length > 0 && item.rules[0].required === true) {
                item.rules = item.rules.filter(item => !item.required);
            }
        });
        setYSBConfig([...YSBConfig]);
        RQDCConfig.map(item => {
            // if(item.required)
            item.required = false;
            if (item.rules && item.rules.length > 0 && item.rules[0].required === true) {
                item.rules = item.rules.filter(item => !item.required);
            }
        });
        setRQDCConfig([...RQDCConfig]);
    };

    useEffect(() => {
        if (Object.keys(props.detail).length > 1) {
            const detail = { ...props.detail };
            poStatusRef.current = props.detail?.poStatus;
            // 初始化表单值转化
            initFormValue(detail);

            // 有虚拟标记的采购单，对所有表单项添加为非必填
            if (detail?.orderTagList?.includes(1)) {
                allNoRequiredChange();
                return;
            }

            // 根据入区方式的值初始化表单校验选项变更
            selectChangeRequire(detail?.entryMode?.toString());
            // 根据报关方式的值初始化表单校验选项变更
            selectChangeApprMarkNo(detail?.declareMode?.toString());
        }
    }, [props.detail]);

    const initFormValue = (detail: any) => {
        detail.estArriveTime && (detail.estArriveTime = moment(detail.estArriveTime, "YYYY-MM-DD hh:mm:ss"));
        detail.actArriveTime && (detail.actArriveTime = moment(detail.actArriveTime, "YYYY-MM-DD hh:mm:ss"));
        detail.preDeclareTime && (detail.preDeclareTime = moment(detail.preDeclareTime, "YYYY-MM-DD hh:mm:ss"));
        detail.releaseTime && (detail.releaseTime = moment(detail.releaseTime, "YYYY-MM-DD hh:mm:ss"));
        detail.corrDocTime && (detail.corrDocTime = moment(detail.corrDocTime, "YYYY-MM-DD hh:mm:ss"));
        detail.declTime && (detail.declTime = moment(detail.declTime, "YYYY-MM-DD hh:mm:ss"));
        detail.actEntryTime && (detail.actEntryTime = moment(detail.actEntryTime, "YYYY-MM-DD hh:mm:ss"));
        detail.declareMode = detail?.declareMode?.toString();
        detail.entryMode = detail?.entryMode?.toString();
        detail.inspectState = detail?.inspectState?.toString();
        detail.transMode = detail?.transMode?.toString();
        form1.setFieldsValue({ ...detail });
        form2.setFieldsValue({ ...detail });
        form3.setFieldsValue({ ...detail });
    };

    useEffect(() => {
        lib.request({
            url: "/ccs/jdPurchaseOrderNew/listPromiseType",
            success(data) {
                const len = BGDConfig.length;
                BGDConfig[len - 1].list = data;
                setBGDConfig([...BGDConfig]);
            },
        });
    }, []);

    return (
        <>
            <div className="form-title">
                <Typography.Title level={4} style={{ margin: "10px 0" }}>
                    海关信息
                </Typography.Title>

                {![enumEoStatus.DECLARE_FINISH, enumEoStatus.CANCELED].includes(props.detail.poStatus) &&
                    (!customsEdit ? (
                        <Button
                            onClick={() => {
                                setCustomsEdit(true);
                            }}>
                            编辑
                        </Button>
                    ) : (
                        <Button
                            onClick={() => {
                                setCustomsInfo();
                            }}>
                            保存
                        </Button>
                    ))}
            </div>
            <Typography.Title level={5} style={{ margin: "10px 0" }}>
                报关单据审核填写
            </Typography.Title>
            <Form form={form1}>
                <FormList
                    configList={BGDConfig}
                    form={form1}
                    formItemFloat={true}
                    detail={props.detail}
                    disableEdit={!customsEdit}
                />
            </Form>

            <Typography.Title level={5} style={{ margin: "10px 0" }}>
                预申报完成填写
            </Typography.Title>
            <Form form={form2}>
                <FormList
                    configList={YSBConfig}
                    form={form2}
                    formItemFloat={true}
                    detail={props}
                    disableEdit={!customsEdit}
                />
            </Form>

            <Typography.Title level={5} style={{ margin: "10px 0" }}>
                入区到仓填写
            </Typography.Title>
            <Form form={form3}>
                <FormList
                    configList={RQDCConfig}
                    form={form3}
                    formItemFloat={true}
                    detail={props}
                    disableEdit={!customsEdit}
                />
            </Form>
        </>
    );
};
