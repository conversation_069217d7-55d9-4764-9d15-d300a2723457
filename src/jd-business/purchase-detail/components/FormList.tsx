import "./FormList.less";

import React, { useState, useEffect } from "react";
import { Select, Form, Modal, Input, Checkbox, DatePicker, Upload, Button, InputNumber, Switch, message } from "antd";
import moment from "moment";
//@ts-ignore
import { Uploader } from "react-single-app";
import "./FormList.less";
import { Rule } from "rc-field-form/lib/interface";

const FormItem = Form.Item;
const Option = Select.Option;
const { TextArea } = Input;
const { RangePicker } = DatePicker;

/**
 * 新建弹窗或者编辑弹窗
 *
 * @param form form
 * @param configList 配置列表
 * ```
 * {type: 'SELECT', labelKey: '', labelName: '', xhr: '请求链接', list: [], placeholder: '', required: false, message: '请输入', }
 * ```
 * @param title
 * @param visible 显示/隐藏弹窗
 * @param detail 当前编辑对象
 */
const FormList: React.FunctionComponent<FormListProps<any>> = ({
    configList,
    formItemFloat,
    children,
    detail,
    form,
    disableEdit,
}) => {
    function switchType(item: NewType) {
        switch (item.type) {
            case "INPUT":
                return (
                    <Input
                        disabled={disableEdit || item.disabled}
                        placeholder={item.placeholder || `请输入${item.labelName}`}
                        maxLength={item.maxLength || 9999}
                        autoComplete={item.autoComplete}
                    />
                );
            case "INPUTNUMBER":
                return (
                    <InputNumber
                        disabled={disableEdit || item.disabled}
                        placeholder={item.placeholder || `请输入${item.labelName}`}
                        max={item.max}
                        min={item.min}
                    />
                );
            case "SELECT":
                return (
                    <Select
                        onChange={item.onChange ? e => item.onChange(e, form) : e => {}}
                        mode={item.mode}
                        style={{ width: "100%" }}
                        showSearch
                        disabled={disableEdit || item.disabled}
                        optionFilterProp="children">
                        {item.list?.length &&
                            item.list.map(
                                (
                                    ite: { id: any; key: any; name: any; value: any },
                                    index: React.Key | null | undefined,
                                ) => {
                                    return (
                                        <Option value={ite.id || ite.key} key={index}>
                                            {item.together ? `${ite.id}-${ite.name}` : ite.name || ite.value}
                                        </Option>
                                    );
                                },
                            )}
                    </Select>
                );
            case "CHECKBOX":
                return (
                    <Checkbox.Group disabled={disableEdit || item.disabled}>
                        {item?.list &&
                            item.list.map(
                                (
                                    ite: {
                                        value: any;
                                        name:
                                            | boolean
                                            | React.ReactChild
                                            | React.ReactFragment
                                            | React.ReactPortal
                                            | null
                                            | undefined;
                                    },
                                    index: React.Key | null | undefined,
                                ) => {
                                    return (
                                        <Checkbox value={ite.value} key={index}>
                                            {ite.name}
                                        </Checkbox>
                                    );
                                },
                            )}
                    </Checkbox.Group>
                );
            case "DATE":
                return <DatePicker showTime={item.mode !== "date"} disabled={disableEdit || item.disabled} />;
            case "DATERANGE":
                return <RangePicker showTime={item.mode !== "date"} disabled={disableEdit || item.disabled} />;
            case "TEXTAREA":
                return <TextArea disabled={disableEdit || item.disabled} maxLength={item.maxLength} />;
            case "SWITCH":
                return <Switch disabled={disableEdit || item.disabled} />;
            case "FILE":
                return (
                    <Uploader
                        allowTypes={item.allowTypes}
                        onUploadStart={() => {
                            console.log("开始上传 ");
                        }}
                        disabled={disableEdit || item.disabled}
                        onUploadEnd={(src: any) => {
                            console.log(src);
                            form.setFieldsValue({
                                [item.labelKey]: src,
                            });
                        }}
                        style={{ width: "60px", height: "40px" }}
                        src={""}
                    />
                );
            case "PASSWORD":
                return <Input.Password disabled={disableEdit || item.disabled} />;
            default:
                return null;
        }
    }
    function renderFormItem(item: NewType, rules: Rule[] | undefined) {
        switch (item.type) {
            case "FILE":
                return (
                    <FormItem label={item.labelName} name={item.labelKey} rules={rules}>
                        {switchType(item)}
                    </FormItem>
                );
            case "TEXT":
                return (
                    <FormItem
                        label={item.labelName}
                        {...item.extra}
                        wrapperCol={{ span: item.wrapperCol || 16 }}
                        labelCol={{ span: item.labelCol || 8 }}>
                        {detail ? (item.render ? item.render(detail) : detail[item.labelKey]) : null}
                    </FormItem>
                );
            default:
                return item.hide !== true ? (
                    <FormItem
                        label={item.labelName}
                        name={item.labelKey}
                        rules={rules}
                        wrapperCol={{ span: item.wrapperCol || 16 }}
                        labelCol={{ span: item.labelCol || 8 }}
                        help={item.help || null}>
                        {switchType(item)}
                    </FormItem>
                ) : null;
        }
    }
    return (
        <div className={formItemFloat ? "form-list-component form-item-float" : "form-list-component"}>
            {configList.map((item, index) => {
                let rules = item.rules || [
                    {
                        required: item.required,
                        message: item.message || `请${item.type === "SELECT" ? "选择" : "输入"}${item.labelName}`,
                    },
                ];
                if (item.max) {
                    //   rules[0].max = item.max;
                }
                return <React.Fragment key={index}>{renderFormItem(item, rules)}</React.Fragment>;
            })}
            {children}
        </div>
    );
};

type NewType = {
    allowTypes: any;
    together?: any;
    list?: any[];
    mode?: "multiple" | "tags" | any;
    onChange?: any;
    min?: any;
    autoComplete?: string | undefined;
    maxLength?: number;
    placeholder?: string;
    disabled?: boolean | undefined;
    required?: boolean;
    message?: string;
    type: string;
    labelName: string;
    max?: number;
    rules?: Rule[] | undefined;
    labelKey: any;
    extra?: any;
    render?: any;
    hide?: any;
    wrapperCol?: any;
    labelCol?: any;
    help?: any;
};

interface FormListProps<T> {
    configList: T[];
    formItemFloat: boolean;
    children?: any;
    detail: T;
    form: any;
    disableEdit?: boolean;
    onFormChange?: () => void;
}

export default FormList;
