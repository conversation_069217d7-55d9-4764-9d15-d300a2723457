import Moment from "moment";
export interface TPurchaseDetailProps {}

export interface IOrderHeaderStatusProps {
    detail: IPoDetail;
    steps: IStepsItem[];
    id: number;
    poStatus: enumEoStatus;
    statusIndex: number;
    reload: () => void;
}

export enum enumEoStatus {
    WAIT_AUDIT = "waitAudit", // "待关务审核"
    AUDIT_REJECT = "auditReject", //"审核驳回"
    START_PRE_DECLARE = "startPreDeclare", //"开始清关预申报"
    WAIT_ENTER = "waitEnter", //"待入区"
    WAIT_TALLY = "waitTally", //"待理货"
    WAIT_DEFINITE = "waitDefinite", //"待确报"
    WAIT_IMPROVE = "waitImprove", //"待改单"
    WAIT_RETURN = "waitReturn", //"待退运"
    DECLARE_FINISH = "DeclareFinish", //"清关完成"
    CANCELED = "canceled", //"已取消"
}

export interface IPoDetail {
    orderTagList: any;
    id: number; // id
    poStatus: enumEoStatus; // 采购单状态

    reload: () => void; // 刷新页面函数

    // 采购单信息
    poNo: string; // 采购单编号
    customsId: string; // 保税区编号
    warehouseNo: string; // 库房编号
    warehouseName: string; // 库房名称
    customsRegion: string; // 关区编号
    providerId: string; // 服务商编码
    providerName: string; // 服务商名称

    // 商品明细信息
    detailResVoList: detailResVoList[]; // 商品明细信息

    // 海关信息
    transMode: string; // 运输方式
    transModeDesc: string; //
    entryMode: string; // 入区方式
    entryModeDesc: string; //
    declareMode: string; // 报关方式
    declareModeDesc: string; //
    departPort: string; // 起运港
    destPort: string; // 目的港
    estArriveTime: DateValueType; // 预计到港时间ETA
    actArriveTime: DateValueType; // 实际到港时间ATA
    transOutRegion: string; // 转出关区
    transInRegion: string; // 转入关区
    corrDocNo: string; // 报关单号同京东的declareNo 多个用英文逗号隔开
    apprMarkNo: string; // 核注清单号 多个用英文逗号隔开
    declNo: string; // 报检单号同京东inspectNo 多个用英文逗号隔开
    inspectState: 0 | 1 | 2; // 查验状态,0:无查验1:口岸查验2:目的地查验
    inspectStateDesc: string; //
    preDeclareTime: DateValueType; // 预申报时间
    releaseTime: DateValueType; // 预申报放行时间
    preDeclareRemark: string; // 预申报备注
    inspectRemark: string; // 查验备注
    licensePlateNumber: string; // 车牌号  多个用英文逗号隔开
    actEntryTime: DateValueType; // 实际入区时间
    corrDocTime: DateValueType; // 报关时间
    declTime: DateValueType; // 报检时间

    // 提单信息
    ladNo: string; // 提单编号
    billWeight: string; // 计费重量
    intryType: string; // 入关方式
    enterWhere: string; // 入关地点
    interTrans: string; // 承运方承运
    interTransDesc: string; //
    jdLadDetailAddParamList: jdLadDetailAddParam[]; //

    // 报关指令信息
    returnCert: string; // 退运凭证
    applyReturnTime: DateValueType; // 申请退运时间
    improveCert: string; // 改单凭证
    applyImproveDate: string; // 申请改单时间
    electronicBill: string; // 电子税单
    needAdditionalTax: "0" | "1" | 0 | 1; // 是否补税,0-不需要1-需要
    additionalTaxAmount: number; // 补税金额
    taxCurrency: string; // 补税币种
    errorMsg: string; // 错误信息
    applyImproveTime: DateValueType; // 申请改单时间
}

export type LadObj = Pick<IPoDetail, "ladNo" | "billWeight" | "intryType" | "enterWhere" | "interTransDesc">;

export interface detailResVoList {
    poNo: string; // 采购单编号
    purchaseOrderId: number; // 采购单编号
    goodsName: string; // 商品名称
    sellerRecord: string; // 商品货号
    recordNo: string; // 料号
    sku: string; // 商品sku
    emg: string; // 主商品编码
    expectedQty: string; // 采购数量
    qty: string; // 理货数量
    upc: string; // UPC条码
    hsCode: string; // HScode
    spec: string; // 规格
    originCountry: string; // 原产国
    netWeight: string; // 净重
    measurement: string; // 申报单位
    id: number; // 表体id
}

export interface jdLadDetailAddParam {
    cabinetFlag: number; // 拼接标识
    cabinetType: string; // 柜型
    actualWeight: number; // 实际重量
    total: number; // 柜数
}

export interface IStepsItem {
    businessStatus: string;
    businessName: string;
    businessTime: string;
    status: "wait" | "process" | "finish" | "error";
    title: string;
    desc: string;
}

export interface DeclareteInfoProps {
    detail: IPoDetail;
    reload: () => void;
}

export type DateValueType = string | number | moment.Moment;
