import React from "react";
import { IPoDetail } from "./type";

export const StepsConfig = [
    {
        title: "待关务审核",
    },
    {
        title: "开始清关（预申报）",
    },
    {
        title: "待入区",
    },
    {
        title: "待理货",
    },
    {
        title: "待确报",
    },
    {
        title: "清关完成",
    },
    {
        title: "审核驳回",
    },
    {
        title: "已取消",
    },
];

// 表单样式配置
export const formItemLayout = { labelCol: { span: 8 }, wrapperCol: { span: 16 } };

// 商品详情配置
export const purchaseDetailConfig = [
    {
        labelName: "采购单号",
        labelKey: "poNo",
        type: "TEXT",
        required: true,
    },
    {
        labelName: "商家采购单号",
        labelKey: "outPoNo",
        type: "TEXT",
        required: true,
    },
    {
        labelName: "保税区编号",
        labelKey: "customsId",
        type: "TEXT",
    },
    {
        labelName: "库房编号",
        labelKey: "warehouseNo",
        type: "TEXT",
    },
    {
        labelName: "库房名称",
        labelKey: "warehouseName",
        type: "TEXT",
    },
    {
        labelName: "关区编号",
        labelKey: "customsRegion",
        type: "TEXT",
    },
    {
        labelName: "服务商名称",
        labelKey: "providerName",
        type: "TEXT",
    },
    {
        labelName: "货主名称",
        labelKey: "ownerName",
        type: "TEXT",
    },
    {
        labelName: "TC转运中心名称",
        labelKey: "tcName",
        type: "TEXT",
    },
];

// 商品明细信息
export const goodsColumns = (fn: (row: any) => JSX.Element, disabled: boolean) => {
    return [
        {
            title: "序号",
            dataIndex: "lineNumber",
            key: "lineNumber",
            width: "100px",
        },
        {
            title: "商品名称",
            dataIndex: "goodsName",
            key: "goodsName",
            width: "200px",
        },
        {
            title: "商品SKU",
            dataIndex: "sku",
            key: "sku",
            width: "100px",
        },
        {
            title: "商家商品编码",
            dataIndex: "isvGoodsNo",
            key: "isvGoodsNo",
            width: "100px",
        },
        {
            title: "UPC条码",
            dataIndex: "upc",
            key: "upc",
            width: "100px",
        },
        {
            title: "备案货号",
            dataIndex: "sellerRecord",
            key: "sellerRecord",
            width: "100px",
        },
        {
            title: "EMG编码",
            dataIndex: "emg",
            key: "emg",
            width: "100px",
        },
        {
            title: "HScode",
            dataIndex: "hsCode",
            key: "hsCode",
            width: "100px",
        },
        {
            title: "数量",
            dataIndex: "amount",
            key: "amount",
            width: "100px",
        },
        {
            title: "实际报关数量",
            dataIndex: "actualAmount",
            key: "actualAmount",
            editable: disabled,
            width: "100px",
            rules: [
                {
                    required: true,
                    message: "请输入实际报关数量",
                },
                {
                    validator(_, value) {
                        if (!value) return Promise.resolve();
                        if (!/^(0|[1-9]{1}\d{0,7})$/.test(value)) {
                            return Promise.reject("请输入不超过8位的整数");
                        }
                        return Promise.resolve();
                    },
                },
            ],
        },
        {
            title: "原产国",
            dataIndex: "originCountry",
            key: "originCountry",
            width: "100px",
        },
        {
            title: "申报单位",
            dataIndex: "measurement",
            key: "measurement",
            width: "100px",
        },
        {
            title: "规格",
            dataIndex: "spec",
            key: "spec",
            width: "100px",
        },
        {
            title: "净重（kg）",
            dataIndex: "netWeight",
            key: "netWeight",
            width: "100px",
        },
        {
            title: "操作栏",
            fixed: "right",
            width: 100,
            render: fn,
        },
    ];
};

// 海关信息

// 提单信息
export const LadingInfo = (firstRender: (e: any) => JSX.Element) => [
    {
        labelName: "提单号",
        labelKey: "ladNo",
        type: "TEXT",
        render: firstRender,
    },
    {
        labelName: "计费重量",
        labelKey: "billWeight",
        type: "TEXT",
    },
    {
        labelName: "入关方式",
        labelKey: "intryTypeDesc",
        type: "TEXT",
    },
    {
        labelName: "入关地点码",
        labelKey: "enterWhere",
        type: "TEXT",
    },
    {
        labelName: "承运方",
        labelKey: "interTransDesc",
        type: "TEXT",
    },
];

// 提单表格
export const LadingColumns = [
    {
        title: "拼接标识",
        dataIndex: "cabinetFlagDesc",
        key: "cabinetFlagDesc",
    },
    {
        title: "柜型",
        dataIndex: "cabinetType",
        key: "cabinetType",
    },
    {
        title: "柜数",
        dataIndex: "total",
        key: "total",
    },
    {
        title: "实际重量（KG）",
        dataIndex: "actualWeight",
        key: "actualWeight",
    },
];

// 报关指令信息
export const CustomsDeclareConfig = (showText: (str: string) => JSX.Element) => [
    {
        labelName: "退运凭证",
        labelKey: "returnCert",
        type: "TEXT",
        disabled: true,
        render: (row: { detail: IPoDetail }) => {
            return showText(row?.detail?.returnCert);
        },
    },
    {
        labelName: "申请退运时间",
        labelKey: "applyReturnTime",
        type: "DATE",
    },
    {
        labelName: "改单凭证",
        labelKey: "improveCert",
        type: "TEXT",
        disabled: true,
        render: (row: { detail: IPoDetail }) => {
            return showText(row?.detail?.improveCert);
        },
    },
    {
        labelName: "电子税单",
        labelKey: "electronicBill",
        type: "TEXT",
        disabled: true,
        render: (row: { detail: IPoDetail }) => {
            return showText(row?.detail?.electronicBill);
        },
    },
    {
        labelName: "是否补税",
        labelKey: "needAdditionalTax",
        type: "SELECT",
        list: [
            {
                id: "1",
                name: "需要",
            },
            {
                id: "0",
                name: "不需要",
            },
        ],
    },
    {
        labelName: "补税金额",
        labelKey: "additionalTaxAmount",
        type: "INPUT",
        rules: [
            {
                validator(_: any, value: string) {
                    if (!value) return Promise.resolve();
                    if (!/^(\d{1,8}(\.\d{1,2})?|1000000)$/.test(value))
                        return Promise.reject(new Error("补税金额整数部分不应超过8位，小数部分不得多于2位"));
                    return Promise.resolve();
                },
            },
        ],
    },
    {
        labelName: "补税币制",
        labelKey: "taxCurrency",
        type: "SELECT",
        list: [],
        from: "/ccs/dictionary/listByType",
    },
    {
        labelName: "申请改单时间",
        labelKey: "applyImproveTime",
        type: "DATE",
    },
];

// 报关指令table
export const CustomsDeclareColumn = (render: (row: any) => React.FunctionComponentElement<null>) => [
    {
        title: "类型",
        dataIndex: "fileType",
        key: "fileType",
    },
    {
        title: "文件名称",
        dataIndex: "fileName",
        key: "fileName",
    },
    {
        title: "操作",
        render: render,
    },
];

// 关联提单弹框参数
export const connectLadConfig = (fn: (e: any) => void) => [
    {
        labelName: "提单创建时间",
        labelKey: "LadCreateTime",
        type: "DATERANGE",
        required: true,
        mode: "date",
        onChange: fn,
    },
    {
        labelName: "提单号",
        labelKey: "ladId",
        type: "SELECT",
        list: [],

        from: "/ccs/jdLadNew/listLadNoByCreateTime",
        sourceFrom: "LadCreateTime",
    },
];

// 添加附件弹框参数
export const UploadFilesConfig = [
    {
        type: "SELECT",
        labelName: "文件凭证",
        labelKey: "type",
        list: [
            {
                id: "return",
                name: "退运",
            },
            {
                id: "improve",
                name: "改单",
            },
            {
                id: "tax",
                name: "税单",
            },
        ],
        required: true,
    },
    {
        type: "FILE",
        labelName: "附件",
        labelKey: "attachmentUrl",
        needName: true,
        accept: [".pdf"],
        action: "https://dante-img.oss-cn-hangzhou.aliyuncs.com",
        newComponent: true,
        required: true,
        size: 4,
        extra: "支持PDF格式，最多不超过4M",
    },
];

// 审批驳回弹框config
export const ImporeConfig = [
    {
        type: "TEXT",
        labelKey: "请输入单据审核驳回的具体原因",
        labelCol: 24,
    },
    {
        type: "TEXTAREA",
        laberName: "请输入单据审核驳回的具体原因",
        labelKey: "reason",
        wrapperCol: 24,
        maxLength: 500,
        required: false,
    },
];

// 推送入区通知弹框config
export const PushInOpenConfig = [
    {
        type: "TEXTAREA",
        labelName: "车牌号",
        labelKey: "licensePlateNumber",
        maxLength: 255,
        required: false,
    },
    {
        type: "DATE",
        labelName: "实际入区时间",
        labelKey: "actEntryTime",
        mode: "date",
        required: false,
    },
];

// 推送确报放行弹框config
export const ConsistConfig = [
    {
        type: "INPUT",
        labelName: "核注清单编号",
        labelKey: "apprMarkNo",
        required: false,
    },
];

// 推送退运放行弹框
export const returnConfig = [
    {
        type: "TEXT",
        labelName: "退运凭证",
        labelKey: "returnCert",
        required: false,
    },
    {
        type: "DATE",
        labelName: "申请退运时间",
        labelKey: "applyReturnTime",
        required: false,
    },
];

// 推送改单放行弹框
export const improveConfig = [
    {
        type: "TEXT",
        labelName: "改单凭证",
        labelKey: "improveCert",
        required: false,
        render: (row: IPoDetail) => {
            return row.improveCert;
        },
    },
    {
        type: "TEXT",
        labelName: "电子税单",
        labelKey: "electronicBill",
        required: false,
        render: (row: IPoDetail) => {
            return row.electronicBill;
        },
    },
    {
        type: "SELECT",
        labelName: "是否补税",
        labelKey: "needAdditionalTax",
        list: [
            {
                id: "1",
                name: "需要",
            },
            {
                id: "0",
                name: "不需要",
            },
        ],
        required: false,
    },
    {
        type: "INPUT",
        labelName: "补税金额",
        labelKey: "additionalTaxAmount",

        validator: {
            validator(_: any, value: string) {
                console.log(_);
                if (!value) return Promise.resolve();
                if (!/^(\d{1,8}(\.\d{1,2})?|1000000)$/.test(value))
                    return Promise.reject(new Error("补税金额整数部分不应超过8位，小数部分不得多于2位"));
                return Promise.resolve();
            },
        },
    },
    {
        type: "SELECT",
        labelName: "补税币制",
        labelKey: "taxCurrency",
        list: [],
        from: "/ccs/dictionary/listByType",
        // params: {},
        required: false,
    },
    {
        type: "DATE",
        labelName: "申请改单时间",
        labelKey: "applyImproveTime",
        required: false,
    },
];

// 商品编辑弹框
export const GoodsConfig = [
    {
        type: "TEXT",
        labelName: "商品SKU",
        labelKey: "sku",
    },
    {
        type: "INPUT",
        labelName: "EMG编码",
        labelKey: "emg",
        required: true,
        maxLength: 128,
    },
];

// 理货报告表格
export const tallyingColumns = [
    {
        title: "序号",
        dataIndex: "index",
        key: "index",
        width: "80px",
    },
    {
        title: "sku",
        dataIndex: "sku",
        key: "sku",
        width: "200px",
    },
    {
        title: "商品备案UPC",
        dataIndex: "upc",
        key: "upc",
        width: "150px",
    },
    {
        title: "商品名称",
        dataIndex: "goodsName",
        key: "goodsName",
        width: "200px",
    },
    {
        title: "采购单规格",
        dataIndex: "spec",
        key: "spec",
        width: "120px",
    },
    {
        title: "原产国",
        dataIndex: "country",
        key: "country",
        width: "100px",
    },
    {
        title: "采购数量",
        dataIndex: "expectedQty",
        key: "expectedQty",
        width: "100px",
    },
    {
        title: "报关数量",
        dataIndex: "actualQty",
        key: "actualQty",
        width: "100px",
    },
    {
        title: "理货数量",
        dataIndex: "tallyAmount",
        key: "tallyAmount",
        width: "100px",
    },
    {
        title: "良品数量",
        dataIndex: "goodAmount",
        key: "goodAmount",
        width: "100px",
    },
    {
        title: "残品数量",
        dataIndex: "defectNumException1",
        key: "defectNumException1",
        width: "100px",
    },
    {
        title: "是否异常",
        dataIndex: "exceptional",
        key: "exceptional",
        width: "100px",
        // render: (val: number) => {
        //     return val == 0 ? "0-否" : "1-是";
        // },
    },
    {
        title: "差异数量",
        dataIndex: "differAmount",
        key: "differAmount",
        width: "100px",
    },
    {
        title: "数量差异类型",
        dataIndex: "differException2",
        key: "differException2",
        width: "150px",
    },
    {
        title: "数量差异处理结果",
        dataIndex: "replyForException2",
        key: "replyForException2",
        width: "150px",
    },
    {
        title: "备案差异类型",
        dataIndex: "recException3",
        key: "recException3",
        width: "150px",
    },
    {
        title: "备案差异处理结果",
        dataIndex: "replyForException3",
        key: "replyForException3",
        width: "150px",
    },
    {
        title: "残品差异处理结果",
        dataIndex: "replyForException1",
        key: "replyForException1",
        width: "150px",
    },
    {
        title: "数量差异补充单号",
        dataIndex: "countDiffNo",
        key: "countDiffNo",
        width: "180px",
    },
    {
        title: "备案差异补充信息",
        dataIndex: "filingsReply",
        key: "filingsReply",
        width: "200px",
    },
    {
        title: "残品差异补充信息/单号",
        dataIndex: "defectDiffNo",
        key: "defectDiffNo",
        width: "200px",
    },
    {
        title: "理货备注",
        dataIndex: "remark",
        key: "remark",
        width: "200px",
    },
    {
        title: "理货备注回复",
        dataIndex: "replyRemark",
        key: "replyRemark",
        width: "200px",
    },
];
