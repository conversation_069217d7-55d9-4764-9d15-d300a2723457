import React, { useState, useEffect } from "react";
import { lib } from "react-single-app";
import OrderBody from "./components/order-body";
import OrderHeaderStatus from "./components/order-header-status";
import { IPoDetail, IStepsItem } from "./type";
import "./index.less";
export default () => {
    const [detail, setDetail] = useState<IPoDetail>({} as IPoDetail);
    const [steps, setSteps] = useState<IStepsItem[]>([]);
    const [statusIndex, setStatusIndex] = useState<number>();
    const getDetail: () => Promise<IPoDetail> = () => {
        return new Promise((resolve, reject) => {
            lib.request({
                url: "/ccs/jdPurchaseOrderNew/getDetailById",
                data: { id: lib.getParam("id") },
                success: (data: IPoDetail) => {
                    resolve(data);
                },
                fail: () => {
                    reject();
                },
            });
        });
    };

    const getDetailStatus: () => Promise<{
        currNode: number;
        statusList: IStepsItem[];
    }> = () => {
        return new Promise((resolve, reject) => {
            lib.request({
                url: "/ccs/jdPurchaseOrderNew/getStateFlow",
                data: { id: lib.getParam("id") },
                success: (data: { currNode: number; statusList: IStepsItem[] }) => {
                    resolve(data);
                },
                fail: () => {
                    reject();
                },
            });
        });
    };

    const init = () => {
        Promise.all([getDetail(), getDetailStatus()])
            .then(res => {
                setDetail(res[0]);
                setSteps(res[1].statusList);
                setStatusIndex(res[1].currNode);
            })
            .catch(err => {});
    };

    useEffect(() => {
        init();
    }, []);

    return (
        <div className="new-purchase-detail">
            <OrderHeaderStatus
                steps={steps}
                id={detail.id}
                poStatus={detail.poStatus}
                statusIndex={statusIndex}
                reload={init}
                detail={detail}
            />
            <OrderBody {...detail} reload={init} />
        </div>
    );
};
