import React, { useState, useEffect, useRef } from "react";
import { lib, event } from "react-single-app";
import { Space, Button, message, Image, Steps, Modal, Form, Input, Divider, Typography, Table, Select } from "antd";
import DetailList from "../components/DetailList";
import FormList from "../components/FormList";
import { FormShow } from "./record-detail";
import "./record-detail.less";
const { Step } = Steps;
const FormItem = Form.Item;
const { Title } = Typography;
const { Option } = Select;
// 基础信息配置列表
const BaseList = [
    // { labelName: "京东sku", labelKey: "skuId", type: "TEXT" },
    { labelName: "备案类型", labelKey: "typeDesc", type: "TEXT" },
    { labelName: "主商品编码", labelKey: "emg", type: "INPUT" },
    // {
    //     labelName: "是否保税区商品",
    //     labelKey: "depotItemFlag",
    //     type: "TEXT",
    //     render: detail => (detail?.depotItemFlag ? "是" : "否"),
    // },
    { labelName: "税务承担方", labelKey: "taxBear", type: "TEXT" },
    { labelName: "商品名称（中文）", labelKey: "itemChineseName", type: "INPUT" },
    { labelName: "商品名称（英文）", labelKey: "itemEnglishName", type: "INPUT" },
    { labelName: "型号", labelKey: "model", type: "INPUT" },
    { labelName: "规格", labelKey: "packSpecification", type: "INPUT" },
    {
        labelName: "申报/合同单位",
        labelKey: "declareAgreementUnit",
        type: "SELECT",
        xhr: "/ccs/customs/listUom",
        list: [],
        together: true,
    },
    { labelName: "品牌（中文）", labelKey: "chineseBrand", type: "INPUT" },
    { labelName: "品牌（英文）", labelKey: "englishBrand", type: "INPUT" },
    { labelName: "商品单价（元）", labelKey: "itemSalePrice", type: "INPUTNUMBER" },
    { labelName: "毛重（kg）", labelKey: "maoZhong", type: "INPUT" },
    { labelName: "净重（kg）", labelKey: "jingZhong", type: "INPUT" },
    { labelName: "条码", labelKey: "upc", type: "INPUT" },
    { labelName: "体积", labelKey: "volume", type: "INPUT" },
    { labelName: "保质期（天）", labelKey: "safeDays", type: "INPUT" },
    { labelName: "销售网址", extra: { className: "full-row" }, type: "TEXT", labelKey: "saleWebsite" },

    {
        labelName: "商品图片",
        extra: { className: "full-row" },
        type: "TEXT",
        render: detail => {
            return detail?.itemPics?.map((item, index) => <Image width={200} src={item} key={index} />);
        },
    },
];
// 申报信息配置列表
const DeclareList = [
    { labelName: "hs编码", labelKey: "hs", type: "SELECT", xhr: "/ccs/customs/listHs", list: [], together: true },
    {
        labelName: "增值税率%",
        labelKey: "vat",
        type: "INPUT",
        rules: [
            {
                validator(_, value) {
                    console.log(_, value);
                    if (/^(\d{1,3})$/.test(value)) {
                        return Promise.resolve();
                    } else {
                        return Promise.reject("请输入3位整数");
                    }
                },
            },
        ],
    },
    {
        labelName: "消费税率%",
        labelKey: "consumption",
        type: "INPUT",
        rules: [
            {
                validator(_, value) {
                    console.log(_, value);
                    if (/^(\d{1,3})$/.test(value)) {
                        return Promise.resolve();
                    } else {
                        return Promise.reject("请输入3位整数");
                    }
                },
            },
        ],
    },
    { labelName: "海关申报要素", labelKey: "declareElement", type: "TEXTAREA" },
    { labelName: "成分", labelKey: "element", type: "TEXTAREA" },
    { labelName: "功能", labelKey: "ability", type: "TEXTAREA" },
    { labelName: "用途", labelKey: "purpose", type: "INPUT" },
    { labelName: "生产企业名称", labelKey: "productCompanyName", type: "INPUT" },
    { labelName: "生产企业地址", labelKey: "productCompanyAddress", type: "TEXT" },
    { labelName: "供应商", labelKey: "shangJiaContacts", type: "TEXT" },
    { labelName: "备注", labelKey: "remark", type: "TEXT" },
];
// 企业备案信息配置列表
const RecordList = [
    { labelName: "商家ID", labelKey: "shangJiaId", type: "TEXT" },
    { labelName: "ECLP事业部编号", labelKey: "eclpCode", type: "TEXT" },
    { labelName: "ECLP事业部名称", labelKey: "eclpName", type: "TEXT" },
    { labelName: "跨境业务模式", labelKey: "mode", type: "TEXT" },
    { labelName: "保税区", labelKey: "depotName", type: "TEXT" },
    { labelName: "关区", labelKey: "customs", type: "TEXT" },
    { labelName: "服务商", labelKey: "serviceId", type: "TEXT" },
];
// 商家信息配置列表
const MerchantList = [
    { labelName: "店铺名称", labelKey: "shopName", type: "TEXT" },
    { labelName: "商家联系方式", labelKey: "shangJiaPhone", type: "TEXT" },
    { labelName: "商家邮箱", labelKey: "shangJiaEmail", type: "TEXT" },
];
// 备案完成推送配置列表
const FinishList = [
    { labelName: "商品货号", labelKey: "itemCode", type: "INPUT" },
    { labelName: "海关备案号", labelKey: "hgRecordCode", type: "INPUT" },
    {
        labelName: "海关原产国",
        labelKey: "hgOriginCountry",
        type: "SELECT",
        xhr: "/ccs/customs/listCountry",
        list: [],
        together: true,
    },
    { labelName: "国检备案号", labelKey: "gjRecordCode", type: "INPUT" },
    {
        labelName: "国检原产国",
        labelKey: "gjOriginCountry",
        type: "SELECT",
        xhr: "/ccs/ciq/listCiqCountry",
        list: [],
        together: true,
    },
    {
        labelName: "海关计量单位",
        labelKey: "hgMeteringUnit",
        type: "SELECT",
        xhr: "/ccs/customs/listUom",
        list: [],
        together: true,
        stateKey: "unitList",
    },
    {
        labelName: "国检计量单位",
        labelKey: "gjMeteringUnit",
        type: "SELECT",
        xhr: "/ccs/customs/listUom",
        list: [],
        together: true,
    },
    {
        labelName: "hs编码",
        labelKey: "hs",
        type: "SELECT",
        xhr: "/ccs/customs/listHs",
        together: true,
        list: [],
        required: true,
    },
    {
        labelName: "增值税率",
        labelKey: "vat",
        type: "INPUT",
        required: true,
        rules: [
            { required: true, message: "请输入增值税率" },
            {
                validator(_, value) {
                    console.log(_, value);
                    if (!value) {
                        return Promise.resolve();
                    }
                    if (/^(\d{1,3})$/.test(value)) {
                        return Promise.resolve();
                    } else {
                        return Promise.reject("请输入3位整数");
                    }
                },
            },
        ],
    },
    {
        labelName: "从价消费税率",
        labelCol: 24,
        labelKey: "adValoremConsumption",
        type: "INPUT",
        rules: [
            { required: true, message: "请输入从价消费税率" },
            {
                validator(_, value) {
                    console.log(_, value);
                    if (!value) {
                        return Promise.resolve();
                    }
                    if (/^\d+(\.\d{1,4})?$/.test(value)) {
                        return Promise.resolve();
                    } else {
                        return Promise.reject("小数点最多4位");
                    }
                },
            },
        ],
    },
    {
        labelName: "从量消费税率",
        labelCol: 24,
        labelKey: "consumption",
        type: "INPUT",
        tooltip: "例如：从量消费税率 0.2495元/升，需填写0.2495",
        rules: [
            {
                validator(_, value) {
                    console.log(_, value);
                    if (!value) {
                        return Promise.resolve();
                    }
                    if (/^\d+(\.\d{1,4})?$/.test(value)) {
                        return Promise.resolve();
                    } else {
                        return Promise.reject("小数点最多4位");
                    }
                },
            },
        ],
    },

    {
        labelName: "税率标识",
        labelKey: "taxFloat",
        type: "SELECT",
        tooltip: (
            <>
                0-不浮动＋从价消费稅
                <br />
                1-化妆品浮动＋从价消费稅
                <br />
                2-面膜浮动＋从价消费税
                <br />
                3- 黄酒不浮动＋从量消费税
                <br />
                4- 机油不浮动＋从量消费税
                <br />
                5-酒水不浮动＋复合消费税（从价和从量都填写）
                <br />
                6-啤酒浮动＋从量消费税
                <br />
                7-手表浮动＋从价消费稅
            </>
        ),
        labelCol: 24,
        // together: true,
        list: [],
        xhr: "/ccs/jdGoodsRecord/taxFloatList",
        required: true,
    },
];

// 审核状态
function AuditStatus({ detail }) {
    let status = "process";
    if (detail?.nodeList?.every(item => item.active)) {
        status = detail.nodeList[detail.nodeList.length - 1].failFlag ? "error" : "finish";
    }
    return (
        <div>
            <div className="audit-status">
                <Steps current={detail?.nodeList?.filter(item => item.active).length - 1} status={status}>
                    {detail?.nodeList?.map((item, index) => (
                        <Step title={item.text} key={index} description={item.showText} />
                    ))}
                </Steps>
                <Space>
                    <Button onClick={() => lib.closePage()}>取消</Button>
                    <Button type="primary" htmlType="submit">
                        保存
                    </Button>
                </Space>
            </div>
            <div style={{ padding: "20px 10px" }}>
                <DetailList
                    configList={[
                        { labelKey: "skuId", label: "京东sku" },
                        { labelKey: "jdRecordNo", label: "JD商品备案号" },
                        // 商品编码
                        { labelKey: "jdEmg", label: "商品编码" },
                        { labelKey: "createTime", label: "备案创建时间" },
                    ]}
                    detail={detail}></DetailList>
            </div>
        </div>
    );
}

// 商品列表
function GoodsTable({ detail }) {
    let dataSource = [{ upc: detail.upc, originArea: detail.originArea, id: 1 }];
    const columns = [
        {
            title: "条形码（UPC）",
            dataIndex: "upc",
            render: (text, record) => (
                <FormItem noStyle name="upc">
                    <Input />
                </FormItem>
            ),
        },
        {
            title: "原产国/地区",
            dataIndex: "originArea",
            render: (text, record) => (
                <FormItem noStyle name="originArea">
                    <Input />
                </FormItem>
            ),
        },
    ];
    return <Table pagination={false} dataSource={dataSource} columns={columns} rowKey="id" style={{ width: 700 }} />;
}

// 备案完成推送模块
function FinishCom({ detail }) {
    let [formList, setFormList] = useState(FinishList);
    let [unitList, setUnitList] = useState([]);
    useEffect(() => {
        if (detail?.display) {
            formList.map((item, index) => {
                if (item.xhr) {
                    lib.request({
                        url: item.xhr,
                        success: res => {
                            setFormList(formList => {
                                formList[index].list = res;
                                return formList.slice();
                            });
                            if (item.stateKey === "unitList") {
                                setUnitList(res);
                            }
                        },
                    });
                }
            });
        }
    }, [detail]);
    return (
        <Form layout="vertical">
            <FormList {...{ configList: formList, detail }} layout="vertical">
                <FormItem style={{ width: "45%" }} label="法定第一计量数量和单位">
                    <Input.Group compact>
                        <FormItem
                            noStyle
                            name="firstAmount"
                            rules={[{ required: true, message: "请输入法定第一计量数量" }]}>
                            <Input style={{ width: "50%" }} />
                        </FormItem>
                        <FormItem
                            noStyle
                            name="legalFirstUnit"
                            rules={[{ required: true, message: "请选择法定第一计量单位" }]}>
                            <Select style={{ width: "50%" }} showSearch optionFilterProp="children">
                                {unitList.map(item => (
                                    <Option value={item.id} key={item.id}>
                                        {item.id}-{item.name}
                                    </Option>
                                ))}
                            </Select>
                        </FormItem>
                    </Input.Group>
                </FormItem>
                <FormItem style={{ width: "45%" }} label="法定第二计量数量和单位">
                    <Input.Group compact>
                        <FormItem noStyle name="secondAmount">
                            <Input style={{ width: "50%" }} />
                        </FormItem>
                        <FormItem noStyle name="legalSecondUnit">
                            <Select style={{ width: "50%" }} showSearch optionFilterProp="children">
                                {unitList.map(item => (
                                    <Option value={item.id} key={item.id}>
                                        {item.id}-{item.name}
                                    </Option>
                                ))}
                            </Select>
                        </FormItem>
                    </Input.Group>
                </FormItem>
                <FormItem label="是否境外套盒" name="ifOverSeaBox">
                    <Select disabled={true} style={{ width: "50%" }} showSearch allowClear optionFilterProp="children">
                        {[
                            { id: 1, name: "是" },
                            { id: 0, name: "否" },
                        ].map(item => (
                            <Option value={item.id} key={item.id}>
                                {item.name}
                            </Option>
                        ))}
                    </Select>
                </FormItem>
            </FormList>
        </Form>
    );
}

// 详情模块
function DetailItem({ title, children, last }) {
    return (
        <div className="detail-item">
            <Title level={5}>{title}</Title>
            {children}
            {/* {!last && <Divider />} */}
        </div>
    );
}

function FileTable({ list }) {
    const columns2 = [
        {
            title: "附件名称",
            dataIndex: "name",
        },
        {
            title: "操作",
            render: row => {
                return (
                    <React.Fragment>
                        <Space>
                            <a className="link" href={row.url} target="_blank">
                                预览
                            </a>
                            <a
                                className="link"
                                onClick={() => {
                                    window.open(row.url);
                                }}>
                                下载
                            </a>
                        </Space>
                    </React.Fragment>
                );
            },
        },
    ];
    return (
        <div>
            <Table
                dataSource={
                    list?.map(item => {
                        return { url: item, name: item };
                    }) || []
                }
                columns={columns2}
                rowKey="id"
                pagination={false}></Table>
        </div>
    );
}
// 境外套盒信息
function OverSeaBoxTable({ detail }) {
    const columns = [
        { title: "货品名", dataIndex: "goodsName" },
        { title: "货品ID", dataIndex: "goodsId" },
        { title: "货品价格", dataIndex: "price" },
        { title: "货品数量", dataIndex: "quantity" },
    ];
    return (
        <Table
            pagination={false}
            dataSource={detail?.overSeaBoxDTO ? [detail?.overSeaBoxDTO] : []}
            columns={columns}
            rowKey="goodsId"
        />
    );
}

export default function () {
    const [detail, setDetail] = useState({});
    const [declareList, setDeclareList] = useState(DeclareList);
    const [baseList, setBaseList] = useState(BaseList);
    const [form] = Form.useForm();
    const ref = useRef();
    // 获取hs列表
    useEffect(() => {
        declareList.map((item, index) => {
            if (item.xhr && !item.list?.length) {
                lib.request({
                    url: item.xhr,
                    success: res => {
                        setDeclareList(declareList => {
                            declareList[index].list = res;
                            return declareList.slice();
                        });
                    },
                });
            }
        });
        baseList.map((item, index) => {
            if (item.xhr && !item.list?.length) {
                lib.request({
                    url: item.xhr,
                    success: res => {
                        setBaseList(baseList => {
                            baseList[index].list = res;
                            return baseList.slice();
                        });
                    },
                });
            }
        });
    }, []);
    // 获取详情
    function fetchDetail() {
        return new Promise((success, fail) => {
            lib.request({
                url: "/ccs/jdGoodsRecord/detail",
                data: {
                    id: lib.getParam("id"),
                },
                needMask: true,
                success,
                fail,
            });
        });
    }
    // 设置详情
    function refreshDetail() {
        fetchDetail().then(res => {
            setDetail(res);
            form.setFieldsValue(res);
            ref.current.form.setFieldsValue(res);
        });
    }
    useEffect(() => {
        refreshDetail();
    }, []);
    // 表单提交
    function submitHandle(values) {
        ref.current.form.validateFields().then(values1 => {
            lib.request({
                url: "/ccs/jdGoodsRecord/update",
                data: {
                    ...detail,
                    ...values,
                    ...values1,

                    id: lib.getParam("id"),
                },
                needMask: true,
                success: res => {
                    message.success("更新备案成功");
                    let refresh_event = lib.getParam("refresh_event");
                    if (refresh_event) {
                        event.emit(refresh_event, true);
                    }
                    lib.closePage();
                },
            });
        });
    }
    const ComList = [
        { title: "", children: <AuditStatus {...{ detail, refreshDetail }} /> },
        { title: "基础信息", children: <FormList {...{ configList: baseList, detail: detail, submitHandle }} /> },
        // { title: "商品列表", children: <GoodsTable {...{ detail }} /> },
        { title: "境外套盒信息", children: <OverSeaBoxTable {...{ detail }} /> },
        { title: "申报信息", children: <FormList {...{ configList: declareList, detail: detail }} /> },
        { title: "企业备案标识", children: <FormList {...{ configList: RecordList, detail: detail }} /> },
        {
            title: "商家信息",
            children: <FormList {...{ configList: MerchantList, detail: detail, form }} />,
            last: true,
        },
        {
            title: "附件",
            children: <FileTable list={detail?.goodsAttachList || []} />,
        },
    ];
    // if (detail?.display && ComList.length === 6) {
    //     ComList[5].last = false;
    //     ComList.push({ title: "审核信息", children: <FinishCom {...{ detail }} />, last: true });
    // }
    return (
        <div className="record-detail">
            <div style={{ width: "69%", marginTop: "20px" }}>
                <Form form={form} onFinish={submitHandle} labelCol={{ span: 8 }} wrapperCol={{ span: 16 }}>
                    {ComList.map((item, index) => (
                        <DetailItem {...item} key={index} />
                    ))}
                </Form>
            </div>
            <div style={{ width: "31%", paddingLeft: "10px" }}>
                <div
                    style={{
                        backgroundColor: "#fff",
                        padding: "20px 0 20px 20px",
                        borderRadius: "8px",
                        marginTop: "20px",
                    }}>
                    <div style={{ display: "flex", alignItems: "center", flexDirection: "row", paddingBottom: "15px" }}>
                        <h3 style={{ marginBottom: 0, marginRight: "10px" }}>审核信息</h3>
                        {["WAIT_EXAMINE_HG", "WAIT_EXAMINE"].includes(detail?.flowState) && (
                            <Button
                                type="primary"
                                onClick={() => {
                                    ref.current.submit(values1 => {
                                        console.log("values1:", values1);
                                        lib.request({
                                            url: "/ccs/jdGoodsRecord/updateExamineInfo",
                                            data: {
                                                ...values1,
                                                consumption: parseFloat(values1.consumption),
                                                adValoremConsumption: parseFloat(values1.adValoremConsumption),
                                                id: lib.getParam("id"),
                                            },
                                            needMask: true,
                                            success: res => {
                                                message.success("保存成功");
                                                fetchDetail();
                                            },
                                        });
                                    });
                                }}>
                                保存
                            </Button>
                        )}
                    </div>

                    <FormShow
                        ref={ref}
                        detail={detail}
                        // disabled={!["WAIT_EXAMINE_HG", "WAIT_EXAMINE"].includes(detail?.flowState)}
                    />
                    {/* <FinishCom detail={detail} /> */}
                </div>
            </div>
        </div>
    );
}
