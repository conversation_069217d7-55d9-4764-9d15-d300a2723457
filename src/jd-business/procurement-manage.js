import React from "react";
import { lib, SearchList, getConfigDataUtils } from "react-single-app";
import axios from "axios";
import EditModal from "../components/EditModal";
import { Space, Button, message } from "antd";
import moment from "moment";

export default class extends SearchList {
    getConfig() {
        return axios.get(getConfigDataUtils.getDataUrlByDataId(238)).then(res => res.data.data);
    }

    renderRightOperation() {
        return (
            <Space>
                <Button
                    type="primary"
                    onClick={() =>
                        lib.openPage("/add-procurement?page_title=新建采购单&t=" + +new Date(), () => this.load())
                    }>
                    新建
                </Button>
            </Space>
        );
    }

    renderLadNo(row) {
        return (
            <span
                className="link"
                onClick={() => lib.openPage(`/lading-bill-detail?id=${row.ladId}&page_title=提单详情&type=watch`)}>
                {row.ladNo}
            </span>
        );
    }

    onOk(values, { id }) {
        let data = Object.assign({}, values);
        data.corrDocTime = moment(data.corrDocTime).format("YYYY-MM-DD HH:mm:ss");
        data.declTime = data.declTime ? moment(data.declTime).format("YYYY-MM-DD HH:mm:ss") : null;
        lib.request({
            url: "/ccs/jdPurchaseOrder/pullCustom",
            data: {
                ...data,
                id,
            },
            needMask: true,
            success: res => {
                message.success("推送成功");
                this.load();
            },
        });
    }

    myOperation(row) {
        const props = {
            title: "完善报关报检信息",
            configList: [
                { labelName: "报关单号", labelKey: "corrDocNo", required: true, type: "INPUT" },
                { labelName: "报关时间", labelKey: "corrDocTime", required: true, type: "DATE" },
                { labelName: "报检单号", labelKey: "declNo", type: "INPUT" },
                { labelName: "报检时间", labelKey: "declTime", type: "DATE" },
            ],
            editRow: row,
            btnProps: {
                text: "推送",
                type: "span",
            },
            onOk: values => this.onOk(values, row),
        };
        return (
            <Space>
                <span
                    className="link"
                    onClick={() => lib.openPage(`/procurement-detail?id=${row.id}&page_title=采购单详情`)}>
                    查看
                </span>
                {row.showPushBtn && <EditModal {...props} />}
            </Space>
        );
    }
}
