.add-procurement {
    padding: 12px;
    box-sizing: border-box;
    .goods-title {
        display: flex;
        justify-content: space-between;
        .btn-div button {
            margin-left: 16px;
            margin-bottom: 16px;
        }
    }
    .submit-btn {
        padding: 20px;
        box-sizing: border-box;
        display: flex;
        flex-direction: row-reverse;
        button {
            margin: 8px;
        }
    }
    td .ant-form-item {
        margin-bottom: 0;
    }
    .audit-status {
        width: 100%;
        display: flex;
        justify-content: space-around;
        .ant-space {
            min-width: 80px;
            margin-left: 20px;
            flex-direction: row-reverse;
        }
    }
    .sync-import {
        position: relative;
    }
    .react-single-app-uploader {
        width: 100%;
        height: 100%;
        z-index: 2;
        opacity: 0;
        position: absolute;
        top: 0;
        left: 0;
    }
    .required-div::before {
        display: inline-block;
        margin-right: 4px;
        color: #ff4d4f;
        font-size: 14px;
        font-family: SimSun, sans-serif;
        line-height: 1;
        content: "*";
    }
}
