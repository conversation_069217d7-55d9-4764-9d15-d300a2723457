import React, { useState, useEffect } from "react";
import { Button, Modal, Space, Form, Input, Skeleton, Spin, Tabs, Alert } from "antd";
import axios from "axios";
import { lib, event, SearchList, getConfigDataUtils, HOC } from "react-single-app";
import NewModal from "../../components/NewModal";
import "./index.less";

class FacilitatorManage extends SearchList {
    constructor(props) {
        super(props);
        this.state.addVisible = false;
        this.state.tokenVisible = false;
        this.state.needClear = false;
        this.state.modalTitle = "";
        this.state.editRow = {};
        this.state.configList = [
            {
                type: "INPUT",
                labelName: "服务商名称",
                labelKey: "name",
                required: true,
                message: "请输入服务商名称",
            },
            {
                type: "INPUT",
                labelName: "服务商编码",
                labelKey: "code",
                required: true,
                message: "请输入服务商编码",
            },
            {
                type: "INPUT",
                labelName: "外部店铺ID",
                labelKey: "outShopId",
                required: true,
            },
            {
                type: "SELECT",
                labelName: "关区名称",
                labelKey: "customsName",
                required: true,
                list: [],
                ccs: "/ccs/customs/dictionary/guaranteeCustoms",
                onChange: (e, form) => {
                    form.setFieldsValue({ customsCode: e });
                },
            },
            {
                type: "INPUT",
                labelName: "关区编码",
                labelKey: "customsCode",
                required: true,
                disabled: true,
                placeholder: "请选择关区名称",
            },
            {
                type: "INPUT",
                labelName: "保税区编码",
                labelKey: "bondedAreaCode",
                required: true,
                placeholder: "请填写保税区编码",
            },
            {
                type: "INPUT",
                labelName: "Access Token",
                labelKey: "accessToken",
                required: true,
            },
            {
                type: "INPUT",
                labelName: "Appkey",
                labelKey: "appKey",
                required: true,
            },
            {
                type: "INPUT",
                labelName: "App Secret",
                labelKey: "appSecret",
                required: true,
                onFocus: form => {
                    const data = form.getFieldValue();
                    if (this.state.needClear && data.appSecret === this.state.editRow.appSecret) {
                        form.setFieldsValue({ appSecret: "" });
                    }
                },
            },
            {
                type: "TEXT",
                labelName: "",
                labelCol: { span: 0 },
                wrapperCol: { span: 20 },
                render: () => {
                    return (
                        <>
                            <p className="tips">密钥加密展示,如需修改请先清空</p>
                        </>
                    );
                },
            },
            {
                type: "INPUT",
                labelName: "京东云Access key",
                labelKey: "cloudAccessKey",
                // required: true,
            },
            {
                type: "INPUT",
                labelName: "京东云Secret Key",
                labelKey: "cloudAccessSecret",
                // required: true,
                onFocus: form => {
                    const data = form.getFieldValue();
                    if (this.state.needClear && data.cloudAccessSecret === this.state.editRow.cloudAccessSecret) {
                        form.setFieldsValue({ cloudAccessSecret: "" });
                    }
                },
            },
            {
                type: "TEXT",
                labelName: "",
                labelCol: { span: 0 },
                wrapperCol: { span: 20 },
                render: () => {
                    return (
                        <>
                            <p className="tips">密钥加密展示,如需修改请先清空</p>
                        </>
                    );
                },
            },
        ];
        this.state.tokenConfigList = [
            {
                type: "INPUT",
                labelName: "Access Token",
                labelKey: "accessToken",
                required: true,
                message: "请输入Access Token",
            },
            {
                type: "INPUT",
                labelName: "Appkey",
                labelKey: "appKey",
                required: true,
                message: "请输入Appkey",
            },
            {
                type: "INPUT",
                labelName: "App Secret",
                labelKey: "appSecret",
                required: true,
                onFocus: form => {
                    const data = form.getFieldValue();
                    if (this.state.needClear && data.appSecret === this.state.editRow.appSecret) {
                        form.setFieldsValue({ appSecret: "" });
                    }
                },
            },
            {
                type: "TEXT",
                labelName: "",
                labelCol: { span: 0 },
                wrapperCol: { span: 20 },
                render: () => {
                    return (
                        <>
                            <p className="tips">密钥加密展示,如需修改请先清空</p>
                        </>
                    );
                },
            },
            {
                type: "INPUT",
                labelName: "京东云Access key",
                labelKey: "cloudAccessKey",
                // required: true,
            },
            {
                type: "INPUT",
                labelName: "京东云Secret Key",
                labelKey: "cloudAccessSecret",
                // required: true,
                onFocus: form => {
                    const data = form.getFieldValue();
                    if (this.state.needClear && data.cloudAccessSecret === this.state.editRow.cloudAccessSecret) {
                        form.setFieldsValue({ cloudAccessSecret: "" });
                    }
                },
            },
            {
                type: "TEXT",
                labelName: "",
                labelCol: { span: 0 },
                wrapperCol: { span: 20 },
                render: () => {
                    return (
                        <>
                            <p className="tips ant-form-item-control-input">密钥加密展示,如需修改请先清空</p>
                        </>
                    );
                },
            },
        ];
    }

    getConfig() {
        let url = getConfigDataUtils.getDataUrlByDataId(776);
        return axios.get(url).then(res => res.data.data);
    }

    renderLeftOperation() {
        return (
            <>
                <Button type="primary" onClick={() => this.initAddModal()}>
                    新增
                </Button>
            </>
        );
    }

    renderModal() {
        let props = {
            title: this.state.modalTitle,
            onOk: this.handleOk.bind(this),
            onCancel: this.handleCancel.bind(this),
            configList: this.state.configList,
            visible: this.state.addVisible,
            editRow: this.state.editRow,
        };
        let tokenProps = {
            title: "访问令牌",
            onOk: this.tokenOk.bind(this),
            onCancel: this.tokenCancel.bind(this),
            configList: this.state.tokenConfigList,
            visible: this.state.tokenVisible,
            editRow: this.state.editRow,
        };
        return (
            <>
                <NewModal {...props}></NewModal>
                <NewModal {...tokenProps}></NewModal>
            </>
        );
    }

    getSelectList(prevConfigList) {
        const row = prevConfigList.map(item => {
            return { ...item };
        });
        const requests = [];
        row.map((item, index) => {
            // 可以加参数的 自己再写一个参数即可
            if (item.ccs) {
                requests.push(
                    new Promise((resolve, reject) => {
                        lib.request({
                            url: item.ccs,
                            needMask: false,
                            success: res => {
                                resolve({ data: res, index: index });
                            },
                            fail: () => {
                                reject();
                            },
                        });
                    }),
                );
            }
        });
        Promise.all(requests)
            .then(res => {
                res.map(item => {
                    row[item.index].list = item.data;
                });
                this.setState({ configList: row });
            })
            .catch(err => {});
    }

    initAddModal() {
        this.getSelectList(this.state.configList);
        this.setState({
            addVisible: true,
            modalTitle: "新增",
            needClear: false,
            editRow: {},
        });
    }

    initEditModal(row) {
        this.getSelectList(this.state.configList);
        this.setState({
            addVisible: true,
            needClear: true,
            modalTitle: "编辑",
            editRow: {
                id: row.id,
                name: row.name?.trim(),
                code: row.code?.trim(),
                outShopId: row.outShopId?.trim(),
                customsName: row.customsCode?.trim(),
                customsCode: row.customsCode?.trim(),
                appKey: row.appKey?.trim(),
                appSecret: row.appSecret?.trim(),
                accessToken: row.accessToken?.trim(),
                cloudAccessKey: row.cloudAccessKey?.trim(),
                cloudAccessSecret: row.cloudAccessSecret?.trim(),
                bondedAreaCode: row.bondedAreaCode?.trim(),
            },
        });
    }

    handleOk(values) {
        const isDelAppSecret = values.appSecret === this.state.editRow.appSecret;
        const cloudAccessSecret = values.cloudAccessSecret === this.state.editRow.cloudAccessSecret;
        const data = Object.assign(this.state.editRow, values);

        if (isDelAppSecret) {
            delete data.appSecret;
        }
        if (cloudAccessSecret) {
            delete data.cloudAccessSecret;
        }
        lib.request({
            url: "/ccs/jdServProvider/addOrEdit",
            data: data,
            needMask: true,
            success: res => {
                this.setState({
                    modalTitle: "",
                    addVisible: false,
                    needClear: false,
                    editRow: {},
                });
                window.location.reload();
            },
        });
    }

    handleCancel() {
        this.setState({
            modalTitle: "",
            addVisible: false,
            needClear: false,
            editRow: {},
        });
    }

    initTokenModal(row) {
        this.setState({
            tokenVisible: true,
            needClear: true,
            editRow: {
                id: row.id,
                name: row.name?.trim(),
                code: row.code?.trim(),
                customsCode: row.customsCode?.trim(),
                appKey: row.appKey?.trim(),
                appSecret: row.appSecret?.trim(),
                accessToken: row.accessToken?.trim(),
                cloudAccessKey: row.cloudAccessKey?.trim(),
                cloudAccessSecret: row.cloudAccessSecret?.trim(),
            },
        });
    }

    tokenOk(values) {
        const isDelAppSecret = values.appSecret === this.state.editRow.appSecret;
        const cloudAccessSecret = values.cloudAccessSecret === this.state.editRow.cloudAccessSecret;
        const data = Object.assign(this.state.editRow, values);

        if (isDelAppSecret) {
            delete data.appSecret;
        }
        if (cloudAccessSecret) {
            delete data.cloudAccessSecret;
        }
        lib.request({
            url: "/ccs/jdServProvider/addOrEdit",
            data: { ...data },
            needMask: true,
            success: res => {
                this.setState({
                    tokenVisible: false,
                    needClear: false,
                    editRow: {},
                });
                this.load();
            },
        });
    }

    tokenCancel() {
        this.setState({
            tokenVisible: false,
            needClear: false,
            editRow: {},
        });
    }

    accessToken(row) {
        return (
            <Space>
                <span className="link" onClick={() => this.initTokenModal(row)}>
                    查看
                </span>
            </Space>
        );
    }

    operation(row) {
        return (
            <Space>
                <span className="link" onClick={() => this.initEditModal(row)}>
                    编辑
                </span>
                <span
                    className="link"
                    onClick={() =>
                        lib.openPage(
                            `/warehouse-allocation?page_title=库房配置&servProviderId=${row.id}&customsCode=${row.customsCode}`,
                            () => this.load(),
                        )
                    }>
                    配置
                </span>
            </Space>
        );
    }
}

export default FacilitatorManage;
