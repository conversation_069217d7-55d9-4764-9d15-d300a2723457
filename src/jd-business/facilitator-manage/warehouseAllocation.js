import React, { useState, useEffect } from "react";
import { Button, Modal, Space, Form, Input, Skeleton, Spin, Tabs, Alert } from "antd";
import axios from "axios";
import { lib, event, SearchList, getConfigDataUtils, HOC } from "react-single-app";
import NewModal from "../../components/NewModal";

class WarehouseAllocation extends SearchList {
    constructor(props) {
        super(props);
        this.state.modalVisible = false;
        this.state.modalTitle = "";
        this.state.editRow = {};
        this.state.configList = [
            {
                type: "SELECT",
                labelName: "库房名称",
                labelKey: "code",
                required: true,
                list: [],
                onChange: (e, form) => {
                    form.setFieldsValue({ code: e });
                },
                ccs: "/ccs/dictionary/listJdWarehouseName",
            },
            {
                type: "INPUT",
                labelName: "库房编码",
                labelKey: "code",
                required: true,
                disabled: true,
                placeholder: "请选择库房名称",
            },
            {
                type: "SELECT",
                labelName: "账册编码",
                labelKey: "customsBookId",
                required: true,
                list: [],
                ccs: "/ccs/customsBook/listAllBookNo",
            },
        ];
    }

    getConfig() {
        let url = getConfigDataUtils.getDataUrlByDataId(777);
        return axios.get(url).then(res => res.data.data);
    }

    renderLeftOperation() {
        return (
            <>
                <Button type="primary" onClick={() => this.initAddModal()}>
                    新增
                </Button>
            </>
        );
    }

    renderModal() {
        let props = {
            title: this.state.modalTitle,
            onOk: this.handleOk.bind(this),
            onCancel: this.handleCancel.bind(this),
            configList: this.state.configList,
            visible: this.state.modalVisible,
            editRow: this.state.editRow,
        };
        return (
            <>
                <NewModal {...props}></NewModal>
            </>
        );
    }

    getSelectList(prevConfigList) {
        const row = prevConfigList.map(item => {
            return { ...item };
        });
        const requests = [];
        row.map((item, index) => {
            // 可以加参数的 自己再写一个参数即可
            if (item.ccs) {
                if (item.labelKey === "code") {
                    var data = { customsCode: lib.getParam("customsCode") };
                }
                requests.push(
                    new Promise((resolve, reject) => {
                        lib.request({
                            url: item.ccs,
                            data,
                            needMask: false,
                            success: res => {
                                resolve({ data: res, index: index });
                            },
                            fail: () => {
                                reject();
                            },
                        });
                    }),
                );
            }
        });
        Promise.all(requests)
            .then(res => {
                res.map(item => {
                    row[item.index].list = item.data;
                });
                this.setState({ configList: row });
            })
            .catch(err => {});
    }

    initEditModal(row) {
        const data = lib.getParam("servProviderId");
        this.getSelectList(this.state.configList);
        this.setState({
            modalVisible: true,
            modalTitle: "编辑",
            editRow: {
                id: row.id,
                code: row.code,
                servProviderId: data,
                customsBookId: row.customsBookId,
            },
        });
    }

    initAddModal() {
        const data = lib.getParam("servProviderId");
        this.getSelectList(this.state.configList);
        this.setState({
            modalVisible: true,
            modalTitle: "新增",
            editRow: {
                servProviderId: data,
            },
        });
    }

    handleOk(values) {
        const data = Object.assign(this.state.editRow, values);
        lib.request({
            url: "/ccs/jdServProvider/jdWarehouse/addOrEdit",
            data: { ...data },
            needMask: true,
            success: res => {
                this.setState({
                    modalTitle: "",
                    modalVisible: false,
                    editRow: {},
                });
                this.load();
            },
        });
    }

    handleCancel() {
        this.setState({
            modalTitle: "",
            modalVisible: false,
            editRow: {},
        });
    }

    operation(row) {
        return (
            <Space>
                <span className="link" onClick={() => this.initEditModal(row)}>
                    编辑
                </span>
            </Space>
        );
    }
}

export default WarehouseAllocation;
