import React, { useState, useEffect } from "react";
import { lib, event } from "react-single-app";
import {
    Space,
    Button,
    message,
    Image,
    Steps,
    Modal,
    Form,
    Input,
    Divider,
    Typography,
    Table,
    Select,
    InputNumber,
} from "antd";
import DetailList from "../components/DetailList";
import FormList from "../components/FormList";
import "./lading-bill-detail.less";
const { Step } = Steps;
const FormItem = Form.Item;
const { TextArea } = Input;
const { Title } = Typography;
const { Option } = Select;
const BaseListWatch = [
    { labelName: "提单号", labelKey: "ladNo" },
    { labelName: "服务商", labelKey: "provider" },
    { labelName: "入关地点", labelKey: "intryWhereStr" },
    { labelName: "入关方式", labelKey: "intryTypeStr" },
    { labelName: "运输方式", labelKey: "transTypeStr" },
    { labelName: "计费重量(kg)", labelKey: "billWeightStr" },
    { labelName: "承运方承运", labelKey: "interTransStr" },
];

const AssociatedList = [{ labelName: "采购单号", labelKey: "poNos" }];
// 审核状态
function AuditStatus({ detail, refreshDetail }) {
    const type = lib.getParam("type");
    function pushHandle() {
        lib.request({
            url: "/ccs/jdLad/pullLadInfo",
            data: {
                id: lib.getParam("id"),
            },
            needMask: true,
            success: res => {
                message.success("推送成功");
                refreshDetail();
            },
        });
    }
    let status = "process";
    if (detail?.nodeList?.every(item => item.active)) {
        status = detail.nodeList[detail.nodeList.length - 1].failFlag ? "error" : "finish";
    }
    return (
        type === "watch" && (
            <div className="audit-status">
                <Steps current={detail?.nodeList?.filter(item => item.active).length - 1} status={status}>
                    {detail?.nodeList?.map((item, index) => (
                        <Step title={item.text} key={index} description={item.showText} />
                    ))}
                </Steps>
                <Space>
                    {detail.ladStatus === "WAIT_PUSH" && (
                        <Button type="primary" onClick={pushHandle}>
                            推送
                        </Button>
                    )}
                </Space>
            </div>
        )
    );
}

// 提单信息
function LadingDetail({ detail, form }) {
    const type = lib.getParam("type");
    let [baseList, setBaseList] = useState([
        { labelName: "提单号", labelKey: "ladNo", type: "INPUT", required: true },
        {
            labelName: "服务商",
            labelKey: "provider",
            type: "SELECT",
            list: [],
            xhr: "/ccs/jdLad/providers",
            required: true,
        },
        {
            labelName: "运输方式",
            labelKey: "transType",
            type: "SELECT",
            list: [],
            xhr: "/ccs/jdLad/transTypes",
            required: true,
            onChange: transTypeChange,
        },
        {
            labelName: "入关地点",
            labelKey: "intryWhere",
            type: "SELECT",
            list: [],
            xhr: "/ccs/dictionary/listIntryWhere",
            required: true,
        },
        {
            labelName: "入关方式",
            labelKey: "intryType",
            type: "SELECT",
            list: [],
            xhr: "/ccs/jdLad/intryTypes",
            required: true,
        },

        { labelName: "计费重量(kg)", labelKey: "billWeight", type: "INPUT", required: false },
        {
            labelName: "承运方承运",
            labelKey: "interTrans",
            type: "SELECT",
            list: [],
            xhr: "/ccs/jdLad/interTrans",
            required: true,
        },
    ]);
    useEffect(() => {
        type === "edit" &&
            baseList.map((item, index) => {
                if (item.xhr) {
                    lib.request({
                        url: item.xhr,
                        success: res => {
                            setBaseList(baseList => {
                                baseList[index].list = res;
                                return baseList.slice();
                            });
                        },
                    });
                }
            });
    }, []);
    function transTypeChange(e) {
        lib.request({
            url: "/ccs/dictionary/listIntryWhere",
            data: { transType: e },
            success: data => {
                baseList[3].list = data;
                setBaseList([...baseList]);
            },
        });
        setBaseList(baseList => {
            baseList[5].required = e == 3 ? true : false;
            return baseList.slice();
        });
    }

    return type === "edit" ? (
        <FormList {...{ configList: baseList, form }} />
    ) : (
        <DetailList {...{ configList: BaseListWatch, detail }} />
    );
}

// 拼接表格
function TableList({ detail, form }) {
    const [dataSource, setDataSource] = useState([]);
    const [flags, setFlags] = useState([]);
    const [types, setTypes] = useState([]);
    const type = lib.getParam("type");
    const requestMap = [
        ["/ccs/jdLad/cabinetFlags", setFlags],
        ["/ccs/jdLad/cabinetTypes", setTypes],
    ];
    function requestFunc() {
        requestMap.map(([url, success]) => lib.request({ url, success }));
    }
    useEffect(() => {
        type === "watch" && setDataSource(detail?.ladDetails || []);
    }, [detail]);
    useEffect(() => {
        let initialValue = [{ key: +new Date() }];
        type === "edit" && setDataSource(initialValue), requestFunc(), form.setFieldsValue({ ladDetail: initialValue });
    }, []);
    function addNewItem() {
        let data = [...form.getFieldValue("ladDetail"), { key: +new Date() }];
        form.setFieldsValue({ ladDetail: data });
        setDataSource(data);
    }
    function deleteHandle(index) {
        setDataSource(() => {
            let data = form.getFieldValue("ladDetail");
            data.splice(index, 1);
            form.setFieldsValue({ ladDetail: data });
            return data.slice();
        });
    }
    let columns = [
        {
            title: "拼接标识",
            render: (text, record, index) =>
                type === "edit" ? (
                    <FormItem
                        noStyle
                        name={["ladDetail", index, "cabinetFlag"]}
                        rules={[{ required: true, message: "请选择拼接标识" }]}>
                        <Select style={{ width: 130 }}>
                            {flags?.map(item => (
                                <Option value={item.id} key={item.id}>
                                    {item.name}
                                </Option>
                            ))}
                        </Select>
                    </FormItem>
                ) : (
                    record.cabinetFlagStr
                ),
        },
        {
            title: "柜型",
            render: (text, record, index) =>
                type === "edit" ? (
                    <FormItem noStyle name={["ladDetail", index, "cabinetType"]}>
                        <Select style={{ width: 130 }}>
                            {types?.map(item => (
                                <Option value={item.id} key={item.id}>
                                    {item.name}
                                </Option>
                            ))}
                        </Select>
                    </FormItem>
                ) : (
                    record.cabinetType
                ),
        },
        {
            title: "柜数",
            render: (text, record, index) =>
                type === "edit" ? (
                    <FormItem noStyle name={["ladDetail", index, "total"]}>
                        <InputNumber min={1} max={10} />
                    </FormItem>
                ) : (
                    record.totalStr
                ),
        },
        {
            title: "实际重量(kg)",
            render: (text, record, index) => {
                let bool = false;
                if (
                    form.getFieldValue("intryType") == 3 &&
                    form.getFieldValue(["ladDetail", index, "cabinetFlag"]) === "123"
                ) {
                    bool = true;
                }
                return type === "edit" ? (
                    <FormItem
                        noStyle
                        name={["ladDetail", index, "actualWeight"]}
                        rules={[{ required: bool, message: "请输入实际重量" }]}>
                        <InputNumber min={1} max={1000000} />
                    </FormItem>
                ) : (
                    record.actualWeightStr
                );
            },
        },
        {
            title: "操作",
            render: (text, record, index) => (
                <React.Fragment>
                    {!!index && (
                        <span className="link" onClick={() => deleteHandle(index)}>
                            删除
                        </span>
                    )}
                    <FormItem name={["ladDetail", index, "key"]} hidden>
                        <Input />
                    </FormItem>
                </React.Fragment>
            ),
        },
    ];
    if (type === "watch") columns.splice(4, 1);
    return (
        <div className="table-list">
            <Table dataSource={dataSource} columns={columns} pagination={false} rowKey="key" />
            {type === "edit" && (
                <React.Fragment>
                    <Button type="dashed" style={{ width: "100%" }} onClick={addNewItem}>
                        +新增
                    </Button>
                    <div className="submit-btn">
                        <Button type="primary" htmlType="submit">
                            提交
                        </Button>
                        <Button onClick={() => lib.closePage()}>取消</Button>
                    </div>
                </React.Fragment>
            )}
        </div>
    );
}

// 详情模块
function DetailItem({ title, children, last }) {
    return (
        <div className="detail-item">
            <Title level={5}>{title}</Title>
            {children}
            {/* {!last && <Divider />} */}
        </div>
    );
}

export default function () {
    const type = lib.getParam("type");
    const [detail, setDetail] = useState({});
    const [form] = Form.useForm();
    function fetchDetail() {
        return new Promise((success, fail) => {
            lib.request({
                url: "/ccs/jdLad/detail",
                data: {
                    id: lib.getParam("id"),
                },
                needMask: true,
                success,
                fail,
            });
        });
    }
    function refreshDetail() {
        fetchDetail().then(res => setDetail(res));
    }
    useEffect(() => {
        type === "watch" && refreshDetail();
    }, []);
    let ComList = [
        { children: <AuditStatus {...{ detail, refreshDetail }} /> },
        {
            title: "提单信息",
            children: <LadingDetail {...{ detail, form }} />,
        },
        { children: <DetailList {...{ configList: AssociatedList, detail }} /> },
        { children: <TableList {...{ detail, form }} /> },
    ];
    if (type === "edit" && ComList.length === 4) {
        ComList.splice(2, 1);
    }
    function submitHandle(values) {
        lib.request({
            url: "/ccs/jdLad/create",
            data: values,
            needMask: true,
            success: res => {
                message.success("新建提单成功");
                let refresh_event = lib.getParam("refresh_event");
                if (refresh_event) {
                    event.emit(refresh_event, true);
                }
                lib.closePage();
            },
        });
    }
    return (
        <div className="lading-bill-detail">
            <Form form={form} onFinish={submitHandle}>
                {ComList.map((item, index) => (
                    <DetailItem {...item} key={index} />
                ))}
            </Form>
        </div>
    );
}
