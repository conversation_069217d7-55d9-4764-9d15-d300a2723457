import {
    getConfigDataUtils,
    event,
    lib,
    SearchList,
    ClassSearchListProps,
    DDYObject,
    ClassSearchListState,
    HOC,
} from "react-single-app";
import {
    NewPurchaseManageDataItem,
    NewPurchaseManageProps,
    NewPurchaseManageState,
    TStepsItem,
    TOrderStatus,
    NewPurchaseManageCounts,
} from "./type";
import axios from "axios";
import React from "react";
import { Space, Button, message, Tabs, Modal, Tag } from "antd";
import { enumEoStatus } from "../purchase-detail/type";
//@ts-ignore
import NewModal from "../../components/NewModal";
import { ConsistOpenConfig, ImporeConfig } from "./config";
import UpdateStatusModal from "@/page/customs-clearance-system/components/update-status-modal";
const { TabPane } = Tabs;
//@ts-ignore
@HOC.mapAuthButtonsToState({ buttonCodeArr: ["exportAuth"] })
export default class NewPurchaseManage extends SearchList<NewPurchaseManageProps, NewPurchaseManageState> {
    async getConfig() {
        let url = getConfigDataUtils.getDataUrlByDataId(637);
        const res = await axios.get(url);
        return res.data.data;
    }

    componentDidMount(): void {
        event.on("onSearchReset", this.onSearchReset.bind(this));
        this.getcustomsCode();
    }
    //@ts-ignore
    load(_?: any, toTop?: boolean) {
        if (!this.state.customsCode) return;
        super.load(_, toTop);
        this.getStatusCount();
    }

    getStatusCount = () => {
        // this.load()
        lib.request({
            url: "/ccs/jdPurchaseOrderNew/statusCount",
            data: {
                customsCode: this.state.customsCode,
                ...this.state.search,
            },
            success: (data: TStepsItem) => {
                const result = {} as NewPurchaseManageCounts;
                data.map(item => {
                    result[item.status] = item.count;
                });
                this.setState(result);
            },
            fail: () => {
                // reject();
            },
        });
    };

    //@ts-ignore
    componentWillUnmount() {
        event.off("onSearchReset", this.onSearchReset);
    }

    onSearchReset() {
        this.setState({ poStatus: "ALL" });
        this.changeImmutable({ poStatus: "ALL" });
    }
    getcustomsCode = () => {
        lib.request({
            url: "/ccs/jdServProvider/listAuthCustoms",
            success: res => {
                const data = Object.assign({}, this.state.search, { customsCode: res[0].id });
                this.setState(
                    {
                        customs: res,
                        customsCode: res[0].id,
                    },
                    () => {
                        // this.getStatusCount();
                        // this.load();
                    },
                );
                setTimeout(() => {
                    this.load();
                }, 10);
            },
        });
    };
    //@ts-ignore
    renderSearchTopView() {
        return (
            <Space>
                <Tabs
                    defaultActiveKey={""}
                    activeKey={this.state.customsCode}
                    onChange={key => {
                        this.changeImmutable({
                            customsCode: key,
                        });
                        this.setState({
                            customsCode: key,
                        });
                    }}>
                    {this.state.customs &&
                        this.state.customs.map(item => {
                            return <TabPane tab={`${item.name}`} key={`${item.id}`} />;
                        })}
                </Tabs>
            </Space>
        );
    }
    renderOperationTopView = () => {
        return (
            <Tabs
                defaultActiveKey={"ALL"}
                activeKey={this.state.poStatus}
                onChange={(activeKey: any) => {
                    this.changeImmutable({
                        poStatus: activeKey,
                    });
                    this.setState({
                        poStatus: activeKey,
                    });
                }}>
                <Tabs.TabPane tab="全部" key={"ALL"}></Tabs.TabPane>
                <Tabs.TabPane
                    tab={`待关务审核(${this.state.waitAudit || 0})`}
                    key={enumEoStatus.WAIT_AUDIT}></Tabs.TabPane>
                <Tabs.TabPane
                    tab={`开始清关预申报(${this.state.startPreDeclare || 0})`}
                    key={enumEoStatus.START_PRE_DECLARE}></Tabs.TabPane>
                <Tabs.TabPane tab={`待入区(${this.state.waitEnter || 0})`} key={enumEoStatus.WAIT_ENTER}></Tabs.TabPane>
                <Tabs.TabPane tab={`待理货(${this.state.waitTally || 0})`} key={enumEoStatus.WAIT_TALLY}></Tabs.TabPane>
                <Tabs.TabPane
                    tab={`待确报(${this.state.waitDefinite || 0})`}
                    key={enumEoStatus.WAIT_DEFINITE}></Tabs.TabPane>
                <Tabs.TabPane
                    tab={`待改单(${this.state.waitImprove || 0})`}
                    key={enumEoStatus.WAIT_IMPROVE}></Tabs.TabPane>
                <Tabs.TabPane
                    tab={`待退运(${this.state.waitReturn || 0})`}
                    key={enumEoStatus.WAIT_RETURN}></Tabs.TabPane>
                <Tabs.TabPane tab={`已取消`} key={enumEoStatus.CANCELED}></Tabs.TabPane>
                <Tabs.TabPane tab={`清关完成`} key={enumEoStatus.DECLARE_FINISH}></Tabs.TabPane>
            </Tabs>
        );
    };

    renderLeftOperation() {
        return (
            <Space>
                <Button
                    type="primary"
                    onClick={() => {
                        this.approveProcess();
                    }}>
                    审核通过
                </Button>
                <Button
                    type="primary"
                    onClick={() => {
                        this.rejectProcess();
                    }}>
                    审核驳回
                </Button>
                <Button
                    type="primary"
                    onClick={() => {
                        this.pushNotice();
                    }}>
                    推送预申报完成
                </Button>
                <Button
                    type="primary"
                    onClick={() => {
                        this.pushInNotice();
                    }}>
                    推送入区通知
                </Button>
                <Button
                    type="primary"
                    onClick={() => {
                        this.pushConsist();
                    }}>
                    推送确报
                </Button>
                {this.state.buttons?.includes("UPDATE-PURCHASE-STATUS") && (
                    <UpdateStatusModal
                        selected={this.state.selectedRows}
                        success={() => {
                            this.load();
                        }}
                        type={"procurement"}
                    />
                )}
                {this.state.buttons?.includes("set-virtual-tag") && (
                    <Button
                        onClick={() => {
                            if (this.state.selectedRows.length === 0) {
                                return message.warn("请勾选数据");
                            }
                            Modal.confirm({
                                title: "设置虚拟标记",
                                content: "确定对采购单设置虚拟标记吗？",
                                onOk: () => {
                                    lib.request({
                                        url: "/ccs/jdPurchaseOrderNew/setVirtualPurchase",
                                        data: {
                                            ids: this.state.selectedRows.map(item => item.id).join(","),
                                        },
                                        success: data => {
                                            message.success("设置成功");
                                            console.log("this:", this);
                                            this.load();
                                        },
                                    });
                                },
                            });
                        }}>
                        设置虚拟标记
                    </Button>
                )}
            </Space>
        );
    }
    configLoadDefaultParams = () => {
        console.log("configLoadDefaultParams");
        return {
            customsCode: this.state.customsCode,
        };
    };
    approveProcess = () => {
        if (this.state.selectedIdList.length === 0) return message.warn("需要勾选采购单");
        Modal.confirm({
            title: "审核通过",
            content: "是否确认采购单信息无误，审核通过",
            onOk: () => {
                // this.state.selectedIdList;
                lib.request({
                    url: "/ccs/jdPurchaseOrderNew/audit",
                    data: {
                        ids: this.state.selectedIdList.join(","),
                        opinion: "1",
                        customsCode: this.state.customsCode,
                    },
                    success: data => {
                        message.success("审核通过");
                        this.load();
                        this.getStatusCount();
                    },
                    fail(code, msg, data) {},
                });
            },
            onCancel: () => {},
        });
    };
    renderRightOperation() {
        return (
            <Space>
                {/* <ErrorList /> */}
                <Button
                    className="btn"
                    onClick={() => {
                        let { pagination, search, customsCode } = this.state;
                        lib.request({
                            url: "/ccs/jdPurchaseOrderNew/export",
                            needMask: true,
                            data: { ...pagination, ...search, customsCode: customsCode },
                            success: json => {
                                lib.openPage("/excel/download-center?page_title=下载中心");
                            },
                        });
                    }}>
                    导出 &#xe638;
                </Button>
            </Space>
        );
    }
    rejectProcess = () => {
        if (this.state.selectedIdList.length === 0) return message.warn("需要勾选采购单");
        this.setState({ imporeOpen: true });
    };

    pushNotice = () => {
        if (this.state.selectedIdList.length === 0) return message.warn("需要勾选采购单");
        Modal.confirm({
            title: "确定推送？",
            content: "确定推送采购单预申报完成吗？",
            onOk: () => {
                lib.request({
                    url: "/ccs/jdPurchaseOrderNew/pushMsg",
                    data: { ids: this.state.selectedIdList.join(","), type: "DR", customsCode: this.state.customsCode },
                    success: data => {
                        message.info("推送成功");
                        this.load();
                        this.getStatusCount();
                    },
                    fail(code, msg, data) {},
                });
            },
            onCancel: () => {},
        });
    };

    pushInNotice = () => {
        if (this.state.selectedIdList.length === 0) return message.warn("需要勾选采购单");
        Modal.confirm({
            title: "推送预申报完成",
            content: "确定推送入区通知吗？",
            onOk: () => {
                lib.request({
                    url: "/ccs/jdPurchaseOrderNew/pushMsg",
                    data: { ids: this.state.selectedIdList.join(","), type: "ET", customsCode: this.state.customsCode },
                    success: data => {
                        message.info("推送成功");
                        this.load();
                        this.getStatusCount();
                    },
                    fail(code, msg, data) {},
                });
            },
            onCancel: () => {},
        });
    };

    pushConsist = () => {
        if (this.state.selectedIdList.length === 0) return message.warn("需要勾选采购单");
        Modal.confirm({
            title: "确定推送",
            content: "确定推送确报放行吗",
            onOk: () => {
                lib.request({
                    url: "/ccs/jdPurchaseOrderNew/pushMsg",
                    data: { ids: this.state.selectedIdList.join(","), type: "RL", customsCode: this.state.customsCode },
                    success: data => {
                        message.info("推送成功");
                        this.load();
                        this.getStatusCount();
                    },
                    fail(code, msg, data) {},
                });
            },
            onCancel: () => {},
        });
    };

    renderPoNo(row: NewPurchaseManageDataItem) {
        console.log("row?.orderTagDescList:", row, row?.orderTagDescList);
        return (
            <span
                className="link"
                onClick={() => lib.openPage(`/purchase-detail?id=${row.id}&page_title=采购单详情&type=watch`)}>
                {row.poNo}
                {row?.orderTagDescList?.map(item => (
                    <p>
                        <Tag color="blue">{item}</Tag>
                    </p>
                ))}
            </span>
        );
    }

    renderLadNo(row: NewPurchaseManageDataItem) {
        return (
            <span
                className="link"
                onClick={() => lib.openPage(`/create-lad-bill?id=${row.ladId}&page_title=提单详情&type=watch`)}>
                {row.ladNo}
            </span>
        );
    }
    imporeOK = (values: { reason: string }) => {
        lib.request({
            url: "/ccs/jdPurchaseOrderNew/audit",
            data: {
                ...values,
                ids: this.state.selectedIdList.join(","),
                opinion: "0",
                customsCode: this.state.customsCode,
            },
            success: data => {
                message.success("驳回成功");
                this.setState({
                    imporeOpen: false,
                });
            },
            fail(code, msg, data) {},
        });
    };

    renderModal = () => {
        return (
            <>
                <NewModal
                    {...{
                        visible: this.state.imporeOpen,
                        configList: ImporeConfig,
                        title: "驳回原因",
                        onOk: this.imporeOK,
                        onCancel: () => {
                            this.setState({ imporeOpen: false });
                        },
                    }}
                />
            </>
        );
    };
}
