export type NewPurchaseManageState = {
    poStatus: TOrderStatus;
    poNo: string; // 采购单号多行
    ladNo: string; // 提单号多行
    warehouseNo: string; // 库房id
    entryMode: string; // 入区方式
    declareMode: string; // 报关方式
    apprMarkNo: string; // 核注清单号多行
    corrDocNo: string; // 报关单号多行
    isImprove: string; // 是否改单
    isReturn: string; // 是否退运
    createTimeBegin: string; // 创建时间
    createTimeEnd: string; // 创建时间
    finishTimeBegin: string; // 完成时间
    finishTimeEnd: string; // 完成时间
    actEntryTimeBegin: string; // 实际入区时间
    actEntryTimeEnd: string; // 实际入区时间
    errorMsg: string; // 错误信息
    imporeOpen: boolean; // 驳回开关
    pushConsistOpen: boolean; // 推送确报开关
    customsCode: string; // tabcode
    customs: any[]; //
} & NewPurchaseManageCounts;

export type NewPurchaseManageCounts = {
    [index in TOrderStatus]: number;
};

export interface NewPurchaseManageDataItem {
    orderTagDescList: any;
    ladId: number; // 提单id
    id: string; // 库房id
    poNo: string; // 采购单号
    errorMsg: string; // 错误信息
    ladNo: string; // 提单号
    createTime: string; // 创建时间
    warehouseName: string; // 入区方式
    entryModeDesc: string; // 报关方式
    declareModeDesc: string; // 核注清单号
    apprMarkNo: string; // 实际入区时间
    actEntryTime: string; // 报关单号
    corrDocNo: string; // 更新时间
    isImprove: string; // 是否改单
    updateTime: string; // 更新时间
    finishTime: string; // 完成时间
}

export interface NewPurchaseManageProps {}
// WAIT_AUDIT="waitAudit",                   // "待关务审核"
// AUDIT_REJECT="auditReject",               //"审核驳回"
// START_PRE_DECLARE="startPreDeclare",      //"开始清关预申报"
// WAIT_ENTER="waitEnter",                   //"待入区"
// WAIT_TALLY="waitTally",                   //"待理货"
// WAIT_DEFINITE="waitDefinite",             //"待确报"
// WAIT_IMPROVE="waitImprove",               //"待改单"
// WAIT_RETURN="waitReturn",                 //"待退运"
// DECLARE_FINISH="DeclareFinish",           //"清关完成"
// CANCELED="canceled",                      //"已取消"
export type TOrderStatus =
    | "waitAudit"
    | "auditReject"
    | "startPreDeclare"
    | "waitEnter"
    | "waitTally"
    | "waitDefinite"
    | "waitImprove"
    | "waitReturn"
    | "DeclareFinish"
    | "canceled"
    | "ALL";
export type TStepsItem = {
    status: TOrderStatus;
    count: number;
}[];
