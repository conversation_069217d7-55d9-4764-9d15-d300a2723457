import React, { useState, useEffect } from "react";
import { lib, SearchList, Uploader, getConfigDataUtils, HOC, event } from "react-single-app";
import axios from "axios";
import { Space, Button, message, Modal, Tabs, Input, Table, Form, Radio, DatePicker, Tooltip, Select } from "antd";
import moment from "moment";
// const {TextArea} = Input
import UpdateStatusModal from "../page/customs-clearance-system/components/update-status-modal";
const { TabPane } = Tabs;
const { RangePicker } = DatePicker;
const FormItem = Form.Item;
import "./record-manage.less";

//@ts-ignore
@HOC.mapAuthButtonsToState({ buttonCodeArr: [] })
class TabItem extends SearchList {
    constructor(props) {
        super(props);
        this.formRef = React.createRef();
        this.iptRef = React.createRef();

        this.state.isRow = {};
        this.state.serverList = [];
    }
    getConfig() {
        return axios.get(getConfigDataUtils.getDataUrlByDataId(237)).then(res => {
            console.log(res.data.data, "res.data.data");
            return res.data.data;
        });
    }

    componentDidMount() {
        this.getServersData(this.props.customsCode);
        this.changeImmutable({ customsCode: this.props.customsCode });
        event.on("onSearchReset", this.onSearchReset.bind(this));
    }
    renderMsg(row) {
        return (
            <Tooltip title={row.errorMsg}>
                <div style={{ width: "100%", overflow: "hidden", textOverflow: "ellipsis" }}>{row.errorMsg}</div>
            </Tooltip>
        );
    }

    load(_, toTop) {
        let { pagination, search, config, customsCode } = this.state;
        console.log(customsCode, "1222");
        var data = {};
        this.setState({ _loading: true });
        Object.assign(data, pagination, search, { customsCode: this.props.customsCode });
        // 审核数据
        lib.request({
            url: "/ccs/jdGoodsRecord/countApprove",
            data: { ...search, a: 1, customsCode: this.props.customsCode },
            needMask: true,
            success: res => {
                this.setState({
                    auditDetail: res,
                });
            },
        });
        // 分页查询
        lib.request({
            url: config.page.api,
            data: data,
            success: data => {
                console.log(data, "data");
                this.setState({
                    pagination: data.page,
                    dataList: data.dataList || [],
                    _loading: false,
                });
                toTop && (document.querySelector(".table-panel .ant-table-body").scrollTop = 0);
                setTimeout(this.resetSize, 100);
            },
            fail: () => {
                this.setState({
                    _loading: false,
                });
            },
        });
        this.getCount();
    }

    getServersData(code) {
        lib.request({
            url: "/ccs/jdServProvider/listServProviderNameByCustomsCode",
            data: {
                customsCode: code,
            },
            success: data => {
                this.setState({
                    serverList: data,
                });
            },
        });
    }
    //审核驳回
    renderLeftOperation() {
        let { auditDetail } = this.state;
        return (
            <Space>
                <Button type="primary" onClick={() => this.auditHandle(true)}>
                    审核通过
                </Button>
                <Button onClick={this.deleteHandle}>备案删除</Button>
                <Button onClick={() => this.setState({ grabVisible: true })}>抓取备案</Button>
                {this.state.buttons?.includes("UPDATE-RECORD-STATUS") && (
                    <UpdateStatusModal
                        selected={this.state.selectedRows}
                        success={() => {
                            this.load();
                        }}
                        type={"record"}
                    />
                )}
                <div>{auditDetail}</div>
            </Space>
        );
    }
    onSearchReset() {
        this.setState({ activeKey: "" });
    }
    getCount() {
        lib.request({
            url: "/ccs/jdGoodsRecord/countByStatus",
            data: {
                ...this.state.search,
                customsCode: this.props.customsCode,
            },
            success: data => {
                this.setState({
                    tabList: data,
                });
            },
        });
    }

    renderOperationTopView() {
        const { tabList } = this.state;
        return (
            <Tabs
                defaultActiveKey={""}
                activeKey={this.state.activeKey}
                onChange={e => {
                    if (e !== this.state.activeKey) {
                        this.setState({ activeKey: e });
                    }
                    this.changeImmutable({ status: e });
                }}>
                {(tabList
                    ? [
                          { id: "", name: "全部" },
                          ...tabList.map(item => {
                              return {
                                  id: item.status,
                                  name: `${item.statusDesc}(${item.count})`,
                              };
                          }),
                      ]
                    : [{ id: "", name: "全部" }]
                ).map(item => (
                    <Tabs.TabPane tab={item.name} key={item.id} />
                ))}
            </Tabs>
        );
    }
    deleteHandle = () => {
        let { selectedRows } = this.state;
        let idList = selectedRows.reduce((prev, curr) => [...prev, curr.id], []);
        if (!idList.length) {
            message.warning("请选择数据");
            return;
        }
        lib.request({
            url: "/ccs/jdGoodsRecord/delete",
            data: {
                idList,
            },
            needMask: true,
            success: res => {
                message.success("删除备案成功");
                this.load();
            },
        });
    };

    onUpload = ({ src, name }) => {
        lib.request({
            url: "/ccs/jdGoodsRecord/importExcelView",
            data: {
                url: src,
            },
            success: res => {
                this.setState({
                    importResult: res,
                    importModalVisible: true,
                    importUrl: src,
                });
            },
        });
    };

    renderRightOperation() {
        const { buttons } = this.state;
        return (
            <Space>
                <ErrorList customsCode={this.props.customsCode} customsName={this.props.customsName} />
                {buttons.includes("batch-update-record") && (
                    <Button
                        onClick={() => {
                            const url = `/excel/import-data?page_title=京东备案批量更新导入&api=${encodeURIComponent(
                                "/park-gate",
                            )}&code=${encodeURIComponent(
                                "IMPORT_JD_ZHIYING_NO_CALLBACK",
                            )}&importExtendParam=${encodeURIComponent("systemCode=CCS_ADMIN")}`;
                            lib.openPage(url, () => {
                                load();
                            });
                        }}>
                        批量更新备案
                    </Button>
                )}
                <Button
                    className="btn"
                    onClick={() => {
                        let { pagination, search } = this.state;
                        lib.request({
                            url: "/ccs/jdGoodsRecord/exportExcel",
                            needMask: true,
                            data: { ...pagination, ...search, customsCode: this.props.customsCode },
                            success: json => {
                                lib.openPage("/excel/download-center?page_title=下载中心");
                            },
                        });
                    }}>
                    导出 &#xe638;
                </Button>
                {buttons.includes("pop-import") && (
                    <Button
                        onClick={() => {
                            let url = `/excel/import-data?page_title=新POP导入&code=${encodeURIComponent(
                                "IMPORT_JD_NEW_POP_OFFLINE",
                            )}&importExtendParam=${encodeURIComponent("")}`;
                            lib.openPage(url, () => {
                                // fetchDetail({ activeKey: "1", id: lib.getParam("id") });
                                this.load();
                            });
                        }}>
                        新POP导入
                    </Button>
                )}
            </Space>
        );
    }
    exportFunc() {}
    importHandle = () => {
        let { importUrl } = this.state;
        lib.request({
            url: "/ccs/jdGoodsRecord/importExcel",
            data: {
                url: importUrl,
            },
            needMask: true,
            success: res => {
                message.success("导入成功");
                this.load();
            },
        });
    };

    grabHandle = () => {
        this.formRef.current.validateFields().then(({ type, time, servProviderId }) => {
            let [beginTime, endTime] = [Number(moment(time[0]).format("x")), Number(moment(time[1]).format("x"))];
            lib.request({
                url: "/ccs/jdGoodsRecord/manualPull",
                data: {
                    type,
                    beginTime,
                    endTime,
                    customsRegionCode: this.props.customsCode,
                    servProviderId: +servProviderId,
                },
                needMask: true,
                success: res => {
                    message.success("抓取备案成功");
                    this.load();
                    this.grabCancel();
                },
            });
        });
    };
    grabCancel = () => {
        this.setState({
            grabVisible: false,
        });
        this.formRef.current.resetFields();
    };

    renderModal() {
        const successColumns = [
            { title: "行号", dataIndex: "index", width: 100 },
            { title: "sku", dataIndex: "sku", width: 150 },
            { title: "条形码", dataIndex: "upc", width: 150 },
            { title: "商家ID", dataIndex: "shangJiaId", width: 150 },
        ];
        const failColumns = [
            { title: "行号", dataIndex: "index", width: 100 },
            { title: "sku", dataIndex: "sku", width: 200 },
            { title: "错误信息", dataIndex: "errorMsg", width: 200 },
        ];
        const formLayout = {
            labelCol: { span: 5 },
            wrapperCol: { span: 18 },
        };
        let { importModalVisible, importResult, grabVisible, isShow, isRow, serverList } = this.state;
        return (
            <React.Fragment>
                <Modal open={grabVisible} title="抓取备案" onOk={this.grabHandle} onCancel={this.grabCancel}>
                    <Form ref={this.formRef} {...formLayout}>
                        <FormItem label="类型" name="type" rules={[{ required: true, message: "请选择备案类型" }]}>
                            <Radio.Group>
                                <Radio value="DIRECT">京东直营</Radio>
                                <Radio value="POP">京东POP</Radio>
                                <Radio value="INDEPENDENT">京东独立站</Radio>
                                {/* <Radio value="POP_NEW">新京东POP</Radio> */}
                            </Radio.Group>
                        </FormItem>
                        <FormItem
                            label="服务商名称"
                            name="servProviderId"
                            rules={[{ required: true, message: "请选择备案类型" }]}>
                            <Select style={{ width: "200px" }}>
                                {serverList.map(item => {
                                    return (
                                        <Select.Option style={{ width: "100%" }} value={item.id} key={item.id}>
                                            {item.name}
                                        </Select.Option>
                                    );
                                })}
                            </Select>
                        </FormItem>
                        <FormItem
                            label="抓取时间"
                            name="time"
                            rules={[
                                { required: true, message: "请选择抓取时间" },
                                {
                                    validator(_, value) {
                                        if (value) {
                                            let [startValue, endValue] = [
                                                Number(moment(value[0]).format("x")),
                                                Number(moment(value[1]).format("x")),
                                            ];
                                            if (startValue > endValue) {
                                                return Promise.reject(new Error("结束时间应大于起始时间"));
                                            }
                                            if (endValue - startValue >= 30 * 60 * 1000) {
                                                return Promise.resolve();
                                            } else {
                                                return Promise.reject(new Error("最短间隔为半小时"));
                                            }
                                        }
                                        return Promise.reject();
                                    },
                                },
                            ]}>
                            <RangePicker showTime />
                        </FormItem>
                    </Form>
                </Modal>
                <Modal
                    open={importModalVisible}
                    title={`导入预览(总数${importResult?.success?.length + importResult?.fail?.length})`}
                    onOk={this.importHandle}
                    onCancel={() => this.setState({ importModalVisible: false, importResult: {} })}
                    width={700}>
                    <Tabs>
                        <TabPane tab={`校验成功(${importResult?.success?.length})`} key="0">
                            <Table dataSource={importResult?.success || []} columns={successColumns} />
                        </TabPane>
                        <TabPane tab={`校验失败(${importResult?.fail?.length})`} key="1">
                            <Table dataSource={importResult?.fail || []} columns={failColumns} />
                        </TabPane>
                    </Tabs>
                </Modal>
                <Modal
                    open={isShow}
                    title="驳回原因"
                    onOk={this.getConfime(isRow)}
                    onCancel={this.getIsShow}
                    destroyOnClose>
                    <Form ref={this.formRef}>
                        {isRow.flowStateLabel == "待审核" ? (
                            <FormItem label="理由" name="type">
                                <Input ref={this.iptRef} />
                            </FormItem>
                        ) : (
                            <FormItem
                                label="驳回原因"
                                name="type"
                                rules={[{ required: true, message: "请填写驳回原因" }]}>
                                <Input ref={this.iptRef} />
                                {/* <TextArea ref={this.iptRef} maxLength="300" /> */}
                            </FormItem>
                        )}
                    </Form>
                </Modal>
            </React.Fragment>
        );
    }

    // 审核
    auditHandle = approveFlag => {
        let { selectedRows } = this.state;
        if (selectedRows?.length === 0) {
            message.warning("请选择数据");
            return;
        }
        let modal = Modal.confirm({
            title: "提示",
            content: "确认审核通过吗?",
            onOk: () => {
                lib.request({
                    url: "/ccs/jdGoodsRecord/approveCustom",
                    data: {
                        approveFlag,
                        ids: selectedRows.reduce((prev, curr) => [...prev, curr.id], []),
                    },
                    needMask: true,
                    success: res => {
                        message.success(`批量审核通过`);
                        this.load();
                        modal.destroy();
                    },
                });
            },
            onCancel: () => modal.destroy(),
        });
    };
    goModel(row) {
        lib.request({
            url: "/ccs/jdGoodsRecord/rejectCheck",
            data: {
                id: row.id,
            },
            success: () => {
                this.setState({
                    isShow: true,
                    isRow: row,
                });
            },
        });
    }
    getIsShow = () => {
        this.setState({
            isShow: false,
        });
    };
    // 确定按钮
    getConfime = isRow => {
        return () => {
            const values = this.formRef.current.getFieldValue("type");

            if (isRow.flowStateLabel == "待审核") {
                lib.request({
                    url: "/ccs/jdGoodsRecord/approveCustom",
                    data: {
                        approveFlag: false,
                        // ids: [lib.getParam("id")],
                        ids: [isRow.id],

                        backReason: values,
                    },
                    needMask: true,
                    success: res => {
                        message.success("审核驳回成功");
                        this.load();
                        this.setState({
                            isShow: false,
                        });
                    },
                });
            } else if (isRow.flowStateLabel == "待海关审核") {
                lib.request({
                    url: "/ccs/jdGoodsRecord/approve",
                    data: {
                        approveFlag: false,
                        id: isRow.id,
                        backReason: values,
                    },
                    needMask: true,
                    success: res => {
                        message.success("海关审核驳回成功");
                        this.load();
                        this.setState({
                            isShow: false,
                        });
                    },
                });
            }
        };
    };
    myOperation(row) {
        return (
            <Space>
                <span
                    className="link"
                    onClick={() => lib.openPage(`/record-detail?id=${row.id}&page_title=备案详情`, () => this.load())}>
                    查看
                </span>
                <span
                    className="link"
                    onClick={() => lib.openPage(`/record-refresh?id=${row.id}&page_title=更新备案`, () => this.load())}>
                    更新备案
                </span>
                {row.flowStateLabel == "待审核" || row.flowStateLabel == "待海关审核" ? (
                    <span className="link" onClick={() => this.goModel(row)}>
                        驳回
                    </span>
                ) : (
                    ""
                )}
            </Space>
        );
    }
}

function ErrorList({ customsCode, customsName }) {
    let [visible, setVisible] = useState(false);
    let [result, setResult] = useState([]);
    function fetchList() {
        lib.request({
            url: "/ccs/jdGoodsRecord/jdExceptionInfo",
            needMask: true,
            data: {
                customsCode,
            },
            success: res => {
                setResult(res);
            },
        });
    }
    useEffect(() => {
        visible && fetchList();
    }, [visible]);
    const columns = [
        {
            title: "京东SKU",
            dataIndex: "skuId",
        },
        {
            title: "条形码",
            dataIndex: "upc",
        },
        {
            title: "商家ID",
            dataIndex: "merchantId",
        },
        {
            title: "错误信息",
            dataIndex: "wrongReason",
        },
        {
            title: "操作",
            render: (_, record) => (
                <span
                    className="link"
                    style={{ color: "#1890ff", textDecoration: "none", cursor: "pointer" }}
                    onClick={() => releaseFunc(record)}>
                    释放
                </span>
            ),
        },
    ];
    function releaseFunc({ skuId, upc, type }) {
        let modal = Modal.confirm({
            title: "提示",
            content: "确定释放该备案信息吗？",
            okText: "确定",
            cancelText: "取消",
            onOk: () => {
                lib.request({
                    url: "/ccs/jdGoodsRecord/manualDelJdException",
                    data: {
                        skuId,
                        upc,
                        type,
                        customsCode: customsCode,
                        // /jdGoodsRecord/jdExceptionInfo
                    },
                    needMask: true,
                    success: res => {
                        message.success("释放成功");
                        fetchList();
                        modal.destroy();
                    },
                });
            },
            onCancel: () => modal.destroy(),
        });
    }
    function okHandle() {
        setVisible(false);
    }
    return (
        <>
            <Modal
                open={visible}
                title="释放拦截备案"
                footer={<Button onClick={okHandle}>确定</Button>}
                onCancel={okHandle}
                width={1200}
                destroyOnClose>
                {result.length ? (
                    <Tabs>
                        <Tabs.TabPane tab={`${customsName}(${result.length})`}>
                            <Table columns={columns} dataSource={result} scroll={{ x: "100%", y: "400px" }} />
                        </Tabs.TabPane>
                    </Tabs>
                ) : (
                    "当前无拦截备案"
                )}
            </Modal>
            <Button onClick={() => setVisible(true)}>释放拦截备案</Button>
        </>
    );
}

export default class page extends React.Component {
    constructor(props) {
        super(props);
        this.state = {
            customs: [],
            customsCode: "",
        };
    }
    componentDidMount() {
        this.getcustomsCode();
    }
    getcustomsCode() {
        lib.request({
            url: "/ccs/jdServProvider/listAuthCustoms",
            success: res => {
                const data = Object.assign({}, this.state.search, { customsCode: res[0].id });
                this.setState({
                    customs: res,
                    customsCode: res[0].id,
                });
                // this.getServersData(res[0].id);
            },
        });
    }

    render() {
        return (
            <div style={{ width: "100%", height: "100%" }}>
                <Tabs defaultActiveKey={""} style={{ width: "100%", height: "100%" }}>
                    {this.state.customs &&
                        this.state.customs.map(item => {
                            return (
                                <TabPane tab={`${item.name}`} key={`${item.id}`} style={{ height: "100%" }}>
                                    <TabItem customsCode={item.id} customsName={item.name} />
                                </TabPane>
                            );
                        })}
                </Tabs>
            </div>
        );
    }
}
