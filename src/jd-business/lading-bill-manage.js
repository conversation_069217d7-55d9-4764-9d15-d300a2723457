import React from "react";
import { lib, SearchList, getConfigDataUtils } from "react-single-app";
import axios from "axios";
import { Space, Modal, message, Button } from "antd";

export default class extends SearchList {
    getConfig() {
        return axios.get(getConfigDataUtils.getDataUrlByDataId(239)).then(res => res.data.data);
    }

    renderRightOperation() {
        return (
            <Space>
                <Button
                    type="primary"
                    onClick={() =>
                        lib.openPage("/add-lading-bill?page_title=新建提单&type=edit&t=" + +new Date(), () =>
                            this.load(),
                        )
                    }>
                    新建
                </Button>
            </Space>
        );
    }

    myOperation(row) {
        return (
            <Space>
                <span
                    className="link"
                    onClick={() => lib.openPage(`/lading-bill-detail?page_title=提单详情&type=watch&id=${row.id}`)}>
                    查看
                </span>
                <span className="link" onClick={() => this.deleteHandle(row)}>
                    删除
                </span>
            </Space>
        );
    }

    deleteHandle = ({ id }) => {
        let modal = Modal.confirm({
            title: "提示",
            content: "确定删除该提单吗？",
            onOk: () => {
                lib.request({
                    url: "/ccs/jdLad/delete",
                    data: { id },
                    needMask: true,
                    success: res => {
                        message.success("删除提单成功");
                        this.load();
                        modal.destroy();
                    },
                });
            },
            onCancel: () => modal.destroy(),
        });
    };
}
