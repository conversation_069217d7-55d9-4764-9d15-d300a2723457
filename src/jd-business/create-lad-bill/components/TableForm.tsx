import React, { useState, useEffect, useImper<PERSON><PERSON><PERSON><PERSON> } from "react";
import { Row, Col, Button } from "antd";
import { LadBillInfoTableHeader } from "../config";
import "../index.less";
import { ladDetail, TableForm } from "../type";
import TableItemForm from "./TableItemForm";
import { lib } from "react-single-app";
import { PlusCircleOutlined } from "@ant-design/icons";
export default React.forwardRef((props: TableForm, ref) => {
    const [list, setList] = useState<ladDetail[]>(
        props.ladDetails || [
            {
                cabinetFlag: "", //  整箱拼箱标识
                cabinetType: "", //  柜型
                actualWeight: null, //  实际重量
                total: null, //  柜数
            },
        ],
    );

    const [selectData, setSelectData] = useState<{ index: number; data: any }[]>([]);

    const initConfig = () => {
        const arr: Promise<any>[] = [];
        LadBillInfoTableHeader.map((item, index) => {
            if (item.from) {
                const promise = new Promise((resolve, reject) => {
                    lib.request({
                        url: item.from,
                        data: {},
                        success(data) {
                            resolve({ index, data });
                        },
                        fail(code, msg, data) {
                            reject();
                        },
                    });
                });
                arr.push(promise);
            }
        });
        Promise.all(arr).then(res => {
            setSelectData(res);
        });
    };

    const addList = () => {
        list.push({
            cabinetFlag: "", //  整箱拼箱标识
            // cabinetFlagStr?: null,  //  整箱拼箱标识展示字段
            cabinetType: "", //  柜型
            actualWeight: null, //  实际重量
            // actualWeightStr?: string; //  实际重量展示字段
            total: null, //  柜数
        });
        setList([...list]);
    };

    const delList = (index: number) => {
        list.splice(index, 1);
        setList([...list]);
    };

    useEffect(() => {
        initConfig();
    }, []);

    useEffect(() => {
        if (props.ladDetails) {
            setList(props.ladDetails);
        }
    }, [props.ladDetails]);

    const validateFields = () => {
        //@ts-ignore;
        return Promise.all(list.map(item => item.itemRef?.form.validateFields()));
    };

    useImperativeHandle(ref, () => {
        return {
            validateFields: validateFields,
        };
    });

    return (
        <>
            <Row className="table-header" align="middle">
                {LadBillInfoTableHeader.map((item, index) => {
                    if (!props.showOperation && props.disableEdit && index === LadBillInfoTableHeader.length - 1)
                        return null;
                    return (
                        <Col span={item.labelCount} key={index}>
                            {item.required && <span style={{ color: "red" }}>*</span>}
                            {item.name}
                        </Col>
                    );
                })}
            </Row>
            {list.map((item, index) => {
                return (
                    <TableItemForm
                        key={index}
                        selectData={selectData}
                        detail={item}
                        delList={delList}
                        transType={props.transType}
                        index={index}
                        disableEdit={props.disableEdit}
                        showOperation={props.showOperation}
                        ref={ref => {
                            //@ts-ignore;
                            item.itemRef = ref;
                        }}
                    />
                );
            })}
            {!props.disableEdit && (
                <div className="add-container">
                    <Button
                        type="link"
                        icon={<PlusCircleOutlined />}
                        onClick={() => {
                            addList();
                        }}>
                        增加
                    </Button>
                </div>
            )}
        </>
    );
});
