import React, { useEffect, useState, useImper<PERSON><PERSON><PERSON><PERSON>, useMemo, useRef } from "react";
import { Form, Row, Col, Input, InputNumber, Select, Button, message } from "antd";
import { ladDetail } from "../type";
import { LadBillInfoTableHeader } from "../config";
const { Option } = Select;

interface TableItemFormProps {
    detail: ladDetail;
    selectData: any[];
    disableEdit?: boolean;
    delList: (index: number) => void;
    index: number;
    transType?: number;
    showOperation?: boolean;
}

export default React.forwardRef((props: TableItemFormProps, ref) => {
    const [form] = Form.useForm();
    const transType = useRef<number | undefined>();

    let setCabinetTypeRequire = useMemo(() => {
        return () => {
            const vals = form.getFieldsValue();
            if (transType.current === 1 && vals.cabinetFlag === 1) {
                //海运 & 整箱
                configs[1].required = true;
                configs[2].required = true;
            } else {
                configs[1].required = false;
                configs[2].required = false;
            }
            if (transType.current === 3 && vals.cabinetFlag === 2) {
                configs[3].required = true;
            } else {
                configs[3].required = false;
            }
            // console.log(configs)
            setConfigs([...configs]);
        };
    }, []);
    const initTableConfig = (fn: () => void, arr: any[]) => {
        const results = arr.map(item => {
            if (item.type !== "SELECT") return { ...item };
            return {
                ...item,
                onSelect: fn,
            };
        });
        return results;
    };
    const [configs, setConfigs] = useState<any[]>(initTableConfig(setCabinetTypeRequire, LadBillInfoTableHeader));

    useEffect(() => {
        if (!!props.detail.id) {
            console.log(props.detail);
            form.setFieldsValue(props.detail);
        }
    }, [props.detail]);

    useEffect(() => {
        props.selectData.forEach((item, index) => {
            configs[item.index].list = item.data;
        });
        setConfigs([...configs]);
    }, [props.selectData]);

    useEffect(() => {
        if (props.transType) {
            transType.current = props.transType;
            form.resetFields();
            setCabinetTypeRequire();
        }
    }, [props.transType]);

    const switchType = (item: any, disableEdit: boolean) => {
        switch (item.type) {
            case "INPUT":
                return item.isPlaceHolder ? (
                    <Input
                        disabled={disableEdit || item.disabled}
                        maxLength={item.maxLength || 9999}
                        autoComplete={item.autoComplete || null}
                    />
                ) : (
                    <Input
                        disabled={item.disabled}
                        placeholder={item.placeholder || `请输入${item.name}`}
                        maxLength={item.maxLength || 9999}
                        autoComplete={item.autoComplete || null}
                    />
                );
            case "INPUTNUMBER":
                return (
                    <InputNumber
                        disabled={disableEdit || item.disabled}
                        placeholder={item.placeholder || `请输入${item.name}`}
                        max={item.max}
                        min={item.min}
                    />
                );
            case "SELECT":
                return (
                    <Select
                        onChange={item.onChange ? e => item.onChange(e, item.label) : e => {}}
                        allowClear={item.allowClear}
                        mode={item.mode}
                        style={{ width: "100%" }}
                        showSearch
                        onSelect={
                            item.onSelect ? (e: string | number) => item.onSelect(e, form) : (e: string | number) => {}
                        }
                        disabled={disableEdit || item.disabled}
                        optionFilterProp="children">
                        {item.list.length &&
                            item.list.map((ite: any, index: number) => {
                                return (
                                    <Option value={ite.value || ite.id} key={index}>
                                        {ite.name}
                                    </Option>
                                );
                            })}
                    </Select>
                );
            default:
                return;
        }
    };

    useImperativeHandle(ref, () => {
        return {
            form: form,
        };
    });

    return (
        <>
            <Form form={form}>
                <Row align="middle">
                    {configs.map((item, index) => {
                        if (!props.showOperation && props.disableEdit && index === configs.length - 1) return null;
                        return (
                            <Col
                                span={item.labelCount}
                                style={{ display: "flex", alignItems: "center", borderBottom: "1px solid #f5f5f5" }}
                                key={index}>
                                {item.label ? (
                                    <Form.Item
                                        name={item.label}
                                        style={{ width: "80%" }}
                                        rules={
                                            item.required
                                                ? [
                                                      {
                                                          required: true,
                                                          message: `请输入${item.name}`,
                                                      },
                                                  ]
                                                : undefined
                                        }>
                                        {switchType(item, !!props.disableEdit)}
                                    </Form.Item>
                                ) : (
                                    <Button
                                        type="link"
                                        style={{ marginBottom: "24px" }}
                                        onClick={() => {
                                            props.delList(props.index);
                                        }}>
                                        移除
                                    </Button>
                                )}
                            </Col>
                        );
                    })}
                </Row>
            </Form>
        </>
    );
});
