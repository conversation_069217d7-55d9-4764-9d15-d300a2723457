export interface CreateLadBill {}

export interface LadBillDetail {
    allowEdit: any; // 是否允许编辑（关联的采购单有清关完成的时候不可编辑）
    id?: number; // id
    ladNo: string; //  提单编号
    provider: string; //  服务商
    intryWhere: string; //  入关地点
    intryWhereStr?: string; //  入关地点描述
    intryType: string; //  入关方式
    intryTypeStr?: string; //  入关方式描述
    transType: string; //  运输方式
    transTypeStr?: string; //  运输方式描述
    billWeight: string; //  计费重量
    billWeightStr?: string; //  计费重量描述
    interTrans: string; //  承运方承运
    interTransStr?: string; //  承运方承运描述
    poNos: string; //  采购单号组成的集合
    ladDetails: ladDetail[]; //  提单拼接详情集合
}

export interface ladDetail {
    itemRef?: React.Ref<any>;
    id?: number;
    cabinetFlag: string; //  整箱拼箱标识
    cabinetFlagStr?: string; //  整箱拼箱标识展示字段
    cabinetType: string; //  柜型
    actualWeight: number | null; //  实际重量
    actualWeightStr?: string; //  实际重量展示字段
    total: number | null; //  柜数
    totalStr?: string; //  柜数展示字段
}

export interface TableForm {
    disableEdit: boolean | undefined;
    ladDetails?: ladDetail[];
    transType?: number;
    disabledEdit?: boolean;
    showOperation?: boolean;
}
