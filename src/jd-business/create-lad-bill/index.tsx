import React, { useState, useEffect, useRef } from "react";
import FormList from "../purchase-detail/components/FormList";
import { Form, Button } from "antd";
import { lib } from "react-single-app";

import { LadBillInfoConfig } from "./config";
import "./index.less";
import TableForm from "./components/TableForm";
import { LadBillDetail } from "./type";
import { message } from "antd";

export default () => {
    const [configList, setConfigList] = useState(LadBillInfoConfig);
    const [transType, setTransType] = useState<number>();
    const [detail, setDetail] = useState<LadBillDetail>({} as LadBillDetail);
    const [edit, setEdit] = useState<boolean>(false);
    let TableFormRef = useRef<{ validateFields: () => Promise<any> }>(null);

    const [form] = Form.useForm();

    const setRequire = (val: number) => {
        setTransType(val);
        console.log(transType, val);
        lib.request({
            url: "/ccs/dictionary/listIntryWhere",
            data: { transType: val },
            success: data => {
                configList[3].list = data;
                setConfigList([...configList]);
            },
        });
        if (val === 3) {
            configList[5].required = true;
        } else {
            configList[5].required = false;
        }
        setConfigList([...configList]);
    };

    const initSelectData = () => {
        const arr: Promise<any>[] = [];
        configList.map((item, index) => {
            if (item.from) {
                const promise = new Promise((resolve, reject) => {
                    lib.request({
                        url: item.from,
                        data: {},
                        success(data) {
                            resolve({ index, data });
                        },
                        fail(code, msg, data) {
                            reject();
                        },
                    });
                });
                arr.push(promise);
            }
        });
        Promise.all(arr).then(res => {
            res.forEach((obj, index) => {
                configList[obj.index].list = obj.data;
            });
            setConfigList([...configList]);
        });
    };

    useEffect(() => {
        initSelectData();
        setTimeout(() => {
            console.log(form.getFieldsValue());
        }, 5000);
    }, []);

    useEffect(() => {
        lib.getParam("id") && getDetail();
    }, []);

    const validate = () => {
        if (!TableFormRef.current) return;
        Promise.all([form.validateFields(), TableFormRef.current.validateFields()])
            .then(res => {
                let params = { ...res[0], ladDetail: res[1] };

                if (lib.getParam("id")) {
                    params.id = lib.getParam("id");
                }
                submitLadOrder(params);
            })
            .catch(err => {});
    };

    const submitLadOrder = (params: LadBillDetail) => {
        lib.request({
            url: params.id ? "/ccs/jdLadNew/update" : "/ccs/jdLadNew/create",
            data: params,
            success(data) {
                message.success(params.id ? "编辑成功" : "创建成功");
                !params.id && lib.closePage();
            },
            fail(code, msg, data) {},
        });
    };

    const getDetail = () => {
        lib.request({
            url: "/ccs/jdLadNew/detail",
            data: { id: +lib.getParam("id") },
            success(data) {
                setDetail(data);
                form.setFieldsValue(data);
            },
            fail(code, msg, data) {},
        });
    };

    // 判断是创建 || 详情或者编辑
    const disableEdit = !!lib.getParam("id");

    return (
        <div className="create-lad-bill">
            <Form
                form={form}
                onValuesChange={vals => {
                    if ("transType" in vals) {
                        setRequire(vals["transType"]);
                    }
                }}>
                <div className="form-header">
                    <h2>提单信息</h2>
                    {("allowEdit" in detail ? detail.allowEdit : true) && !edit && disableEdit && (
                        <Button
                            onClick={() => {
                                setEdit(true);
                            }}>
                            编辑
                        </Button>
                    )}
                </div>

                <FormList
                    disableEdit={disableEdit && !edit}
                    form={form}
                    configList={configList}
                    formItemFloat={false}
                    detail={undefined}
                />
            </Form>

            {disableEdit && !edit && <div className="lad-bill-more-info">关联采购单号: {detail?.poNos}</div>}

            <TableForm
                // @ts-ignore
                ref={ref => (TableFormRef.current = ref)}
                transType={transType}
                disableEdit={disableEdit && !edit}
                showOperation={!disableEdit}
                ladDetails={detail.ladDetails}
            />
            {(!disableEdit || edit) && (
                <div className="page-btn">
                    <Button
                        onClick={() => {
                            lib.closePage();
                        }}>
                        取消
                    </Button>
                    <Button type="primary" onClick={validate} style={{ marginLeft: "15px" }}>
                        提交
                    </Button>
                </div>
            )}
        </div>
    );
};
