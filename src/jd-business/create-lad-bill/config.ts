export const LadBillInfoConfig = [
    {
        labelName: "提单号",
        labelKey: "ladNo",
        type: "INPUT",
        required: true,
    },
    // {
    //     labelName: "库房名称",
    //     labelKey: "warehouseName",
    //     type: "SELECT",
    //     from: "/ccs/dictionary/listJdWarehouseName",
    //     list: [],
    //     required: true,
    // },
    {
        labelName: "服务商",
        labelKey: "provider",
        type: "SELECT",
        from: "/ccs/jdLadNew/providers",
        list: [],
        required: true,
    },
    {
        labelName: "运输方式",
        labelKey: "transType",
        type: "SELECT",
        from: "/ccs/jdLadNew/transTypes",
        required: true,
    },
    {
        labelName: "入关地点",
        labelKey: "intryWhere",
        type: "SELECT",
        from: "/ccs/dictionary/listIntryWhere",
        list: [],
        required: true,
    },
    {
        labelName: "入关方式",
        labelKey: "intryType",
        type: "SELECT",
        from: "/ccs/jdLadNew/intryTypes",
        list: [],
        required: true,
    },

    {
        labelName: "计费重量（kg）",
        labelKey: "billWeight",
        type: "INPUT",
        required: false,
    },
    {
        labelName: "承运方式",
        labelKey: "interTrans",
        type: "SELECT",
        from: "/ccs/jdLadNew/interTrans",
        list: [],
        required: true,
    },
];

export const LadBillInfoTableHeader = [
    {
        name: "拼接标识",
        required: true,
        list: [],
        from: "/ccs/jdLadNew/cabinetFlags",
        label: "cabinetFlag",
        type: "SELECT",
        labelCount: 4,
    },
    {
        name: "柜型",
        required: false,
        list: [],
        from: "/ccs/jdLadNew/cabinetTypes",
        label: "cabinetType",
        type: "SELECT",
        labelCount: 4,
    },
    {
        name: "柜数",
        required: false,
        list: [],
        label: "total",
        type: "INPUTNUMBER",
        labelCount: 4,
        max: 10,
        min: 1,
    },
    {
        name: "实际重量（kg）",
        required: false,
        list: [],
        label: "actualWeight",
        type: "INPUTNUMBER",
        labelCount: 4,
        max: 1000000,
        min: 1,
    },
    {
        name: "操作",
        labelCount: 4,
    },
];
