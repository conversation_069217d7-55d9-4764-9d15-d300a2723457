import React, { useState, useEffect } from "react";
import { lib } from "react-single-app";
import { Space, Button, message, Image, Steps, Modal, Form, Input, Divider, Typography, Table, Select } from "antd";
import DetailList from "../components/DetailList";
import EditModal from "../components/EditModal";
import moment from "moment";
import "./add-procurement.less";
const { Step } = Steps;
const { Title } = Typography;
// 理货报告信息配置列表
const BaseList = [
    { label: "采购单号", labelKey: "poNo" },
    { label: "提单号", labelKey: "ladNo" },
    { label: "库房名称", labelKey: "warehouseName" },
    { label: "货主名称", labelKey: "ownerName" },
    { label: "货主编码", labelKey: "ownerNo" },
];
const CustomsList = [
    { label: "报关单号", labelKey: "corrDocNo" },
    { label: "报关时间", labelKey: "corrDocTimeStr" },
    { label: "报检单号", labelKey: "declNo" },
    { label: "报检时间", labelKey: "declTimeStr" },
];

// 审核状态
function AuditStatus({ detail, refreshDetail }) {
    const props = {
        title: "完善报关报检信息",
        configList: [
            { labelName: "报关单号", labelKey: "corrDocNo", required: true, type: "INPUT" },
            { labelName: "报关时间", labelKey: "corrDocTime", required: true, type: "DATE" },
            { labelName: "报检单号", labelKey: "declNo", type: "INPUT" },
            { labelName: "报检时间", labelKey: "declTime", type: "DATE" },
        ],
        editRow: detail,
        btnProps: {
            text: "推送报关报检信息",
            type: "BUTTON",
        },
        onOk,
    };
    function onOk(values) {
        let data = Object.assign({}, values);
        data.corrDocTime = moment(data.corrDocTime).format("YYYY-MM-DD HH:mm:ss");
        data.declTime = data.declTime ? moment(data.declTime).format("YYYY-MM-DD HH:mm:ss") : null;
        lib.request({
            url: "/ccs/jdPurchaseOrder/pullCustom",
            data: {
                ...data,
                id: detail.id,
            },
            needMask: true,
            success: res => {
                message.success("推送成功");
                refreshDetail();
            },
        });
    }
    let status = "process";
    if (detail?.nodeList?.every(item => item.active)) {
        status = detail.nodeList[detail.nodeList.length - 1].failFlag ? "error" : "finish";
    }
    return (
        <div className="audit-status">
            <Steps current={detail?.nodeList?.filter(item => item.active).length - 1} status={status}>
                {detail?.nodeList?.map((item, index) => (
                    <Step title={item.text} key={index} description={item.showText} />
                ))}
            </Steps>
            {detail?.showPushBtn && (
                <Space>
                    <EditModal {...props} />
                </Space>
            )}
        </div>
    );
}

// 商品明细
function GoodsTable({ detail }) {
    const columns = [
        {
            title: "商品名称",
            dataIndex: "goodsName",
            width: 200,
        },
        {
            title: "商品货号",
            dataIndex: "sellerRecord",
            width: 160,
        },
        {
            title: "主商品编码",
            dataIndex: "emg",
            width: 140,
        },
        {
            title: "京东SKU",
            dataIndex: "sku",
            width: 160,
        },
        {
            title: "数量",
            dataIndex: "qtyStr",
            width: 120,
        },
    ];
    return (
        <div className="goods-table">
            <Table dataSource={detail?.poGoodsList} pagination={false} columns={columns} rowKey="id" />
        </div>
    );
}

// 理货详情
function TallyItem({ detail }) {
    const columns = [
        {
            title: "商品名称",
            dataIndex: "goodsName",
            width: 200,
        },
        {
            title: "京东SKU",
            dataIndex: "sku",
            width: 160,
        },
        {
            title: "货号",
            dataIndex: "sellerRecord",
            width: 160,
        },
        {
            title: "主商品编码",
            dataIndex: "emg",
            width: 140,
        },
        {
            title: "采购数量",
            dataIndex: "qtyStr",
            width: 120,
        },
        {
            title: "理货数量",
            dataIndex: "expectedQtyStr",
            width: 120,
        },
    ];
    return (
        <React.Fragment>
            <DetailList {...{ configList: BaseList, detail }} />
            <Table dataSource={detail?.poGoodsList} columns={columns} pagination={false} rowKey="id" />
        </React.Fragment>
    );
}

// 详情模块
function DetailItem({ title, children, last }) {
    return (
        <div className="detail-item">
            <Title level={5}>{title}</Title>
            {children}
            {!last && <Divider />}
        </div>
    );
}

export default function () {
    const [detail, setDetail] = useState({});
    function fetchDetail() {
        return new Promise((success, fail) => {
            lib.request({
                url: "/ccs/jdPurchaseOrder/detail",
                data: {
                    id: lib.getParam("id"),
                },
                needMask: true,
                success,
                fail,
            });
        });
    }
    function refreshDetail() {
        fetchDetail().then(res => setDetail(res));
    }
    useEffect(() => {
        refreshDetail();
    }, []);

    let ComList = [
        { title: "", children: <AuditStatus {...{ detail, refreshDetail }} /> },
        { title: "商品明细信息", children: <GoodsTable {...{ detail }} /> },
        { title: "理货报告", children: <TallyItem {...{ detail }} /> },
        { title: "报关报检信息", children: <DetailList {...{ configList: CustomsList, detail }} />, last: true },
    ];
    if (detail?.poStatus === "WAIT_INSPECTION") {
        ComList.splice(3, 1);
        ComList[2].last = true;
    }

    return (
        <div className="record-detail">
            {ComList.map((item, index) => (
                <DetailItem {...item} key={index} />
            ))}
        </div>
    );
}
