import React, { useState, useEffect } from "react";
import { lib, Uploader, event } from "react-single-app";
import {
    Space,
    Button,
    message,
    Image,
    Steps,
    Modal,
    Form,
    Input,
    Divider,
    Typography,
    Table,
    Select,
    InputNumber,
    Tabs,
} from "antd";
import DetailList from "../components/DetailList";
import FormList from "../components/FormList";

import "./add-procurement.less";
import AppendModal from "../components/AppendModal";

const { Step } = Steps;
const FormItem = Form.Item;
const { TextArea } = Input;
const { Title } = Typography;
const { Option } = Select;
const initialList = [
    { labelName: "采购单号", labelKey: "poNo", type: "INPUT", required: true },
    {
        labelName: "提单号",
        labelKey: "ladNo",
        type: "SELECT",
        xhr: "/ccs/jdLad/paging",
        data: { status: "WAIT_PUSH", pageIgnore: 1 },
        list: [],
        required: true,
    },
    {
        labelName: "货主",
        labelKey: "ownerNo",
        type: "SELECT",
        required: true,
        list: [
            { id: "JD_DIRECT", name: "京东直营" },
            { id: "JD_POP", name: "京东pop" },
        ],
    },
    {
        labelName: "收货仓库",
        labelKey: "warehouseNo",
        type: "SELECT",
        required: true,
        list: [
            { id: "JYCSXTC", name: "金义宠食协同仓" },
            { id: "JYPTXTC", name: "金义平台协同仓" },
        ],
    },
];

// 采购单信息
function FormCom({ configList, refreshLadNo }) {
    configList[1].help = (
        <span>
            尚未创建提单？
            <span
                className="link"
                onClick={() => lib.openPage("/add-lading-bill?page_title=新建提单号&type=edit", () => refreshLadNo())}>
                去创建
            </span>
        </span>
    );
    return (
        <div className="detail-item">
            <Title level={5}>采购单信息</Title>
            <FormList {...{ configList }}></FormList>
        </div>
    );
}

// 商品明细
function GoodsTable({ form }) {
    const [dataSource, setDataSource] = useState([]);
    const [visible, setVisible] = useState(false);
    const modalColumns = [
        {
            title: "商品名称",
            dataIndex: "goodsName",
        },
        {
            title: "商品货号",
            dataIndex: "itemCode",
        },
        {
            title: "京东SKU",
            dataIndex: "skuId",
        },
    ];
    const columns = [
        {
            title: "商品名称",
            dataIndex: "goodsName",
            width: 200,
        },
        {
            title: "商品货号",
            dataIndex: "itemCode",
            width: 160,
        },
        {
            title: <div className="required-div">主商品编码</div>,
            dataIndex: "emg",
            width: 140,
            render: (text, record, index) => {
                return (
                    <Input
                        maxLength={40}
                        value={dataSource[index].emg}
                        onChange={({ target: { value } }) => {
                            setDataSource(dataSource => {
                                dataSource[index].emg = value;
                                dataSource[index].skuId = value;
                                return dataSource.slice();
                            });
                        }}
                    />
                );
            },
        },
        {
            title: "商品SKU",
            dataIndex: "skuId",
            width: 160,
            render: text => {
                return <Input value={text} disabled />;
            },
        },
        {
            title: "数量",
            dataIndex: "total",
            width: 120,
            render: (text, record, index) => {
                return (
                    <InputNumber
                        max="1000000"
                        min={1}
                        value={dataSource[index].total}
                        onChange={value => {
                            setDataSource(dataSource => {
                                dataSource[index].total = value;
                                return dataSource.slice();
                            });
                        }}
                    />
                );
            },
        },
        {
            title: "操作",
            dataIndex: "goodsName",
            width: 100,
            render: (text, record, index) => {
                return (
                    <Space>
                        <span
                            className="link"
                            onClick={() =>
                                setDataSource(dataSource => {
                                    dataSource.splice(index, 1);
                                    form.setFieldsValue({ poGoods: dataSource });
                                    return dataSource.slice();
                                })
                            }>
                            删除
                        </span>
                    </Space>
                );
            },
        },
    ];
    const props = {
        title: "添加商品",
        visible,
        searchList: [
            {
                labelName: "商品货号",
                labelKey: "goodsCode",
                type: "INPUT",
            },
            {
                labelName: "京东SKU",
                type: "INPUT",
                labelKey: "sku",
            },
        ],
        props: {
            status: "RECORD_SUCCESS",
        },
        currRows: dataSource,
        url: "/ccs/jdGoodsRecord/paging",
        columns: modalColumns,
        rowKey: "id",
        onOk,
        onCancel,
    };

    function onOk(data) {
        setVisible(false);
        setDataSource(dataSource => {
            data.map(item => {
                if (!dataSource.some(ite => ite.itemCode === item.itemCode)) {
                    item.skuId = item.emg;
                    dataSource.push(item);
                }
            });
            return dataSource.slice();
        });
    }

    function onCancel() {
        setVisible(false);
    }

    const [importResult, setImportResult] = useState({});
    const [preVisible, setPreVisible] = useState(false);

    function onUpload({ src, name }) {
        lib.request({
            url: "/ccs/jdPurchaseOrder/importExcelView",
            data: {
                url: src,
            },
            needMask: true,
            success: res => {
                setImportResult(res);
                setPreVisible(true);
            },
        });
    }

    function transData(arr) {
        return arr.map(item => {
            return {
                sku: item.skuId,
                emg: item.emg,
                goodsName: item.goodsName,
                sellerRecord: item.itemCode,
                total: item.total,
            };
        });
    }

    function submitHandle() {
        form.validateFields().then(values => {
            if (!dataSource.length) {
                message.warning("请添加商品数据");
                return;
            }
            if (dataSource.some(item => !item.emg)) {
                message.warning("主商品编码不能为空");
                return;
            }
            lib.request({
                url: "/ccs/jdPurchaseOrder/create",
                data: {
                    ...values,
                    poGoods: transData(dataSource),
                },
                needMask: true,
                success: res => {
                    message.success("新建采购单成功");
                    let refresh_event = lib.getParam("refresh_event");
                    if (refresh_event) {
                        event.emit(refresh_event, true);
                    }
                    lib.closePage();
                },
            });
        });
    }

    const successColumns = [
        {
            title: "行号",
            dataIndex: "index",
        },
        {
            title: "商品货号",
            dataIndex: "itemCode",
        },
        {
            title: "商品sku",
            dataIndex: "skuId",
        },
        {
            title: "主商品编码",
            dataIndex: "emg",
        },
    ];
    const failColumns = [
        {
            title: "行号",
            dataIndex: "index",
        },
        {
            title: "商品sku",
            dataIndex: "skuId",
        },
        {
            title: "错误信息",
            dataIndex: "errorMsg",
        },
    ];

    function importHandle() {
        if (importResult.success?.length) {
            setDataSource(dataSource => {
                importResult.success.map(item => {
                    if (!dataSource.some(ite => ite.itemCode === item.itemCode)) {
                        item.total = item.number;
                        item.skuId = item.emg;
                        item.goodsName = item.itemChineseName;
                        dataSource.push(item);
                    }
                });
                return dataSource.slice();
            });
            setPreVisible(false);
            setImportResult({});
        } else {
            message.warning("暂无有效数据");
        }
    }

    function importCancel() {
        setPreVisible(false);
        setImportResult({});
    }

    return (
        (<div className="goods-table">
            <Modal title="导入预览" open={preVisible} onOk={importHandle} onCancel={importCancel} width={1100}>
                <Tabs>
                    <Tabs.TabPane tab={`校验成功(${importResult?.success?.length})`} key="1">
                        <Table dataSource={importResult?.success || []} columns={successColumns} />
                    </Tabs.TabPane>
                    <Tabs.TabPane tab={`校验失败(${importResult?.fail?.length})`} key="2">
                        <Table dataSource={importResult?.fail || []} columns={failColumns} />
                    </Tabs.TabPane>
                </Tabs>
            </Modal>
            <div className="goods-title">
                <Title level={5}>商品明细信息</Title>
                <div className="btn-div">
                    <Button onClick={() => setVisible(true)}>添加商品</Button>
                    <Button className="sync-import">
                        <Uploader onChange={onUpload} />
                        导入商品
                    </Button>
                </div>
            </div>
            <Table dataSource={dataSource} pagination={false} columns={columns} rowKey="id" scroll={{ x: "100%" }} />
            <div className="submit-btn">
                <Button type="primary" onClick={submitHandle}>
                    提交
                </Button>
                <Button onClick={() => lib.closePage()}>取消</Button>
            </div>
            <AppendModal {...props} />
        </div>)
    );
}

export default function () {
    const [form] = Form.useForm();
    const [configList, setConfigList] = useState(initialList);

    function refreshLadNo() {
        configList.map((item, index) => {
            if (item.xhr) {
                lib.request({
                    url: item.xhr,
                    data: item.data,
                    needMask: true,
                    success: res => {
                        setConfigList(configList => {
                            configList[index].list = res.dataList.map(item => ({ id: item.ladNo, name: item.ladNo }));
                            return configList.slice();
                        });
                    },
                });
            }
        });
    }

    useEffect(() => {
        refreshLadNo();
    }, []);

    return (
        <div className="add-procurement">
            <Form form={form}>
                <FormCom {...{ configList, refreshLadNo, form }} />
                <GoodsTable {...{ form }} />
            </Form>
        </div>
    );
}
