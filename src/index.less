@mainColor: #0268ff;
#root .sub-content .menu {
    overflow: visible;
}
#react-single-app .wait .mask {
    z-index: 9999 !important;
}
@font-face {
    font-family: "iconfont"; /* project id 1978815 */
    src: url("//at.alicdn.com/t/font_1978815_dm4myw0ox7v.eot");
    src: url("//at.alicdn.com/t/font_1978815_dm4myw0ox7v.eot?#iefix") format("embedded-opentype"),
        url("//at.alicdn.com/t/font_1978815_dm4myw0ox7v.woff2") format("woff2"),
        url("//at.alicdn.com/t/font_1978815_dm4myw0ox7v.woff") format("woff"),
        url("//at.alicdn.com/t/font_1978815_dm4myw0ox7v.ttf") format("truetype"),
        url("//at.alicdn.com/t/font_1978815_dm4myw0ox7v.svg#iconfont") format("svg");
}
@font-face {
    font-family: "D-DIN";
    src: url("https://dante-img.oss-cn-hangzhou.aliyuncs.com/96575305317.ttf");
}
.sub-content .menu.full .menu-group .main-title .icon {
    font-family: "iconfont";
}
.ant-select-show-text {
    .ant-select-selector {
        color: rgba(0, 0, 0, 0.85) !important;
        background: #fff !important;
        border: none !important;
        cursor: text !important;
    }
    .ant-select-arrow {
        display: none;
    }
}
.main-page {
    .sync-import {
        position: relative;
        .react-single-app-uploader {
            width: 100%;
            height: 100%;
            z-index: 2;
        }
    }
    .search-controls {
        .group {
            label {
                line-height: 32px;
                display: inline-block;
                vertical-align: top;
                .ant-select {
                    max-width: 114px;
                    min-width: 80px;
                    .ant-select-selector {
                        padding: 0 6px;
                    }
                    .ant-select-selection-item {
                        font-size: 12px;
                        padding-right: 12px;
                    }
                    .ant-select-arrow {
                        right: 4px;
                    }
                }
            }
            textarea {
                box-sizing: border-box;
                margin: 0;
                font-variant: tabular-nums;
                list-style: none;
                font-feature-settings: "tnum";
                position: relative;
                display: inline-block;
                width: 100%;
                padding: 4px 11px;
                color: rgba(0, 0, 0, 0.85);
                font-size: 14px;
                background-color: #fff;
                background-image: none;
                border: 1px solid #d9d9d9;
                border-radius: 2px;
                transition: all 0.3s;
                max-width: 100%;
                height: auto;
                min-height: 32px;
                line-height: 1.5715;
                vertical-align: bottom;
                &::selection {
                    color: #fff;
                    background: #1890ff;
                }
                &:focus {
                    border-color: #40a9ff;
                    border-right-width: 1px !important;
                    outline: 0;
                    -webkit-box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
                    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
                }
            }
        }
    }
}

// 修复antd升级的样式问题
.ant-form-item .ant-select,
.ant-form-item .ant-cascader-picker {
    width: 100%;
}

.react-single-v2 .search-item-label {
    color: rgba(0, 0, 0, 0.85) !important;
}
