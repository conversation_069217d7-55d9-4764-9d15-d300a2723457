import React from "react";
import { lib, SearchList, Uploader, getConfigDataUtils } from "react-single-app";
import axios from "axios";
import { Space, Button, message, Modal, Tabs, Table, Form, Radio, DatePicker } from "antd";
import moment from "moment";

export default class extends SearchList {
    constructor(props) {
        super();
    }

    getConfig() {
        return axios.get(getConfigDataUtils.getDataUrlByDataId(247)).then(res => res.data.data);
    }
}
