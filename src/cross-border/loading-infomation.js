import React from "react";
import { lib, SearchList, Uploader, getConfigDataUtils } from "react-single-app";
import axios from "axios";
import { Space, Button, message, Modal, Tabs, Table, Form, Radio, DatePicker } from "antd";
import moment from "moment";

export default class extends SearchList {
    constructor(props) {
        super();
    }

    getConfig() {
        return axios.get(getConfigDataUtils.getDataUrlByDataId(246)).then(res => res.data.data);
    }

    generator(row) {
        let modal = Modal.confirm({
            title: "提示",
            content: "确定生成出库单?",
            onOk: () => {
                lib.request({
                    url: "/ccs/suNingFPLoad/generateExportOrder",
                    data: {
                        id: row.id,
                    },
                    needMask: true,
                    success: res => {
                        message.success("生成出库单成功");
                        this.load();
                        modal.destroy();
                    },
                });
            },
            onCancel: () => modal.destroy(),
        });
    }

    auditHandle(row) {
        let modal = Modal.confirm({
            title: "提示",
            content: "确定审核通过吗?",
            onOk: () => {
                lib.request({
                    url: "/ccs/suNingFPLoad/audit",
                    data: {
                        id: row.id,
                    },
                    needMask: true,
                    success: res => {
                        message.success("审核成功");
                        this.load();
                        modal.destroy();
                    },
                });
            },
            onCancel: () => modal.destroy(),
        });
    }

    myOperation(row) {
        return (
            <Space>
                <span
                    className="link"
                    onClick={() => lib.openPage(`/loading-infomation-detail?page_title=装载单详情&loadId=${row.id}`)}>
                    查看
                </span>
                {row.allowAudit && (
                    <span className="link" onClick={() => this.auditHandle(row)}>
                        审核通过
                    </span>
                )}
                {row.allowGenerateExport && (
                    <span className="link" onClick={() => this.generator(row)}>
                        生成出库单
                    </span>
                )}
            </Space>
        );
    }
}
