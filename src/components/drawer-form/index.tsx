import { ConfigDataItem, DrawerFormProps } from "./type";
import { Drawer, Form, Input, Checkbox, Row, Select, Button, message } from "antd";
//@ts-ignore
import { ConfigFormCenter, DDYObject, FormList } from "react-single-app";
import React, { useEffect, useRef, useState } from "react";
import { DeleteOutlined, PlusOutlined } from "@ant-design/icons";
import "./index.less";
const { TextArea } = Input;
export default (props: DrawerFormProps) => {
    const [groupList, setGroupList] = useState([]);
    const { flag, configData } = props;
    const [form] = Form.useForm();
    const onClose = () => {
        props.onClose();
    };

    const formItemLayout = {
        labelCol: { span: 6 },
        wrapperCol: { span: 14 },
    };

    const add = () => {
        const arr = [...props.groups];
        const len = groupList.length / arr.length;
        if (len > (props.maxColumns | 3)) {
            return message.warning(`最多只能新增${props.maxColumns | 3}条`);
        }
        for (let j = 0; j < arr.length; j++) {
            const item = { ...arr[j] };
            arr[j] = item;
            item.name = item.name + "" + (len + 1);
            item.label = item.label + "" + (len + 1);
            groupList.push(item);
        }
        setGroupList([...groupList]);
    };

    const del = (item: DDYObject, index: number) => {
        groupList.splice(index, 2);
        setGroupList([...groupList]);
    };

    useEffect(() => {
        if (props.open) {
            const values = props.beforeSetDetail(props.editRow);
            form.setFieldsValue(values);
        } else {
            form.resetFields();
        }
    }, [props.open, props.editRow]);

    //@ts-ignore
    const FormTypeItem: React.FunctionComponent<ConfigDataItem> = item => {
        switch (item.type) {
            case "input":
                return <Input maxLength={32} disabled={flag || item.disabled} />;
            case "text":
                return (value: string | number, onChange) => {
                    item.render ? item.render(value) : value;
                };
            case "textarea":
                return (
                    <TextArea
                        className={"form-control"}
                        disabled={flag || item.disabled}
                        placeholder={item.placeholder || "请输入" + item.label}
                    />
                );

            case "select":
                let together = false;
                if (item.customConfig && item.customConfig.together) {
                    together = item.customConfig.together;
                }
                return (
                    <Select
                        disabled={flag || item.disabled}
                        // disabled={true}
                        style={{ width: "100%" }}
                        showSearch
                        optionFilterProp="children"
                        allowClear
                        onChange={e => {
                            item?.onChange && item?.onChange(e);
                        }}>
                        {item.list.map(ite => (
                            <Select.Option key={ite.id} value={ite.id}>
                                {together ? ite.id + ":" + ite.name : ite.name}
                            </Select.Option>
                        ))}
                    </Select>
                );

            default:
                return (value: string | number, onChange) => <></>;
        }
    };

    useEffect(() => {
        let len = 3;
        if (props.editRow && Array.isArray(props.editRow.regexCheckList)) {
            len = props.editRow.regexCheckList.length || 3;
        }
        let result = [];
        for (let i = 0; i < len; i++) {
            const arr = [...props.groups];
            for (let j = 0; j < arr.length; j++) {
                const item = { ...arr[j] };
                arr[j] = item;
                item.name = item.name + "" + (i + 1);
                item.label = item.label + "" + (i + 1);
            }
            result = result.concat(arr);
        }
        setGroupList([...result]);
    }, [props.groups, props.editRow]);

    const onOk = () => {
        form.validateFields().then(res => {
            const data = form.getFieldsValue();
            props.onOk && props.onOk(data);
        });
    };

    return (
        <>
            <Drawer
                title={props.title}
                placement={props.placement}
                onClose={onClose}
                width={600}
                visible={props.open}
                footer={
                    <div className="drawer-form-bottom">
                        <Button type="primary" onClick={props.onClose}>
                            取消
                        </Button>
                        <Button
                            type="primary"
                            onClick={() => {
                                onOk();
                            }}>
                            确定
                        </Button>
                    </div>
                }>
                <div className="drawer-form-content">
                    <Form form={form}>
                        {configData.map((item, index) => (
                            <Form.Item
                                {...(item.formItemLayout || formItemLayout)}
                                hidden={item.hidden}
                                label={item.label}
                                name={item.name}
                                rules={item.rules}
                                key={item.name}
                                tooltip={item.tooltip}>
                                {FormTypeItem(item)}
                            </Form.Item>
                        ))}
                        {groupList.map((item, index) => {
                            let itemLayout = item.formItemLayout || formItemLayout;
                            const { delMain } = item;
                            return delMain ? (
                                <Form.Item
                                    {...itemLayout}
                                    allowClear
                                    key={index + configData.length}
                                    label={item.label}
                                    tooltip={item.tooltip}>
                                    <Row>
                                        <Form.Item
                                            style={{ width: "78%", marginBottom: "unset" }}
                                            name={item.name}
                                            tooltip={item.tooltip}
                                            rules={item.rules}>
                                            {/* <FormTypeItem {...item} /> */}
                                            {FormTypeItem(item)}
                                        </Form.Item>
                                        <Button
                                            type={"link"}
                                            onClick={() => {
                                                del(item, index);
                                            }}>
                                            <DeleteOutlined />
                                        </Button>{" "}
                                    </Row>
                                </Form.Item>
                            ) : (
                                <Form.Item
                                    {...formItemLayout}
                                    key={index}
                                    label={item.label}
                                    name={item.name}
                                    rules={item.rules}>
                                    {FormTypeItem(item)}
                                </Form.Item>
                            );
                        })}

                        {props.allowAdd && (
                            <Button type="dashed" disabled={flag} onClick={() => add()} block>
                                <PlusOutlined /> 新增正则表达式
                            </Button>
                        )}
                    </Form>
                </div>
            </Drawer>
        </>
    );
};
