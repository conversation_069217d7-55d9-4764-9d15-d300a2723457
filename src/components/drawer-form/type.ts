import { DDYObject } from "react-single-app";
export interface DrawerFormProps {
    beforeSetDetail(editRow: DDYObject): DDYObject;
    title: string;
    placement: "right" | "top" | "bottom" | "left";
    open: boolean;
    onClose: () => void;
    submitUrl: string;
    configData: ConfigDataItem[];
    editRow?: DDYObject;
    flag?: boolean;
    selectChange?: (value: any) => void;
    groups: ConfigDataItem[];
    maxColumns?: number;
    onOk?: (data: DDYObject) => void;
    allowAdd?: boolean;
}

export interface ConfigDataItem {
    [x: string]: any;
    formItemLayout: { labelCol: { span: number }; wrapperCol: { span: number } };
    tooltip: string;
    type: "input" | "text" | "textarea" | "select" | "groupList";
    disabled?: boolean;
    rules?: any[];
    label: string;
    name: string;
    max?: string;
}
