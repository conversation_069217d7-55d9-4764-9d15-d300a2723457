import React, { useState, useEffect } from "react";
import {
    Select,
    Form,
    Modal,
    Input,
    Checkbox,
    DatePicker,
    Upload,
    Button,
    InputNumber,
    Switch,
    message,
    Space,
    Col,
    Row,
    Table,
    Steps,
} from "antd";
import moment from "moment";
import { Uploader, lib } from "react-single-app";
import "./AppendModal.less";

const FormItem = Form.Item;
const Option = Select.Option;
const { TextArea } = Input;
const { Step } = Steps;
const { RangePicker } = DatePicker;

/**
 *
 * @param configList 配置列表
 * ```
 * {type: 'SELECT', labelKey: '', labelName: '', xhr: '请求链接', list: [], placeholder: '', required: false, message: '请输入', }
 * ```
 * @param title
 * @param visible 显示/隐藏弹窗
 * @param onOk 确认回调方法
 * @param onCancel 取消或者关闭回调方法
 * @param props 传入数据
 * @param modalStyle 弹窗样式
 */

function AppendModal({
    title,
    visible,
    props,
    searchList,
    onOk,
    onCancel,
    modalStyle,
    url,
    columns,
    rowKey,
    currRows = [],
    modalProps = {},
    wrapClassName,
    tableSelectionType,
    getCheckboxProps = () => {},
    modalWidth,
    scroll,
}) {
    const [form] = Form.useForm();
    let [pagination, setPagination] = useState({ pageSize: 20, current: 1 });
    let [dataSource, setDataSource] = useState([]);
    let [totalData, setTotalData] = useState({});
    let [totalKey, setTotalKey] = useState({});
    let [selectedRowKeys, setSelectedRowKeys] = useState([]);
    let [loading, setLoading] = useState(false);
    let originalKeys = [];
    if (currRows?.length) {
        originalKeys = currRows.reduce((curr, next) => curr.concat([next[rowKey]]), []);
    }
    if (props) {
        Object.keys(props)?.forEach(key => {
            if (props[key] === "") {
                delete props[key];
            }
        });
    }
    useEffect(() => {
        if (visible) {
            fetchList({ pageSize: 20, current: 1 });
            form.resetFields();
            form.setFieldsValue();
        }
    }, [visible]);

    function calcKeys(current, dataList) {
        setTotalKey(prev => {
            let arr = prev?.original ? prev.original || [] : originalKeys;
            let currPageSelectedRowKeys = arr.filter(item => dataList.some(ite => ite[rowKey] === item));
            if (prev[current]?.length) {
                prev[current] = Array.from(new Set(prev[current].concat(currPageSelectedRowKeys)));
            } else {
                prev[current] = currPageSelectedRowKeys;
            }
            setSelectedRowKeys(prev[current]);
            let original = arr.filter(item => prev[current].indexOf(item) === -1);
            prev.original = original;
            return prev;
        });
    }

    function fetchList(page, params) {
        setLoading(true);
        let pagi = Object.assign(pagination, page);

        lib.request({
            url,
            data: {
                currentPage: pagi.current,
                pageSize: pagi.pageSize,
                ...props,
                ...params,
            },
            needMask: true,
            success: res => {
                setLoading(false);
                if (rowKey === "rowKey") {
                    res.dataList.map(item => {
                        item[rowKey] = `${item.goodsCode}-${item.barcode}`;
                    });
                }
                setDataSource(res.dataList);
                setTotalData(Object.assign(totalData, { [res.page.currentPage]: res.dataList }));
                calcKeys(res.page.currentPage, res.dataList);
                let page = {
                    current: res.page.currentPage,
                    total: res.page.totalCount,
                    pageSize: res.page.pageSize,
                    showTotal: total => `共${total}条`,
                };
                setPagination(page);
            },
        });
    }

    function onSelectChange(rowKeys) {
        if (tableSelectionType === "radio") {
            setTotalKey({ [pagination.current]: rowKeys });
        } else {
            setTotalKey(Object.assign(totalKey, { [pagination.current]: rowKeys }));
        }
        setSelectedRowKeys(rowKeys);
    }

    function switchType(item) {
        switch (item.type) {
            case "INPUT":
                return (
                    <Input
                        disabled={item.disabled}
                        placeholder={item.placeholder || `请输入${item.labelName}`}
                        maxLength={item.maxLength || 9999}
                        autoComplete={item.autoComplete || null}
                    />
                );
            case "INPUTNUMBER":
                return (
                    <InputNumber
                        disabled={item.disabled}
                        placeholder={item.placeholder || `请输入${item.labelName}`}
                        max={item.max}
                        min={item.min}
                    />
                );
            case "SELECT":
                return (
                    <Select
                        onChange={item.onChange ? e => item.onChange(e, form) : e => {}}
                        mode={item.mode}
                        style={{ width: "100%" }}
                        showSearch
                        disabled={item.disabled}
                        optionFilterProp="children">
                        {item.list.length &&
                            item.list.map((ite, index) => {
                                return (
                                    <Option value={ite.value || ite.id} key={index}>
                                        {ite.name}
                                    </Option>
                                );
                            })}
                    </Select>
                );
            case "CHECKBOX":
                return (
                    <Checkbox.Group>
                        {item.list.length &&
                            item.list.map((ite, index) => {
                                return (
                                    <Checkbox value={ite.value} key={index}>
                                        {ite.name}
                                    </Checkbox>
                                );
                            })}
                    </Checkbox.Group>
                );
            case "DATE":
                return <DatePicker showTime />;
            case "DATERANGE":
                return <RangePicker showTime />;
            case "TEXTAREA":
                return <TextArea disabled={item.disabled} maxLength={item.maxLength || null} />;
            case "SWITCH":
                return <Switch />;
            case "FILE":
                return (
                    <Uploader
                        onUploadStart={() => {
                            console.log("开始上传 ");
                        }}
                        onChange={src => {
                            form.setFieldsValue({
                                [item.labelKey]: src,
                            });
                        }}
                        style={{ width: "60px", height: "40px" }}
                        src={""}
                    />
                );
            case "PASSWORD":
                return <Input.Password />;
            default:
                return null;
        }
    }

    function filterArrayByRowKeys(arr) {
        let theArr = [];
        arr.map(item => {
            if (!hasSameKeyItem(theArr, item)) {
                theArr.push(item);
            }
        });
        return theArr;
    }

    function hasSameKeyItem(arr, ite) {
        return arr.some(item => item[rowKey] === ite[rowKey]);
    }

    function okHandle() {
        let rows = [],
            keys = [],
            resultRows = [];
        Object.values(totalData).map(item => {
            rows = rows.concat(item);
        });
        rows = filterArrayByRowKeys(rows.concat(currRows));
        Object.values(totalKey).map(item => {
            keys = keys.concat(item);
        });
        resultRows = rows.filter(item => keys.concat(selectedRowKeys).indexOf(item[rowKey]) !== -1);
        onOk(resultRows);
    }

    function afterClose() {
        setPagination({ pageSize: 20, current: 1 });
        setTotalData({});
        setTotalKey({});
    }

    function cancelHandle() {
        form.resetFields();
        let defaultPage = {
            current: 1,
            pageSize: 20,
        };
        setTotalData({});
        setTotalKey({});
        setPagination(defaultPage);
        onCancel();
    }

    function handleTableChange(pagination, filters, sorter) {
        let page = {
            ...pagination,
            total: pagination.total,
        };
        setSelectedRowKeys(totalKey[pagination.current] || []);
        setPagination(page);
        form.validateFields().then(values => {
            fetchList(page, values);
        });
    }

    function search() {
        let defaultPage = {
            current: 1,
            pageSize: 20,
        };
        form.validateFields().then(values => {
            Object.keys(values).map(item => {
                if (values[item] === "") {
                    delete values[item];
                }
            });
            fetchList(defaultPage, values);
        });
    }

    function reset() {
        let defaultPage = {
            current: 1,
            pageSize: 20,
        };
        form.resetFields();
        form.setFieldsValue(props);
        setPagination({ current: 1, pageSize: 20 });
        fetchList(defaultPage, {});
    }

    const formItemLayout = formItemLayout || {
        labelCol: { span: 8 },
        wrapperCol: { span: 16 },
    };
    const rowSelection = {
        selectedRowKeys,
        onChange: onSelectChange,
        type: tableSelectionType || "checkbox",
        getCheckboxProps,
    };
    useEffect(() => {
        if (props && searchList?.length && visible) {
            form.setFieldsValue(props);
        }
    }, [visible]);
    return (
        <Modal
            title={title}
            open={visible}
            onOk={() => okHandle()}
            onCancel={() => cancelHandle()}
            width={modalWidth || 1100}
            style={modalStyle}
            maskClosable={false}
            destroyOnClose={true}
            wrapClassName={`append-modal ${wrapClassName}`}
            afterClose={afterClose}
            {...modalProps}>
            {searchList?.length ? (
                <Col style={{ padding: "12px 10px" }}>
                    <Form form={form} layout="inline" {...formItemLayout}>
                        {searchList.map((item, index) => {
                            return (
                                <FormItem label={item.labelName} name={item.labelKey} key={index}>
                                    {switchType(item)}
                                </FormItem>
                            );
                        })}
                        <FormItem>
                            <Space>
                                <Button type="primary" onClick={search}>
                                    查询
                                </Button>
                                <Button onClick={reset}>重置</Button>
                            </Space>
                        </FormItem>
                    </Form>
                </Col>
            ) : (
                ""
            )}
            <Table
                loading={loading}
                dataSource={dataSource}
                columns={columns}
                onChange={handleTableChange}
                rowSelection={rowSelection}
                rowKey={rowKey}
                pagination={pagination}
                scroll={scroll}
            />
        </Modal>
    );
}

export default AppendModal;
