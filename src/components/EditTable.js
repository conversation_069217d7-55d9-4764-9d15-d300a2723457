import React, { useEffect, useState } from "react";
import { Select, Form, Modal, Input, Checkbox, message, Table, DatePicker, InputNumber, Button, Tooltip } from "antd";
import moment from "moment";

const FormItem = Form.Item;
const Option = Select.Option;
const { TextArea } = Input;

const Models = ({ visible, setVisible, lists }) => {
    const handleOk = () => {
        setTimeout(() => {
            setVisible(false);
        }, 3000);
    };

    const handleCancel = () => {
        setVisible(false);
    };

    const handleCopy = () => {
        var copyDOM = document.querySelector(".copy-text"); //需要复制文字的节点
        var range = document.createRange(); //创建一个range
        window.getSelection().removeAllRanges(); //清楚页面中已有的selection
        range.selectNode(copyDOM);
        window.getSelection().addRange(range); // 执行选中元素
        var successful = document.execCommand("copy"); // 执行 copy 操作
        if (successful) {
            message.success("复制成功！");
        } else {
            message.warning("复制失败，请手动复制！");
        }
        // 移除选中的元素
        window.getSelection().removeAllRanges();
    };
    return (
        (<Modal
            open={visible}
            title="报文"
            onOk={handleOk}
            onCancel={handleCancel}
            footer={[
                <Button key="copy" onClick={handleCopy}>
                    复制内容
                </Button>,
                <Button type="primary" key="back" onClick={handleCancel}>
                    关闭
                </Button>,
            ]}>
            <div className="copy-text">{<p>{lists}</p>}</div>
        </Modal>)
    );
};
function EditTable({ dataSource, columns, scroll = {}, rowKey, loading, showSubmitBtn, extra, id, form }) {
    const [visible, setVisible] = useState(false);
    const [lists, setList] = useState([
        "Some contents...",
        "Some contents...",
        "Some contents...",
        "Some contents...",
        "Some contents3...",
    ]);
    const objs = {
        visible,
        setVisible,
        lists,
    };
    function switchType(item, name) {
        switch (item.formType) {
            case "INPUT":
                return <Input />;
            case "INPUTNUMBER":
                return <InputNumber min={0} />;
            case "SELECT":
                return (
                    <Select
                        style={{ width: 160 }}
                        placeholder={showSubmitBtn ? "请选择" + item.title : ""}
                        showSearch
                        disabled={!showSubmitBtn || item.disabled}
                        className={showSubmitBtn && !item.disabled ? "" : "ant-select-show-text"}
                        optionFilterProp="children"
                        onChange={item.onChange ? e => item.onChange(e, form, name) : e => {}}
                        allowClear>
                        {item.selectData &&
                            item.selectData.map((ite, index) => (
                                <Option value={String(ite.id)} key={index}>
                                    {item.onChange
                                        ? item.together
                                            ? `${ite.id}:${ite.name}`
                                            : ite.id
                                        : `${ite.id}:${ite.name}`}
                                </Option>
                            ))}
                    </Select>
                );
            case "TEXTAREA":
                return <TextArea />;
            case "DATE":
                return <DatePicker />;
            case "DATETIME":
                return <DatePicker format={"YYYY-MM-dd hh:mm:ss"} showTime />;
            case "AREA":
                return <Input />;
            default:
                return "";
        }
    }

    function renderItem(value) {
        // return value.xmlReqeust ? <Button onClick={() => {
        //     let modal = Modal.info({
        //         content: value.xmlReqeust,
        //         onOk: () => {
        //             modal.destroy()
        //         }
        //     })
        // }}>报文</Button> : null
        return value.hasXmlMessage == 0 ? (
            ""
        ) : value.operateDes && value.operateDes.includes("清单申报") && value.hasXmlMessage == 1 ? (
            <Button
                onClick={() => {
                    setVisible(true);
                    setList(value?.content || "");
                }}>
                请求报文
            </Button>
        ) : (
            <Button
                onClick={() => {
                    setVisible(true);
                    setList(value?.content || "");
                }}>
                响应报文
            </Button>
        );
    }

    function renderItem2(value) {
        return value.xmlResponse ? (
            <Button
                onClick={() => {
                    let modal = Modal.info({
                        content: value.xmlResponse,
                        onOk: () => {
                            modal.destroy();
                        },
                    });
                }}>
                响应报文
            </Button>
        ) : null;
    }
    function renderItem3(value) {
        return value && <div className="rizhi">{value}</div>;
    }
    useEffect(() => {
        form.current.resetFields();
        columns = columns.map((item, index) => {
            if (!item) return;
            if (item.extra && item.formType !== "SELECT") {
                if (item.renderType === "func" && item.extra === "renderItem") {
                    item.render = value => {
                        return renderItem(value);
                    };
                } else if (item.renderType === "func" && item.extra === "renderItem2") {
                    item.render = value => {
                        return renderItem2(value);
                    };
                } else if (item.renderType === "func" && item.extra === "renderItem3") {
                    item.render = value => {
                        return renderItem3(value);
                    };
                } else {
                    item.render = eval(item.extra);
                }
            }
            if ((item.formType !== "TEXT" && showSubmitBtn) || item.formType === "SELECT") {
                if (extra) {
                    item.render = (text, record, inde) => {
                        item.name = [extra, "itemList", inde, item.dataIndex];
                        return (
                            <FormItem
                                style={{ margin: 0 }}
                                key={`${index}_${inde}`}
                                name={item.name}
                                rules={[
                                    {
                                        required: item.required || false,
                                        message: item.message || `请输入${item.title}`,
                                    },
                                ]}
                                normalize={value => {
                                    if (item.formType === "INPUTNUMBER" && item.numberType) {
                                        value = String(value || "");
                                        if (item.numberType === "decimal ") {
                                            if (value.indexOf(".") !== -1) {
                                                value = parseFloat(parseFloat(value).toFixed(4));
                                            }
                                        } else if (item.numberType === "integer") {
                                            value = parseInt(value);
                                        }
                                    }
                                    return value;
                                }}>
                                {switchType(item, [extra, "itemList", inde, item.dataIndex])}
                            </FormItem>
                        );
                    };
                } else {
                    item.render = (text, record, inde) => {
                        return (
                            <FormItem
                                style={{ margin: 0 }}
                                key={`${index}_${inde}`}
                                name={item.dataIndex}
                                rules={[
                                    {
                                        required: item.required || false,
                                        message: item.message || `请输入${item.title}`,
                                    },
                                ]}>
                                {switchType(item)}
                            </FormItem>
                        );
                    };
                }
            }
        });
    }, [id]);

    return (
        <>
            <Table
                loading={loading}
                rowKey={rowKey || "id"}
                dataSource={dataSource}
                columns={columns}
                scroll={scroll}
                pagination={false}></Table>
            <Models {...objs} />
        </>
    );
}
export default EditTable;
