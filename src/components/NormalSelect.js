import React, { useState, useEffect } from "react";
import { Select } from "antd";
import { lib } from "react-single-app";
const NormalSelect = ({ value, onChange, optionsApi, ...rest }) => {
    const [options, setOptions] = useState([]);
    useEffect(() => {
        lib.request({
            url: optionsApi,
            success: res => {
                const options = res.map(item => {
                    return {
                        value: item.id,
                        label: item.name,
                    };
                });
                setOptions(options);
            },
        });
    }, []);
    return <Select options={options} value={value} onChange={onChange} {...rest}></Select>;
};
export default NormalSelect;
