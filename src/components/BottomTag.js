import React from "react";
import { Select, Form, Modal, Input, Checkbox, Table, DatePicker, Tabs, Button, message } from "antd";
import EditTable from "./EditTable.js";
import moment from "moment";

const { TabPane } = Tabs;

class BottomTag extends React.Component {
    constructor() {
        super();
        this.formRef = React.createRef();
    }

    bottomTabSubmit() {
        const { tabSubmit } = this.props;
        this.formRef.current
            .validateFields()
            .then(values => {
                if (JSON.stringify(values) === "{}") return;
                for (let i in values) {
                    if (moment.isMoment(values[i])) {
                        values[i] = values[i].format("YYYY-MM-DD HH:mm:ss");
                    }
                }
                tabSubmit(values);
            })
            .catch(err => {
                console.log(err);
                //   if (err.errorFields.length) {
                //     this.formRef.current.scrollToField(err.errorFields[0].name[0]);
                //     message.warning(err.errorFields[0].errors[0])
                //     return;
                //   }
            });
    }
    tabsChange(value) {
        if (this) {
            const { tabOnClick } = this.props;

            tabOnClick && tabOnClick(value);
        }
    }

    render() {
        const { tabsList, loading, showSubmitBtn, id } = this.props;
        let initialValues = tabsList && tabsList[0] && tabsList[0].dataList && tabsList[0].dataList[0];
        return (
            <div>
                {showSubmitBtn && (
                    <Button
                        style={{ position: "absolute", right: 60, zIndex: 10, top: 4 }}
                        type="primary"
                        onClick={() => this.bottomTabSubmit()}>
                        确定
                    </Button>
                )}
                <Form ref={this.formRef} initialValues={initialValues}>
                    <Tabs defaultActiveKey="0" onChange={value => this.tabsChange(value)}>
                        {tabsList.map((item, index) => (
                            <TabPane tab={item.tab} key={index} forceRender={true}>
                                {item.type === "TABLE" && (
                                    <EditTable
                                        form={this.formRef}
                                        id={id}
                                        dataSource={item.dataList}
                                        columns={item.columns}
                                        scroll={{ x: "max-content" }}
                                        rowKey="id"
                                        loading={loading}
                                        showSubmitBtn={showSubmitBtn}
                                        extra={item.extra}
                                    />
                                )}
                                {item.type === "TAB" && item.tabsList && item.tabsList.length ? (
                                    <Tabs defaultActiveKey="0" onChange={this.tabsChange}>
                                        {item.tabsList &&
                                            item.tabsList.length &&
                                            item.tabsList.map((ite, inde) => {
                                                return (
                                                    <TabPane forceRender={true} tab={ite.tab} key={inde}>
                                                        {ite.type === "TABLE" && (
                                                            <EditTable
                                                                form={this.formRef}
                                                                dataSource={ite.dataList}
                                                                columns={ite.columns}
                                                                scroll={{ x: "max-content" }}
                                                                id={id}
                                                                rowKey="id"
                                                                loading={loading}
                                                                showSubmitBtn={showSubmitBtn}
                                                                extra={ite.extra}
                                                            />
                                                        )}
                                                    </TabPane>
                                                );
                                            })}
                                    </Tabs>
                                ) : (
                                    ""
                                )}
                            </TabPane>
                        ))}
                    </Tabs>
                </Form>
            </div>
        );
    }
}

export default BottomTag;
