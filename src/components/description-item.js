import React from "react";
import "./description-item.less";
import classNames from "classnames";

export default function DescriptionItem({ className, label, labelClassName, content, contentClassName, onClick }) {
    const prefixCls = classNames("dt-description-item", className);
    const labelCls = classNames(`label`, labelClassName);
    const contentCls = classNames(`content`, contentClassName);
    return (
        <div className={prefixCls} onClick={onClick}>
            <div className={labelCls}>{label}</div>
            <div className={contentCls}>{content}</div>
        </div>
    );
}
