import React, { useState, useEffect } from "react";
import { Select, Form, Modal, Input, Checkbox, DatePicker, Upload, Button, InputNumber, Switch, message } from "antd";
import moment from "moment";
import { Uploader } from "react-single-app";

const FormItem = Form.Item;
const Option = Select.Option;
const { TextArea } = Input;
const { RangePicker } = DatePicker;

/**
 * 编辑弹窗
 *
 * @param configList 配置列表
 * ```
 * {type: 'SELECT', labelKey: '', labelName: '', xhr: '请求链接', list: [], placeholder: '', required: false, message: '请输入', }
 * ```
 * @param title
 * @param visible 显示/隐藏弹窗
 * @param onOk 确认回调方法
 * @param editRow 当前编辑对象
 * @param formItemLayout form布局
 * @param modalStyle 弹窗样式
 */

export default function ({ title, editRow, modalStyle, configList, onOk, btnProps }) {
    const [form] = Form.useForm();
    const [visible, setVisible] = useState(false);
    useEffect(() => {
        !visible && form.resetFields();
    }, [visible]);
    function switchType(item) {
        switch (item.type) {
            case "INPUT":
                return (
                    <Input
                        disabled={item.disabled}
                        placeholder={item.placeholder || `请输入${item.labelName}`}
                        maxLength={item.maxLength || 40}
                        autoComplete={item.autoComplete || null}
                    />
                );
            case "INPUTNUMBER":
                return (
                    <InputNumber
                        disabled={item.disabled}
                        placeholder={item.placeholder || `请输入${item.labelName}`}
                        max={item.max}
                        min={item.min}
                    />
                );
            case "SELECT":
                return (
                    <Select
                        onChange={item.onChange ? e => item.onChange(e, form) : e => {}}
                        mode={item.mode}
                        style={{ width: "100%" }}
                        showSearch
                        disabled={item.disabled}
                        optionFilterProp="children">
                        {item.list.length &&
                            item.list.map((ite, index) => {
                                return (
                                    <Option value={ite.value || ite.id} key={index}>
                                        {ite.name}
                                    </Option>
                                );
                            })}
                    </Select>
                );
            case "CHECKBOX":
                return (
                    <Checkbox.Group>
                        {item.list.length &&
                            item.list.map((ite, index) => {
                                return (
                                    <Checkbox value={ite.value} key={index}>
                                        {ite.name}
                                    </Checkbox>
                                );
                            })}
                    </Checkbox.Group>
                );
            case "DATE":
                return <DatePicker showTime />;
            case "DATERANGE":
                return <RangePicker showTime />;
            case "TEXTAREA":
                return <TextArea disabled={item.disabled} maxLength={item.maxLength || null} />;
            case "SWITCH":
                return <Switch />;
            case "FILE":
                return <Uploader style={{ width: "60px", height: "40px" }} />;
            case "PASSWORD":
                return <Input.Password />;
            default:
                return null;
        }
    }

    function okHandle() {
        form.validateFields()
            .then(values => {
                onOk(values);
            })
            .catch(err => {
                console.log("Validate Failed:", err);
            });
    }

    function cancelHandle() {
        form.resetFields();
        setVisible(false);
    }

    function renderFormItem(item, rules) {
        switch (item.type) {
            case "FILE":
                return (
                    <FormItem label={item.labelName} name={item.labelKey} rules={rules}>
                        {switchType(item)}
                    </FormItem>
                );
            case "TEXT":
                return <FormItem label={item.labelName}>{editRow ? [item.labelKey] : null}</FormItem>;
            default:
                return item.hide !== true ? (
                    <FormItem label={item.labelName} name={item.labelKey} rules={rules}>
                        {switchType(item)}
                    </FormItem>
                ) : null;
        }
    }

    let formItemLayout = formItemLayout || {
        labelCol: { span: 6 },
        wrapperCol: { span: 14 },
    };
    function Btn({ type, text }) {
        return type === "span" ? (
            <span className="link" onClick={() => setVisible(true)}>
                {text}
            </span>
        ) : (
            <Button onClick={() => setVisible(true)}>{text}</Button>
        );
    }
    return (
        (<React.Fragment>
            <Modal
                cancelText="取消" // 11.6临时加的
                okText="确定"
                title={title}
                open={visible}
                onOk={() => okHandle()}
                onCancel={() => cancelHandle()}
                width={(modalStyle && modalStyle.width) || 520}
                maskClosable={false}
                destroyOnClose={true}>
                <Form {...formItemLayout} form={form} initialValues={editRow}>
                    {configList.map((item, index) => {
                        let rules = [
                            {
                                required: item.required,
                                message:
                                    item.message || `请${item.type === "SELECT" ? "选择" : "输入"}${item.labelName}`,
                            },
                        ];
                        if (item.max) {
                            rules[0].max = item.max;
                        }
                        if (item.validator) {
                            rules.push(item.validator);
                        }
                        return <React.Fragment key={index}>{renderFormItem(item, rules)}</React.Fragment>;
                    })}
                </Form>
            </Modal>
            <Btn {...btnProps} />
        </React.Fragment>)
    );
}
