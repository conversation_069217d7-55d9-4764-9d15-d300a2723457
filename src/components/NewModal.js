import React, { useState, useEffect, useImperativeHandle } from "react";
import {
    Select,
    Form,
    Modal,
    Input,
    Checkbox,
    DatePicker,
    Upload,
    Button,
    InputNumber,
    Switch,
    Image,
    Radio,
} from "antd";
import moment from "moment";
import { Uploader } from "react-single-app";
import { DTUploaderFile } from "@dt/components";

const FormItem = Form.Item;
const Option = Select.Option;
const { TextArea } = Input;
const { RangePicker } = DatePicker;

/**
 * 新建弹窗或者编辑弹窗
 *
 * @param form form
 * @param configList 配置列表
 * ```
 * {type: 'SELECT', labelKey: '', labelName: '', xhr: '请求链接', list: [], placeholder: '', required: false, message: '请输入', }
 * ```
 * @param title
 * @param visible 显示/隐藏弹窗
 * @param onOk 确认回调方法
 * @param onCancel 取消或者关闭回调方法
 * @param editRow 当前编辑对象
 * @param formItemLayout form布局
 * @param modalStyle 弹窗样式
 * @param onFieldsChange 表单值改变函数
 */

function NewModal({
    title,
    visible,
    editRow,
    modalStyle,
    configList,
    onOk,
    onCancel,
    cancelText,
    okText,
    formItemLayout,
    keyboard,
    onValuesChange,
    myRef,
    header,
}) {
    const [form] = Form.useForm();

    useEffect(() => {
        if (visible) {
            form.setFieldsValue(editRow);
        } else {
            form.resetFields();
        }
    }, [visible]);

    function switchType(item) {
        // console.log(item, "item");
        switch (item.type) {
            case "INPUT":
                return item.isPlaceHolder ? (
                    <Input
                        disabled={item.disabled}
                        allowClear={item.allowClear}
                        maxLength={item.maxLength || 9999}
                        autoComplete={item.autoComplete || null}
                        onFocus={() => {
                            item.onFocus && item.onFocus(form);
                        }}
                    />
                ) : (
                    <Input
                        disabled={item.disabled}
                        allowClear={item.allowClear}
                        placeholder={item.placeholder || `请输入${item.labelName}`}
                        maxLength={item.maxLength || 9999}
                        autoComplete={item.autoComplete || null}
                        onFocus={() => {
                            item.onFocus && item.onFocus(form);
                        }}
                    />
                );
            case "INPUTNUMBER":
                return (
                    <InputNumber
                        allowClear={item.allowClear}
                        disabled={item.disabled}
                        placeholder={item.placeholder || `请输入${item.labelName}`}
                        max={item.max}
                        min={item.min}
                    />
                );
            case "SELECT":
                return (
                    <Select
                        onChange={
                            item.onChange
                                ? e => {
                                      let selects = [];
                                      if (Array.isArray(e)) {
                                          selects = item.list.filter(obj => e.includes(obj.id));
                                      } else {
                                          selects = item.list.filter(obj => e === obj.id);
                                      }
                                      item.onChange(e, form, selects);
                                  }
                                : e => {}
                        }
                        allowClear={item.allowClear}
                        mode={item.mode}
                        style={{ width: "100%" }}
                        showSearch
                        onSelect={item.onSelect ? e => item.onSelect(e, form) : e => {}}
                        disabled={item.disabled}
                        optionFilterProp="children">
                        {item.list.length &&
                            item.list.map((ite, index) => {
                                let name = ite.name;
                                if (item.showValue) {
                                    name = ite.id + ":" + ite.name;
                                }
                                return (
                                    <Option value={ite.value || ite.id} key={index}>
                                        {name}
                                    </Option>
                                );
                            })}
                    </Select>
                );
            case "CHECKBOX":
                return (
                    <Checkbox.Group onChange={item.onChange ? e => item.onChange(e, form) : e => {}}>
                        {item.list.length &&
                            item.list.map((ite, index) => {
                                return (
                                    <Checkbox value={ite.value || ite.id} key={index}>
                                        {ite.name}
                                    </Checkbox>
                                );
                            })}
                    </Checkbox.Group>
                );
            case "RADIO":
                return (
                    <Radio.Group onChange={item.onChange ? e => item.onChange(e, form) : e => {}}>
                        {item.list.length &&
                            item.list.map((ite, index) => {
                                return (
                                    <Radio value={ite.value || ite.id} key={index}>
                                        {ite.name}
                                    </Radio>
                                );
                            })}
                    </Radio.Group>
                );
            case "DATE":
                return <DatePicker showTime />;
            case "DATERANGE":
                return (
                    <RangePicker
                        showTime={item.showTime ? item.showTime : item.mode !== "date"}
                        onChange={e => {
                            item.onChange && item.onChange(e, item);
                        }}
                        disabledDate={current => {
                            return item.disabledDate && item.disabledDate(current, item.dates);
                        }}
                        onCalendarChange={val => {
                            item.dates = val;
                        }}
                    />
                );
            case "TEXTAREA":
                return (
                    <TextArea showCount={item.showCount} disabled={item.disabled} maxLength={item.maxLength || null} />
                );
            case "SWITCH":
                return <Switch onChange={item.onChange ? e => item.onChange(e, form) : e => {}} />;
            case "FILE":
                // 使用新的上传组件 （兼容旧的代码）
                if (item.newComponent) {
                    return <DTUploaderFile {...item} />;
                }
                if (item.disabled) {
                    let src = editRow[item.labelKey]?.src;
                    if (src) {
                        let type = src.split(".").reverse()[0];
                        if (["jpg", "jpeg", "png", "git", "svg", "webp", "bmp"].indexOf(type) > -1) {
                            return <Image src={src} width={100} />;
                        } else {
                            let name = src.match(/([^/*.]+)\.\w+$/)[0];
                            return (
                                <a href={src} target={"_blank"}>
                                    {name}
                                </a>
                            );
                        }
                    } else {
                        return "";
                    }
                } else {
                    return <Uploader allowTypes={item.allowTypes} />;
                }
            case "PASSWORD":
                return <Input.Password />;
            default:
                return null;
        }
    }

    function okHandle() {
        form.validateFields()
            .then(values => {
                configList.map(item => {
                    if (item.type == "FILE") {
                        if (!item.needName) {
                            values[item.labelKey] = values[item.labelKey]?.src;
                        }
                    }
                });
                onOk(values);
                // setFileList([])
            })
            .catch(err => {
                console.log("Validate Failed:", err);
            });
    }

    function cancelHandle(e) {
        form.resetFields();
        onCancel(e);
    }

    function renderFormItem(item, rules) {
        switch (item.type) {
            case "FILE":
                return (
                    <FormItem
                        label={item.labelName}
                        name={item.labelKey}
                        rules={rules}
                        extra={item.extra}
                        valuePropName={item.newComponent && "fileList"}
                        getValueFromEvent={
                            item.newComponent
                                ? e => {
                                      if (Array.isArray(e)) return e;
                                      return e?.fileList;
                                  }
                                : null
                        }>
                        {switchType(item)}
                    </FormItem>
                );
            case "TEXT":
                return (
                    <FormItem
                        label={item.labelName}
                        extra={item.extra}
                        labelCol={item.labelCol || { span: 6 }}
                        wrapperCol={item.wrapperCol || { span: 14 }}>
                        {editRow
                            ? item.render
                                ? item.render(editRow)
                                : editRow[item.labelKey]?.toString() || null
                            : null}
                    </FormItem>
                );
            default:
                //tooltip="What do you want others to call you?"
                // return (item.hide !== true ? <FormItem label={item.labelName}  name={item.labelKey} rules={rules}>
                //   {
                //     switchType(item)
                //   }
                // </FormItem> : null)
                return item.hide == true ? null : (
                    <FormItem
                        label={item.labelName}
                        name={item.labelKey}
                        rules={rules}
                        help={item.help || null}
                        extra={item.extra || null}
                        labelCol={item.labelCol || { span: 6 }}
                        wrapperCol={item.wrapperCol || { span: 14 }}>
                        {switchType(item)}
                    </FormItem>
                );
        }
    }

    let mergeFormItemLayout = formItemLayout || {
        labelCol: { span: 6 },
        wrapperCol: { span: 14 },
    };
    useImperativeHandle(myRef, () => ({
        setFormValue: arr => {
            form.setFields(arr);
        },
        form: form,
    }));
    return (
        <Modal
            keyboard={keyboard}
            cancelText={cancelText || "取消"} // 11.6临时加的
            okText={okText || "确定"}
            title={title}
            open={visible}
            onOk={() => okHandle()}
            onCancel={e => cancelHandle(e)}
            width={(modalStyle && modalStyle.width) || 520}
            maskClosable={false}
            destroyOnClose={true}>
            <>
                {header && header}
                <Form {...mergeFormItemLayout} form={form} onValuesChange={onValuesChange}>
                    {configList.map((item, index) => {
                        let rules = [
                            {
                                required: item.required,
                                message:
                                    item.message ||
                                    `请${
                                        item.type === "SELECT" || item.type === "CHECKBOX" || item.type === "RADIO"
                                            ? "选择"
                                            : "输入"
                                    }${item.labelName}`,
                            },
                        ];
                        if (item.max) {
                            rules[0].max = item.max;
                        }
                        if (item.validator) {
                            rules.push(item.validator);
                        }
                        return (
                            <React.Fragment key={index}>{!item.hidden && renderFormItem(item, rules)}</React.Fragment>
                        );
                    })}
                </Form>
            </>
        </Modal>
    );
}

export default React.forwardRef((props, ref) => <NewModal {...props} myRef={ref} />);
