import React, { useState, useEffect } from "react";
import { lib, event, SearchList } from "react-single-app";
import { Button, Space, message, Modal, Table, Input, Select, Form, Tooltip, CheckBox } from "antd";
import { Resizable } from "react-resizable";
import { QuestionCircleOutlined } from "@ant-design/icons";

const ResizableTitle = props => {
    const { onResize, ...restProps } = props;
    let [width, setWidth] = useState(props.width);
    let [resizing, setResizing] = useState(false);
    if (!width) {
        return <th {...restProps} />;
    }
    let thStyle = { ...restProps.style },
        handleStyle = {};
    if (resizing) {
        handleStyle = { left: `${width - 11}px` };
        thStyle.zIndex = 10;
    }
    return (
        <Resizable
            width={width}
            height={0}
            handle={
                <div
                    className={`resizable-handle ${resizing && "resizing"}`}
                    onClick={e => {
                        e.stopPropagation();
                    }}
                    style={handleStyle}></div>
            }
            onResizeStart={() => setResizing(true)}
            onResize={(e, { size }) => {
                if (size.width > 80 && size.width < 800) {
                    setWidth(size.width);
                }
            }}
            onResizeStop={(e, p) => {
                setResizing(false);
                onResize(e, p);
            }}
            draggableOpts={{ enableUserSelectHack: false }}>
            <th {...restProps} style={thStyle}>
                <div className="title">{restProps.children}</div>
            </th>
        </Resizable>
    );
};

export default class MergeTdSearchList extends SearchList {
    isMerge(item) {
        let { mergeKeyList } = this.state;
        return mergeKeyList.indexOf(item.key) === -1;
    }

    renderTable() {
        let { config, dataList, table, selectedIdList, pagination, _loading } = this.state;
        let dataSource = this.transData(dataList);
        let maxWidth = 0;
        let list = config.tableFieldList
            .filter(item => item.fixed != "hide")
            .sort((a, b) => {
                var type = ["left", "show", "right"];
                return type.indexOf(a.fixed) - type.indexOf(b.fixed);
            });
        console.log(dataSource);
        let columns = list.map(item => {
            var column = {
                title: item.tooltip ? (
                    <Tooltip placement="topLeft" title={item.tooltip} arrowPointAtCenter>
                        {item.title} <QuestionCircleOutlined />
                    </Tooltip>
                ) : (
                    item.title
                ),
                dataIndex: item.key,
                width: item.width,
                ellipsis: { showTitle: false },
                fixed: item.fixed,
                onHeaderCell: column => {
                    return {
                        width: column.width,
                        onResize: (e, { size }) => {
                            item.width = size.width;
                            this.setState({ config });
                        },
                    };
                },
            };
            maxWidth += column.width;
            column.render = (text, row, index) => {
                let rowSpan = 1,
                    isMerge = this.isMerge(item);
                if (isMerge) {
                    if (row.inde === 0 && row.listLength) {
                        rowSpan = row.listLength;
                    } else {
                        rowSpan = 0;
                    }
                }
                if (item.type == "function" || item.type == "js") {
                    try {
                        let children = item.type == "js" ? eval(item.key) : this[item.key](row, index);
                        return {
                            props: { rowSpan },
                            children,
                        };
                    } catch (e) {
                        console.error(new Error(`error expression ${item.key}`));
                    }
                } else {
                    return {
                        props: { rowSpan },
                        children: isMerge ? text : row?.[item.key],
                    };
                }
            };
            return column;
        });
        let rowSelection = config.page.isBatch && {
            onChange: (selectedIdList, selectedRows) => {
                this.setState({ selectedIdList, selectedRows });
            },
            fixed: true,
            preserveSelectedRowKeys: true,
            selectedRowKeys: selectedIdList,
            renderCell: (text, row, index, node) => {
                let rowSpan;
                if (row.inde === 0 && row.listLength) {
                    rowSpan = row.listLength;
                } else {
                    rowSpan = 0;
                }
                return {
                    props: {
                        rowSpan,
                    },
                    children: node,
                };
            },
        };
        //列表展开
        let expandable = !this.renderExpandRow
            ? null
            : {
                  rowExpandable: row => this.renderExpandRow(row, false) != null,
                  expandedRowRender: (row, _, __, expanded) => this.renderExpandRow(row, expanded),
              };
        return (
            <div className="search-list-table">
                <Table
                    rowSelection={rowSelection}
                    rowKey="rowKey"
                    size="small"
                    pagination={false}
                    expandable={expandable}
                    components={{ header: { cell: ResizableTitle } }}
                    dataSource={dataSource}
                    columns={columns}
                    scroll={{ x: maxWidth, y: table.height }}></Table>
            </div>
        );
    }

    transData() {
        let { dataList, mergeDataListKey } = this.state;
        let dataSource = [];
        dataList.map((item, index) => {
            let theItem = lib.clone(item);
            if (item[mergeDataListKey] && item[mergeDataListKey].length) {
                item[mergeDataListKey].map((ite, inde) => {
                    let pushItem = {
                        ...theItem,
                        ...ite,
                        inde,
                        id: item.id,
                        listLength: item?.[mergeDataListKey]?.length || 0,
                        rowKey: `${index}-${inde}`,
                    };
                    dataSource.push(pushItem);
                });
            } else {
                let pushItem = {
                    ...theItem,
                    id: item.id,
                    inde: 0,
                    listLength: 1,
                    rowKey: `${index}-${0}`,
                };
                dataSource.push(pushItem);
            }
        });
        return dataSource;
    }
}
