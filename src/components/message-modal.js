import React from "react";
import { Modal, message, Button } from "antd";

export function MessageModal({ visible, setVisible, messageContent }) {
    const handleCancel = () => {
        setVisible(false);
    };

    const handleCopy = () => {
        const copyDOM = document.querySelector(".copy-text");
        const range = document.createRange();
        window.getSelection().removeAllRanges();
        range.selectNode(copyDOM);
        window.getSelection().addRange(range); // 执行选中元素
        const successful = document.execCommand("copy");
        if (successful) {
            message.success("复制成功！");
        } else {
            message.warning("复制失败，请手动复制！");
        }
        // 移除选中的元素
        window.getSelection().removeAllRanges();
    };
    return (<>
        <Modal
            open={visible}
            title={"报文"}
            onCancel={handleCancel}
            footer={[
                <Button key="copy" onClick={handleCopy}>
                    复制内容
                </Button>,
                <Button type="primary" key="back" onClick={handleCancel}>
                    关闭
                </Button>,
            ]}>
            <div className="copy-text">{<p>{messageContent}</p>}</div>
        </Modal>
    </>);
}
