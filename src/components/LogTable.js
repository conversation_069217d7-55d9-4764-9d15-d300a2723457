import React, { useState, useEffect } from "react";
import { lib } from "react-single-app";
import { Table, Pagination, Button, Modal } from "antd";
import "./LogTable.less";

/**
 * 日志列表
 * @param prop columns：列描述  api 请求接口
 * @returns
 * @constructor
 */
function LogTable(prop) {
    const [logData, setLogData] = useState([]);
    const tableRef = React.useRef();
    const [tableHeight, setTableHeight] = useState(500);
    const [operateExtra, setOperateExtra] = useState([]);
    const [detailVisible, setDetailVisible] = useState(false);
    const [tableColumns, setTableColumns] = useState([
        {
            title: "操作类型",
            dataIndex: "operateType",
            width: 200,
        },
        {
            title: "操作说明",
            dataIndex: "productId",
            width: 200,
            render: (text, record) => {
                return record.operateDes && record.operateDes !== "" ? (
                    record.operateDes
                ) : record.operateExtra && record.operateExtra.length > 0 ? (
                    <Button
                        onClick={() => {
                            setOperateExtra(record.operateExtra);
                            setDetailVisible(true);
                        }}>
                        点击查看变动明细
                    </Button>
                ) : (
                    "无变动明细"
                );
            },
        },
        {
            title: "操作时间",
            dataIndex: "operateTime",
            width: 200,
        },
        {
            title: "操作人",
            dataIndex: "operator",
            width: 200,
        },
    ]);
    const columnsDetail = [
        {
            title: "名称",
            dataIndex: "field",
            width: 200,
        },
        {
            title: "修改前",
            dataIndex: "before",
            width: 200,
            render: (text, record, _) => {
                return text === "null" ? "" : text;
            },
        },
        {
            title: "修改后",
            dataIndex: "after",
            width: 200,
            render: (text, record, _) => {
                return text === "null" ? "" : text;
            },
        },
    ];
    let [pagination, setPagination] = useState({
        currentPage: 1,
        pageSize: 20,
        totalPage: 1,
    });

    function requestLogData() {
        lib.request({
            url: prop.api,
            data: Object.assign(pagination, prop.param),
            success: data => {
                setLogData(data.dataList);
                setPagination(data.page);
                setTimeout(resetSize, 100);
            },
        });
    }

    useEffect(() => {
        if (prop.insertColumns) {
            let { position, columns } = prop.insertColumns;

            tableColumns.splice(position, 0, ...columns);
            setTableColumns(tableColumns);
        }
        window.addEventListener("resize", resetSize, false);
        setTimeout(resetSize, 100);
        requestLogData();
    }, []);

    const resetSize = () => {
        resetSize.timer && clearTimeout(resetSize.timer);
        resetSize.timer = setTimeout(() => {
            if (tableRef.current?.offsetHeight) {
                let height = tableRef.current.offsetHeight - 47;
                setTableHeight(height);
            }
        }, 100);
    };
    return (
        (<div className={"log-table"}>
            <div className="table-panel" ref={tableRef}>
                <Table
                    dataSource={logData}
                    columns={prop.columns || tableColumns}
                    rowKey="id"
                    size={"middle"}
                    scroll={{ y: tableHeight }}
                    pagination={false}
                />
            </div>
            <div className={"pagination-panel"}>
                <Pagination
                    showQuickJumper
                    showSizeChanger
                    pageSize={pagination.pageSize}
                    pageSizeOptions={["10", "20", "30", "40", "50", "100", "200"]}
                    current={pagination.currentPage}
                    total={pagination.totalCount}
                    onChange={(page, pageSize) => {
                        pagination.currentPage = page;
                        if (pagination.pageSize != pageSize) {
                            pagination.pageSize = pageSize;
                            pagination.currentPage = 1;
                        }
                        setPagination(pagination);
                        requestLogData();
                    }}
                    showTotal={total => `总共 ${total} 条`}
                />
            </div>
            <Modal
                title={"明细"}
                okText={"关闭"}
                width={800}
                destroyOnClose
                open={detailVisible}
                cancelButtonProps={{ style: { display: "none" } }}
                onCancel={() => setDetailVisible(false)}
                onOk={() => setDetailVisible(false)}>
                <Table
                    dataSource={operateExtra}
                    columns={columnsDetail}
                    scroll={{ y: "320px" }}
                    pagination={false}
                    rowKey={record => JSON.stringify(record)}
                />
            </Modal>
        </div>)
    );
}

export default LogTable;
