import React, { useRef } from "react";
import { ConfigFormCenter, lib } from "react-single-app";
import { Modal } from "antd";

/**
 * 批量手动处理弹框
 * @param detail
 * @returns
 * @constructor
 */
export default function BatchManualProcessingModal({ ids, config, visible, close, submitUrl, type }) {
    const ref = useRef();
    const handleOk = () => {
        ref.current.submitForm();
    };
    function handleClose(success) {
        close(success);
    }

    const handleCancel = () => {
        handleClose();
    };
    const onConfigLoadSuccess = config => {
        config.baseInfo.children.map(item => {
            ref.current.initSelect(item, { type: type });
        });
    };
    const beforeSubmit = values => {
        values.idList = ids;
        return values;
    };
    const onSubmitSuccess = data => {
        handleClose(true);
    };
    const onSinglesSelectChange = desc => {};
    return (
        (<Modal
            title={"批量手动操作"}
            zIndex={1000}
            destroyOnClose={true}
            open={visible}
            onOk={handleOk}
            onCancel={handleCancel}>
            <ConfigFormCenter
                ref={ref}
                onSinglesSelectChange={onSinglesSelectChange}
                confData={config}
                onConfigLoadSuccess={onConfigLoadSuccess}
                submitUrl={submitUrl}
                onSubmitSuccess={onSubmitSuccess}
                beforeSubmit={beforeSubmit}
            />
        </Modal>)
    );
}
