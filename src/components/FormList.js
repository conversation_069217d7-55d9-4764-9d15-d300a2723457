import React, { useState, useEffect } from "react";
import { Select, Form, Modal, Input, Checkbox, DatePicker, Upload, Button, InputNumber, Switch, message } from "antd";
import moment from "moment";
import { Uploader } from "react-single-app";
import "./FormList.less";
import { CopyOutlined } from "@ant-design/icons";
import { copyVal, isEmptyValue } from "@dt/util";
const FormItem = Form.Item;
const Option = Select.Option;
const { TextArea } = Input;
const { RangePicker } = DatePicker;

/**
 * 新建弹窗或者编辑弹窗
 *
 * @param form form
 * @param configList 配置列表
 * ```
 * {type: 'SELECT', labelKey: '', labelName: '', xhr: '请求链接', list: [], placeholder: '', required: false, message: '请输入', }
 * ```
 * @param title
 * @param visible 显示/隐藏弹窗
 * @param detail 当前编辑对象
 */

function FormList({ configList, formItemFloat, children, detail, form }) {
    function switchType(item) {
        switch (item.type) {
            case "INPUT":
                return (
                    <Input
                        disabled={item.disabled}
                        placeholder={item.placeholder || `请输入${item.labelName}`}
                        maxLength={item.maxLength || 9999}
                        autoComplete={item.autoComplete || null}
                    />
                );
            case "INPUTNUMBER":
                return (
                    <InputNumber
                        disabled={item.disabled}
                        placeholder={item.placeholder || `请输入${item.labelName}`}
                        max={item.max}
                        min={item.min}
                    />
                );
            case "SELECT":
                return (
                    <Select
                        onChange={item.onChange ? e => item.onChange(e, form) : e => {}}
                        mode={item.mode}
                        style={{ width: "100%" }}
                        showSearch
                        disabled={item.disabled}
                        optionFilterProp="children">
                        {item.list?.length &&
                            item.list.map((ite, index) => {
                                return (
                                    <Option value={ite.id || ite.key} key={index}>
                                        {item.together ? `${ite.id}-${ite.name}` : ite.name || ite.value}
                                    </Option>
                                );
                            })}
                    </Select>
                );
            case "CHECKBOX":
                return (
                    <Checkbox.Group>
                        {item.list.length &&
                            item.list.map((ite, index) => {
                                return (
                                    <Checkbox value={ite.value} key={index}>
                                        {ite.name}
                                    </Checkbox>
                                );
                            })}
                    </Checkbox.Group>
                );
            case "DATE":
                return <DatePicker showTime />;
            case "DATERANGE":
                return <RangePicker showTime />;
            case "TEXTAREA":
                return <TextArea disabled={item.disabled} maxLength={item.maxLength || null} />;
            case "SWITCH":
                return <Switch />;
            case "FILE":
                return (
                    <Uploader
                        onUploadStart={() => {
                            console.log("开始上传 ");
                        }}
                        onUploadEnd={src => {
                            console.log(src);
                            form.setFieldsValue({
                                [item.labelKey]: src,
                            });
                        }}
                        style={{ width: "60px", height: "40px" }}
                        src={""}
                    />
                );
            case "PASSWORD":
                return <Input.Password />;
            default:
                return null;
        }
    }
    function renderFormItem(item, rules) {
        switch (item.type) {
            case "FILE":
                return (
                    <FormItem
                        label={item.labelName}
                        labelCol={{ span: item.labelCol || 8 }}
                        name={item.labelKey}
                        tooltip={item.tooltip}
                        rules={rules}>
                        {switchType(item)}
                    </FormItem>
                );
            case "TEXT":
                return (
                    <FormItem
                        tooltip={item.tooltip}
                        label={item.labelName}
                        labelCol={{ span: item.labelCol || 8 }}
                        {...item.extra}>
                        {detail ? (item.render ? item.render(detail) : detail[item.labelKey]) : null}
                    </FormItem>
                );
            default:
                return item.hide !== true ? (
                    <FormItem
                        label={
                            <>
                                {item.needCopy ? (
                                    <>
                                        {item.labelName}
                                        <CopyOutlined
                                            style={{ fontSize: "16px", color: "#1890ff", marginLeft: 8 }}
                                            onClick={() => {
                                                const val = form.getFieldValue(item.labelKey);
                                                if (val) {
                                                    copyVal(val);
                                                } else {
                                                    message.error("没有可复制内容");
                                                }
                                            }}
                                        />
                                    </>
                                ) : (
                                    item.labelName
                                )}
                            </>
                        }
                        name={item.labelKey}
                        rules={rules}
                        tooltip={item.tooltip}
                        wrapperCol={{ span: item.wrapperCol || 16 }}
                        labelCol={{ span: item.labelCol || 8 }}
                        help={item.help || null}>
                        {switchType(item)}
                    </FormItem>
                ) : null;
        }
    }
    return (
        <div className={formItemFloat ? "form-list-component form-item-float" : "form-list-component"}>
            {configList.map((item, index) => {
                let rules = item.rules || [
                    {
                        required: item.required,
                        message: item.message || `请${item.type === "SELECT" ? "选择" : "输入"}${item.labelName}`,
                    },
                ];
                if (item.max) {
                    rules[0].max = item.max;
                }
                return <React.Fragment key={index}>{renderFormItem(item, rules)}</React.Fragment>;
            })}
            {children}
        </div>
    );
}

export default FormList;
