import React from "react";
import { Descriptions } from "antd";

export default function ({ configList, title, detail = {}, children }) {
    return (
        <div className="form-list-component">
            <Descriptions title={title} column={{ xxl: 3, xl: 3, lg: 3, md: 3, sm: 3, xs: 1 }}>
                {configList.map((item, index) => {
                    let children = item.render ? item.render(detail) : detail[item.labelKey];
                    return (
                        <Descriptions.Item label={item.label || item.labelName} key={index} span={item.span || 1}>
                            {children || "-"}
                        </Descriptions.Item>
                    );
                })}
                {children}
            </Descriptions>
        </div>
    );
}
