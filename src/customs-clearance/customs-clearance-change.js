import React, { useState, useEffect } from "react";
import { lib, event, SearchList, getConfigDataUtils } from "react-single-app";
import { Button, Space, message, Modal, Table, Input, Select, Form, Tooltip } from "antd";
import axios from "axios";
import MergeTdSearchList from "../components/MergeTdSearchList";
import { CopyOutlined } from "@ant-design/icons";

const FormItem = Form.Item;
const { Option } = Select;
export default class extends MergeTdSearchList {
    constructor(props) {
        super(props);
        this.state.unitList = [];
        this.state.mergeKeyList = ["productId", "goodsSeqNo", "hsCode"];
        this.state.mergeDataListKey = "declareItemVos";
    }
    getConfig() {
        return axios.get(getConfigDataUtils.getDataUrlByDataId(296)).then(e => e.data.data);
    }

    componentDidMount() {
        let requestMap = [["/ccs/customs/listUom", "unitList"]];
        requestMap.map(([url, key]) => {
            lib.request({
                url,
                success: res => {
                    this.setState({
                        [key]: res || [],
                    });
                },
            });
        });
    }

    getBatchId = () => {
        let { selectedRows } = this.state;
        console.log(selectedRows);
        return selectedRows.reduce((prev, curr) => [...prev, curr.id], []);
    };

    getTypeConfig = type => {
        const map = {
            declare: ["/ccs/modify/declare/batch/declare", "申报"],
            delete: ["/ccs/modify/declare/batch/delete", "删除"],
            cancel: ["/ccs/modify/declare/batch/cancel", "取消"],
        };
        return map[type];
    };

    batchHandle = type => {
        let [url, text] = this.getTypeConfig(type),
            idList = this.getBatchId();
        if (idList.length == 0) {
            message.warning("请选择数据");
            return;
        }
        lib.request({
            url,
            data: {
                idList,
            },
            needMask: true,
            success: res => {
                message.success(`批量${text}成功`);
                this.load();
            },
        });
    };

    renderLeftOperation() {
        return (
            <Space>
                <Button onClick={this.batchHandle.bind(this, "declare")} type="primary">
                    批量申报
                </Button>
                <Button onClick={this.batchHandle.bind(this, "delete")}>批量删除</Button>
                <Button onClick={this.batchHandle.bind(this, "cancel")}>批量取消</Button>
            </Space>
        );
    }

    myOperation(row) {
        let { unitList } = this.state;
        const showStatusList = [10, 40, 50];
        return (
            <Space>
                {showStatusList.some(item => item == row.status) && (
                    <EditModal row={row} onOk={() => this.load()} unitList={unitList} />
                )}
            </Space>
        );
    }
}

function EditModal({ row = {}, onOk, unitList }) {
    const [visible, visibleSet] = useState(false);
    const [map, setMap] = useState(new Map());
    const [form] = Form.useForm();

    useEffect(() => {
        if (visible) {
            form.setFieldsValue(row.declareItemVos);
            let productIds = row.declareItemVos?.reduce((prev, curr) => [...prev, curr.productId], []);
            if (!productIds || !productIds.length) return;
            lib.request({
                url: "/ccs/invenorder/product/info/list",
                data: {
                    productIds,
                    areaBookId: row.accountBookId,
                },
                needMask: true,
                success: res => {
                    let map = new Map();
                    res.map(item => {
                        map.set(item.productId, item.infoList);
                    });
                    setMap(map);
                },
            });
        } else {
            form.resetFields();
        }
    }, [visible]);

    let copy = value => {
        let transfer = document.createElement("input");
        document.body.appendChild(transfer);
        transfer.value = value; // 这里表示想要复制的内容
        transfer.focus();
        transfer.select();
        if (document.execCommand("copy")) {
            document.execCommand("copy");
        }
        transfer.blur();
        message.success("复制成功");
        document.body.removeChild(transfer);
    };

    function editModalCancel() {
        visibleSet(false);
    }
    function editModalOk() {
        form.validateFields().then(values => {
            let itemReqVos = [];
            console.log(values);
            Object.values(values).map(item => {
                item.firstAmount = String(item.firstAmount);
                if (item.secondAmount) {
                    item.secondAmount = String(item.secondAmount);
                } else {
                    item.secondAmount = null;
                }
                if (!item.secondUnit) {
                    item.secondUnit = "";
                }
                itemReqVos.push(item);
            });
            let errorItem = itemReqVos.find(
                item => (item.secondAmount && !item.secondUnit) || (!item.secondAmount && item.secondUnit),
            );
            if (errorItem?.goodsSeqNo) {
                message.warning(`商品料号为${errorItem.goodsSeqNo}的表体，请输入第二法定数量和第二法定计量单位`);
                return;
            }
            lib.request({
                url: "/ccs/modify/declare/item/update",
                data: { list: itemReqVos },
                needMask: true,
                success: res => {
                    message.success("编辑表体成功");
                    visibleSet(false);
                    onOk();
                },
            });
        });
    }

    const columns = [
        {
            title: "商品料号",
            dataIndex: "productId",
            width: 200,
            render: (text, record, index) => {
                return (
                    <div>
                        <FormItem name={[index, "id"]} noStyle hidden>
                            <Input />
                        </FormItem>
                        {text}
                    </div>
                );
            },
        },
        {
            title: "金二序号",
            dataIndex: "goodsSeqNo",
            width: 120,
            render: (text, record, index) => {
                return (
                    <FormItem name={[index, "goodsSeqNo"]} noStyle>
                        <Select style={{ width: 100 }}>
                            {map.get(record.productId)?.map(item => (
                                <Option value={item.id} key={item.id}>
                                    {item.name}
                                </Option>
                            ))}
                        </Select>
                    </FormItem>
                );
            },
        },
        {
            title: "HS编码",
            dataIndex: "hsCode",
            width: 160,
            render: (text, record, index) => {
                return (
                    <FormItem name={[index, "hsCode"]} noStyle>
                        <Input />
                    </FormItem>
                );
            },
        },
        {
            title: "法定数量",
            dataIndex: "firstAmount",
            width: 150,
            render: (text, record, index) => {
                return (
                    <FormItem name={[index, "firstAmount"]} noStyle>
                        <Input />
                    </FormItem>
                );
            },
        },
        {
            title: "法定计量单位",
            dataIndex: "firstUnit",
            width: 150,
            render: (text, record, index) => {
                return (
                    <FormItem name={[index, "firstUnit"]} noStyle>
                        <Select style={{ width: 100 }} allowClear>
                            {unitList.map(item => (
                                <Option value={item.id} key={item.id}>
                                    {item.id}:{item.name}
                                </Option>
                            ))}
                        </Select>
                    </FormItem>
                );
            },
        },
        {
            title: "第二法定数量",
            dataIndex: "secondAmount",
            width: 150,
            render: (text, record, index) => {
                return (
                    <FormItem name={[index, "secondAmount"]} noStyle>
                        <Input />
                    </FormItem>
                );
            },
        },
        {
            title: "第二法定计量单位",
            dataIndex: "secondUnit",
            width: 150,
            render: (text, record, index) => {
                return (
                    <FormItem name={[index, "secondUnit"]} noStyle>
                        <Select style={{ width: 130 }} allowClear>
                            {unitList.map(item => (
                                <Option value={item.id} key={item.id}>
                                    {item.id}:{item.name}
                                </Option>
                            ))}
                        </Select>
                    </FormItem>
                );
            },
        },
    ];

    return (
        <>
            <Modal
                open={visible}
                title="修改表体"
                onOk={editModalOk}
                onCancel={editModalCancel}
                width={1000}
                forceRender>
                <p>
                    申报单号：<span className="link">{row.declareNo}</span>
                    <CopyOutlined onClick={() => copy(row.declareNo)} />
                </p>
                <Form form={form} layout="horizontal">
                    <Table
                        tableLayout={"fixed"}
                        dataSource={row.declareItemVos}
                        columns={columns}
                        scroll={{ x: columns.reduce((pre, curr) => (pre += curr.width), 0) }}
                    />
                </Form>
            </Modal>
            <span className="link" onClick={() => visibleSet(!visible)}>
                修改表体
            </span>
        </>
    );
}
