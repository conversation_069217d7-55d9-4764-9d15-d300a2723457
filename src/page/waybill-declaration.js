import React from "react";
import { Button, Input, message } from "antd";
import { ConfigCenter, lib } from "react-single-app";
import moment from "moment";
class App extends ConfigCenter {
    constructor(props) {
        super(props);
        this.state.detail = {};
    }
    componentDidMount() {}

    getCheckedRows() {
        return this.state.dataList.filter(item => item.checked);
    }

    renderLeftOperation() {
        return (
            <React.Fragment>
                <Button type="primary" onClick={() => this.pushAgain("DECLARE_LOGISTICS")}>
                    运单重推
                </Button>
            </React.Fragment>
        );
    }
    pushAgain(action) {
        let list = this.getCheckedRows();
        if (list.length !== 0) {
            let ids = [];
            list.map(item => {
                ids += `${item.orderId},`;
            });
            lib.request({
                url: "/ccs/order/reDeclare",
                data: {
                    action,
                    ids: ids.slice(0, -1),
                },
                method: "POST",
                needMask: true,
                success: res => {
                    if (res.errorMessage) {
                        message.error(res.errorMessage);
                    } else {
                        message.success("重推成功");
                        this.load(true);
                    }
                },
            });
        } else {
            message.warning("请选择数据");
        }
    }
}

export default App;
