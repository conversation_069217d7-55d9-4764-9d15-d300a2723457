/* CDN 服务仅供平台体验和调试使用，平台不承诺服务的稳定性，企业客户需下载字体包自行发布使用并做好备份。 */
@font-face {
    font-family: "iconfont-component"; /* Project id 3858387 */
    src: url("//at.alicdn.com/t/c/font_3858387_s36ul6hbt9.woff2?t=1675215687645") format("woff2"),
        url("//at.alicdn.com/t/c/font_3858387_s36ul6hbt9.woff?t=1675215687645") format("woff"),
        url("//at.alicdn.com/t/c/font_3858387_s36ul6hbt9.ttf?t=1675215687645") format("truetype"),
        url("//at.alicdn.com/t/c/font_3858387_s36ul6hbt9.svg?t=1675215687645#iconfont-component") format("svg");
}

.all-link-monitoring {
    padding: 8px 16px;
    box-sizing: border-box;
    max-height: 100% !important;
    .ant-tabs,
    .ant-tabs-content {
        height: 100%;
    }
    .config-center-v3 .my-draggable .draggable-box {
        position: inherit;
    }
    .ant-table-body {
        max-height: 100% !important;
    }
    .edit-icon {
        font-size: 16px;
        color: #1890ff;
        cursor: pointer;
        font-family: "iconfont-component";
        margin-right: 3px;
        position: relative;
        top: 1px;
    }
}
