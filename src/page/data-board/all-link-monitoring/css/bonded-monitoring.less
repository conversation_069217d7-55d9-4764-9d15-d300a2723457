.all-link-monitoring {
    .platform-bonded {
        // overflow-x: auto;
        margin-bottom: 16px;
        .platform-bonded-top {
            margin-bottom: 16px;
            .ant-checkbox-group {
                .isWarning {
                    .ant-checkbox-checked .ant-checkbox-inner {
                        background-color: #4587e7;
                        border-color: #4587e7;
                    }
                }
                .isOvertime {
                    .ant-checkbox-checked .ant-checkbox-inner {
                        background-color: rgb(246, 106, 95);
                        border-color: rgb(246, 106, 95);
                    }
                }
                .isNormal {
                    .ant-checkbox-checked .ant-checkbox-inner {
                        background-color: #f5ad1d;
                        border-color: #f5ad1d;
                    }
                }
            }
        }
        .platform {
            // min-width: 4000px;
            // overflow-x: auto;
            margin-bottom: 16px;
            position: relative;
            .leftButton,
            .rightButton {
                position: absolute;
                border: none;
                height: 20px;
                width: 20px;
                transition: 1s;
                border-radius: 50%;
                background-color: #1890ff;
                position: absolute;
                top: 40%;
                z-index: 10;
                transform: translateY(-50%);
                opacity: 0.5;
            }
            // .slick-list {
            //     margin: 0 16px;
            // }
            .leftButton:hover,
            .rightButton:hover {
                opacity: 1;
            }
            .ant-radio-button-wrapper {
                width: 176px;
                height: 119px;
                line-height: 28px;
                padding: 0;
            }
            .radio-btnn {
                // margin-right: 16px;
                border-radius: 2px;
                .ten_thousand {
                    margin-left: 2px;
                    font-size: 12px;
                    color: #333333;
                    vertical-align: middle;
                }
            }
            .bonded {
                height: 119px;
                .bonded-top {
                    width: 176px;
                    height: 27px;
                    line-height: 27px;
                    border-bottom: 1px solid #e4e4e4;
                    .bonded-top-left {
                        margin-left: 10px;
                        font-size: 14px;
                        font-family: PingFangSC-Regular, PingFang SC;
                        font-weight: 400;
                        color: rgba(0, 0, 0, 0.65);
                    }
                    .bonded-top-cen {
                        font-size: 16px;
                        margin-left: 15px;
                        margin-right: 18px;
                        font-family: PingFangSC-Medium, PingFang SC;
                        font-weight: 500;
                        color: rgba(0, 0, 0, 0.1);
                    }
                    .bonded-top-rig {
                        font-size: 14px;
                        // margin-left: 18px;
                    }
                }
                .bonded-top-color {
                    background: rgba(24, 144, 255, 0.6);
                    .bonded-top-left {
                        margin-left: 10px;
                        font-size: 14px;
                        font-family: PingFangSC-Regular, PingFang SC;
                        font-weight: 400;
                        color: #ffffff;
                    }
                    .bonded-top-cen {
                        font-size: 16px;
                        margin-left: 15px;
                        margin-right: 18px;
                        font-weight: 500;
                        color: rgba(255, 255, 255, 0.26);
                    }
                    .bonded-top-rig {
                        font-size: 14px;
                        font-family: PingFangSC-Regular, PingFang SC;
                        font-weight: 400;
                        line-height: 22px;
                        color: #ffffff;
                        margin-left: 19px;
                    }
                }
            }
        }
    }
    .ant-radio-group-solid .ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled) {
        border-top: 3px solid #0268ff;
    }
    .ant-radio-group-solid .ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled):focus-within {
        box-shadow: none;
    }
}
