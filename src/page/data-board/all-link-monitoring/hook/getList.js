import React, { useState, useEffect } from "react";
import { getWarehouse, getChannel, getCodeItem } from "../http/index";

function useList() {
    const [warehouseList, setWarehouseList] = useState([]);
    const [channelList, setChannelList] = useState([]);
    const [codeItem, setCodeItem] = useState([]);
    useEffect(() => {
        getChannel().then(res => setChannelList(formatList(res)));
        getWarehouse().then(res => setWarehouseList(formatList(res)));
        getCodeItem().then(setCodeItem);
    }, []);

    function formatList(list) {
        return list.map(item => {
            item.value = item.id;
            item.label = item.name;
            return item;
        });
    }

    return {
        warehouseList,
        channelList,
        codeItem,
    };
}

export { useList };
