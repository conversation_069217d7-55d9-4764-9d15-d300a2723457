import React from "react";
import { BondedMonitoring, BigTradeMonitoring } from "./component/index";
import { Tabs } from "antd";
import "./css/all-link-monitoring.less";

const AllLinkMonitoring = () => {
    return (
        <div className="all-link-monitoring">
            <Tabs defaultActiveKey="1">
                <Tabs.TabPane tab="自营仓储" key="1">
                    <BondedMonitoring tradeType={1} />
                </Tabs.TabPane>
                <Tabs.TabPane tab="平台仓储" key="2">
                    {/* 前期报税和大茂搜索条件字段都是一样的,就以一个组件,后期看产品需求 */}
                    <BigTradeMonitoring tradeType={2} />
                </Tabs.TabPane>
            </Tabs>
        </div>
    );
};

export default AllLinkMonitoring;
