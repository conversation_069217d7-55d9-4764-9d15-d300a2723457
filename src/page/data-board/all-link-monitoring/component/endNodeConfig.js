import { PlusOutlined } from "@ant-design/icons";
import { Button, Form, Modal } from "antd";
import React, { useEffect, useMemo } from "react";
import { useList } from "../hook/getList";
import { getEndCongigList } from "../http/index";
import FormListContent from "./formListContent";

function EndNodeConfig() {
    const [form] = Form.useForm();
    const [visible, setVisible] = React.useState(false);
    const [detail, setDetail] = React.useState([]);
    const list = useList();

    useEffect(() => {
        if (visible) {
            getConfig();
        }
        return () => setDetail([]);
    }, [visible]);

    function getConfig() {
        getEndCongigList().then(data => {
            if (data.length) {
                setDetail(data);
                form.setFieldsValue({ FormList: data });
            } else {
                form.setFieldsValue({ FormList: [{}] });
            }
        });
    }

    function onCancel() {
        setVisible(false);
        form.resetFields();
    }

    function MemoComponent(props) {
        let component = useMemo(() => <FormListContent {...props} />, [detail]);
        return component;
    }

    return (<>
        <a onClick={() => setVisible(true)}>
            <span className="edit-icon">&#xe68d;</span>
            终节点配置
        </a>
        <Modal
            open={visible}
            title="终态节点设置"
            onCancel={onCancel}
            footer={<Button onClick={onCancel}>关闭</Button>}
            destroyOnClose
            width={800}>
            <Form form={form}>
                <Form.List name="FormList" rules={[{ required: true }]}>
                    {(fields, { add, remove }) => (
                        <>
                            {fields.map(({ name }, index) => (
                                <>
                                    <MemoComponent
                                        index={index}
                                        remove={remove}
                                        name={name}
                                        form={form}
                                        list={list}
                                        detail={detail[index]}
                                        onCancel={onCancel}
                                        callback={getConfig}
                                    />
                                </>
                            ))}
                            <Button block onClick={() => add()}>
                                <PlusOutlined />
                                添加方案
                            </Button>
                        </>
                    )}
                </Form.List>
            </Form>
        </Modal>
    </>);
}
export default EndNodeConfig;
