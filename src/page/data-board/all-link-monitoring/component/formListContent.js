import { DeleteFilled, EditFilled } from "@ant-design/icons";
import { Button, Form, Select, Modal, message } from "antd";
import React, { useEffect, useMemo, useState } from "react";
import { deleteList, submit } from "../http/index";
import IndeterminateCheckbox from "./IndeterminateCheckbox";
import { event } from "react-single-app";

function FormListContent({ index, remove, form, name, list, detail, callback, onCancel }) {
    const [editable, setEditable] = useState(false);
    const [editableActive, setEditableActive] = useState(false);
    const { warehouseList, channelList, codeItem } = list;

    useEffect(() => {
        if (detail) {
            setEditable(true);
        }
        return () => {
            setEditable(false);
            setEditableActive(false);
        };
    }, [detail]);

    function header(index) {
        return (
            <div className="charge-schcemeTop">
                <div className="charge-schcemeTop-title">{`方案${index + 1}`}</div>
                {operatorWrap}
            </div>
        );
    }

    function deleteHandle() {
        let data = {
            programNo: detail?.programNo,
        };
        if (!data.programNo) {
            const { FormList } = form.getFieldsValue();
            FormList.splice(index, 1);
            form.setFieldsValue({ FormList: FormList });
            return;
        }
        let modal = Modal.confirm({
            title: "删除",
            content: "是否确认删除？",
            onOk: () => {
                deleteList(data).then(res => onCancel());
            },
            onCancel: () => modal.destroy(),
            okText: "确认删除",
            cancelText: "取消",
        });
    }

    function content() {
        return (
            <>
                <Form.Item name={[name, "finalNodeId"]} label="终节点" rules={[{ required: true }]}>
                    <Select
                        style={{ width: 200 }}
                        showSearch
                        allowClear
                        placeholder="请选择终节点"
                        optionFilterProp="children"
                        disabled={editable && !editableActive}>
                        {codeItem.map((item, i) => (
                            <Select.Option value={item.id} key={i}>
                                {item.name}
                            </Select.Option>
                        ))}
                    </Select>
                </Form.Item>
                <Form.Item name={[name, "warehouseCodeList"]} label="实体仓" rules={[{ required: true }]}>
                    <IndeterminateCheckbox options={warehouseList} disabled={editable && !editableActive} />
                </Form.Item>
                <Form.Item name={[name, "channelCodeList"]} label="渠道" rules={[{ required: true }]}>
                    <IndeterminateCheckbox options={channelList} disabled={editable && !editableActive} />
                </Form.Item>
            </>
        );
    }

    async function submitData() {
        const values = await form.validateFields([
            ["FormList", index, "finalNodeId"],
            ["FormList", index, "warehouseCodeList"],
            ["FormList", index, "channelCodeList"],
        ]);
        const { FormList } = values;
        let data = {
            programNo: detail?.programNo,
            finalNodeId: FormList[index].finalNodeId,
            warehouseCodeList: FormList[index].warehouseCodeList,
            channelCodeList: FormList[index].channelCodeList,
        };
        submit(data).then(res => {
            message.success("提交成功");
            callback();
            setEditableActive(false);
            setEditable(true);
            event.emit("onSearchResetInit");
        });
    }

    const operatorWrap = useMemo(() => {
        return (
            <>
                {!editable || editableActive ? (
                    <div>
                        <div>
                            <Button size="small" type="primary" onClick={submitData}>
                                提交
                            </Button>
                            <DeleteFilled className="charge-detele-btn" onClick={() => deleteHandle()} />
                        </div>
                    </div>
                ) : (
                    <div>
                        <EditFilled style={{ cursor: "pointer" }} onClick={() => setEditableActive(true)} />
                        <DeleteFilled className="charge-detele-btn" onClick={() => deleteHandle()} />
                    </div>
                )}
            </>
        );
    }, [editable, editableActive]);

    return (
        <>
            {header(index, remove)}
            {content(index)}
        </>
    );
}

export default FormListContent;
