import React, { useEffect, useState, useCallback, useRef, useMemo } from "react";
import ReactEcharts from "echarts-for-react";

const BondedEcharsBar = ({ item, checkedValue, barClick }) => {
    const getData = useMemo(() => {
        return () => {
            if (checkedValue.length > 0) {
                let dataList = [];
                checkedValue.map(element => {
                    if (element === "isOvertime") {
                        dataList.push({
                            value: item.overtimeCount,
                            itemStyle: {
                                color: "rgb(246,106,95)",
                            },
                            type: "isOvertime",
                        });
                    } else if (element === "isNormal") {
                        dataList.push({
                            value: item.normalCount,
                            itemStyle: {
                                color: "#F5AD1D",
                            },
                            type: "isNormal",
                        });
                    }
                });
                let newList = dataList.filter(item => item.value !== 0);
                return option(newList || []);
            } else {
                let newDate = [item.warningCount, item.overtimeCount, item.normalCount].filter(item => item !== 0);
                return option(newDate);
            }
        };
    }, [checkedValue, item]);
    function option(data) {
        return {
            xAxis: {
                type: "category",
                // boundaryGap: false,
                splitLine: {
                    show: false,
                },
                axisLine: {
                    show: false,
                },
                show: false,
                data: [],
            },
            yAxis: {
                splitLine: {
                    show: false,
                },
                axisLine: {
                    show: false,
                },
                show: false,
                type: "value",
            },
            max: 20,
            series: [
                {
                    data: data,
                    type: "bar",
                    barWidth: "90%",
                    // itemStyle: {
                    //     normal: {
                    //         color: function (params) {
                    //             const colorList = ["#4587E7", "#35AB33", "#F5AD1D"];
                    //             return colorList[params.dataIndex];
                    //         },
                    //     },
                    // },
                    label: {
                        show: true,
                        position: "top",
                        textStyle: {
                            fontSize: 12,
                            color: "auto",
                        },
                    },
                    backgroundStyle: {
                        color: "rgba(180, 180, 180, 0.2)",
                    },
                },
            ],
            grid: {
                show: false,
                top: "20%",
                right: "5%",
                bottom: "2%",
                left: "4%",
            },
        };
    }

    const onEvents = useMemo(() => {
        return {
            click: (selects, echarts, e) => {
                barClick && barClick(selects.data.type);
            },
        };
    }, []);

    return <ReactEcharts option={getData()} style={{ height: 90, maxHeight: 90 }} onEvents={onEvents} />;
};

export default BondedEcharsBar;
