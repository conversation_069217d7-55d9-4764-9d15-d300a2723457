import React, { useEffect, useState } from "react";
import { lib } from "react-single-app";
import { Button, Col, Form, Input, Row, Table, message, Tag } from "antd";
import "../css/order-inquiry-detail.less";
import { Copytext } from "../component/index";
import MonitoringModal from "../../component/monitoringModal";
import copy from "copy-to-clipboard";
import { CopyOutlined } from "@ant-design/icons";

const OrderInquiryDetail = () => {
    const [detail, setDetail] = useState({});
    const [dataSource, setDataSource] = useState([]);
    const [value, setvalue] = useState({ globalSystemSn: lib.getParam("globalSystemSn") });
    useEffect(() => {
        lib.request({
            url: "/ares-admin/channelOrder/detailV2",
            data: { ...value },
            needMask: true,
            success: res => {
                setDetail(res);
            },
        });
        lib.request({
            url: "/ares-admin/orderLog/queryList",
            data: { ...value },
            needMask: true,
            success: res => {
                setDataSource(res);
            },
        });
    }, [value]);
    const columns = [
        {
            title: "开始状态",
            dataIndex: "statusDesc",
            key: "statusDesc",
        },
        {
            title: "创建时间",
            dataIndex: "createTime",
            key: "createTime",
        },
        {
            title: "操作说明",
            dataIndex: "action",
            key: "action",
        },
        // highlightType 高亮信息类型（英文字符串）用于前端设置颜色
        // highlightInfo 高亮信息内容 用于前端设置文本
        {
            title: "备注说明",
            dataIndex: "remark",
            key: "remark",
            render: (text, record, index) => {
                return (
                    <div>
                        <span>{text}</span>
                        {record.highlightInfo !== null ? (
                            <Tag color={record.highlightType}>{record.highlightInfo}</Tag>
                        ) : (
                            ""
                        )}
                        <></>
                    </div>
                );
            },
        },
    ];
    return (
        <div>
            <AdvancedSearchForm setvalue={setvalue} />
            <div className="order-center">
                <div className="order-detail">
                    <div className="detail-title">
                        <img
                            className="detail-img"
                            src="https://dante-img.oss-cn-hangzhou.aliyuncs.com/67646599531.svg"
                        />
                    </div>
                    <div className="detail-content">
                        <h2 className="detail-content-h2">
                            订单编号：
                            <Copytext text={detail?.globalSystemSn} />
                        </h2>
                        <div className="detail-content-footer">
                            <span className="footer-span1">
                                外部订单编号：
                                <Copytext text={detail.outOrderNo} />
                            </span>
                            <span className="footer-span2">
                                销售订单号： <Copytext text={detail.saleOrderNo} />
                            </span>
                            <span className="footer-span3">
                                订单状态：<span>{detail.statusDesc}</span>
                            </span>
                            <span className="footer-span4">
                                <span style={{ marginRight: 16 }}> 运单号:</span>
                                <MonitoringModal row={value} itemCode={detail?.expressNo} style={{ marginLeft: 8 }} />
                                <CopyOutlined
                                    style={{
                                        color: "#000000",
                                        marginLeft: 16,
                                        fontSize: 14,
                                        lineHeight: "24px",
                                    }}
                                    onClick={() => {
                                        copy(detail?.expressNo);
                                        message.success("复制成功");
                                    }}
                                />
                            </span>
                        </div>
                    </div>
                </div>
            </div>
            <div className="order_table">
                <Table dataSource={dataSource} columns={columns} pagination={false} size="small" />
            </div>
        </div>
    );
};

const AdvancedSearchForm = props => {
    const [form] = Form.useForm();
    const onFinish = values => {
        props.setvalue(values);
    };
    // 重置
    const resetFields = () => {
        form.resetFields();
        props.setvalue({ globalSystemSn: lib.getParam("globalSystemSn") });
    };
    const getFields = () => {
        return (
            <Col span={8}>
                <Form.Item name={"globalSystemSn"} label="查询条件">
                    <Input placeholder="请输入外部订单号/运单号" />
                </Form.Item>
            </Col>
        );
    };
    return (
        <Form form={form} name="advanced_search" className="ant-advanced-search-form" onFinish={onFinish}>
            <Row gutter={24}>
                {getFields()}
                <Button type="primary" htmlType="submit" style={{ marginRight: 16 }}>
                    查询
                </Button>
                <Button onClick={resetFields}>重置</Button>
            </Row>
        </Form>
    );
};

export default OrderInquiryDetail;
