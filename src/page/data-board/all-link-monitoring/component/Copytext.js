import React, { useMemo } from "react";
import copy from "copy-to-clipboard";
import { message } from "antd";
import { CopyOutlined } from "@ant-design/icons";
const Copytext = ({ text }) => {
    return (
        <span>
            {text && (
                <>
                    <span>{text}</span>
                    <CopyOutlined
                        style={{
                            color: "#000000",
                            marginLeft: 16,
                            fontSize: 14,
                        }}
                        onClick={() => {
                            copy(text);
                            message.success("复制成功");
                        }}
                    />
                </>
            )}
        </span>
    );
};

export default Copytext;
