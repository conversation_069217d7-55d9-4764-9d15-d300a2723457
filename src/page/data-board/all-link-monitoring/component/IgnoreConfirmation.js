import React, { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, message } from "antd";
import { lib } from "react-single-app";

// 忽略 和批量忽略 一个modal
const IgnoreConfirmation = ({ load, row, type, selectedRows, handelCheckboxCancel }) => {
    const [isModalOpen, setIsModalOpen] = useState(false);
    const [confirmLoading, setConfirmLoading] = useState(false);
    const showModal = () => {
        //  是否正常订单
        if (type === "batch") {
            if (selectedRows?.length === 0) {
                message.warning("请选择有效数据");
            } else {
                setIsModalOpen(true);
            }
        } else {
            setIsModalOpen(true);
        }
    };
    const handleOk = () => {
        setConfirmLoading(true);
        try {
            const data = {};
            if (type === "batch") {
                const idList = selectedRows?.map(item => item.id);
                Object.assign(data, { idList });
            } else {
                Object.assign(data, { idList: [row?.id] });
            }
            lib.request({
                url: "/ares-admin/packageOut/ignore",
                data,
                success: res => {
                    setTimeout(() => {
                        setConfirmLoading(false);
                        message.success("忽略成功");
                        setIsModalOpen(false);
                        handelCheckboxCancel();
                        load();
                    }, 2000);
                },
            });
        } catch (error) {}
    };
    const handleCancel = () => {
        setIsModalOpen(false);
        handelCheckboxCancel();
    };
    return (
        (<div>
            {type === "batch" ? <Button onClick={showModal}>批量忽略</Button> : <a onClick={showModal}>忽略</a>}
            <Modal
                destroyOnClose
                title="忽略确认"
                open={isModalOpen}
                onOk={handleOk}
                onCancel={handleCancel}
                footer={
                    <div>
                        <Button type="primary" loading={confirmLoading} onClick={handleOk}>
                            确认忽略
                        </Button>
                        <Button onClick={handleCancel}>暂不忽略</Button>
                    </div>
                }>
                {type === "batch" && <Alert message={`你共选择了${selectedRows?.length}条订单`} type="info" />}
                <div style={{ marginTop: 16 }}>是否确认忽略当前这个订单的异常？忽略后此订单将不再被统计</div>
            </Modal>
        </div>)
    );
};

export default IgnoreConfirmation;
