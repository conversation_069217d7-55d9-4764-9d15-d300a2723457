// 平台仓储
import React, { useState, useEffect, useImperativeHandle, forwardRef, useRef } from "react";
import { lib, SearchList, getConfigDataUtils, event, HOC } from "react-single-app";
import { HistoryOutlined, RightOutlined, LeftOutlined, MinusCircleOutlined } from "@ant-design/icons";
import axios from "axios";
import { AdjustmentModal, IgnoreConfirmation, BondedEcharsBar } from "./index";
import { Checkbox, Space, Tag, Radio, message, Carousel, Button, Row, Tooltip } from "antd";
import { formatNumber } from "../../../../common/format";
import moment from "moment";
import "../css/bonded-monitoring.less";
import NewModal from "../../../../components/NewModal";
import AddTimeoutReason from "../../abnormal-statistics/component/AddTimeoutReason";
const { mapAuthButtonsToState } = HOC;
@mapAuthButtonsToState({ pagePath: "/all-link-monitoring" })
class BondedMonitoring extends SearchList {
    constructor(props) {
        super(props);
        this.onSearchReset = this.onSearchReset.bind(this);
        this.radioCheckedRef = React.createRef();
        this.state.status = { isOvertime: true, isNormal: false };
        this.params = { isOvertime: true, isNormal: false };
        this.state.isFirstRender = true;
        this.state.exportBol = false;
    }
    getConfig() {
        let url = getConfigDataUtils.getDataUrlByDataId(706);
        return axios.get(url).then(res => res.data.data);
    }
    configLoadDefaultParams() {
        return { ...this.params, isPlatform: true };
    }
    componentDidMount() {
        event.on("onSearchReset", this.onSearchReset);
        this.changeImmutable({ ...this.state.status });
    }
    componentWillUnmount() {
        event.off("onSearchReset", this.onSearchReset);
    }
    onSearchReset() {
        this.params = { isOvertime: true, isNormal: false };
        this.radioCheckedRef.current.resetCheck();
    }
    onSearch(search) {
        const { isFirstRender } = this.state;
        this.radioCheckedRef.current.logistCountDetails({ ...search, ...this.params }, isFirstRender);
        this.radioCheckedRef.current.childValues(search.node);
        this.setState({ isFirstRender: false });
    }
    // 关闭Modal 清除多选框
    handelCheckboxCancel() {
        const search = this.state.search;
        this.radioCheckedRef.current.logistCountDetails(search);
        this.setState({ selectedIdList: [], selectedRows: [] });
    }
    // 页面头部左边的菜单
    renderLeftOperation(row) {
        let { selectedRows } = this.state;
        return (
            <Space>
                {this.state.buttons.includes("batch-remark") && (
                    <AddTimeoutReason
                        submitUrl={"/ares-admin/packageOut/normalRemark"}
                        selectData={this.state.selectedIdList}
                        btnTitle={"批量备注"}
                        modalTitle={"批量备注"}
                        btnType={"primary"}
                        load={() => this.load()}
                        submitData={{
                            idList: this.state.selectedIdList,
                        }}
                        logRequestParams={{}}
                        type="batch"
                    />
                )}
                {this.state.buttons.includes("batch-ignore") && (
                    <IgnoreConfirmation
                        load={() => this.load(true)}
                        row={row}
                        type="batch"
                        selectedRows={selectedRows.filter(item => item.id)}
                        handelCheckboxCancel={() => this.handelCheckboxCancel()}
                    />
                )}
            </Space>
        );
    }
    // 页面头部右边的菜单
    renderRightOperation() {
        return (
            <Button
                className="btn"
                onClick={() => {
                    this.setState({
                        exportBol: true,
                    });
                }}>
                导出 &#xe638;
            </Button>
        );
    }
    // 接受子组件的值
    getChildValue(values, val) {
        const { search } = this.state;
        this.params = values;
        this.changeImmutable({
            ...values,
            node: Boolean(val) === false ? "" : val,
        });
    }
    renderOperationTopView() {
        return (
            <OrderMonitoring
                ref={this.radioCheckedRef}
                onChange={(values, val) => this.getChildValue(values, val)}
                onColumnarClick={(values, val) => {
                    this.state.search.node = values;
                    for (let i in this.params) {
                        this.params[i] = i === val;
                    }
                    this.load();
                }}
            />
        );
    }

    currentExceptionFn(row) {
        return (
            <>
                {row.currentExceptions &&
                    row.currentExceptions.map((item, index) => {
                        return (
                            <Tag color="red" key={index}>
                                {item}
                            </Tag>
                        );
                    })}
                <Row style={{ color: "#c5c5c5" }}>{row.currentExceptionTimeDsc}</Row>
            </>
        );
    }

    historyExceptionFn(row) {
        return (
            <>
                {row.historyExceptions &&
                    row.historyExceptions.map((item, index) => {
                        return (
                            <Tag color="red" key={index}>
                                {item}
                            </Tag>
                        );
                    })}
            </>
        );
    }

    operationFn(row) {
        return (
            <Space>
                {this.state.buttons.includes("remark") && (
                    <AddTimeoutReason
                        submitUrl={"/ares-admin/packageOut/normalRemark"}
                        btnTitle={"备注"}
                        modalTitle={"备注"}
                        btnType={"link"}
                        submitData={{
                            idList: [row.id],
                        }}
                        logRequestParams={{
                            supervisionId: row.id,
                        }}
                        load={() => this.load()}
                        row={row}
                        type="Single"
                    />
                )}
                {this.state.buttons.includes("ignore") && (
                    <IgnoreConfirmation
                        load={() => this.load(true)}
                        row={row}
                        type="Single"
                        handelCheckboxCancel={() => this.handelCheckboxCancel()}
                    />
                )}
            </Space>
        );
    }

    declareOrderNoFn(row) {
        return (
            <Button
                type="link"
                onClick={() => {
                    lib.request({
                        url: "/ccs/order/findOrderIdByDeclareOrderNo",
                        data: {
                            declareOrderNo: row.declareOrderNo,
                        },
                        success(data) {
                            lib.openPage(`/ccs/declaration-manage-detail?orderId=${data}&page_title=申报单详情`);
                        },
                        fail() {},
                    });
                }}>
                {row.declareOrderNo}
            </Button>
        );
    }

    remarkFn(row) {
        return (
            <>
                <Row>
                    {row.isSystemDesc && <Tag color="blue">{row.isSystemDesc}</Tag>}
                    {row.remark}
                </Row>
            </>
        );
    }

    exceptionDetailFn(row) {
        return (
            <>
                <Tooltip title={row.exceptionDetail}>{row.exceptionDetail}</Tooltip>
            </>
        );
    }

    renderModal(row) {
        const props = {
            visible: this.state.exportBol,
            configList: [
                {
                    type: "SELECT",
                    labelName: "数据类型",
                    labelKey: "dataType",
                    required: true,
                    list: [
                        {
                            id: "/ccs/supervisionMonitor/exportExportOrder",
                            name: "包裹出区",
                        },
                        {
                            id: "/ccs/supervisionMonitor/exportProduct",
                            name: "料号结转",
                        },
                    ],
                },
            ],
            title: "导出",
            onOk: data => {
                let { pagination, search } = this.state;
                lib.request({
                    // url: "/ccs/supervision/exportSupervisionStatistics",
                    url: data.dataType,
                    needMask: true,
                    data: { ...pagination, ...search, isPlatform: true },
                    success: json => {
                        this.setState({
                            exportBol: false,
                        });
                        lib.openPage("/excel/download-center?page_title=下载中心");
                    },
                });
            },
            onCancel: () => {
                this.setState({ exportBol: false });
            },
        };
        return (
            <div>
                <NewModal {...props} />
            </div>
        );
    }
}
export default BondedMonitoring;

const OrderMonitoring = forwardRef(({ onChange, onColumnarClick }, ref) => {
    const [detail, setDetail] = useState([]);
    const [checkedValue, setCheckedValue] = useState(["isOvertime"]); // 多选
    const [value, setStateValue] = useState(); // 柱状图的点击的值
    const carouselEL = useRef(null);
    function logistCountDetails(search, isFirstRender) {
        if (Object.keys(search).length === 0) {
            Object.assign(search, { isOvertime: false, isNormal: false, node: "" });
        }
        lib.request({
            url: "/ares-admin/packageOut/getStatistics",
            needMask: true,
            data: {
                ...search,
                isPlatform: true,
            },
            success: res => {
                setDetail(res);
                if (isFirstRender) {
                    setTimeout(() => {
                        carouselEL.current.goTo(0);
                    }, 200);
                }
            },
        });
    }
    useImperativeHandle(ref, () => ({
        logistCountDetails: logistCountDetails,
        resetCheck: resetCheck,
        childValues: setStateValue,
    }));
    // 重置的时候改变checkbox的状态和 请求
    const resetCheck = () => {
        setStateValue("");
        setCheckedValue(["isOvertime"]);
    };

    // 多选的checked
    const handleCheckChange = checkedValues => {
        if (checkedValues.length === 0) {
            message.warning("至少选择一个,不可取消");
            return;
        }
        let isOvertime = checkedValues.includes("isOvertime");
        let isNormal = checkedValues.includes("isNormal");
        setCheckedValue(checkedValues);
        onChange({ isOvertime, isNormal }, value);
    };
    // 直方图单选框的值
    const handelRadioChange = (e, type) => {
        let isOvertime = type ? type === "isOvertime" : checkedValue.includes("isOvertime");
        let isNormal = type ? type === "isNormal" : checkedValue.includes("isNormal");
        const val = e;
        setStateValue(val);
        onChange({ isOvertime, isNormal }, val);
    };
    // 轮播基础设置
    const settings = {
        dots: false,
        infinite: false,
        slidesToShow: 8,
        arrows: true,
        slidesToScroll: 4,
        autoplay: false,
        responsive: [
            { breakpoint: 1000, settings: { slidesToShow: 3, slidesToScroll: 2 } },
            { breakpoint: 1100, settings: { slidesToShow: 4, slidesToScroll: 2 } },
            { breakpoint: 1200, settings: { slidesToShow: 5, slidesToScroll: 4 } },
            { breakpoint: 1300, settings: { slidesToShow: 5, slidesToScroll: 4 } },
            { breakpoint: 1400, settings: { slidesToShow: 5, slidesToScroll: 4 } },
            { breakpoint: 1500, settings: { slidesToShow: 6, slidesToScroll: 4 } },
            { breakpoint: 1600, settings: { slidesToShow: 6, slidesToScroll: 4 } },
            { breakpoint: 1700, settings: { slidesToShow: 6, slidesToScroll: 4 } },
            { breakpoint: 1800, settings: { slidesToShow: 7, slidesToScroll: 4 } },
            { breakpoint: 1900, settings: { slidesToShow: 7, slidesToScroll: 4 } },
            { breakpoint: 1960, settings: { slidesToShow: 8, slidesToScroll: 4 } },
        ],
    };
    return (
        <div className="platform-bonded">
            <div className="platform-bonded-top">
                <Checkbox.Group value={checkedValue} onChange={handleCheckChange}>
                    <Checkbox value={"isOvertime"} className="isOvertime">
                        已超时订单
                    </Checkbox>
                    <Checkbox value={"isNormal"} className="isNormal">
                        正常订单
                    </Checkbox>
                </Checkbox.Group>
            </div>
            <div className="platform">
                <Button
                    className="leftButton"
                    style={{ top: 70, left: -23 }}
                    onClick={() => {
                        carouselEL.current.prev();
                    }}
                    icon={<LeftOutlined style={{ color: "#FFFFFF", fontSize: 10 }} />}></Button>
                <Button
                    className="rightButton"
                    style={{ top: 70, right: -7 }}
                    onClick={() => {
                        carouselEL.current.next();
                    }}
                    icon={<RightOutlined style={{ color: "#FFFFFF", fontSize: 10 }} />}></Button>
                <Carousel {...settings} ref={carouselEL}>
                    {/* <Radio.Group onChange={handelRadioChange} value={value}> */}
                    {detail &&
                        detail?.map((item, index) => {
                            return (
                                <Radio.Group value={value}>
                                    <Radio.Button className="radio-btnn" key={index} value={item.node}>
                                        <div
                                            className="bonded"
                                            onClick={() => {
                                                handelRadioChange(item.node);
                                            }}>
                                            <div className={value === item.node ? "bonded-top-color" : "bonded-top"}>
                                                <span className="bonded-top-left">{item.nodeName || 0}</span>
                                                <span className="bonded-top-cen">{item.businessSystem || 0}</span>
                                                <span className="bonded-top-rig">
                                                    {formatNumber(item.totalCount || 0)}
                                                    {Number(item.totalCount) >= 10000 && (
                                                        <span className="ten_thousand">万</span>
                                                    )}
                                                </span>
                                            </div>
                                            <div
                                                className="bonded-fot"
                                                onClick={e => {
                                                    e.nativeEvent.stopImmediatePropagation();
                                                }}>
                                                <BondedEcharsBar
                                                    item={item}
                                                    checkedValue={checkedValue}
                                                    barClick={(type, e) => {
                                                        const ev = e || window.event;
                                                        ev.stopPropagation();
                                                        setStateValue(item.node);
                                                        // handelRadioChange(item.node, type)
                                                        onColumnarClick(item.node, type);
                                                    }}
                                                />
                                            </div>
                                        </div>
                                    </Radio.Button>
                                </Radio.Group>
                            );
                        })}
                    {/* </Radio.Group> */}
                </Carousel>
            </div>
        </div>
    );
});
