import { Checkbox } from "antd";
import React, { useEffect, useState } from "react";

const IndeterminateCheckbox = ({ options, value, onChange, ...rest }) => {
    const [indeterminate, setIndeterminate] = useState(false);
    const [checkAll, setCheckAll] = useState(false);
    const [checkedList, setCheckedList] = useState(value);

    useEffect(() => {
        setIndeterminate(!!value?.length && value?.length < options.length);
        setCheckAll(value?.length === options.length);
    }, [value]);

    function onCheckAllChange(e) {
        const { checked } = e.target;
        setCheckAll(checked);
        setIndeterminate(false);
        setCheckedList(checked ? options.map(item => item.value) : []);
        onChange(checked ? options.map(item => item.value) : []);
    }
    function singleOnChange(e) {
        onChange(e);
        setCheckedList(e);
        setCheckAll(e.length === options.length);
        setIndeterminate(!!e.length && e.length < options.length);
    }
    return (
        <>
            <Checkbox indeterminate={indeterminate} onChange={onCheckAllChange} checked={checkAll} {...rest}>
                全选
            </Checkbox>
            <Checkbox.Group options={options} value={checkedList} onChange={singleOnChange} {...rest} />
        </>
    );
};

export default IndeterminateCheckbox;
