import React, { useState } from "react";
import { Modal, Form, InputNumber, message, Radio, Input, Alert, Button, DatePicker } from "antd";
import moment from "moment";
import NormalSelect from "../../../../components/NormalSelect";
import { lib } from "react-single-app";

//批量调整和调整时效 单个Modal
const AdjustmentModal = ({ load, row, type, selectedRows, handelCheckboxCancel }) => {
    const [form] = Form.useForm();
    const { RangePicker } = DatePicker;
    const { TextArea } = Input;
    const [isModalOpen, setIsModalOpen] = useState(false);
    const [radioFlag, setradioFlag] = useState(false);
    const showModal = () => {
        setIsModalOpen(true);
    };
    const handleOk = async () => {
        try {
            const values = await form.validateFields();
            const data = Object.assign(values);
            if (type === "batch" && !radioFlag) {
                const idList = selectedRows?.map(item => item.id);
                Object.assign(data, { idList });
            } else if (type === "batch" && radioFlag === true) {
                const activeTimeStart = moment(values.time[0]).valueOf();
                const activeTimeEnd = moment(values.time[1]).valueOf();
                Object.assign(data, { activeTimeStart, activeTimeEnd });
            } else {
                Object.assign(data, { idList: [row?.id] });
            }
            lib.request({
                url: "/ares-admin/supervision/adjust",
                data,
                success: res => {
                    message.success("调整成功");
                    setIsModalOpen(false);
                    handelCheckboxCancel();
                    load();
                },
            });
        } catch (error) {}
    };
    const handleCancel = () => {
        setIsModalOpen(false);
        setradioFlag(false);
        handelCheckboxCancel();
    };
    const handleRadioValue = e => {
        const value = e.target.value;
        if (value) {
            setradioFlag(value);
        } else {
            setradioFlag(value);
        }
    };

    const disabledDate = current => {
        return current && current < moment().subtract(1, "days").endOf("day");
    };
    const range = (start, end) => {
        const result = [];
        for (let i = start; i <= end; i++) {
            result.push(i);
        }
        return result;
    };
    //日期不能选择之前日期 例如今天11号11号之前就会被禁用
    const disabledTime = function (current) {
        let currentDay = moment().date(); //当下的时间
        let currentHours = moment().hours();
        let currentMinutes = moment().minutes(); //设置的时间
        let settingHours = moment(current).hours();
        let settingDay = moment(current).date();
        if (current && settingDay === currentDay && settingHours === currentHours) {
            return {
                disabledHours: () => range(0, currentHours - 1), //设置为当天现在这小时，禁用该小时，该分钟之前的时间
                disabledMinutes: () => range(0, currentMinutes - 1),
            };
        } else if (current && settingDay === currentDay && settingHours > currentHours) {
            return {
                disabledHours: () => range(0, currentHours - 1), //设置为当天现在这小时之后，只禁用当天该小时之前的时间
            };
        } else if (current && settingDay === currentDay && settingHours < currentHours) {
            return {
                disabledHours: () => range(0, currentHours - 1), //若先设置了的小时小于当前的，再设置日期为当天，需要禁用当天现在这小时之前的时间和所有的分
                disabledMinutes: () => range(0, 59),
            };
        } else if (current && settingDay > currentDay) {
            return {
                disabledHours: () => [], //设置为当天之后的日期，则不应有任何时间分钟的限制
                disabledMinutes: () => [],
            };
        }
    };
    return (
        (<div>
            {type === "batch" ? <Button onClick={showModal}>批量调整</Button> : <a onClick={showModal}>调整时效</a>}
            <Modal destroyOnClose title="调整时效" open={isModalOpen} onOk={handleOk} onCancel={handleCancel}>
                {type === "batch" && (
                    <div style={{ marginLeft: 20 }}>
                        <Radio.Group onChange={handleRadioValue} defaultValue={false}>
                            <Radio value={false}>现有订单</Radio>
                            <Radio value={true}>未来订单</Radio>
                        </Radio.Group>
                    </div>
                )}
                {/* {type === "batch" && selectedRows?.length !== 0 && (
                    <Alert
                        message={`你共选择了${selectedRows?.length}条订单`}
                        type="info"
                        style={{ margin: "16px 0" }}
                    />
                )}
                {type === "batch" && selectedRows?.length === 0 && (
                    <Alert
                        message="你尚未选择订单，你可以设置未来订单增加时效"
                        type="info"
                        style={{ margin: "16px 0" }}
                    />
                )} */}
                {type === "batch" ? (
                    selectedRows?.length !== 0 ? (
                        <Alert
                            message={`你共选择了${selectedRows?.length}条订单`}
                            type="info"
                            style={{ margin: "16px 0" }}
                        />
                    ) : (
                        <Alert
                            message="你尚未选择订单，你可以设置未来订单增加时效"
                            type="info"
                            style={{ margin: "16px 0" }}
                        />
                    )
                ) : null}
                <Form form={form} preserve={false} labelCol={{ span: 6 }} style={{ minHeight: 200 }}>
                    {radioFlag && (
                        <Form.Item name="warehouseList" label="生效实体仓" rules={[{ required: true }]}>
                            <NormalSelect
                                showSearch
                                optionFilterProp="label"
                                mode="multiple"
                                optionsApi="/ares-admin/deduct/entityWarehouseList"
                                style={{ width: "100%" }}
                            />
                        </Form.Item>
                    )}
                    {radioFlag && (
                        <Form.Item name="time" label="规则生效时间" rules={[{ required: true }]}>
                            <RangePicker
                                format="YYYY-MM-DD HH:mm:ss"
                                style={{ width: "100%" }}
                                showTime
                                disabledDate={disabledDate}
                                disabledTime={disabledTime}
                            />
                        </Form.Item>
                    )}
                    {type === "batch" && radioFlag && selectedRows?.length === 0 ? (
                        <Form.Item name="hours" label="增加时效" rules={[{ required: true }]}>
                            <InputNumber min={0} style={{ marginRight: 8 }} addonAfter="小时" />
                        </Form.Item>
                    ) : null}
                    {selectedRows?.length > 0 ? (
                        <>
                            <Form.Item name="hours" label="增加时效" rules={[{ required: true }]}>
                                <InputNumber min={0} style={{ marginRight: 8 }} addonAfter="小时" />
                            </Form.Item>
                            <Form.Item name="adjustReason" label="调整原因" rules={[{ required: true }]}>
                                <TextArea showCount autoSize={{ minRows: 3, maxRows: 5 }} maxLength={200} />
                            </Form.Item>
                        </>
                    ) : null}
                    {type === "batch" && radioFlag && selectedRows?.length === 0 ? (
                        <Form.Item name="adjustReason" label="调整原因" rules={[{ required: true }]}>
                            <TextArea showCount autoSize={{ minRows: 3, maxRows: 5 }} maxLength={200} />
                        </Form.Item>
                    ) : null}
                    {type === "Single" ? (
                        <>
                            <Form.Item name="hours" label="增加时效" rules={[{ required: true }]}>
                                <InputNumber min={0} style={{ marginRight: 8 }} addonAfter="小时" />
                            </Form.Item>
                            <Form.Item name="adjustReason" label="调整原因" rules={[{ required: true }]}>
                                <TextArea showCount autoSize={{ minRows: 3, maxRows: 5 }} maxLength={200} />
                            </Form.Item>
                        </>
                    ) : null}
                </Form>
            </Modal>
        </div>)
    );
};

export default AdjustmentModal;
