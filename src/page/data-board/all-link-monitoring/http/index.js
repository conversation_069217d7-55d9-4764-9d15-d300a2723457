import { lib } from "react-single-app";

function getWarehouse() {
    return new Promise((resolve, reject) => {
        lib.request({
            url: "/ares-admin/deduct/entityWarehouseList",
            needMask: true,
            success: resolve,
            fail: reject,
        });
    });
}

function getChannel() {
    return new Promise((resolve, reject) => {
        lib.request({
            url: "/ares-admin/deduct/channelList",
            needMask: true,
            success: resolve,
            fail: reject,
        });
    });
}

function getCodeItem() {
    return new Promise((resolve, reject) => {
        lib.request({
            url: "/ares-admin/nodeSummary/idCodeItem",
            needMask: true,
            success: resolve,
            fail: reject,
        });
    });
}

function submit(data) {
    return new Promise((resolve, reject) => {
        lib.request({
            url: "/ares-admin/nodeFinalConfig/saveOrModify",
            needMask: true,
            data,
            success: resolve,
            fail: reject,
        });
    });
}

function getEndCongigList() {
    return new Promise((resolve, reject) => {
        lib.request({
            url: "/ares-admin/nodeFinalConfig/getList",
            needMask: true,
            success: resolve,
            fail: reject,
        });
    });
}

function deleteList(data) {
    return new Promise((resolve, reject) => {
        lib.request({
            url: "/ares-admin/nodeFinalConfig/delete",
            data,
            needMask: true,
            success: resolve,
            fail: reject,
        });
    });
}

export { getWarehouse, getChannel, getCodeItem, submit, getEndCongigList, deleteList };
