import React, { useEffect, useRef } from "react";
import { DTEditForm, DTEditFormRefs, DTEditFormConfigs } from "@dt/components";
import { Modal, message } from "antd";
// import { depFn } from "../../warehouse-address-manage/component/add-modal";
import { lib } from "react-single-app";
export default ({ open, closeFn, ids }) => {
    const configs: DTEditFormConfigs = [
        {
            type: "SELECT",
            fProps: {
                label: "超时原因",
                name: "overdueReasonType",
                labelCol: { span: 6 },
                rules: [{ required: true, message: "请选择超时原因" }],
            },
            dataUrl: "/ccs/overdue/overdueReasonTypeList",
            list: [],
        },
        {
            type: "TEXTAREA",
            fProps: {
                label: " ",
                name: "overdueReason",
                colon: false,
                labelCol: { span: 6 },
                // rules: [{ required: true, message: "请输入超时原因" }],
            },
        },
        {
            type: "FILE",
            fProps: {
                label: "举证",
                name: "proofUrl",
                extra: "支持png,jpg,jpeg格式，最多不超过2M",
                labelCol: { span: 6 },
            },
            cProps: {
                listType: "text",
                maxCount: 1,
                size: 2,
                accept: ".jpg,.jpeg,.png",
            },
        },
    ];
    const ref = useRef<DTEditFormRefs>();

    const submit = () => {
        ref.current.form.validateFields().then(values => {
            if (values.proofUrl?.[0]) {
                values.proofName = values.proofUrl?.[0].name;
                values.proofUrl = values.proofUrl?.[0].url;
            } else {
                delete values.proofUrl;
            }
            values.id = ids[0].id;
            lib.request({
                url: "/ccs/overdue/feedBack",
                data: values,
                success(data) {
                    message.success("提交成功");
                    closeFn(true);
                },
            });
        });
    };

    useEffect(() => {
        if (open) {
            const obj = ids[0];
            if (obj) {
                if (obj.overdueProofUrl) {
                    obj.proofUrl = obj.proofUrl ? [{ url: obj.overdueProofUrl, name: obj.overdueProofName }] : [];
                }
                ref.current.form.setFieldsValue(obj);
            }
        }
    }, [open]);

    return (
        <Modal
            open={open}
            title={"提交反馈"}
            onCancel={() => {
                ref.current.form.resetFields();
                closeFn();
            }}
            onOk={() => {
                submit();
            }}>
            <DTEditForm
                configs={configs}
                layout={{
                    mode: "appoint",
                    colNum: 1,
                }}
                ref={ref}
            />
        </Modal>
    );
};
