import React, { useRef, useState, useEffect } from "react";
import { SearchList } from "@dt/components";
import { DDYObject, getConfigDataUtils, lib } from "react-single-app";
import { Button, Modal, message, Popover, Tabs, Tooltip } from "antd";
import axios from "axios";
import NewModal from "@/components/NewModal";
import { request } from "@dt/networks";
import FeedbackModal from "./feedback-modal";
import LogModal from "./log-modal";

export default () => {
    const [feedbackOpen, setFeedbackOpen] = useState(false);
    // const [ids, setIds] = useState();
    const searchListRef = useRef(null);
    // const selecteds = useRef([]);
    const [selecteds, setSelecteds] = useState([]);
    const [logOpen, setLogOpen] = useState(false);
    const [tabsData, setTabsData] = useState({});
    const modalRef = useRef({});
    const getConfig = async () => {
        let url = getConfigDataUtils.getDataUrlByDataId(893);
        const res = await axios.get(url);
        return res.data.data;
    };

    const getTabs = () => {
        // console.log("getTabs", searchListRef.current);
        const data = searchListRef.current?.getSearchData() || {};
        lib.request({
            url: "/ccs/overdue/statusCount",
            data: {
                ...data,
            },
            success(data) {
                setTabsData(data);
            },
        });
    };
    useEffect(() => {
        // getDatas();
        // getTabs();
        setTimeout(() => {
            getTabs();
        }, 500);
    }, []);

    return (
        <>
            <SearchList
                ref={searchListRef}
                searchConditionConfig={{
                    size: "middle",
                }}
                headerMode={"sticky"}
                // preventLoadProcess={() => true}
                getConfig={getConfig}
                renderLeftOperation={() => {
                    return (
                        <>
                            <Button
                                type="primary"
                                onClick={() => {
                                    // const importExtendParamBase64 = window.btoa(`inventoryOrderId=${lib.getParam("id")}`);
                                    let url = `/excel/import-data?page_title=批量导入反馈&code=${encodeURIComponent(
                                        "IMPORT_INVENTORY_ORDER_OVERDUE_FEEDBACK",
                                    )}&importExtendParam=${encodeURIComponent("")}`;
                                    lib.openPage(url, () => {
                                        // fetchDetail({ activeKey: "1", id: lib.getParam("id") });
                                        searchListRef.current.load();
                                    });
                                }}>
                                批量反馈
                            </Button>
                            {/* <Button onClick={() => {}}>导出</Button> */}
                        </>
                    );
                }}
                onSearchReset={() => {
                    getTabs();
                }}
                onSearch={() => {
                    getTabs();
                }}
                renderTableTopView={() => {
                    return (
                        <>
                            <Tabs
                                onChange={e => {
                                    searchListRef.current.changeImmutable({
                                        exceptionDetailStatus: e || null,
                                    });
                                }}>
                                <Tabs.TabPane tab="全部" key={""}></Tabs.TabPane>
                                <Tabs.TabPane tab={`待关务反馈(${tabsData[0] || 0})`} key={0}></Tabs.TabPane>
                                <Tabs.TabPane tab={`已反馈(${tabsData[1] || 0})`} key={1}></Tabs.TabPane>
                            </Tabs>
                        </>
                    );
                }}
                tableCustomFun={{
                    operateFn: row => {
                        return (
                            <>
                                <Button
                                    type="link"
                                    onClick={() => {
                                        setSelecteds([row]);
                                        setFeedbackOpen(true);
                                    }}>
                                    提交反馈
                                </Button>
                                <Button
                                    type="link"
                                    onClick={() => {
                                        setSelecteds([row]);
                                        setLogOpen(true);
                                    }}>
                                    日志
                                </Button>
                            </>
                        );
                    },
                    inventoryOrderIdFn: row => {
                        return (
                            <a
                                onClick={() => {
                                    lib.openPage(
                                        `/customs-clearance-detail2?pageTitle=清关单详情&id=${row.inventoryOrderId}`,
                                        () => {
                                            searchListRef.current.load();
                                        },
                                    );
                                }}>
                                {row.inventoryOrderSn}
                            </a>
                        );
                    },
                    overdueProofFn: row => {
                        return <a href={row.overdueProofUrl}>{row.overdueProofName}</a>;
                    },
                    overdueBeginFn: row => {
                        return (
                            <>
                                <span style={{ color: "red" }}>{row.overdueBeginNodeDesc}</span>
                                <br />
                                {row.overdueBeginTime}
                            </>
                        );
                    },
                    overdueEndFn: row => {
                        return (
                            <>
                                <span style={{ color: "red" }}>{row.overdueEndNodeDesc}</span>
                                <br />
                                {row.overdueEndTime}
                            </>
                        );
                    },
                }}
                renderModal={() => {
                    return (
                        <>
                            <FeedbackModal
                                ids={selecteds}
                                open={feedbackOpen}
                                closeFn={load => {
                                    setFeedbackOpen(false);
                                    setSelecteds([]);
                                    if (load) {
                                        searchListRef.current.load();
                                    }
                                }}
                            />
                            <LogModal
                                row={selecteds}
                                open={logOpen}
                                closeFn={load => {
                                    setLogOpen(false);
                                    setSelecteds([]);
                                    if (load) {
                                        searchListRef.current.load();
                                    }
                                }}
                            />
                        </>
                    );
                }}
                onTableSelected={(row, selectedRows) => {
                    // selecteds.current = selectedRows;
                    setSelecteds(selectedRows);
                }}
            />
        </>
    );
};
