export const customsReceiptConfig = [
    {
        type: "SELECT",
        labelName: "回执类型",
        label: "callbackType",
        // rules: [{required:true,message:'回执类型是必填的'}],
        required: true,
        list: [],
        ccs: "/ccs/callbackLog/listCallbackType",
    },
    {
        type: "SELECT",
        labelName: "回执内容",
        label: "callbackContent",
        list: [],
        rules: [{ required: true, message: "回执内容是必填的" }],

        required: true,
        ccs: "/ccs/callback/listContent",
    },
    {
        type: "DATE",
        labelName: "发送时间",
        label: "sendTime",
        rules: [{ required: true, message: "发送时间是必选的" }],
        required: true,
        require: true,
    },
];

export const successColumns = [
    {
        title: "序号",
        dataIndex: "seqNo",
        width: 100,
    },
    {
        title: "申报单号",
        dataIndex: "declareOrderNo",
        width: 150,
    },
    {
        title: "清单编号",
        dataIndex: "inveNo",
        width: 150,
    },
    {
        title: "运单编号",
        dataIndex: "mailNo",
        width: 150,
    },
    {
        title: "错误信息",
        dataIndex: "errorInfo",
        width: 200,
    },
];
export const failColumns = [];
