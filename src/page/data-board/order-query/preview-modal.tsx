import React from "react";
import { Modal, Tabs, Table, Button, message } from "antd";
import { failColumns, successColumns } from "./config";
const { TabPane } = Tabs;
import { handleCopy } from "../../../common/utils";

export default ({ open, importResult, closeModal }) => {
    const importHandle = () => {
        closeModal();
    };
    return (
        <Modal
            visible={open}
            title={`导入预览(${importResult?.totalCount})`}
            onOk={importHandle}
            onCancel={() => {
                closeModal();
            }}
            width={900}>
            <div style={{ position: "relative" }}>
                <Tabs defaultActiveKey="1">
                    <TabPane tab={`执行成功(${importResult?.successCount})`} key="0">
                        <Table dataSource={importResult?.successList || []} columns={successColumns} />
                    </TabPane>
                    <TabPane tab={`执行失败(${importResult?.failCount})`} key="1">
                        <Table dataSource={importResult?.failList || []} columns={successColumns} />
                    </TabPane>
                </Tabs>
                <Button
                    style={{ position: "absolute", top: "10px", right: "50px" }}
                    onClick={() => {
                        const msg = importResult?.failList.map(item => item.declareOrderNo).join(",");
                        if (importResult?.failCount <= 0) {
                            message.warning("无失败数据可供复制");
                            return;
                        }
                        handleCopy(msg);
                    }}>
                    复制失败数据
                </Button>
            </div>
        </Modal>
    );
};
