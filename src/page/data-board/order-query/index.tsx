import React, { useRef, useState, useEffect } from "react";
import { SearchList } from "@dt/components";
import { DDYObject, getConfigDataUtils, lib } from "react-single-app";
import { Button, Modal, message, Popover } from "antd";
import axios from "axios";
import NewModal from "@/components/NewModal";
import { request } from "@dt/networks";
// import { MergeRelationUpdateConfig } from './config';
import { OrderQueryDataItem, SimulateReceiptItem } from "./type";
import { CopyOutlined } from "@ant-design/icons";
import { handleCopy } from "../../../common/utils";
import PreviewModal from "./preview-modal";

export default () => {
    const [modalOpen, setModalOpen] = useState(false);
    const SearchListRef = useRef(null);
    const selecteds = useRef([]);
    const [failList, setFailList] = useState([]);
    const [previewOpen, setPreviewOpen] = useState(false);
    const [importResult, setImportResult] = useState({});
    const modalRef = useRef({});
    const getConfig = async () => {
        let url = getConfigDataUtils.getDataUrlByDataId(745);
        const res = await axios.get(url);
        return res.data.data;
    };
    const customsReceiptConfig = [
        {
            type: "SELECT",
            labelName: "回执类型",
            labelKey: "callbackType",
            // rules: [{required:true,message:'回执类型是必填的'}],
            required: true,
            list: [],
            ccs: "/ccs/callbackLog/listCallbackType",
            onChange: e => {
                lib.request({
                    url: "/ccs/callback/listContent",
                    data: { callbackType: e },
                    success(data) {
                        configs[1].list = data;
                        setConfigs([...configs]);
                        //@ts-ignore
                        modalRef.current?.form.setFieldsValue({ callbackContent: null });
                    },
                    fail(code, msg, data) {},
                });
            },
        },
        {
            type: "SELECT",
            labelName: "回执内容",
            labelKey: "callbackContent",
            list: [],
            required: true,
            // ccs: '/ccs/callback/listContent',
        },
        {
            type: "DATE",
            labelName: "发送时间",
            labelKey: "sendTime",
            required: true,
        },
    ];

    const [configs, setConfigs] = useState(customsReceiptConfig);

    const getDatas = () => {
        const arr = [];
        configs.forEach((item, index) => {
            if (item.ccs) {
                arr.push(
                    new Promise((resolve, reject) => {
                        request.request({
                            url: item.ccs,
                            data: {},
                            success: data => {
                                resolve({
                                    data,
                                    index,
                                });
                            },
                            fail: () => {
                                reject();
                            },
                        });
                    }),
                );
            }
        });

        Promise.all(arr)
            .then(res => {
                res.map(item => {
                    configs[item.index].list = item.data;
                });
                setConfigs([...configs]);
            })
            .catch(err => {});
    };

    const openModal = () => {
        if (selecteds.current.length <= 0) {
            return message.warning("请勾选至少一条数据");
        }
        setModalOpen(true);
    };

    const updateItem = (obj: SimulateReceiptItem) => {
        obj.sendTime = obj.sendTime.valueOf();
        lib.request({
            url: "/ccs/orderQuery/simulateReceipt",
            // url: 'http://yapi.yang800.cn/mock/82/orderQuery/simulateReceipt',
            data: {
                ...obj,
                declareOrderNoList: selecteds.current.map(item => item.declareOrderNo),
            },
            success(data) {
                message.success("操作成功");
                SearchListRef.current.load();
                setImportResult(data);
                setModalOpen(false);
                setPreviewOpen(true);
            },
            fail(code, msg, data) {},
        });
    };

    const getErrorOrderData = () => {
        const search = SearchListRef.current?.getSearchData();
        const panigation = SearchListRef.current.getPanigation();
        lib.request({
            url: "/ccs/orderQuery/getUnExistQueryNos",
            data: {
                ...search,
                ...panigation,
            },
            success(data) {
                if (Array.isArray(data) && data.length <= 0) {
                    return message.warn("没有失败列表数据");
                }
                Modal.info({
                    icon: null,
                    maskClosable: true,
                    content: (
                        <div style={{ padding: "20px" }}>
                            {data.map((item, index) => {
                                return (
                                    <p key={index} style={{ margin: 0, padding: 0 }}>
                                        {item}
                                    </p>
                                );
                            })}
                        </div>
                    ),
                });
            },
            fail(code, msg, data) {},
        });
    };

    useEffect(() => {
        getDatas();
    }, []);

    return (
        <>
            <SearchList
                ref={SearchListRef}
                searchConditionConfig={{
                    size: "middle",
                }}
                headerMode={"sticky"}
                // preventLoadProcess={() => true}
                defaultLoading={false}
                getConfig={getConfig}
                renderLeftOperation={() => {
                    return (
                        <>
                            <Button
                                type="primary"
                                onClick={() => {
                                    openModal();
                                }}>
                                模拟海关回执
                            </Button>
                            <Button
                                type="link"
                                onClick={() => {
                                    getErrorOrderData();
                                }}>
                                失败列表
                            </Button>
                        </>
                    );
                }}
                tableCustomFun={{
                    renderDeclare: (row: DDYObject) => {
                        return (
                            <>
                                <div
                                    style={{ color: "#1890ff" }}
                                    onClick={() => {
                                        lib.openPage(
                                            `/ccs/declaration-manage-detail?orderId=${row.id}&page_title=申报单详情`,
                                        );
                                    }}>
                                    {row.declareOrderNo}
                                </div>
                            </>
                        );
                    },
                }}
                renderModal={() => {
                    return (
                        <>
                            <NewModal
                                visible={modalOpen}
                                onOk={(obj: SimulateReceiptItem) => {
                                    console.log("obj:", obj);
                                    updateItem(obj);
                                    // setModalOpen(false)
                                }}
                                ref={modalRef}
                                configList={configs}
                                title={"模拟海关回执"}
                                // editRow={editrow}
                                onCancel={() => {
                                    setModalOpen(false);
                                }}
                                modalStyle={{ width: 600 }}
                                okText={"保存"}
                            />
                            <PreviewModal
                                open={previewOpen}
                                closeModal={() => {
                                    setPreviewOpen(false);
                                }}
                                importResult={importResult}
                            />
                        </>
                    );
                }}
                onTableSelected={(row, selectedRows) => {
                    console.log(row, selectedRows);
                    selecteds.current = selectedRows;
                }}
            />
        </>
    );
};
