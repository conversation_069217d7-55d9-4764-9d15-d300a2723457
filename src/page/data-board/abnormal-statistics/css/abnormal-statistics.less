.abnormal-statistics {
    .operation-top-panel {
        margin: 0 12px;
    }
    width: 100%;
    min-width: 1008px;
    .mom_name {
        white-space: pre-wrap;
        .mom_nameOne {
            font-size: 14px;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #333333;
        }
        .mom_nameTwo {
            margin-left: 8px;
            font-size: 14px;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #999999;
        }
    }
    .logist {
        display: flex;
        flex-direction: column;
        .logist_spanOne {
            display: flex;
            flex-direction: column;
            .logist_spanOne_div {
                display: flex;
                .logist_spanOne_fir {
                    font-size: 14px;
                    font-family: PingFangSC-Regular, PingFang SC;
                    font-weight: 400;
                    color: #333333;
                    margin-right: 8px;
                }
            }
        }
        .logist_spanTwo {
            :first-child {
                font-size: 14px;
                font-family: PingFangSC-Regular, PingFang SC;
                font-weight: 400;
                color: #999999;
                margin-right: 8px;
            }
            :last-child {
                font-size: 14px;
                font-family: PingFangSC-Regular, Ping<PERSON>ang SC;
                font-weight: 400;
                color: #333333;
            }
        }
    }

    .platform {
        width: 100%;
        min-width: 1108px;
        margin-bottom: 16px;
        .ant-radio-button-wrapper {
            width: 140px;
            height: 88px;
            padding: 0;
        }
    }
    .monitoring {
        width: 140px;
        height: 88px;
        .monitoring_top {
            .monitoring_top_span {
                margin-right: 8px;
                margin-top: 12px;
                margin-left: 16px;
                font-size: 12px;
                font-family: PingFangSC-Medium, PingFang SC;
                font-weight: 500;
            }
        }
        .monitoring_num {
            margin-top: 8px;
            margin-left: 16px;
            font-size: 24px;
            font-family: D-DIN-Bold, D-DIN;
            font-weight: bold;
        }
    }
    .ant-radio-button-wrapper {
        background: linear-gradient(180deg, #fafafa 0%, #fdfdfd 100%);
    }
    .ant-radio-button-wrapper {
        position: relative;
        display: inline-block;
        height: 32px;
        margin: 0;
        margin-top: 0px;
        margin-left: 0px;
        padding: 0 15px;
        color: rgba(0, 0, 0, 0.85);
        color: #333333;
        font-size: 14px;
        line-height: 30px;
        border: none;
        border-top-width: 1.02px;
        border-left-width: 0;
        cursor: pointer;
        transition: color 0.3s, background 0.3s, border-color 0.3s, box-shadow 0.3s;
    }
    .ant-radio-group-solid .ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled) {
        color: #0268ff !important;
        background: linear-gradient(180deg, #e9f1ff 0%, #f6f9ff 100%);
        border-top: 3px solid #0268ff;
    }
    .ant-radio-button-wrapper:not(:first-child)::before {
        left: 0px;
    }
    .ant-radio-button-wrapper:not(:first-child)::before {
        position: absolute;
        top: -1px;
        left: -1px;
        display: block;
        box-sizing: content-box;
        width: 0px;
        height: 100%;
        padding: 1px 0;
        background-color: #d9d9d9;
        transition: background-color 0.3s;
        content: "";
    }
    .ant-radio-group-solid .ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled):focus-within {
        box-shadow: none;
    }
    .ant-radio-group-solid .ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled):focus-within {
        box-shadow: none;
    }
    .ant-table-tbody > tr > td {
        border-bottom: 1px solid #f0f0f0;
        transition: background 0.3s;
        border-left: 1px solid #f0f0f0;
    }
}
