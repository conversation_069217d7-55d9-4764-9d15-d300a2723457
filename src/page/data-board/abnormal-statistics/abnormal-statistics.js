import React, { useState, useImperativeHandle, forwardRef } from "react";
import { lib, SearchList, getConfigDataUtils, event, HOC } from "react-single-app";
import axios from "axios";
import { Tag, Radio } from "antd";
import { OvertimeReason, AddTimeoutReason } from "./component/index";
import "./css/abnormal-statistics.less";
const { mapAuthButtonsToState } = HOC;
@mapAuthButtonsToState({ pagePath: "/ccs/abnormal-statistics" })
class AbnormalStatistics extends SearchList {
    constructor(props) {
        super(props);
        this.onSearchReset = this.onSearchReset.bind(this);
        this.radioCheckedRef = React.createRef();
    }
    getConfig() {
        let url = getConfigDataUtils.getDataUrlByDataId(707);
        return axios.get(url).then(res => res.data.data);
    }
    componentDidMount() {
        event.on("onSearchReset", this.onSearchReset);
    }
    componentWillUnmount() {
        event.off("onSearchReset", this.onSearchReset);
    }
    onSearchReset() {
        this.radioCheckedRef.current.resetCheck();
    }
    onSearch(search) {
        this.radioCheckedRef.current.logistCountDetails(search);
        if (search.ruleCategoryIdList?.length > 1) {
            this.radioCheckedRef.current.childValues([-1]);
        } else {
            this.radioCheckedRef.current.childValues(search.ruleCategoryIdList);
        }
    }

    // 页面头部右边的菜单
    renderRightOperation() {
        return <OvertimeReason load={() => this.load()} />;
    }
    // 页面头部左边的菜单
    renderLeftOperation(row) {
        return (
            <>
                {this.state.buttons.includes("batch-remark") && (
                    <AddTimeoutReason
                        submitUrl="/ares-admin/packageOut/exceptionRemark"
                        selectData={this.state.selectedIdList}
                        btnTitle="批量备注"
                        modalTitle="批量备注"
                        btnType="primary"
                        load={() => this.load()}
                        submitData={{
                            idList: this.state.selectedIdList,
                        }}
                        type="batch"
                    />
                )}
            </>
        );
    }
    getValueChild(values, ruleCategoryIdList) {
        const { search } = this.state;
        // if (!Boolean(values)) {
        if (values === -1) {
            let ids = ruleCategoryIdList.map(item => item.ruleCategoryId).filter(it => it !== -1);
            this.changeImmutable({ ruleCategoryIdList: ids });
            this.setState({
                search: { ...search, ruleCategoryIdList: ids },
            });
        } else {
            this.changeImmutable({ ruleCategoryIdList: [values] });
            this.setState({
                search: { ...search, ruleCategoryIdList: [values] },
            });
        }
    }
    renderOperationTopView() {
        return (
            <StorageRadio
                ref={this.radioCheckedRef}
                onChange={(values, ruleCategoryIdList) => this.getValueChild(values, ruleCategoryIdList)}
            />
        );
    }

    remarkFn(row) {
        return (
            <>
                {row.isSystemDesc && <Tag color="blue">{row.isSystemDesc}</Tag>}
                {row.remark}
            </>
        );
    }

    ruleCategoryFn(row) {
        return <Tag color="error">{row.ruleCategory}</Tag>;
    }

    // 异常
    getAbnormal(row) {
        return (
            <div
                style={{
                    wordWrap: "break-word",
                    wordBreak: "break-all",
                    whiteSpace: "normal",
                }}>
                <span>{row.exceptionTimeDesc}</span>
                <Tag color="magenta">{row.ruleCategory}</Tag>
                <span>{row.exceptionMsg}</span>
            </div>
        );
    }
    //超时开始节点与时间
    getStartTime(row) {
        return row.startNodeDesc + row.startNodeTimeDesc;
    }
    //超时结束节点与时间
    getEndTime(row) {
        return row.endNodeDesc + row.endNodeTimeDesc;
    }
    myOperation(row) {
        return (
            <>
                {this.state.buttons.includes("remark") && (
                    <AddTimeoutReason
                        load={() => this.load()}
                        row={row}
                        submitUrl={"/ares-admin/packageOut/exceptionRemark"}
                        selectData={this.state.selectedIdList}
                        btnTitle="备注"
                        modalTitle="备注"
                        type="Single"
                        btnType="link"
                        submitData={{
                            idList: [row.id],
                        }}
                        logRequestParams={{
                            exceptionId: row.id,
                        }}
                    />
                )}
            </>
        );
    }
}
export default AbnormalStatistics;
const StorageRadio = forwardRef(({ onChange }, ref) => {
    const [ruleCategoryIdList, setRuleCategoryIdList] = useState([]);
    const [value, setStateValue] = useState();
    function logistCountDetails(search) {
        lib.request({
            url: "/ares-admin/packageOut/getExceptionStatistics",
            data: {
                ...search,
                ruleCategoryIdList: search.ruleCategoryIdList,
                shipTimeStart: search.shipTimeStart,
                shipTimeEnd: search.shipTimeEnd,
                sourceNo: search.sourceNo,
                merchantList: search.merchantList,
                ruleCategoryIdList: search.ruleCategoryIdList,
                warehouseList: search.warehouseList,
                channelList: search.channelList,
                shopList: search.shopList,
                overtimeCategoryIdList: search.overtimeCategoryIdList,
            },
            success: res => {
                res.map(item => {
                    if (!item.ruleCategoryId) {
                        item.ruleCategoryId = -1;
                    }
                });
                setRuleCategoryIdList(res);
            },
        });
    }
    useImperativeHandle(ref, () => ({
        resetCheck: resetCheck,
        logistCountDetails: logistCountDetails,
        childValues: setStateValue,
    }));
    const resetCheck = () => {
        setStateValue("");
    };
    const handelRadioChange = e => {
        const values = e.target.value;
        setStateValue(values);
        onChange(values, ruleCategoryIdList);
    };
    return (
        <div className="platform">
            <Radio.Group buttonStyle="solid" onChange={handelRadioChange} value={+value?.toString()}>
                {ruleCategoryIdList &&
                    ruleCategoryIdList?.map((item, index) => {
                        return (
                            <Radio.Button
                                value={item.ruleCategoryId}
                                style={{ marginLeft: 8, marginTop: 8 }}
                                key={index}>
                                <div className="monitoring">
                                    <div className="monitoring_top">
                                        <span className="monitoring_top_span">{item.ruleCategory}</span>
                                    </div>
                                    <div className="monitoring_num">{item.num}</div>
                                </div>
                            </Radio.Button>
                        );
                    })}
            </Radio.Group>
        </div>
    );
});
