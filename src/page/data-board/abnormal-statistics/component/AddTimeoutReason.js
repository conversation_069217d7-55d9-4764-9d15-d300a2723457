import React, { useState, useEffect } from "react";
import { Modal, Form, Radio, message, Table, Button, Input, Alert } from "antd";
import { lib } from "react-single-app";

const AddTimeoutReason = ({
    load,
    row,
    submitUrl,
    type,
    selectData,
    submitData,
    btnTitle,
    modalTitle,
    btnType,
    logRequestParams,
}) => {
    const [form] = Form.useForm();
    const { TextArea } = Input;
    const [isModalOpen, setIsModalOpen] = useState(false);
    const [dataSource, setDataSource] = useState([]);
    const [openList, setOpenList] = useState([]);
    useEffect(() => {
        if (isModalOpen) {
            lib.request({
                url: "/ares-admin/packageOut/remark/selectItem",
                data: {
                    hasSystem: false,
                },
                success: res => {
                    setOpenList(
                        res.map(item => {
                            return { id: item.id, name: item.reason };
                        }),
                    );
                },
            });
            if (type === "Single") {
                lib.request({
                    url: "/ares-admin/packageOut/operateRecord",
                    data: logRequestParams,
                    success: res => {
                        setDataSource(res);
                    },
                });
            }
        }
    }, [isModalOpen]);
    const showModal = () => {
        if (type === "batch" && selectData.length === 0) {
            return message.warning("请选择数据");
        }
        setIsModalOpen(true);
    };
    const handleOk = async () => {
        try {
            const values = await form.validateFields();
            lib.request({
                url: submitUrl,
                data: { ...submitData, ...values },
                success: res => {
                    load();
                    message.success("添加成功");
                    setIsModalOpen(false);
                },
            });
        } catch (error) {}
    };
    const handleCancel = () => {
        setIsModalOpen(false);
    };

    const columns = [
        {
            title: "异常类型",
            dataIndex: "overtimeCategory",
            key: "overtimeCategory",
            width: 100,
        },
        {
            title: "备注",
            dataIndex: "remark",
            key: "remark",
            width: 200,
            textWrap: "word-break",
        },
        {
            title: "操作人",
            dataIndex: "operator",
            key: "operator",
            width: 100,
        },
        {
            title: "操作时间",
            dataIndex: "createdTimeDesc",
            key: "createdTimeDesc",
            width: 100,
        },
    ];

    return (<>
        <Button type={btnType} style={{ padding: btnType === "link" ? "0" : "4px 15px" }} onClick={showModal}>
            {btnTitle}
        </Button>
        <Modal
            title={modalTitle}
            destroyOnClose
            footer={null}
            open={isModalOpen}
            onOk={handleOk}
            onCancel={handleCancel}
            width={800}>
            <Alert
                style={{ marginBottom: 8 }}
                message="请按照实际情况进行备注，若分类与实际不符，则补充说明"
                type="info"
            />
            <Form form={form} preserve={false}>
                <Form.Item label="备注分类" name="remarkId" rules={[{ required: true }]}>
                    <Radio.Group>
                        {openList &&
                            openList.map(item => (
                                <Radio value={item.id} key={item.id}>
                                    {item.name}
                                </Radio>
                            ))}
                    </Radio.Group>
                </Form.Item>
                <Form.Item name="remark" rules={[{ required: true }]}>
                    <TextArea showCount autoSize={{ minRows: 3, maxRows: 5 }} maxLength={255} />
                </Form.Item>
            </Form>
            <div style={{ display: "flex", flexDirection: "row-reverse" }}>
                <Button type="primary" onClick={handleOk}>
                    提交
                </Button>
            </div>
            {type === "Single" && (
                <>
                    <h2 style={{ fontWeight: "bold" }}>操作记录</h2>
                    <Table
                        dataSource={dataSource}
                        columns={columns}
                        pagination={false}
                        scroll={{ x: "100%", y: "300px" }}
                    />
                </>
            )}
        </Modal>
    </>);
};

export default AddTimeoutReason;
