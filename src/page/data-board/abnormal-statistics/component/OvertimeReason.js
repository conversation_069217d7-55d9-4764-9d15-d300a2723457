import React, { useState, useEffect } from "react";
import { Modal, Form, Space, message, Al<PERSON>, Button, Input } from "antd";
import { lib } from "react-single-app";
import { PlusOutlined, DeleteOutlined } from "@ant-design/icons";
// import { IntToChinese } from "../../../common/format";
import "../css/overtime.less";
const OvertimeReason = ({ load }) => {
    const [form] = Form.useForm();
    const [isModalOpen, setIsModalOpen] = useState(false);
    const [typeNameList, setTypeNameList] = useState([]);
    useEffect(() => {
        if (isModalOpen) {
            lib.request({
                url: "/ares-admin/packageOut/remark/selectItem",
                data: { hasSystem: false },
                success: res => {
                    setTypeNameList(res);
                    form.setFieldsValue({ typeNameList: res });
                },
            });
        }
    }, [isModalOpen]);
    const showModal = () => {
        setIsModalOpen(true);
    };
    const handleOk = async () => {
        try {
            const { typeNameList } = await form.validateFields();
            const modifyList = typeNameList.filter(item => !item.id && item.reason);
            let data = modifyList.map(item => {
                return {
                    reason: item.reason,
                    type: "PACKAGE_OUT",
                };
            });
            if (modifyList.length) {
                lib.request({
                    url: "/ares-admin/overtimeCategory/modify",
                    data: { modifyList: data },
                    needMask: true,
                    success: res => {
                        message.success("新增成功");
                        setIsModalOpen(false);
                    },
                });
            } else {
                setIsModalOpen(false);
            }
        } catch (error) {}
    };

    const handleCancel = () => {
        setIsModalOpen(false);
    };
    const addBusType = () => {
        setTypeNameList([...typeNameList, { reason: "" }]);
    };
    const deleteBusType = async index => {
        const { typeNameList } = await form.validateFields();
        if (typeNameList[index]?.id) {
            lib.request({
                url: "/ares-admin/overtimeCategory/modify",
                data: {
                    modifyList: [{ id: typeNameList[index].id }],
                },
                success: res => {
                    message.success("删除成功");
                    typeNameList.splice(index, 1);
                    setTypeNameList(typeNameList);
                    form.setFieldsValue({ typeNameList: typeNameList });
                },
            });
        } else {
            typeNameList.splice(index, 1);
            setTypeNameList(typeNameList);
            form.setFieldsValue({ typeNameList: typeNameList });
        }
    };
    return (
        (<div>
            <a onClick={showModal}>备注归类管理</a>
            <Modal
                destroyOnClose
                title="备注归类管理"
                open={isModalOpen}
                onOk={handleOk}
                onCancel={handleCancel}
                className="overtime"
                width={660}>
                <Alert
                    style={{ marginBottom: 8 }}
                    message="以下是人工备注的归类，已创建的备注类别只可删除不可编辑，删除后的类别不再出现于可选列表中，不影响历史订单的备注"
                    type="info"
                />
                <div className="overTime-height">
                    <Form form={form} preserve={false} layout="inline">
                        {typeNameList &&
                            typeNameList?.map((item, index) => {
                                return (
                                    <Form.Item label={`备注类型${index + 1}`} style={{ marginTop: 12 }} key={index}>
                                        <Form.Item noStyle name={["typeNameList", index, "reason"]}>
                                            <Input style={{ width: 180 }} />
                                        </Form.Item>
                                        <Form.Item hidden name={["typeNameList", index, "id"]}>
                                            <Input style={{ width: 180 }} />
                                        </Form.Item>
                                        <DeleteOutlined
                                            style={{ marginLeft: 12, cursor: "pointer" }}
                                            onClick={() => deleteBusType(index)}
                                        />
                                    </Form.Item>
                                );
                            })}
                        <Button onClick={addBusType} style={{ marginTop: 12 }}>
                            <PlusOutlined />
                            新增类型
                        </Button>
                    </Form>
                </div>
            </Modal>
        </div>)
    );
};

export default OvertimeReason;
