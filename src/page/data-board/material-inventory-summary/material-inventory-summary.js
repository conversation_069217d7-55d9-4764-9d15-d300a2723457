import React, { useEffect } from "react";
import { lib, SearchList, HOC, getConfigDataUtils } from "react-single-app";
import { <PERSON><PERSON>, Button, Modal, Table } from "antd";
import axios from "axios";

export default class MaterialInventorySummary extends SearchList {
    constructor(props) {
        super(props);
        this.state.isModalOpen = false;
        this.state.modalTitle = "总入";
        this.state.goodsSeqNoCondition = "Default";
        this.state.dataType = "2";
        this.state.detailTableData = [];
        this.state.modalPagination = {
            currentPage: 1,
            pageSize: 20,
            totalPage: 0,
        };
        console.log(this.state.pagination);
    }

    getConfig() {
        let url = getConfigDataUtils.getDataUrlByDataId(760);
        return axios.get(url).then(e => e.data.data);
    }

    configLoadDefaultParams() {
        return { goodsSeqNoCondition: this.state.goodsSeqNoCondition, dataType: this.state.dataType };
    }
    renderLeftOperation() {
        // console.log('state', this.state)
        this.state._loading = false;
        return (
            <>
                <Button onClick={() => this.handleSort()}>金二序号排序</Button>
            </>
        );
    }

    renderRightOperation() {
        return (
            <>
                <Button
                    className="btn"
                    onClick={() => {
                        let { pagination, search, goodsSeqNoCondition, dataType } = this.state;
                        lib.request({
                            url: "/ccs/itemstocksummary/excelExport",
                            needMask: true,
                            data: { goodsSeqNoCondition, dataType, ...pagination, ...search },
                            success: json => {
                                lib.openPage("/excel/download-center?page_title=下载中心");
                            },
                        });
                    }}>
                    导出 &#xe638;
                </Button>
            </>
        );
    }

    renderOperationTopView() {
        return (
            <Tabs defaultActiveKey="2" onChange={e => this.handleChange(e)}>
                {/* <Tabs.TabPane tab="清关平台" key="1" /> */}
                <Tabs.TabPane tab="海关平台" key="2" />
            </Tabs>
        );
    }
    renderModal = () => {
        const { isModalOpen, modalTitle } = this.state;
        const columns = [
            {
                title: "企业内部编号",
                dataIndex: "sn",
                key: "sn",
            },
            {
                title: "核注清单编号",
                dataIndex: "realOrderNo",
                key: "realOrderNo",
            },
            {
                title: "数量",
                dataIndex: "eventQty",
                key: "eventQty",
            },
        ];
        const { detailTableData, modalPagination } = this.state;
        return (
            <>
                <Modal title={modalTitle} visible={isModalOpen} footer={null} onCancel={() => this.handleCancel()}>
                    <Table
                        columns={columns}
                        dataSource={detailTableData}
                        pagination={{
                            current: modalPagination.currentPage,
                            pageSize: modalPagination.pageSize,
                            total: modalPagination.totalCount,
                            showTotal: total => `总共 ${total} 条`,
                            showSizeChanger: true,
                            pageSizeOptions: ["10", "20", "30", "40", "50", "100", "200"],
                            onChange: (page, pageSize) => {
                                let { bookId, type, goodsName, goodsSeqNo, customsRecordProductId } = this.cacheRow;
                                this.showDetail(
                                    bookId,
                                    type,
                                    goodsName,
                                    goodsSeqNo,
                                    customsRecordProductId,
                                    page,
                                    pageSize,
                                );
                            },
                        }}
                    />
                </Modal>
            </>
        );
    };

    handleCancel = () => {
        this.setState({
            isModalOpen: false,
            modalPagination: {
                currentPage: 1,
                pageSize: 20,
                totalPage: 0,
            },
        });
    };

    handleChange = e => {
        const type = ["1", "2"];
        this.setState({
            dataType: type[e - 1],
        });
        this.changeImmutable({ dataType: type[e - 1] });
    };

    handleSort = () => {
        const condition = ["Desc", "Asc", "Default"];
        let num = condition.indexOf(this.state.goodsSeqNoCondition);
        num = (num + 1) % 3;
        this.setState({
            goodsSeqNoCondition: condition[num],
        });
        this.changeImmutable({ goodsSeqNoCondition: condition[num] });
    };

    allInDetail(row) {
        return (
            <a
                onClick={() => {
                    this.showDetail(row.bookId, "总入", row.goodsName, row.goodsSeqNo, row.customsRecordProductId);
                }}>
                {row.totalInQty}
            </a>
        );
    }

    allOutDetail(row) {
        return (
            <a
                onClick={() => {
                    this.showDetail(row.bookId, "总出", row.goodsName, row.goodsSeqNo, row.customsRecordProductId);
                }}>
                {row.totalOutQty}
            </a>
        );
    }

    showDetail(bookId, type, goodsName, goodsSeqNo, customsRecordProductId, page, pageSize) {
        const { modalTitle, modalPagination, search, dataType } = this.state;
        let url = "/ccs/itemstocksummary/findindetails";
        if (type === "总出") url = "/ccs/itemstocksummary/findoutdetails";
        this.cacheRow = {
            bookId,
            type,
            goodsName,
            goodsSeqNo,
            customsRecordProductId,
        };
        const params = {
            dataType: dataType,
            ...modalPagination,
            bookId,
            goodsName,
            goodsSeqNo,
            customsRecordProductId,
        };
        if (page) {
            params.currentPage = page;
        }
        if (pageSize) {
            params.pageSize = pageSize;
        }
        this.setState({ isModalOpen: true, modalTitle: type });
        lib.request({
            url: url,
            data: params,
            success: res => {
                this.setState({
                    detailTableData: res.dataList.map(item => {
                        return { sn: item.sn, realOrderNo: item.realOrderNo, eventQty: item.eventQty };
                    }),
                    modalPagination: res.page,
                });
            },
        });
    }

    differenceNum(row) {
        if (row.gainType == "Error") return <div style={{ color: "red" }}>错误</div>;
        return row.diffQty;
    }
}
