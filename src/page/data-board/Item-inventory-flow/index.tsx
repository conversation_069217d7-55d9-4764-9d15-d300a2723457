import React, { useState, useEffect, useImperative<PERSON><PERSON><PERSON>, forwardRef } from "react";
import { lib, SearchList, getConfigDataUtils, HOC, event, DDYObject } from "react-single-app";
import axios from "axios";
import { Button, Space, Badge, Tag, Modal, message, Radio, Tooltip, Switch, Tabs } from "antd";
import NewModal from "@/components/NewModal";
//@ts-ignore
@HOC.mapAuthButtonsToState({
    buttonCodeArr: ["add", "edit"],
})
class ItemInventoryFlow extends SearchList<any, any> {
    constructor(props: any) {
        super(props);
        this.state = {
            ...this.state,
            dataType: "1", // 1 清关; 2 海关
            goodsSeqNoCondition: "Default", //Desc  降序;Asc  升序;Default  默认排序
        };
    }

    getConfig() {
        let url = getConfigDataUtils.getDataUrlByDataId(759);
        return axios.get(url).then(res => res.data.data);
    }
    componentDidMount() {
        event.on("onSearchReset", this.onSearchReset);
    }

    componentWillUnmount() {
        event.off("onSearchReset", this.onSearchReset);
    }

    onSearchReset() {}

    operateFn(row) {
        return (
            <Button
                type="link"
                onClick={() => {
                    this.setState({
                        status: "edit",
                        imporeOpen: true,
                        editRow: row,
                    });
                }}>
                编辑
            </Button>
        );
    }
    addNew() {
        this.setState({
            status: "add",
            imporeOpen: true,
            editRow: null,
        });
    }
    renderRightOperation() {
        return (
            <>
                <Button
                    className="btn"
                    onClick={() => {
                        let { pagination, search, goodsSeqNoCondition, dataType } = this.state;
                        lib.request({
                            url: "/ccs/itemstocklist/excelExport",
                            needMask: true,
                            data: { goodsSeqNoCondition, dataType, ...pagination, ...search },
                            success: json => {
                                lib.openPage("/excel/download-center?page_title=下载中心");
                            },
                        });
                    }}>
                    导出 &#xe638;
                </Button>
            </>
        );
    }
    // @ts-ignore
    renderOperationTopView() {
        return (
            <>
                <Tabs
                    onChange={e => {
                        // dataType
                        this.setState(
                            {
                                dataType: e,
                            },
                            () => {
                                this.load();
                            },
                        );
                    }}>
                    <Tabs.TabPane tab={`清关平台`} key="1"></Tabs.TabPane>
                    <Tabs.TabPane tab={`海关平台`} key="2"></Tabs.TabPane>
                </Tabs>
            </>
        );
    }
    // @ts-ignore
    configLoadDefaultParams() {
        return {
            dataType: this.state.dataType,
            goodsSeqNoCondition: this.state.goodsSeqNoCondition,
        };
    }
    renderLeftOperation() {
        return (
            <Space>
                <Button
                    onClick={() => {
                        this.setState(
                            {
                                goodsSeqNoCondition: this.state.goodsSeqNoCondition === "Asc" ? "Desc" : "Asc",
                            },
                            () => {
                                this.load();
                            },
                        );
                    }}
                    type="primary">
                    金二序号排序
                </Button>
                <Button
                    onClick={() => {
                        const { selectedIdList } = this.state;
                        if (selectedIdList.length === 0) {
                            return message.warning("请选择至少一条数据删除");
                        }
                        Modal.confirm({
                            title: "批量删除",
                            content: `是否确认删除料号库存流水？`,
                            onOk: () => {
                                lib.request({
                                    url: "/ccs/itemstocklist/deleteByIds",
                                    data: {
                                        idList: selectedIdList,
                                    },
                                    success: () => {
                                        message.success("批量删除成功");
                                        this.load();
                                    },
                                });
                            },
                        });
                    }}
                    type="primary">
                    批量删除
                </Button>
                <Button
                    onClick={() => {
                        const { selectedIdList } = this.state;
                        if (selectedIdList.length === 0) {
                            return message.warning("请选择至少一条数据撤销");
                        }
                        Modal.confirm({
                            title: "批量撤销异常",
                            content: `是否确认撤销异常？`,
                            onOk: () => {
                                lib.request({
                                    url: "/ccs/itemstocklist/cancelAbnormalTypeByIds",
                                    data: {
                                        idList: selectedIdList,
                                    },
                                    success: () => {
                                        message.success("批量撤销异常成功");
                                        this.load();
                                    },
                                });
                            },
                        });
                    }}
                    type="primary">
                    批量撤销异常
                </Button>
            </Space>
        );
    }
}

export default ItemInventoryFlow;
