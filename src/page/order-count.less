.order-count {
    display: flex;
    flex-direction: column;
    height: 100%;
    overflow: hidden;
    .search-conditions {
        background: #fff;
        padding: 20px 50px 20px 0;
    }
    .row {
        margin-bottom: 20px;
        .col {
            max-width: 360px;
        }
    }
    .label {
        width: 120px;
        padding-right: 10px;
        text-align: right;
        display: inline-block;
    }
    .select {
        width: calc(100% - 120px);
    }
    .chart {
        background: #fff;
        margin-top: 10px;
        position: relative;
        flex-grow: 1;
        overflow: hidden;
        .type {
            position: absolute;
            left: 50%;
            top: 10px;
            transform: translateX(-50%);
            z-index: 10;
        }
    }
}
