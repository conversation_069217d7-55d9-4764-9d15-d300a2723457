import React, { useEffect, useState } from "react";
import { Tabs, Typography } from "antd";
import { ListDeclarationDetails } from "./components/list-declaration-details";
import { OrderDeclarationDetail } from "./components/order-declaration-detail";
import DeclarationTrackLog from "./components/declaration-track-log";
import { CopyOutlined } from "@ant-design/icons";
import "./index.less";
import { lib } from "react-single-app";
import { handleCopy } from "../../common/utils";
const { Title } = Typography;
export function DeclarationManageDetail() {
    const [detailData, setDetailData] = useState({});
    useEffect(() => {
        setImmediate(() => {
            getDetail();
        });
    }, []);
    function getDetail() {
        console.log(lib.getParam("orderId"));
        lib.request({
            url: "/ccs/order/getOrderDetail",
            data: { orderId: lib.getParam("orderId") },
            success: res => {
                setDetailData(res);
            },
        });
    }
    return (
        <div className={"declaration-manage-detail"}>
            <Title level={4} className={"titleTabel"}>
                申报单号：{detailData?.declareOrderNo}
            </Title>
            <div className="detail-order-info">
                <div className="detail-order-info-item">
                    渠道订单号： {detailData.outOrderNo}
                    <CopyOutlined
                        style={{
                            fontSize: "16px",
                            color: "#1890ff",
                            marginLeft: 8,
                        }}
                        onClick={() => {
                            handleCopy(detailData.outOrderNo);
                        }}
                    />
                </div>
                <div className="detail-order-info-item">
                    运单编号： {detailData?.customsInventory?.logisticsNo}
                    <CopyOutlined
                        style={{
                            fontSize: "16px",
                            color: "#1890ff",
                            marginLeft: 8,
                        }}
                        onClick={() => {
                            handleCopy(detailData?.customsInventory?.logisticsNo);
                        }}
                    />
                </div>
            </div>

            <div className={"declaration-manage-detail-tab"}>
                <Tabs
                    defaultActiveKey={"1"}
                    destroyInactiveTabPane={true}
                    style={{ height: "100%", marginLeft: "20px" }}>
                    <Tabs.TabPane tab={"清单申报"} key="1">
                        <ListDeclarationDetails
                            detailData={detailData}
                            load={() => {
                                getDetail();
                            }}
                        />
                    </Tabs.TabPane>
                    {detailData?.order !== null && (
                        <Tabs.TabPane tab={"订单申报"} key="2">
                            <OrderDeclarationDetail detailData={detailData} />
                        </Tabs.TabPane>
                    )}
                    <Tabs.TabPane tab={"轨迹日志"} key="3" style={{ height: "100%" }}>
                        <DeclarationTrackLog sn={detailData?.sn} />
                    </Tabs.TabPane>
                </Tabs>
            </div>
        </div>
    );
}
export default DeclarationManageDetail;
