import React, { useEffect, useState } from "react";
import { ConfigFormCenter } from "react-single-app";
import { ListDeclarationDetailsConfig } from "./view-config";

export function ListDeclarationDetails({ detailData, load }) {
    const ref = React.useRef();
    const configData = ListDeclarationDetailsConfig(() => {
        load();
    });
    useEffect(() => {
        if (detailData) {
            const isReturnBol =
                detailData?.customsInventory?.refundMailNo ||
                detailData?.customsInventory?.refundCustomCodeDesc ||
                detailData?.customsInventory?.refundPartFlagDesc ||
                detailData?.customsInventory?.totalRefundTax;
            ref.current.config.refundInfo.hideen = !isReturnBol;
            if (isReturnBol) {
                ref.current.config.goodsInfo.columns.splice(17, 0, {
                    title: "逆向申报数量",
                    name: "refundDeclareQty",
                    width: 100,
                    type: "text",
                    wrapperCol: { span: 24 },
                });
                ref.current.changeConfig(ref.current.config);
            }
            ref.current.setMergeDetail(detailData);
        }
    }, [detailData]);

    return <ConfigFormCenter ref={ref} confData={configData} />;
}
