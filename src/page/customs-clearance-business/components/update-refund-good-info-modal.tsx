import React, { useRef, useEffect, useState } from "react";
import { <PERSON><PERSON>, But<PERSON>, Alert, message } from "antd";
import { DTEditForm, DTEditFormRefs, DTEditFormConfigs } from "@dt/components";
import { lib } from "react-single-app";
// import './index.less';

interface ModifyReturnGoodsModalProps {
    onClose: (success?: boolean) => void;
    initialValues?: {
        id: number;
        barCode?: string;
        refundCount?: number;
    };
}

const ModifyReturnGoodsModal: React.FC<ModifyReturnGoodsModalProps> = ({ onClose, initialValues }) => {
    const [open, setOpen] = useState(false);
    const formRef = useRef<DTEditFormRefs>({} as DTEditFormRefs);

    // 表单配置
    const configs: DTEditFormConfigs = [
        {
            type: "INPUT",
            fProps: {
                label: "条码",
                name: "barCode",
                rules: [{ required: true, message: "请输入条码" }],
                labelCol: { span: 6 },
                wrapperCol: { span: 18 },
            },
        },
        {
            type: "INPUTNUMBER",
            fProps: {
                label: "退货数量",
                name: "count",
                rules: [{ required: true, message: "请输入退货数量" }],
                labelCol: { span: 6 },
                wrapperCol: { span: 18 },
            },
            cProps: {
                min: 1,
                precision: 0,
                style: { width: "100%" },
            },
        },
    ];

    // 当弹窗显示时，设置初始值
    useEffect(() => {
        if (open && initialValues) {
            setTimeout(() => {
                formRef.current.mergeDetail({ ...initialValues, count: initialValues.refundCount });
            }, 300);
        }
    }, [open, initialValues]);

    // 保存处理
    const handleSave = () => {
        formRef.current.form.validateFields().then(values => {
            lib.request({
                url: "/ccs/calloff/editRefundGoodsInfo",
                data: {
                    ...values,
                    id: initialValues.id,
                    orderId: lib.getParam("orderId"),
                },
                success: res => {
                    message.success("修改成功");
                    onClose(true);
                    setOpen(false);
                    formRef.current.form.resetFields();
                },
            });
        });
    };

    return (
        <>
            <a
                onClick={() => {
                    setOpen(true);
                }}>
                修改
            </a>
            <Modal
                title="修改"
                open={open}
                onCancel={() => {
                    setOpen(false);
                    formRef.current.form.resetFields();
                }}
                footer={[
                    <Button
                        key="cancel"
                        onClick={() => {
                            setOpen(false);
                            formRef.current.form.resetFields();
                        }}>
                        取消
                    </Button>,
                    <Button key="save" type="primary" onClick={handleSave}>
                        保存
                    </Button>,
                ]}
                destroyOnClose
                width={550}
                className="modify-return-goods-modal"
                maskClosable={false}>
                <Alert
                    message="修改前请与退货仓核实包裹货品信息！"
                    type="info"
                    showIcon={false}
                    className="info-alert"
                    style={{ marginBottom: "20px" }}
                />

                <DTEditForm
                    ref={formRef}
                    configs={configs}
                    layout={{
                        mode: "appoint",
                        colNum: 1,
                    }}
                />
            </Modal>
        </>
    );
};

export default ModifyReturnGoodsModal;
