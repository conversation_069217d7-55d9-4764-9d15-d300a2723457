import React from "react";
import { Form, Tooltip, Space, Tag } from "antd";
import UpdateRefundGoodInfoModal from "./update-refund-good-info-modal";
/**
 * 清单申报
 * @returns {{baseInfo: {children: [{editEnable: boolean, name: string, label: string, type: string}, {editEnable: boolean, name: string, label: string, type: string}, {editEnable: boolean, name: string, label: string, type: string}, {editEnable: boolean, name: string, label: string, type: string}, {editEnable: boolean, name: string, label: string, type: string}, null, null, null, null, null, null, null, null, null, null, null, null, null], name: string, className: string, label: string, isGroup: boolean}, goodsInfo: {columns: [{name: string, width: string, title: string, type: string}, {name: string, width: string, title: string, type: string}, {name: string, width: string, title: string, type: string}, {name: string, width: string, title: string, type: string}, {name: string, width: string, title: string, type: string}, null, null, null, null, null, null, null, null, null, null, null, null, null, null], name: string, className: string, label: string, type: string, isGroup: boolean}}}
 * @constructor
 */
export function ListDeclarationDetailsConfig(onClose, detailData) {
    const columns = [
        {
            title: "商品序号",
            name: "sort",
            width: 100,
            type: "text",
            wrapperCol: { span: 24 },
        },
        {
            title: "sku",
            name: "itemNo",
            width: 180,
            type: "container",
            wrapperCol: { span: 24 },
            render: (value, record) => {
                return (
                    <>
                        {value}
                        <Space>
                            {record.tagList &&
                                record.tagList.map((item, index) => {
                                    return (
                                        <Tag color="blue" key={index}>
                                            {item}
                                        </Tag>
                                    );
                                })}
                        </Space>
                    </>
                );
            },
        },
        {
            title: "统一料号",
            name: "unifiedProductId",
            width: 150,
            type: "text",
            wrapperCol: { span: 24 },
        },
        {
            title: "海关备案料号",
            name: "productId",
            width: 150,
            type: "text",
            wrapperCol: { span: 24 },
        },
        {
            title: "金二序号",
            name: "goodsSeqNo",
            width: 100,
            type: "text",
            wrapperCol: { span: 24 },
        },
        {
            title: "账册商品名称",
            name: "goodsName",
            width: 100,
            type: "text",
            wrapperCol: { span: 24 },
        },
        //  {
        //     title: "备案名称",
        //     name: 'goodsName',
        //     width: 100,
        //     type: "text",
        //     wrapperCol: {span: 24}
        // },
        {
            title: "HS编码",
            name: "hsCode",
            width: 150,
            type: "text",
            wrapperCol: { span: 24 },
        },
        {
            title: "条码",
            name: "barCode",
            width: 100,
            type: "text",
            wrapperCol: { span: 24 },
        },
        {
            title: "规格型号",
            name: "goodsModel",
            width: 100,
            type: "text",
            wrapperCol: { span: 24 },
        },
        {
            title: "原产国",
            name: "originCountryDesc",
            width: 100,
            type: "text",
            wrapperCol: { span: 24 },
        },
        {
            title: "法一数量",
            name: "firstUnitAmount",
            width: 100,
            type: "text",
            wrapperCol: { span: 24 },
        },
        {
            title: "法一单位",
            name: "firstUnitDesc",
            width: 100,
            type: "text",
            wrapperCol: { span: 24 },
        },
        {
            title: "法二数量",
            name: "secondUnitAmount",
            width: 100,
            type: "text",
            wrapperCol: { span: 24 },
        },
        {
            title: "法二单位",
            name: "secondUnitDesc",
            width: 150,
            type: "text",
            wrapperCol: { span: 24 },
        },
        {
            title: "申报单位",
            name: "declareUnitDesc",
            width: 100,
            type: "text",
            wrapperCol: { span: 24 },
        },
        {
            title: "单价",
            name: "unitPrice",
            width: 100,
            type: "text",
            wrapperCol: { span: 24 },
        },
        {
            title: "数量",
            name: "count",
            width: 100,
            type: "text",
            wrapperCol: { span: 24 },
        },
        // {
        //     title: "逆向申报数量",
        //     name: "refundDeclareQty",
        //     width: 100,
        //     type: "text",
        //     wrapperCol: { span: 24 },
        // },
        {
            title: "总价",
            name: "totalPrice",
            width: 100,
            type: "text",
            wrapperCol: { span: 24 },
        },
        {
            title: "净重（kg）",
            name: "netWeight",
            width: 100,
            type: "text",
            wrapperCol: { span: 24 },
        },
        {
            title: "毛重（kg）",
            name: "grossWeight",
            width: 100,
            type: "text",
            wrapperCol: { span: 24 },
        },
        {
            title: "税金",
            name: "taxPrice",
            width: 100,
            type: "text",
            wrapperCol: { span: 24 },
        },
    ];
    const isreturnbol = detailData?.customsInventory?.calloffOrderTagDescList?.includes("退货仓");
    const isjsreturnbol = detailData?.customsInventory?.calloffOrderTagDescList?.includes("京东退货");
    return {
        baseInfo: {
            children: [
                {
                    label: "清单编号",
                    editEnable: true,
                    name: ["customsInventory", "inventoryNo"],
                    type: "text",
                },
                {
                    label: "企业内部编号",
                    editEnable: true,
                    // name: ["customsInventory", "sn"],
                    name: "sn",
                    type: "text",
                },
                {
                    label: "申报口岸",
                    editEnable: true,
                    name: ["customsInventory", "customs"],
                    type: "text",
                },
                {
                    label: "申报单状态",
                    editEnable: true,
                    name: ["customsInventory", "orderStatusDesc"],
                    type: "text",
                },
                {
                    label: "海关状态",
                    editEnable: true,
                    name: ["customsInventory", "customsStatusDesc"],
                    type: "text",
                },
                {
                    label: "电商平台",
                    editEnable: true,
                    name: ["customsInventory", "ebpName"],
                    // name: "ebpName",
                    type: "text",
                },
                {
                    label: "电商企业",
                    editEnable: true,
                    name: ["customsInventory", "ebcName"],
                    type: "text",
                },
                {
                    label: "物流企业",
                    editEnable: true,
                    name: ["customsInventory", "logisticsCompanyName"],
                    type: "text",
                },
                {
                    label: "清关企业",
                    editEnable: true,
                    name: ["customsInventory", "agentCompanyName"],
                    type: "text",
                },
                {
                    label: "担保企业",
                    editEnable: true,
                    name: ["customsInventory", "assureCompanyName"],
                    type: "text",
                },
                {
                    label: "区内企业",
                    editEnable: true,
                    name: ["customsInventory", "areaCompanyName"],
                    type: "text",
                },
                {
                    label: "实体仓名称",
                    editEnable: true,
                    name: ["customsInventory", "erpPhyWarehouseName"],
                    type: "text",
                },
                {
                    label: "账册编号",
                    editEnable: true,
                    name: ["customsInventory", "bookNo"],
                    type: "text",
                },
                {
                    label: "创建时间",
                    editEnable: true,
                    name: ["customsInventory", "orderCreateTime"],
                    type: "text",
                },
                {
                    label: "申报时间",
                    editEnable: true,
                    name: ["customsInventory", "orderDeclareTime"],
                    type: "text",
                },
                {
                    label: "订单总金额",
                    editEnable: true,
                    name: ["customsInventory", "totalPrice"],
                    type: "text",
                },
                {
                    label: "运费",
                    editEnable: true,
                    name: ["customsInventory", "feeAmount"],
                    type: "text",
                },
                {
                    label: "预扣税金",
                    editEnable: true,
                    name: ["customsInventory", "taxFee"],
                    type: "text",
                },
                {
                    label: "总税金",
                    editEnable: true,
                    name: ["customsInventory", "taxPrice"],
                    type: "text",
                },
                {
                    label: "应征关税",
                    editEnable: true,
                    name: ["customsInventory", "customsTax"],
                    type: "text",
                },
                {
                    label: "应征增值税",
                    editEnable: true,
                    name: ["customsInventory", "valueAddedTax"],
                    type: "text",
                },
                {
                    label: "应征消费税",
                    editEnable: true,
                    name: ["customsInventory", "consumptionTax"],
                    type: "text",
                },
                {
                    label: "订购人姓名",
                    editEnable: true,
                    name: ["customsInventory", "buyerName"],
                    type: "text",
                },
                {
                    label: "订购人身份证号",
                    editEnable: true,
                    name: ["customsInventory", "buyerIdNumber"],
                    type: "text",
                },
                {
                    label: "订购人电话",
                    editEnable: true,
                    name: ["customsInventory", "buyerTelNumber"],
                    type: "text",
                },
                {
                    label: "收件人姓名",
                    editEnable: true,
                    name: ["customsInventory", "consigneeName"],
                    type: "text",
                },
                {
                    label: "收件人电话",
                    editEnable: true,
                    name: ["customsInventory", "consigneeTel"],
                    type: "text",
                },
                {
                    label: "收货（省）",
                    editEnable: true,
                    name: ["customsInventory", "consigneeProvince"],
                    type: "text",
                },
                {
                    label: "市",
                    editEnable: true,
                    name: ["customsInventory", "consigneeCity"],
                    type: "text",
                },
                {
                    label: "区",
                    editEnable: true,
                    name: ["customsInventory", "consigneeDistrict"],
                    type: "text",
                },
                {
                    label: "街道",
                    editEnable: true,
                    name: ["customsInventory", "consigneeStreet"],
                    type: "text",
                },
                {
                    label: "详细地址",
                    editEnable: true,
                    name: ["customsInventory", "consigneeAddress"],
                    type: "text",
                },
                {
                    label: "总净重",
                    editEnable: true,
                    name: ["customsInventory", "totalNetWeight"],
                    // name: "totalNetWeight",
                    type: "text",
                },
                {
                    label: "总毛重",
                    editEnable: true,
                    name: ["customsInventory", "totalGrossWeight"],
                    // name: "totalGrossWeight",
                    type: "text",
                },
                {
                    label: "备注",
                    name: ["customsInventory", "note"],
                    type: "renderContainer",
                    labelCol: { span: 4 },
                    customConfig: { style: { width: "740px" } },
                    render: (item, disabled, value, name, formId) => {
                        return (
                            <div className={"text-line-overflow"}>
                                <Tooltip title={value}>
                                    <div className={"text-line-overflow clamp-text"} style={{ WebkitLineClamp: 2 }}>
                                        {value}
                                    </div>
                                </Tooltip>
                            </div>
                        );
                    },
                },
            ],
            label: "基本信息",
            name: "baseInfo",
            isGroup: true,
            className: "baseInfo",
        },
        deliveryInfo: {
            children: [
                {
                    label: "是否包含非保赠品",
                    // editEnable: true,
                    name: ["customsInventory", "containFbGifts"],
                    // name: "totalGrossWeight",
                    type: "text",
                },
            ],
            label: "发货信息",
            isGroup: true,
            name: "deliveryInfo",
        },
        refundInfo: {
            children: [
                {
                    label: "退货运单号",
                    editEnable: true,
                    name: ["customsInventory", "refundMailNo"],
                    // name: "totalGrossWeight",
                    type: "text",
                },
                {
                    label: "申报口岸",
                    editEnable: true,
                    name: ["customsInventory", "refundCustomCodeDesc"],
                    // name: "totalGrossWeight",
                    type: "text",
                },
                {
                    label: "是否跨关区",
                    editEnable: true,
                    name: ["customsInventory", "refundCrossCustomsFlagDesc"],
                    // name: "totalGrossWeight",
                    type: "text",
                },
                {
                    label: "是否部分退",
                    editEnable: true,
                    name: ["customsInventory", "refundPartFlagDesc"],
                    // name: "totalGrossWeight",
                    type: "text",
                },
                {
                    label: "退货后总税金",
                    editEnable: true,
                    name: ["customsInventory", "totalRefundTax"],
                    // name: "totalGrossWeight",
                    type: "text",
                },
            ],
            label: "退货信息",
            name: "customsInventory",
            isGroup: true,
            className: "customsInventory",
        },
        refundGoodsInfo: {
            type: "tableInput",
            name: ["customsInventory", "refundGoodsInfo"],
            customConfig: { tableLayout: "fixed" },
            columns: [
                {
                    title: "SKU",
                    name: "sku",
                },
                {
                    title: "条码",
                    name: "barCode",
                },
                {
                    title: "货品名称",
                    name: "goodsName",
                },
                {
                    title: "商品货号",
                    name: "productId",
                },
                {
                    title: "正向申报数量",
                    name: "declareCount",
                },
                {
                    title: "退货数量",
                    name: "refundCount",
                },
                {
                    title: "异常",
                    name: "errorMsg",
                },
                {
                    title: "操作",
                    render: (_, record, index) => {
                        return <UpdateRefundGoodInfoModal initialValues={record} onClose={onClose} />;
                    },
                },
            ].filter(item => {
                if (isjsreturnbol) {
                    return ["sku", "productId", "goodsName", "refundCount"].includes(item.name);
                } else {
                    return !["productId"].includes(item.name);
                }
                return true;
            }),
            isGroup: false,
            label: "退货商品信息",
            hidden: !(isjsreturnbol || isreturnbol),
        },
        goodsInfo: {
            type: "tableInput",
            name: ["customsInventory", "itemList"],
            customConfig: { tableLayout: "fixed" },
            columns: columns,
            label: "商品信息",
            isGroup: false,
            className: "customsWarehouseInfo",
        },
    };
}

/**
 * 清单申报
 * @returns {{baseInfo: {children: [{editEnable: boolean, name: string, label: string, type: string}, {editEnable: boolean, name: string, label: string, type: string}, {editEnable: boolean, name: string, label: string, type: string}, {editEnable: boolean, name: string, label: string, type: string}, {editEnable: boolean, name: string, label: string, type: string}, null, null, null, null, null, null, null, null, null, null, null, null, null], name: string, className: string, label: string, isGroup: boolean}, goodsInfo: {columns: [{name: string, width: string, title: string, type: string}, {name: string, width: string, title: string, type: string}, {name: string, width: string, title: string, type: string}, {name: string, width: string, title: string, type: string}, {name: string, width: string, title: string, type: string}, null, null, null, null, null, null, null, null, null, null, null, null, null, null], name: string, className: string, label: string, type: string, isGroup: boolean}}}
 * @constructor
 */
export function OrderDeclarationDetailsConfig() {
    return {
        baseInfo: {
            children: [
                {
                    label: "电商平台",
                    editEnable: true,
                    // name: "outOrderNo",
                    name: ["order", "ebpName"],
                    type: "text",
                },
                {
                    label: "电商企业",
                    editEnable: true,
                    name: ["order", "ebcName"],
                    type: "text",
                },
                {
                    label: "申报口岸",
                    editEnable: true,
                    name: ["order", "customs"],
                    type: "text",
                },
                {
                    label: "海关状态",
                    editEnable: true,
                    name: ["order", "customsStatusDesc"],
                    type: "text",
                },
                {
                    label: "申报企业",
                    editEnable: true,
                    name: ["order", "agentCompanyName"],
                    type: "text",
                },
                {
                    label: "支付企业",
                    editEnable: true,
                    name: ["order", "payCompanyName"],
                    type: "text",
                },
                {
                    label: "订单总金额",
                    editEnable: true,
                    name: ["order", "totalAmount"],
                    type: "text",
                },
                {
                    label: "实际支付总金额",
                    editEnable: true,
                    name: ["order", "paidAmount"],
                    type: "text",
                },
                {
                    label: "商品税款",
                    editEnable: true,
                    name: ["order", "tax"],
                    type: "text",
                },
                {
                    label: "折扣金额",
                    editEnable: true,
                    name: ["order", "discount"],
                    type: "text",
                },
                {
                    label: "订购人姓名",
                    editEnable: true,
                    name: ["order", "buyerName"],
                    type: "text",
                },
                {
                    label: "订购人身份证",
                    editEnable: true,
                    name: ["order", "buyerIdNumber"],
                    type: "text",
                },
                {
                    label: "收件人姓名",
                    editEnable: true,
                    name: ["order", "consigneeName"],
                    type: "text",
                },
                {
                    labelCol: { span: 4 },
                    label: "收件人电话",
                    editEnable: true,
                    name: ["order", "consigneeTel"],
                    type: "text",
                    customConfig: { style: { width: "740px" } },
                },
                {
                    label: "收货（省）",
                    editEnable: true,
                    name: ["order", "consigneeProvince"],
                    type: "text",
                },
                {
                    label: "市",
                    editEnable: true,
                    name: ["order", "consigneeCity"],
                    type: "text",
                },
                {
                    label: "区",
                    editEnable: true,
                    name: ["order", "consigneeDistrict"],
                    type: "text",
                },
                {
                    label: "街道",
                    editEnable: true,
                    name: ["order", "consigneeStreet"],
                    type: "text",
                },
                {
                    label: "详细地址",
                    editEnable: true,
                    name: ["order", "consigneeAddress"],
                    type: "text",
                },
            ],
            label: "基本信息",
            name: "baseInfo",
            isGroup: true,
            className: "baseInfo",
        },
        goodsInfo: {
            type: "tableInput",
            name: ["order", "itemList"],
            customConfig: { tableLayout: "fixed" },
            columns: [
                {
                    title: "商品序号",
                    name: "sort",
                    width: 100,
                    type: "text",
                    wrapperCol: { span: 24 },
                },
                {
                    title: "sku",
                    name: "goodsNo",
                    width: 180,
                    type: "text",
                    wrapperCol: { span: 24 },
                },
                {
                    title: "统一料号",
                    name: "unifiedProductId",
                    width: 130,
                    type: "text",
                    wrapperCol: { span: 24 },
                },
                {
                    title: "海关备案料号",
                    name: "recordNo",
                    width: 180,
                    type: "text",
                    wrapperCol: { span: 24 },
                },
                {
                    title: "账册商品名称",
                    name: "name",
                    width: 180,
                    type: "text",
                    wrapperCol: { span: 24 },
                },
                {
                    title: "HS编码",
                    name: "hsCode",
                    width: 150,
                    type: "text",
                    wrapperCol: { span: 24 },
                },
                {
                    title: "条码",
                    name: "barcode",
                    width: 150,
                    type: "text",
                    wrapperCol: { span: 24 },
                },
                {
                    title: "原产国",
                    name: "originCountryDesc",
                    width: 150,
                    type: "text",
                    wrapperCol: { span: 24 },
                },
                {
                    title: "申报单价",
                    name: "declareUnitDesc",
                    width: 150,
                    type: "text",
                    wrapperCol: { span: 24 },
                },
                {
                    title: "单价",
                    name: "unitPrice",
                    width: 100,
                    type: "text",
                    wrapperCol: { span: 24 },
                },
                {
                    title: "数量",
                    name: "goodsCount",
                    width: 100,
                    type: "text",
                    wrapperCol: { span: 24 },
                },
                {
                    title: "总价",
                    name: "goodsAmount",
                    width: 100,
                    type: "text",
                    wrapperCol: { span: 24 },
                },
            ],
            label: "商品信息",
            isGroup: false,
            className: "customsWarehouseInfo",
        },
    };
}
