import React, { useEffect } from "react";
import { OrderDeclarationDetailsConfig } from "./view-config";
import { ConfigFormCenter } from "react-single-app";

export function OrderDeclarationDetail({ detailData }) {
    const ref = React.useRef();

    const configData = OrderDeclarationDetailsConfig();
    useEffect(() => {
        if (detailData) {
            ref.current.setMergeDetail(detailData);
        }
    }, [detailData]);

    return <ConfigFormCenter ref={ref} confData={configData} />;
}
