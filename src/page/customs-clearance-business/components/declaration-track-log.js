import React from "react";
import { lib, SearchList, getConfigDataUtils, HOC } from "react-single-app";
import axios from "axios";
import { Button, Fragment, Modal, message } from "antd";
import "./declaration-track-log.less";
@HOC.mapAuthButtonsToState({ pagePath: "/declaration-manage", buttonCodeArr: ["requestMessage"] })
export default class DeclarationTrackLog extends SearchList {
    constructor(props) {
        super(props);
    }

    getConfig() {
        return axios.get(getConfigDataUtils.getDataUrlByDataId(561)).then(res => res.data.data);
    }

    handleOk() {
        this.setState({
            isvisible: false,
        });
    }

    handleCancel() {
        this.setState({
            isvisible: false,
            requestMessage: null,
        });
    }

    handleCopy(requestMessage) {
        let transfer = document.createElement("input");
        document.body.appendChild(transfer);
        transfer.value = requestMessage; // 这里表示想要复制的内容
        transfer.focus();
        transfer.select();
        if (document.execCommand("copy")) {
            document.execCommand("copy");
        }
        transfer.blur();
        message.success("复制成功");
        document.body.removeChild(transfer);
        this.handleCancel();
    }

    renderCustomsReceipt(row) {
        return (
            <div style={{ textAlign: "center" }}>
                {row.customsReceipt ? (
                    <Button
                        type="link"
                        onClick={() => {
                            this.setState({
                                isvisible: true,
                                requestMessage: row.customsReceipt,
                            });
                        }}>
                        查看
                    </Button>
                ) : (
                    "暂无"
                )}
            </div>
        );
    }

    getOperation(row) {
        return (
            <>
                {this.state.buttonAuth.requestMessage && row.requestMessage != null && (
                    <Button
                        type="link"
                        onClick={() => {
                            this.setState({
                                isvisible: true,
                                requestMessage: row.requestMessage,
                                // requestData:row.requestData
                            });
                        }}>
                        查看报文
                    </Button>
                )}
            </>
        );
    }

    configLoadDefaultParams() {
        return {
            orderSn: this.props.sn,
        };
    }

    renderModal() {
        let requestMessage = this.state.requestMessage;
        return (
            <>
                {this.state.isvisible && (
                    <Modal
                        open={this.state.isvisible}
                        title="原始报文"
                        onCancel={() => this.handleCancel()}
                        footer={[
                            <Button onClick={() => this.handleCancel()}>取消</Button>,
                            <Button onClick={() => this.handleCopy(requestMessage)}>复制报文</Button>,
                        ]}
                        width={700}>
                        <p>{this.state.requestMessage}</p>
                    </Modal>
                )}
            </>
        );
    }
}
