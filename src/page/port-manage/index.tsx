import React, { useEffect, useMemo, useRef, useState } from "react";
import { SearchList } from "@dt/components";
import { DDYObject, getConfigDataUtils, lib } from "react-single-app";
import { Switch, Button, message } from "antd";
import axios from "axios";
//@ts-ignore
import NewModal from "../../components/NewModal";

export default () => {
    const searchListRef = useRef();
    const [detail, setDetail] = useState<DDYObject>();
    const [modal, setModal] = useState(false);

    const getConfig = async () => {
        let url = getConfigDataUtils.getDataUrlByDataId(816);
        const res = await axios.get(url);
        return res.data.data;
    };

    const configList = [
        {
            labelName: "口岸名称",
            labelKey: "name",
            type: "INPUT",
            required: true,
        },
        {
            labelName: "口岸编码",
            labelKey: "code",
            type: "INPUT",
            required: true,
        },
        {
            labelName: "关区编码",
            labelKey: "portCode",
            type: "INPUT",
            required: true,
        },
        {
            labelName: "所属海关",
            labelKey: "customs",
            type: "INPUT",
            required: true,
        },
    ];

    const addOK = (data: DDYObject) => {
        lib.request({
            url: detail?.id ? "/ccs/customs/manager/update" : "/ccs/customs/manager/insert",
            data: { ...data, id: detail?.id },
            success: data => {
                message.success(detail?.id ? "编辑成功" : "新增成功");
                //@ts-ignore
                searchListRef.current.load();
                setModal(false);
            },
        });
    };

    return (
        <>
            <SearchList
                ref={searchListRef}
                searchConditionConfig={{
                    size: "middle",
                }}
                headerMode={"sticky"}
                getConfig={getConfig}
                tableCustomFun={{
                    statusFn: (row: DDYObject, index: number) => {
                        return (
                            <>
                                <Switch
                                    checked={row.status == 1}
                                    onChange={e => {
                                        console.log("e:", e);
                                        lib.request({
                                            url: "/ccs/customs/manager/enableSwitch",
                                            data: {
                                                id: row.id,
                                                status: row.status == 0 ? 1 : 0,
                                            },
                                            success(data) {
                                                //@ts-ignore
                                                searchListRef.current.load();
                                            },
                                        });
                                    }}
                                />
                            </>
                        );
                    },
                    operateFn: (row: DDYObject, index: number) => {
                        if (row.status === 1) return null;
                        return (
                            <Button
                                type="link"
                                onClick={() => {
                                    setModal(true);
                                    setDetail(row);
                                }}>
                                编辑
                            </Button>
                        );
                    },
                }}
                renderModal={() => (
                    <NewModal
                        {...{
                            visible: modal,
                            configList: configList,
                            title: detail?.id ? "编辑口岸" : "新增口岸",
                            onOk: addOK,
                            // ref: newModalRef,
                            editRow: detail,
                            onCancel: () => {
                                setModal(false);
                            },
                        }}
                    />
                )}
                renderLeftOperation={() => {
                    return (
                        <>
                            <Button
                                onClick={() => {
                                    setDetail({});
                                    setModal(true);
                                }}>
                                新增
                            </Button>
                        </>
                    );
                }}
            />
        </>
    );
};
