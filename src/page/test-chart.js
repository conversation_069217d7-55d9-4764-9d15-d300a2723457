import React, { useEffect, useState, useRef } from "react";
import { Select, Row, Col, Radio, DatePicker, Button } from "antd";
var echarts = require("echarts");
import "./test-chart.less";
import ReactEcharts from "echarts-for-react";

function createData(search) {
    function create(obj, y) {
        y.map(item => {
            if (item == "全部") {
                obj[item] = obj["已创建"] + obj["进行中"] + obj["已完成"];
            } else {
                obj[item] = parseInt(Math.random() * 500);
            }
        });
        return obj;
    }
    var data = [{ title: "订单总数", mark: "订单总数\n" }];
    if (search.type == "双类形") {
        data = [
            { title: "订单总数", mark: "订单总数\n" },
            { title: "订单总金额", mark: "订单总金额\n" },
        ];
    }
    let line = ["一根线", "二根线", "三根线", "四根线"].indexOf(search.line);
    data.map(item => {
        item.list = [];
        item.y = ["已创建", "进行中", "已完成", "全部"].slice(0, line + 1);
    });
    if (search.date == "今日") {
        data.map(item => {
            for (var i = 2; i < 25; i += 2) {
                var obj = { date: i + "点" };
                item.list.push(create(obj, item.y));
            }
        });
    } else if (search.date == "近七天" || search.date == "本周") {
        data.map(item => {
            for (var i = 1; i < 8; i++) {
                var obj = { date: "星期" + i };
                item.list.push(create(obj, item.y));
            }
        });
    } else if (search.date == "本月") {
        data.map(item => {
            for (var i = 1; i < 31; i++) {
                var obj = { date: `2020-11-${i}` };
                item.list.push(create(obj, item.y));
            }
        });
    } else if (search.date == "本年") {
        data.map(item => {
            for (var i = 1; i < 31; i++) {
                var obj = { date: `2020-${i}` };
                item.list.push(create(obj, item.y));
            }
        });
    } else if (search.date == "全部") {
        data.map(item => {
            for (var i = 1; i < 60; i++) {
                var obj = { date: `2020-${10 + parseInt(i / 30)}-${(i % 30) + 1}` };
                item.list.push(create(obj, item.y));
            }
        });
    }
    data.map(item => {
        item.mark = search.date + item.mark;
        item.y.map(node => (item.mark += `${node} : xxx\n`));
    });
    console.log(search, data);
    return data;
}

function SearchCondition({ data, setData }) {
    let lines = ["一根线", "二根线", "三根线", "四根线"];
    let types = ["单类形", "双类形"];
    let dates = ["今日", "近七天", "本周", "本月", "本年", "全部"];
    let [search, setSearch] = useState({
        line: "二根线",
        type: "单类形",
        date: "今日",
    });
    useEffect(() => setData(createData(search)), [search]);
    return (
        <div className="search-conditions">
            <Row className="row">
                <Col span={9} className="col">
                    <label className="label">lines</label>
                    <Radio.Group
                        options={lines.map(item => ({ label: item, value: item }))}
                        onChange={e => {
                            setSearch({ ...search, line: e.target.value });
                        }}
                        value={search.line}
                        optionType="button"
                        buttonStyle="solid"
                    />
                </Col>
                <Col span={5} className="col">
                    <label className="label">types</label>
                    <Radio.Group
                        options={types.map(item => ({ label: item, value: item }))}
                        onChange={e => {
                            setSearch({ ...search, type: e.target.value });
                        }}
                        value={search.type}
                        optionType="button"
                        buttonStyle="solid"
                    />
                </Col>
                <Col span={10} className="col">
                    <label className="label">日期</label>
                    <Radio.Group
                        options={dates.map(item => ({ label: item, value: item }))}
                        onChange={e => {
                            setSearch({ ...search, date: e.target.value });
                        }}
                        value={search.date}
                        optionType="button"
                        buttonStyle="solid"
                    />
                </Col>
            </Row>
        </div>
    );
}

function MyChart({ data }) {
    let [type, setType] = useState(0);
    var option = null;
    if (data.length <= type) {
        type = 0;
    }
    if (data.length) {
        let obj = data[type];
        option = {
            tooltip: {
                trigger: "axis",
            },
            legend: {
                data: obj.y,
                bottom: 20,
            },
            grid: {
                left: 20,
                right: 20,
                top: 80,
                bottom: 80,
                containLabel: true,
            },
            xAxis: {
                type: "category",
                boundaryGap: false,
                data: obj.list.map(item => item.date),
            },
            yAxis: {
                type: "value",
            },
            series: obj.y.map(name => {
                var line = {
                    name,
                    type: "line",
                    smooth: true,
                };
                line.data = obj.list.map(item => item[name]);
                return line;
            }),
        };
    }
    return (
        <div className="chart">
            {data.length > 1 && (
                <div className="type">
                    <Radio.Group
                        options={data.map((item, index) => ({ label: item.title, value: index }))}
                        onChange={e => setType(e.target.value)}
                        value={type}
                        optionType="button"
                        buttonStyle="solid"
                    />
                </div>
            )}
            <ReactEcharts option={option || {}} style={{ height: "100%" }} />
            <div className="mark" dangerouslySetInnerHTML={{ __html: data[type]?.mark.replace(/\n/g, "<br/>") }}></div>
            <a
                href="https://dante-img.oss-cn-hangzhou.aliyuncs.com/79789464799.js"
                target="_blank"
                className="link download">
                下载数据
            </a>
        </div>
    );
}

function TestChart() {
    let [data, setData] = useState([]);
    return (
        <div className="test-chart">
            <SearchCondition setData={setData} key={1} />
            <MyChart data={data} />
        </div>
    );
}

export default TestChart;
