.work_overview {
    background: #f6f7fa;
    overflow: hidden;
    min-width: 1008px;
    .work_top {
        display: flex;
        .work_Top_Left {
            flex: 1;
            display: flex;
            .work_Top_Left_img {
                margin: 18px 0 18px 24px;
            }
            .work_Top_Left_h2 {
                margin-top: 14px;
                margin-left: 16px;
                font-size: 20px;
                font-family: PingFangSC-Medium, PingFang SC;
                font-weight: 500;
                color: rgba(0, 0, 0, 0.85);
            }
        }
        .work_Top_Right {
            margin-right: 16px;
            margin-top: 16px;
            position: relative;
            .work_Top_Right_Span {
                font-size: 12px;
                font-family: PingFangSC-Regular, PingFang SC;
                font-weight: 400;
                color: #999999;
                margin-right: 8px;
            }
            .buss_single_RightTop {
                position: absolute;
                right: 0px;
                top: 0px;
                z-index: 99;
            }
        }
    }
    .ant-radio-group {
        position: relative;
        z-index: 100;
    }
    .ant-picker-focused {
        border-color: #40a9ff;
        box-shadow: none !important;
        border-right-width: 1px;
        outline: 0;
    }
}
