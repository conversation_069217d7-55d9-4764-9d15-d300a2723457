import React, { useEffect, useState } from "react";
import { lib, SearchList, getConfigDataUtils, ConfigFormCenter } from "react-single-app";
import axios from "axios";
import { Modal, Button, message, Space, Form, Input, Switch, Select } from "antd";

export default class MailFilter extends SearchList {
    constructor(props) {
        super(props);
        this.state.buttons = [];
    }
    getConfig() {
        let url = getConfigDataUtils.getDataUrlByDataId(625);
        return axios.get(url).then(res => res.data.data);
    }

    renderRightOperation() {
        return (
            <Space>
                <Button
                    onClick={() => {
                        this.addHandle();
                    }}
                    type="primary">
                    新增
                </Button>
            </Space>
        );
    }

    upset(row, data) {
        lib.request({
            url: "/ccs/mailFilter/save",
            method: "POST",
            data: {
                id: row.id,
                ...data,
            },
            needMask: true,
            success: () => {
                this.load(true);
                message.success("操作成功");
            },
        });
    }

    enableFunc(row) {
        let content = row.status === 1 ? "是否禁用?" : "是否启用";
        Modal.confirm({
            cancelText: "取消",
            okText: "确定",
            title: "提示",
            content: content,
            onOk: () => {
                let status;
                if (row.status === 2) {
                    status = 1;
                } else if (row.status === 1) {
                    status = 2;
                }
                this.upset(row, { status: status });
            },
        });
    }

    enableStatus(row) {
        return (
            <React.Fragment>
                <Switch
                    checked={row.status === 1}
                    checkedChildren="ON"
                    unCheckedChildren="OFF"
                    onChange={() => this.enableFunc(row)}
                />
            </React.Fragment>
        );
    }

    getOperation(row) {
        return (
            <Space style={{ display: "flex" }}>
                {row.status == 2 ? <a onClick={() => this.editHandle(row)}>编辑</a> : null}
                <a
                    onClick={() => {
                        this.deleteHandle(row.id, row.deleted);
                    }}>
                    删除
                </a>
            </Space>
        );
    }
    editHandle(row) {
        this.setState({
            showModal: true,
            rowData: row,
        });
    }

    addHandle() {
        this.setState({
            showModal: true,
        });
    }
    deleteHandle = (id, deleted) => {
        let modal = Modal.confirm({
            title: "提示",
            content: "确定要删除吗？",
            onOk: () => {
                lib.request({
                    url: "/ccs/mailFilter/save",
                    data: { id, deleted: 1 },
                    needMask: true,
                    success: res => {
                        message.success("删除成功");
                        this.load();
                        modal.destroy();
                    },
                });
            },
            onCancel: () => modal.destroy(),
        });
    };

    renderModal() {
        return (
            <React.Fragment>
                {
                    <EditModal
                        rowData={this.state.rowData}
                        dialogClose={ok => {
                            this.setState({
                                rowData: null,
                                showModal: false,
                            });
                            if (ok) {
                                this.load();
                            }
                        }}
                        showModal={this.state.showModal}
                    />
                }
            </React.Fragment>
        );
    }
}
function EditModal({ rowData, showModal, dialogClose }) {
    const ref = React.useRef();
    const handleCancel = () => {
        dialogClose(false);
    };
    const handleOk = () => {
        ref.current.submitForm();
    };
    const beforeSubmit = values => {
        if (rowData) values.id = rowData.id;
        return values;
    };
    const onConfigLoadSuccess = config => {
        config.baseInfo.children.map(item => {
            if (item.type === "single-select" && !item.fromParam) {
                ref.current.initSelect(item);
            }
        });
        if (rowData) {
            ref.current.setMergeDetail(rowData);
        }
    };
    const onSubmitSuccess = () => {
        message.success(rowData ? "编辑邮件账号成功" : "添加邮件账号成功");
        dialogClose(true);
    };
    return (
        (<Modal
            destroyOnClose
            onCancel={handleCancel}
            onOk={handleOk}
            title={rowData ? "编辑邮件账号" : "新增邮件账号"}
            open={showModal}>
            <ConfigFormCenter
                ref={ref}
                confData={data}
                submitUrl={"/ccs/mailFilter/save"}
                beforeSubmit={beforeSubmit}
                onConfigLoadSuccess={onConfigLoadSuccess}
                onSubmitSuccess={onSubmitSuccess}
            />
        </Modal>)
    );
}

const data = {
    baseInfo: {
        children: [
            {
                label: "邮箱账号",
                editEnable: true,
                name: "account",
                type: "textInput",
                rules: [
                    {
                        required: true,
                        message: "你输入的邮箱格式不匹配",
                        pattern:
                            /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/,
                    },
                ],
            },
            {
                label: "归属人",
                editEnable: true,
                name: "name",
                type: "textInput",
                rules: [
                    {
                        validator: (rule, value, callback) => {
                            let reg = new RegExp(
                                "[`~!%@#$^&*()=|{}':;',\\[\\]<>《》/?~！@#￥……&*（）|{}【】‘；：”“'\"。，、？.·-]",
                            );
                            if (reg.test(value)) {
                                return callback("请输入中文数字字母");
                            } else {
                                return callback();
                            }
                        },
                    },
                    //   {
                    //     pattern:/^((?!\+|\.|\\|\/|:|\*|\?|<|>|\||'|%|@|#|&|\$|\^|&|\*).){1,50}$/, // 正则表达式，禁止+ . \ / : * ? < > | ' % @ # & $ ^ &
                    //     message: "请输入名称(不包含特殊字符)",
                    //   },
                    {
                        pattern: /^[^\s]*$/,
                        message: "禁止输入空格",
                    },
                ],
            },
        ],
        label: "",
        name: "baseInfo",
        isGroup: true,
        className: "baseInfo",
    },
};
