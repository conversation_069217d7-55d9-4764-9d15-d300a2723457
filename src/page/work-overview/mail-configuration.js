// 邮件配置
import React, { useState, useEffect } from "react";
import { lib, SearchList, getConfigDataUtils } from "react-single-app";
import axios from "axios";
import { Button, Modal, Switch, Form, Input, message } from "antd";
import { EyeInvisibleOutlined, EyeTwoTone } from "@ant-design/icons";
class MailConfiguration extends SearchList {
    getConfig() {
        let url = getConfigDataUtils.getDataUrlByDataId(597);
        return axios.get(url).then(res => res.data.data);
    }
    // 密码
    getPassword(row) {
        return <span>{row.password.replace(/^(.).*(.)$/, `$12*****$22`)}</span>;
    }
    EnableStatusChange(row, e) {
        let modal = Modal.confirm({
            title: "提示",
            content: `${e ? "开启邮件抓取" : "关闭后邮件将不再抓取，是否确认关闭该邮箱"}`,
            onOk: () => {
                lib.request({
                    url: "/ccs/mailConfig/save",
                    data: {
                        id: row.id,
                        status: e ? 1 : 2,
                    },
                    needMask: true,
                    success: res => {
                        message.success(`${e ? "开启成功" : "关闭成功"}`);
                        this.load();
                        modal.destroy();
                    },
                });
            },
            onCancel: () => modal.destroy(),
        });
    }
    EnableStatus(row) {
        return <Switch onChange={e => this.EnableStatusChange(row, e)} checked={row.status === 1 ? true : false} />;
    }
    renderRightOperation() {
        return <MailConfigModal load={() => this.load(true)} />;
    }
}
export default MailConfiguration;
// 新增Modal
const MailConfigModal = ({ load }) => {
    const [form] = Form.useForm();
    const [isModalVisible, setIsModalVisible] = useState(false);
    useEffect(() => {
        if (isModalVisible) {
            form.setFieldsValue({ status: true });
        }
    }, [isModalVisible]);
    const showModal = () => {
        setIsModalVisible(true);
        form.resetFields();
    };
    const handleOk = async () => {
        const values = await form.validateFields(values);
        lib.request({
            url: "/ccs/mailConfig/save",
            data: { ...values, status: values.status === false ? 2 : 1 },
            success: res => {
                message.success("新增成功");
                load();
                setIsModalVisible(false);
                form.resetFields();
            },
        });
    };
    const handleCancel = () => {
        setIsModalVisible(false);
    };
    return (
        (<div>
            <Button type="primary" onClick={showModal}>
                新增
            </Button>
            <Modal title="添加邮箱" open={isModalVisible} onOk={handleOk} onCancel={handleCancel}>
                <Form form={form}>
                    <Form.Item label="邮箱名称" name="name" rules={[{ required: true }]}>
                        <Input placeholder="请输入" />
                    </Form.Item>
                    <Form.Item
                        label="邮箱账号"
                        name="account"
                        rules={[
                            {
                                required: true,
                                message: "你输入的邮箱格式不匹配",
                                pattern:
                                    /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/,
                            },
                        ]}>
                        <Input placeholder="请输入邮箱账号" />
                    </Form.Item>
                    <Form.Item
                        label="邮箱密码"
                        name="password"
                        rules={[
                            {
                                required: true,
                            },
                        ]}>
                        <Input.Password
                            placeholder="请输入"
                            iconRender={visible => (visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />)}
                        />
                    </Form.Item>
                    <Form.Item label="启用状态" name="status" rules={[{ required: true }]} valuePropName="checked">
                        <Switch />
                    </Form.Item>
                </Form>
            </Modal>
        </div>)
    );
};
