import React from "react";
import { But<PERSON>, Table, Pagination, DatePicker, Tooltip } from "antd";
import { useAntdTable } from "ahooks";
import { lib } from "react-single-app";
import { QuestionCircleOutlined } from "@ant-design/icons";
import "../css/mail-items.less";
async function getTableData({ currentPage, pageSize, timeRange }) {
    return await lib
        .request({
            url: "/ccs/mailPool/mailCountByUserId",
            data: {
                currentPage,
                pageSize,
                startTime: timeRange[0],
                endTime: timeRange[1],
            },
        })
        .then(res => {
            return { total: res.page.totalCount, list: res.dataList };
        });
}
function MailItems({ timeRange }) {
    const { tableProps, pagination } = useAntdTable(
        res => {
            return getTableData({
                currentPage: res.current,
                pageSize: res.pageSize,
                timeRange,
            });
        },
        {
            refreshDeps: [timeRange], // 依赖项
            defaultPageSize: 5,
        },
    );
    const columns = [
        {
            title: (
                <span>
                    用户名
                    <Tooltip title="当前选中时间段中处理过数据的所有人员">
                        <QuestionCircleOutlined style={{ fontSize: 13, marginLeft: 8 }} />
                    </Tooltip>
                </span>
            ),
            dataIndex: "userName",
            key: "userName",
        },
        {
            title: (
                <span>
                    接收任务
                    <Tooltip title="当前时间段中自己认领、领导分配、他人转交的都需要被计算在内">
                        <QuestionCircleOutlined style={{ fontSize: 13, marginLeft: 8 }} />
                    </Tooltip>
                </span>
            ),
            dataIndex: "receiveTask",
            key: "receiveTask",
        },
        {
            title: (
                <span>
                    未完成
                    <Tooltip title="当待处理、处理中、已挂起">
                        <QuestionCircleOutlined style={{ fontSize: 13, marginLeft: 8 }} />
                    </Tooltip>
                </span>
            ),
            dataIndex: "unfinish",
            key: "unfinish",
        },
        {
            title: (
                <span>
                    处理完成
                    <Tooltip title="处理状态是已完成">
                        <QuestionCircleOutlined style={{ fontSize: 13, marginLeft: 8 }} />
                    </Tooltip>
                </span>
            ),
            dataIndex: "finish",
            key: "finish",
        },
        {
            title: "无需处理",
            dataIndex: "noDispose",
            key: "noDispose",
        },
        {
            title: (
                <span>
                    转交
                    <Tooltip title="当前时间段中当前用户处理过转交的订单，（同一个订单多次转交的话，只算一次）">
                        <QuestionCircleOutlined style={{ fontSize: 13, marginLeft: 8 }} />
                    </Tooltip>
                </span>
            ),
            dataIndex: "careOf",
            key: "careOf",
        },
        {
            title: "操作",
            dataIndex: "",
            render: (text, record, index) => {
                return (
                    <a
                        onClick={() => {
                            lib.openPageV2({
                                url: `/mail-pool?page_title=邮件池`,
                                params: { userId: record.userId.toString() },
                            });
                        }}>
                        查看详情
                    </a>
                );
            },
        },
    ];
    const handelExportClick = async () => {
        await lib.request({
            url: "/ccs/mailPool/mailCountExport",
            needMask: true,
            data: {
                startTime: timeRange[0],
                endTime: timeRange[1],
                currentPage: pagination.current,
                pageSize: pagination.pageSize,
                totalCount: pagination.total,
                totalPage: pagination.totalPage,
            },
            success: res => {
                lib.openPage("/download-center?page_title=下载中心");
            },
        });
    };
    return (
        <div className="mail_itemTab">
            <div className="mail_tab_top">
                <h2>邮件事项</h2>
                <div className="mail_rig_but">
                    <Button onClick={handelExportClick}>导出</Button>
                </div>
            </div>
            <div className="mail_tab">
                <div style={{ marginLeft: 16, marginRight: 16, marginTop: 16 }}>
                    <Table
                        size="small"
                        {...tableProps}
                        columns={columns}
                        scroll={{
                            y: 200,
                        }}
                        pagination={false}
                    />
                    <Pagination
                        size={"small"}
                        current={pagination.current}
                        pageSize={pagination.pageSize}
                        total={pagination?.total}
                        onChange={pagination.onChange}
                        onShowSizeChange={pagination.onChange}
                        pageSizeOptions={[5, 10, 20, 30, 50, 100]}
                        showQuickJumper
                        showSizeChanger
                        style={{ marginTop: 16, textAlign: "right", paddingBottom: 16 }}
                    />
                </div>
            </div>
        </div>
    );
}

export default MailItems;
