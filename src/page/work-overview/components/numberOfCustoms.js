// 清关完成数
import React, { useState } from "react";
import { Radio, Button, Table, Pagination, Tooltip } from "antd";
import { QuestionCircleOutlined } from "@ant-design/icons";
import { useAntdTable } from "ahooks";
import { lib } from "react-single-app";
import "../css/mail-items.less";
async function getTableData({ currentPage, pageSize, timeRange, radioType }) {
    return await lib
        .request({
            url: "/ccs/customs/complete",
            data: {
                currentPage,
                pageSize,
                type: radioType,
                startTime: timeRange[0],
                endTime: timeRange[1],
            },
        })
        .then(res => {
            return { total: res?.page.totalCount, list: res?.dataList };
        });
}
function NumberOfCustoms({ timeRange }) {
    const [radioType, setRadioType] = useState(1);
    const { tableProps, pagination } = useAntdTable(
        res => {
            return getTableData({
                currentPage: res.current,
                pageSize: res.pageSize,
                radioType,
                timeRange,
            });
        },
        {
            refreshDeps: [timeRange, radioType], // 依赖项
            defaultPageSize: 5,
        },
    );
    const columns = [
        {
            title: "用户名",
            dataIndex: "userName",
        },
        {
            title: "一线入境",
            dataIndex: "oneLineIn",
        },
        {
            title: "区间流转（入）",
            dataIndex: "sectionIn",
        },
        {
            title: "区内流转（入）",
            dataIndex: "sectionInnerIn",
        },
        {
            title: "区间流转（出）",
            dataIndex: "sectionOut",
        },
        {
            title: "区内流转（出）",
            dataIndex: "sectionInnerOut",
        },
        {
            title: "退货入区",
            dataIndex: "refundInArea",
        },
        {
            title: "销毁",
            dataIndex: "destroy",
        },
    ];
    const column = [
        {
            title: "用户名",
            dataIndex: "userName",
        },
        {
            title: "一线入境",
            dataIndex: "oneLineIn",
        },
        {
            title: "区间流转（入）",
            dataIndex: "sectionIn",
        },
        {
            title: "区间流转（出）",
            dataIndex: "sectionOut",
        },
        {
            title: "销毁",
            dataIndex: "destroy",
        },
    ];
    const handleRadioChange = e => {
        setRadioType(e.target.value);
    };
    const handelExportClick = async () => {
        await lib.request({
            url: "/ccs/businessCenter/customsComplete/export",
            needMask: true,
            data: {
                startTime: timeRange[0],
                endTime: timeRange[1],
                currentPage: pagination.current,
                pageSize: pagination.pageSize,
                totalCount: pagination.total,
                totalPage: pagination.totalPage,
                type: radioType,
            },
            success: res => {
                lib.openPage("/download-center?page_title=下载中心");
            },
        });
    };
    return (
        <div className="mail_itemTab">
            <div className="mail_tab_top">
                <h2>
                    清关完成数
                    <span style={{ fontSize: 13, marginLeft: 8 }}>
                        <Tooltip title="统计每个人完成的实际业务的单数">
                            <QuestionCircleOutlined />
                        </Tooltip>
                    </span>
                </h2>
                <div className="mail_rig_but">
                    <Button onClick={handelExportClick}>导出</Button>
                </div>
            </div>
            <div style={{ marginLeft: 16 }}>
                <Radio.Group defaultValue={1} onChange={handleRadioChange}>
                    <Radio value={1}>清关单</Radio>
                    <Radio value={2}>核注单</Radio>
                    <Radio value={3}>核放单</Radio>
                </Radio.Group>
            </div>
            <div className="mail_tab">
                <div style={{ marginLeft: 16, marginRight: 16, marginTop: 16 }}>
                    <Table
                        size="small"
                        {...tableProps}
                        pagination={false}
                        columns={radioType !== 3 ? columns : column}
                        scroll={{
                            y: 200,
                        }}
                    />
                    <Pagination
                        size={"small"}
                        current={pagination.current}
                        pageSize={pagination.pageSize}
                        total={pagination?.total}
                        onChange={pagination.onChange}
                        onShowSizeChange={pagination.onChange}
                        pageSizeOptions={[5, 10, 20, 30, 50, 100]}
                        showQuickJumper
                        showSizeChanger
                        style={{ marginTop: 16, textAlign: "right", paddingBottom: 16 }}
                    />
                </div>
            </div>
        </div>
    );
}
export default NumberOfCustoms;
