//  备案
import React from "react";
import { Button, Table, Tooltip, Pagination } from "antd";
import { useAntdTable } from "ahooks";
import { lib } from "react-single-app";
import "../css/mail-items.less";
import { QuestionCircleOutlined } from "@ant-design/icons";
async function getTableData({ currentPage, pageSize, timeRange }) {
    return await lib
        .request({
            url: "/ccs/goodsRecord/statusCount",
            data: {
                currentPage,
                pageSize,
                startTime: timeRange[0],
                endTime: timeRange[1],
            },
        })
        .then(res => {
            return { total: res.page.totalCount, list: res.dataList };
        });
}
function KeepRecord({ timeRange }) {
    const { tableProps, pagination } = useAntdTable(
        res => {
            return getTableData({
                currentPage: res.current,
                pageSize: res.pageSize,
                timeRange,
            });
        },
        {
            refreshDeps: [timeRange], // 依赖项
            defaultPageSize: 5,
        },
    );
    const columns = [
        {
            title: "用户名",
            dataIndex: "userName",
        },
        {
            title: "总提交审核数量",
            title: (
                <span>
                    总提交审核数量
                    <Tooltip title="当前时间段内所有的需要审核的备案的条数">
                        <QuestionCircleOutlined style={{ fontSize: 13, marginLeft: 8 }} />
                    </Tooltip>
                </span>
            ),
            dataIndex: "allSubmitAudit",
        },
        {
            title: "审核通过",
            title: (
                <span>
                    审核通过
                    <Tooltip title="当前时间段发起的需备案的条数，当前人操作审核通过的条数">
                        <QuestionCircleOutlined style={{ fontSize: 13, marginLeft: 8 }} />
                    </Tooltip>
                </span>
            ),
            dataIndex: "auditPass",
        },
        {
            title: "审核驳回",
            title: (
                <span>
                    审核驳回
                    <Tooltip title="当前时间段发起的需备案的条数，当前人操作审核驳回的条数">
                        <QuestionCircleOutlined style={{ fontSize: 13, marginLeft: 8 }} />
                    </Tooltip>
                </span>
            ),
            dataIndex: "auditReject",
        },
        {
            title: "总未审核",
            title: (
                <span>
                    总未审核
                    <Tooltip title="当前时间段内所有的需要审核的备案的条数中还没有操作审核通过/驳回的">
                        <QuestionCircleOutlined style={{ fontSize: 13, marginLeft: 8 }} />
                    </Tooltip>
                </span>
            ),
            dataIndex: "allAuditPending",
        },
    ];
    const handelExportClick = async () => {
        await lib.request({
            url: "/ccs/businessCenter/goodsRecord/export",
            needMask: true,
            data: {
                startTime: timeRange[0],
                endTime: timeRange[1],
                currentPage: pagination.current,
                pageSize: pagination.pageSize,
                totalCount: pagination.total,
                totalPage: pagination.totalPage,
            },
            success: res => {
                lib.openPage("/download-center?page_title=下载中心");
            },
        });
    };
    return (
        <div className="mail_itemTab">
            <div className="mail_tab_top">
                <h2>备案</h2>
                <div className="mail_rig_but">
                    <Button onClick={handelExportClick}>导出</Button>
                </div>
            </div>
            <div className="mail_tab">
                <div style={{ marginLeft: 16, marginRight: 16, marginTop: 16 }}>
                    <Table
                        size="small"
                        {...tableProps}
                        columns={columns}
                        scroll={{
                            y: 200,
                        }}
                        pagination={false}
                    />
                    <Pagination
                        size={"small"}
                        current={pagination.current}
                        pageSize={pagination.pageSize}
                        total={pagination?.total}
                        onChange={pagination.onChange}
                        pageSizeOptions={[5, 10, 20, 30, 50, 100]}
                        onShowSizeChange={pagination.onChange}
                        showQuickJumper
                        showSizeChanger
                        style={{ marginTop: 16, textAlign: "right", paddingBottom: 16 }}
                    />
                </div>
            </div>
        </div>
    );
}
export default KeepRecord;
