// 保税售后
import React from "react";
import { Radio, Button, Table, Pagination, DatePicker, Tooltip } from "antd";
import { QuestionCircleOutlined } from "@ant-design/icons";
import { useAntdTable } from "ahooks";
import { lib } from "react-single-app";
import "../css/mail-items.less";
async function getTableData({ currentPage, pageSize, timeRange }) {
    return await lib
        .request({
            url: "/ccs/bonded/postSale",
            data: {
                currentPage,
                pageSize,
                startTime: timeRange[0],
                endTime: timeRange[1],
            },
        })
        .then(res => {
            return { total: res?.page?.totalCount, list: res?.dataList };
        });
}
function BondedAfterSales({ timeRange }) {
    const { tableProps, pagination } = useAntdTable(
        res => {
            return getTableData({
                currentPage: res.current,
                pageSize: res.pageSize,
                timeRange,
            });
        },
        {
            refreshDeps: [timeRange], // 依赖项
            defaultPageSize: 5,
        },
    );
    const columns = [
        {
            title: "用户名",
            dataIndex: "userName",
            key: "userName",
        },
        {
            title: "取消单",
            children: [
                {
                    title: (
                        <span>
                            需处理
                            <Tooltip title="取消单管理中，该是时间段创建的取消单数">
                                <QuestionCircleOutlined style={{ fontSize: 13, marginLeft: 8 }} />
                            </Tooltip>
                        </span>
                    ),
                    dataIndex: ["calloffCount", "pending"],
                },
                {
                    title: (
                        <span>
                            已处理
                            <Tooltip title="取消管理中，该时间中变更为取消中+取消成功+取消失败">
                                <QuestionCircleOutlined style={{ fontSize: 13, marginLeft: 8 }} />
                            </Tooltip>
                        </span>
                    ),
                    dataIndex: ["calloffCount", "processed"],
                },
                {
                    title: (
                        <span>
                            取消驳回
                            <Tooltip title="取消管理中，截至选中时间状态变更为取消驳回">
                                <QuestionCircleOutlined style={{ fontSize: 13, marginLeft: 8 }} />
                            </Tooltip>
                        </span>
                    ),
                    dataIndex: ["calloffCount", "reject"],
                },
                {
                    title: (
                        <span>
                            未处理
                            <Tooltip title="取消管理中，截至选中时间状态仍为待取消的">
                                <QuestionCircleOutlined style={{ fontSize: 13, marginLeft: 8 }} />
                            </Tooltip>
                        </span>
                    ),
                    dataIndex: "unprocessed",
                    dataIndex: ["calloffCount", "unprocessed"],
                },
            ],
        },
        {
            title: "撤单",
            key: "cancelCount",
            children: [
                {
                    title: (
                        <span>
                            需申报
                            <Tooltip title="撤单管理中，该时间段创建的撤单数">
                                <QuestionCircleOutlined style={{ fontSize: 13, marginLeft: 8 }} />
                            </Tooltip>
                        </span>
                    ),
                    dataIndex: ["cancelCount", "initDeclare"],
                },
                {
                    title: (
                        <span>
                            处理申报
                            <Tooltip title="撤单管理中，该时间段撤单创建成功的单数">
                                <QuestionCircleOutlined style={{ fontSize: 13, marginLeft: 8 }} />
                            </Tooltip>
                        </span>
                    ),
                    dataIndex: ["cancelCount", "processingDeclare"],
                },
                {
                    title: (
                        <span>
                            申报成功
                            <Tooltip title="撤单管理中，截至选中时间处理申报的订单中申报成功的单数,审核状态为：审核通过">
                                <QuestionCircleOutlined style={{ fontSize: 13, marginLeft: 8 }} />
                            </Tooltip>
                        </span>
                    ),
                    dataIndex: ["cancelCount", "successfulDeclare"],
                },
                {
                    title: (
                        <span>
                            申报失败
                            <Tooltip title="撤单管理中，截至选中时间处理申报的订单中申报失败的单数,审核状态为：申报失败">
                                <QuestionCircleOutlined style={{ fontSize: 13, marginLeft: 8 }} />
                            </Tooltip>
                        </span>
                    ),
                    dataIndex: ["cancelCount", "failedDeclare"],
                },
                {
                    title: (
                        <span>
                            取消撤单
                            <Tooltip title="撤单管理中，截至选中时间处理申报的订单中取消撤单的单数，审核状态为：取消撤单">
                                <QuestionCircleOutlined style={{ fontSize: 13, marginLeft: 8 }} />
                            </Tooltip>
                        </span>
                    ),
                    dataIndex: ["cancelCount", "cancel"],
                },
            ],
        },
        {
            title: "退货",
            key: "refundCount",
            children: [
                {
                    title: (
                        <span>
                            需申报
                            <Tooltip title="退货管理中，该时间段创建的退货单数">
                                <QuestionCircleOutlined style={{ fontSize: 13, marginLeft: 8 }} />
                            </Tooltip>
                        </span>
                    ),
                    dataIndex: ["refundCount", "initDeclare"],
                },
                {
                    title: (
                        <span>
                            处理申报
                            <Tooltip title="退货管理中，该时间段退货创建成功的单数">
                                <QuestionCircleOutlined style={{ fontSize: 13, marginLeft: 8 }} />
                            </Tooltip>
                        </span>
                    ),
                    dataIndex: ["refundCount", "processingDeclare"],
                },
                {
                    title: (
                        <span>
                            申报成功
                            <Tooltip title="退货管理中，截至选中时间处理申报的订单中申报成功的单数，审核状态为：审核通过">
                                <QuestionCircleOutlined style={{ fontSize: 13, marginLeft: 8 }} />
                            </Tooltip>
                        </span>
                    ),
                    dataIndex: ["refundCount", "successfulDeclare"],
                },
                {
                    title: (
                        <span>
                            申报失败
                            <Tooltip title="退货管理中，截至选中时间处理申报的订单中申报失败的单数，审核状态为：总署驳回且退货状态为待退货">
                                <QuestionCircleOutlined style={{ fontSize: 13, marginLeft: 8 }} />
                            </Tooltip>
                        </span>
                    ),
                    dataIndex: ["refundCount", "failedDeclare"],
                },
                {
                    title: (
                        <span>
                            取消退货
                            <Tooltip title="退货管理中，截至选中时间处理申报的订单中取消退货的单数，退货状态为：退货关闭">
                                <QuestionCircleOutlined style={{ fontSize: 13, marginLeft: 8 }} />
                            </Tooltip>
                        </span>
                    ),
                    dataIndex: ["refundCount", "cancel"],
                },
            ],
        },
    ];
    const handelExportClick = async () => {
        await lib.request({
            url: "/ccs/businessCenter/postSale/export",
            needMask: true,
            data: {
                startTime: timeRange[0],
                endTime: timeRange[1],
                currentPage: pagination.current,
                pageSize: pagination.pageSize,
                totalCount: pagination.total,
                totalPage: pagination.totalPage,
            },
            success: res => {
                lib.openPage("/download-center?page_title=下载中心");
            },
        });
    };
    return (
        <div className="mail_itemTab">
            <div className="mail_tab_top">
                <h2>保税售后</h2>
                <div className="mail_rig_but">
                    <Button onClick={handelExportClick}>导出</Button>
                </div>
            </div>
            <div className="mail_tab">
                <div style={{ marginLeft: 16, marginRight: 16, marginTop: 16 }}>
                    <Table
                        size="small"
                        {...tableProps}
                        columns={columns}
                        pagination={false}
                        bordered={true}
                        scroll={{
                            y: 200,
                            x: 1500,
                        }}
                    />
                    <Pagination
                        size={"small"}
                        current={pagination.current}
                        pageSize={pagination.pageSize}
                        total={pagination?.total}
                        onChange={pagination.onChange}
                        onShowSizeChange={pagination.onChange}
                        pageSizeOptions={[5, 10, 20, 30, 50, 100]}
                        showQuickJumper
                        showSizeChanger
                        style={{ marginTop: 16, textAlign: "right", paddingBottom: 16 }}
                    />
                </div>
            </div>
        </div>
    );
}

export default BondedAfterSales;
