// 我的邮件事项
import React, { useState, useEffect } from "react";
import { lib, SearchList, getConfigDataUtils } from "react-single-app";
import axios from "axios";
import { Button, Modal, Space, Tooltip, Badge } from "antd";
import MailEditModal, { MAIL_POOL_OPERATE_TYPE } from "../workbench/components/editModal";
class MyMailItems extends SearchList {
    getConfig() {
        let url = getConfigDataUtils.getDataUrlByDataId(598);
        return axios.get(url).then(res => res.data.data);
    }
    // 认领状态
    getStatus(row) {
        return row.status === 0 ? "待认领" : "已认领";
    }
    // 邮件详情
    mailDetail(row) {
        lib.request({
            url: "/ccs/mailPool/detail",
            data: { id: row.id },
            success: res => {
                lib.openPage(`/mail-detail?page_title=邮件详情&id=${row.id}`, () => {
                    this.load();
                });
            },
        });
    }
    // 邮件状态
    getStatusDesc(row) {
        const logisticsStatus = [
            { name: "待处理", status: "warning", id: 1 },
            { name: "处理中", status: "processing", id: 2 },
            { name: "无需处理", status: "default", id: 3 },
            { name: "已挂起", status: "warning", id: 4 },
            { name: "已完成", status: "success", id: 5 },
        ];
        const status = logisticsStatus.filter(item => item.id === row.status);
        return <Badge status={status[0]?.status} text={status[0]?.name} />;
    }
    // 邮件标题
    getSubject(row) {
        return (
            <Tooltip placement="topLeft" title={row?.subject}>
                {row.subject}
            </Tooltip>
        );
    }
    myOperation(row) {
        return (
            <Space>
                <a onClick={() => this.mailDetail(row)}>查看</a>
                {row.status !== 3 && <MyMailModal row={row} load={() => this.load(true)} type="claim" />}
                {row.status === 3 && <MyMailModal row={row} load={() => this.load(true)} type="reprocess" />}
            </Space>
        );
    }
}
export default MyMailItems;
// 新增Modal
const MyMailModal = ({ row, load, type }) => {
    const [showModal, setShowModal] = useState(false);
    const [showModalType, setShowModalType] = useState();
    return (
        <div>
            {type === "claim" && (
                <a
                    onClick={() => {
                        setShowModalType(MAIL_POOL_OPERATE_TYPE.transfer);
                        setShowModal(true);
                    }}>
                    转交
                </a>
            )}
            {type === "reprocess" && (
                <a
                    onClick={() => {
                        setShowModalType(MAIL_POOL_OPERATE_TYPE.reprocess);
                        setShowModal(true);
                    }}>
                    重新处理
                </a>
            )}
            <MailEditModal
                id={row?.id}
                showModal={showModal}
                type={showModalType}
                dialogClose={isSuccess => {
                    setShowModal(false);
                    if (isSuccess) {
                        load();
                    }
                }}
            />
        </div>
    );
};
