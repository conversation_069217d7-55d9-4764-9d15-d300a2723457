import React, { useState, useEffect } from "react";
import { Radio, DatePicker } from "antd";
import moment from "moment";
import "./css/work-overview.less";
import MailItems from "./components/mailItems";
import KeepRecord from "./components/keepRecord";
import BondedAfterSales from "./components/bondedAfterSales";
import NumberOfCustoms from "./components/numberOfCustoms";
function WorkOverview() {
    const { RangePicker } = DatePicker;
    const startWeek = moment(
        moment()
            .subtract(7, "days")
            .format("YYYY-MM-DD" + " 00:00:00"),
    ).valueOf();
    const today = moment(
        moment()
            .subtract(1, "days")
            .endOf("day")
            .format("YYYY-MM-DD" + " 23:59:59"),
    ).valueOf();
    const [isShow, setIsShow] = useState(false);
    const [timeRange, settimeRange] = useState([startWeek, today]);
    //日期切换  7 30 90 自然日
    const handleDateChange = e => {
        if (e.target.value === 1) {
            setIsShow(false);
            settimeRange([startWeek, today]);
        } else if (e.target.value === 2) {
            setIsShow(false);
            const startWeek = moment(
                moment()
                    .subtract(30, "days")
                    .format("YYYY-MM-DD" + " 00:00:00"),
            ).valueOf();
            settimeRange([startWeek, today]);
        } else if (e.target.value === 3) {
            setIsShow(false);
            const startWeek = moment(
                moment()
                    .subtract(90, "days")
                    .format("YYYY-MM-DD" + " 00:00:00"),
            ).valueOf();
            settimeRange([startWeek, today]);
        } else {
            setIsShow(true);
        }
    };
    const handleNaturalChange = value => {
        const startWeek = moment(moment(value[0]).format("YYYY-MM-DD" + " 00:00:00")).valueOf();
        const today = moment(moment(value[1]).format("YYYY-MM-DD" + " 23:59:59")).valueOf();
        console.log(startWeek, today);
        settimeRange([startWeek, today]);
        setIsShow(false);
    };
    //未来日期禁用
    const disabledDate = current => {
        return current && current > moment().endOf("day");
    };
    return (
        <div className="work_overview">
            <div className="work_top">
                <div className="work_Top_Left">
                    <div className="work_Top_Left_img">
                        <img src="https://dante-img.oss-cn-hangzhou.aliyuncs.com/1472598205.svg" />
                    </div>
                    <h2 className="work_Top_Left_h2">工作总览</h2>
                </div>
                <div className="work_Top_Right">
                    <span className="work_Top_Right_Span">
                        统计日期：{moment(timeRange[0]).format("YYYY-MM-DD")} 至
                        {moment(timeRange[1]).format("YYYY-MM-DD")}
                    </span>
                    <Radio.Group defaultValue={1} onChange={handleDateChange}>
                        <Radio.Button value={1}>近7天</Radio.Button>
                        <Radio.Button value={2}>近30天</Radio.Button>
                        <Radio.Button value={3}>近90天</Radio.Button>
                        <Radio.Button value={4} onClick={() => setIsShow(true)}>
                            自然日
                        </Radio.Button>
                    </Radio.Group>
                    <div className="buss_single_RightTop" id="right">
                        {isShow && (
                            <RangePicker
                                open={isShow}
                                disabledDate={disabledDate}
                                getPopupContainer={() => document.getElementById("right")}
                                onChange={handleNaturalChange}
                            />
                        )}
                    </div>
                </div>
            </div>
            {/* 邮件事项 */}
            <MailItems timeRange={timeRange} />
            {/* 备案 */}
            <KeepRecord timeRange={timeRange} />
            {/* 保税售后 */}
            <BondedAfterSales timeRange={timeRange} />
            {/* 清关完成数 */}
            <NumberOfCustoms timeRange={timeRange} />
        </div>
    );
}

export default WorkOverview;
