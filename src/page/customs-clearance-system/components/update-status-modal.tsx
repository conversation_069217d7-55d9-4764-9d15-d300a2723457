import { FC, useEffect, useState } from "react";
import { batchUpdateConfigsList } from "../../../common/utils";
import NewModal from "../../../components/NewModal";
import React from "react";
import { lib } from "react-single-app";
import { message, Button } from "antd";
type updateStatusModalProps = FC<{
    type: "declaration" | "customs" | "record" | "procurement" | "inOrOut" | "release" | "hailing";
    selected?: any[];
    success: () => void;
    selectedType?: "array";
}>;

const titleMeet = {
    declaration: "修改海关状态",
    customs: "修改清关状态",
    record: "修改备案状态",
    procurement: "修改采购单状态",
    inOrOut: "修改出入库状态",
    release: "修改核放单状态",
    hailing: "修改约车状态",
};
const submitUrlMeet = {
    declaration: "/ccs/order/updateCustomsStatus",
    customs: "/ccs/invenorder/updateStatusByIds",
    record: "/ccs/jdGoodsRecord/updateJdGoodsRecordStatusByIds",
    procurement: "/ccs/jdPurchaseOrderNew/updatePoStatus",
    inOrOut: "/ccs/fbStockInOut/updateStatusByIds",
    release: "/ccs/fbChecklist/updateStatusByIds",
    hailing: "/danding-wcms/api/order/batchUpdateState/ccs",
};
const submitIdKeyMeet = {
    declaration: "sns",
    customs: "ids",
    record: "ids",
    procurement: "ids",
    inOrOut: "ids",
    release: "ids",
    hailing: "orderNoList",
};
const submitStatusKeyMeet = {
    declaration: "status",
    customs: "status",
    record: "flowState",
    procurement: "poStatus",
    inOrOut: "status",
    release: "status",
    hailing: "targetOrderState",
};

const urlsMeet = {
    declaration: "/ccs/receiptMapping/modify/customsStat",
    customs: "/ccs/invenorder/listModifyStatus",
    record: "/ccs/jdGoodsRecord/statusList",
    procurement: "/ccs/jdPurchaseOrderNew/getModifyPoStatusList",
    inOrOut: "/ccs/fbStockInOut/listModifyFbStockInOutStatus",
    release: "/ccs/fbChecklist/listModifyStatus",
    hailing: "/danding-wcms/api/order/statusList/ccs",
};

const updateStatusModal: updateStatusModalProps = props => {
    const [open, setOpen] = useState(false);
    const [configs, setConfigs] = useState([
        {
            type: "RADIO",
            labelName: titleMeet[props.type].replace("修改", ""),
            labelKey: submitStatusKeyMeet[props.type],
            required: true,
            list: [],
            from: urlsMeet[props.type],
        },
    ]);

    const onOK = values => {
        const ids = props.selected.map(item => {
            // props.type === "declaration" ? item.sn : item.id
            if (props.type === "declaration") return item.sn;
            if (props.type === "hailing") return item.orderNo;
            return item.id;
        });
        lib.request({
            url: submitUrlMeet[props.type],
            data: { ...values, [submitIdKeyMeet[props.type]]: props.selectedType === "array" ? ids : ids.join(",") },
            success(data) {
                message.success("修改状态成功");
                setOpen(false);
                props.success && props.success();
            },
        });
    };

    useEffect(() => {
        batchUpdateConfigsList(configs).then(res => {
            res.map(({ data, index }) => {
                configs[index].list = data;
            });
            setConfigs(configs);
        });
    }, []);
    return (
        <>
            <Button
                onClick={() => {
                    if (props.selected.length == 0) {
                        message.info("请勾选数据");
                        return;
                    }
                    setOpen(true);
                }}>
                {titleMeet[props.type]}
            </Button>
            <NewModal
                title={titleMeet[props.type]}
                visible={open}
                configList={configs}
                onOk={data => {
                    onOK(data);
                }}
                onCancel={() => {
                    setOpen(false);
                }}
            />
        </>
    );
};

export default updateStatusModal;
