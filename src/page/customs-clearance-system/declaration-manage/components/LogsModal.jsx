import React, { useState, useEffect } from "react";
import { Form, message, Modal, Button, Input } from "antd";
import { lib } from "react-single-app";
const { TextArea } = Input;

const handleCopy = value => {
    let transfer = document.createElement("input");
    document.body.appendChild(transfer);
    transfer.value = value; // 这里表示想要复制的内容
    transfer.focus();
    transfer.select();
    if (document.execCommand("copy")) {
        document.execCommand("copy");
    }
    transfer.blur();
    message.success("复制成功");
    document.body.removeChild(transfer);
};

function LogsModal({ row, declarationManageLogPush }) {
    const [visible, setVisible] = useState(false);
    const [disabled, setDisabled] = useState(true);
    const visibleChange = () => {
        setDisabled(true);
        setVisible(v => !v);
    };
    const [form] = Form.useForm();

    function fetchLogs() {
        if (declarationManageLogPush) {
            lib.request({
                url: "/ccs/order/getOrderSubmit",
                data: {
                    id: row.id,
                },
                needMask: true,
                success: res => {
                    form.setFieldsValue({ msg: res });
                    visibleChange();
                },
            });
        } else {
            message.error("没有权限！");
        }
    }

    const onsubmit = values => {
        Modal.confirm({
            cancelText: "取消",
            okText: "确定",
            title: "提示",
            content: "是否重推报文?",
            onOk: () => {
                if (declarationManageLogPush) {
                    lib.request({
                        url: "/ccs/order/reSubmit",
                        data: values,
                        needMask: true,
                        success: res => {
                            setDisabled(true);
                        },
                        fail: () => {
                            setDisabled(true);
                        },
                    });
                }
            },
        });
    };
    return (
        <>
            <Modal
                open={visible}
                destroyOnClose={true}
                title="原始报文"
                onCancel={visibleChange}
                footer={
                    <>
                        {declarationManageLogPush && (
                            <Button
                                onClick={() => {
                                    if (!disabled) {
                                        form.submit();
                                    } else {
                                        setDisabled(false);
                                    }
                                }}>
                                {disabled ? "编辑报文" : "重推报文"}
                            </Button>
                        )}
                        <Button onClick={() => handleCopy(form.getFieldValue("msg"))}>复制内容</Button>
                        <Button type="primary" onClick={visibleChange}>
                            关闭
                        </Button>
                    </>
                }>
                <Form
                    form={form}
                    onFinish={values => {
                        onsubmit(values);
                    }}>
                    <Form.Item name={"msg"}>
                        <TextArea autoSize={{ minRows: 6, maxRows: 20 }} disabled={disabled} />
                    </Form.Item>
                </Form>
            </Modal>
            <span className="link" onClick={fetchLogs}>
                查看
            </span>
        </>
    );
}

export default LogsModal;
