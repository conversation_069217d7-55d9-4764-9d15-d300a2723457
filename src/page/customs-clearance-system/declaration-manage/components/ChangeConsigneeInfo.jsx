import React, { useState, useEffect } from "react";
import { Form, message, Modal, Button, Input } from "antd";
import { lib, ConfigFormCenter } from "react-single-app";
import loadScriptOnce from "load-script-once";
const CONFIG_DATA = {
    baseInfo: {
        children: [
            {
                label: "收件人姓名",
                name: "name",
                editEnable: false,
                type: "textInput",
                rules: [
                    { required: true },
                    {
                        max: 100,
                    },
                ],
                labelCol: { span: 6 },
                customConfig: { style: { width: "100%" } },
            },
            {
                label: "收件人电话",
                name: "telephone",
                editEnable: false,
                type: "textInput",
                labelCol: { span: 6 },
                rules: [
                    {
                        required: true,
                    },
                    {
                        validator(_, value) {
                            if (!value) return Promise.resolve();
                            // if (/^1[3-9]\d{9}$/.test(value)) {
                            //     return Promise.resolve()
                            // }
                            if (value.length <= 13) return Promise.resolve();
                            return Promise.reject(new Error("格式不对，请重新输入"));
                        },
                        whitespace: true,
                    },
                ],
                customConfig: { style: { width: "100%" } },
            },
            {
                label: "详细地址",
                name: "address",
                editEnable: false,
                labelCol: { span: 6 },
                type: "textarea",
                rules: [
                    { required: true },
                    {
                        max: 200,
                    },
                ],
                customConfig: { style: { width: "100%" }, maxLength: 50 },
            },
        ],
        label: "收件人信息",
        name: "changeConsigneeInfo",
        isGroup: true,
        className: "declaration-manage changeConsigneeInfo",
    },
    secondInfo: {
        children: [
            {
                label: "订购人姓名",
                name: "buyerName",
                editEnable: false,
                type: "textInput",
                labelCol: { span: 6 },
                rules: [
                    { required: true, message: "订购人姓名是必填的" },
                    {
                        max: 100,
                    },
                ],
                customConfig: { style: { width: "100%" } },
            },
            {
                label: "订购人电话",
                name: "buyerTelephone",
                editEnable: false,
                type: "textInput",
                rules: [
                    { required: true, message: "订购人电话是必填的" },
                    {
                        validator(_, value) {
                            if (!value) return Promise.resolve();
                            // if (/^1[3-9]\d{9}$/.test(value)) {
                            //     return Promise.resolve()
                            // }
                            if (value.length <= 13) return Promise.resolve();
                            return Promise.reject(new Error("格式不对，请重新输入"));
                        },
                        whitespace: true,
                    },
                ],
                labelCol: { span: 6 },
                customConfig: { style: { width: "100%" } },
            },
        ],
        label: "订购人信息",
        name: "changeConsigneeInfo2",
        isGroup: true,
    },
};

function ChangeConsigneeInfo({ visible, id, sn, onClose }) {
    const ref = React.useRef();
    const code = React.useRef("");
    const [configs, setConfigs] = useState({ ...CONFIG_DATA });
    useEffect(() => {
        if (visible) {
            getPre();
            // getDetail();
        }
    }, [visible]);

    const getPre = async () => {
        try {
            // const pageCode = await getPageCode()  //获取 pagecode，可选
            console.log("PDD_OPEN_init");
            let finishStatus = true;
            setTimeout(() => {
                if (finishStatus) {
                    // throw new Error("错误");
                    getDetail();
                }
            }, 400);
            await PDD_OPEN_init({
                // code: "81f9d70222b147af124e3049eba74e47f82888a08f9a", // 上一步中拿到的 pagecode，可选
            });
            const pati = await window.PDD_OPEN_getPati();
            finishStatus = false;
            code.current = pati;
            getDetail();
        } catch (e) {
            console.log("e", e);
            getDetail();
        }
    };

    const getDetail = fn => {
        lib.request({
            url: "/ccs/order/getConsigneeInfo",
            data: { orderSn: sn, pati: code.current },
            success: res => {
                const newConfig = { ...CONFIG_DATA };
                if (res.origin == "pdd" && res.buyerName === null) {
                    newConfig.secondInfo.children[0].required = false;
                    newConfig.secondInfo.children[0].rules = null;
                    // "拼多多订单无权解密订购人姓名，修改请直接填写"
                    newConfig.secondInfo.children[0].placeholder = "拼多多订单无权解密订购人姓名，修改请直接填写";
                } else {
                    newConfig.secondInfo.children[0].rules = [
                        { required: true, message: "订购人姓名是必填的" },
                        {
                            max: 100,
                        },
                    ];
                }
                setConfigs(newConfig);
                ref.current.setMergeDetail(res);
                code.current = "";
            },
        });
    };

    const beforeSubmit = values => {
        values.id = id;
        values.orderSn = sn;
        return values;
    };
    const handleOk = () => {
        ref.current.getForm.validateFields().then(res => {
            ref.current.submitForm();
        });
    };
    const onCancel = () => {
        onClose();
    };
    const onSubmitSuccess = () => {
        ref.current.getForm.validateFields().then(res => {
            console.log("res:", res);
            onClose(true);
            message.success("修改成功");
        });
    };
    return (
        <Modal open={visible} title={"修改订单信息"} onOk={handleOk} onCancel={onCancel} width={600} destroyOnClose>
            <ConfigFormCenter
                ref={ref}
                confData={configs}
                beforeSubmit={beforeSubmit}
                onSubmitSuccess={onSubmitSuccess}
                submitUrl={"/ccs/order/updateConsigneeInfo"}
            />
        </Modal>
    );
}

export default ChangeConsigneeInfo;
