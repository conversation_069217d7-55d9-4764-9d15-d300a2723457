import React, { useState, useEffect } from "react";
import { Form, message, Modal, Button, Input, Radio } from "antd";
import { lib } from "react-single-app";

function ReDeclareModal({ auditModalVisible: modalVisible, onClose }) {
    const [form] = Form.useForm();

    const handleOk = () => {
        form.validateFields().then(res => {
            form.submit();
        });
    };
    const handleCancel = () => {
        onClose(false, "");
        form.resetFields();
    };
    const onfinish = values => {
        form.validateFields().then(res => {
            onClose(true, values.action);
            form.resetFields();
        });
    };

    return (
        <Modal
            cancelText={"取消"}
            okText={"确定"}
            title={"重置申报"}
            destroyOnClose={true}
            open={modalVisible}
            onOk={handleOk}
            onCancel={handleCancel}>
            <div style={{ padding: 10 }}>重置申报会跳过10分钟的系统校验，请谨慎执行。</div>
            <Form form={form} onFinish={onfinish}>
                <Form.Item label={"申报类型"} name={"action"} rules={[{ required: true, message: "请选择申报类型" }]}>
                    <Radio.Group>
                        {[
                            { id: "DECLARE_ORDER", name: "订单" },
                            { id: "DECLARE_LOGISTICS", name: "运单" },
                            { id: "DECLARE_INVENTORY", name: "清单" },
                        ].map((item, index) => {
                            return (
                                <Radio key={index} value={item.id}>
                                    {item.name}
                                </Radio>
                            );
                        })}
                    </Radio.Group>
                </Form.Item>
            </Form>
        </Modal>
    );
}

export default ReDeclareModal;
