import React from "react";
import { SearchList, lib, event, HOC, getConfigDataUtils } from "react-single-app";
import { Button, Modal, message, Tooltip, Space, Tag, Row } from "antd";
import BottomTag from "../../../components/BottomTag";
import axios from "axios";
import NewModal from "../../../components/NewModal";
import { batchUpdateConfigsList } from "../../../common/utils";
import LogsModal from "./components/LogsModal";
import ChangeConsigneeInfo from "./components/ChangeConsigneeInfo";
import ReDeclareModal from "./components/ReDeclareModal";
import UpdateStatusModal from "../components/update-status-modal";
import "../../shop-good.less";
import "./index.less";
import loadScriptOnce from "load-script-once";
const statusDist = {
    10: "待申报",
    20: "申报中",
    100: "已放行",
    "-10": "取消申报",
    "-1": "申报失败",
};

@HOC.mapAuthButtonsToState({ buttonCodeArr: ["exportAuth"] })
class App extends SearchList {
    constructor(props) {
        super(props);
        this.state.upUrl = "/ccs/customsBookItem/upset";
        this.state.detailUrl = "";
        this.state.pushList = [];
        this.state.pushType = "DECLARE_LOGISTICS";
        this.state.returnDisabled = false;
        this.state.reason = "";
        this.state.detail = 0;
        this.state.detailInfo = {};
        this.state.buttonAuth = { exportAuth: false };
        this.state.tabsList = [
            {
                tab: "清单申报",
                type: "TAB",
                tabsList: [
                    {
                        tab: "基础信息",
                        type: "TABLE",
                        columns: [
                            {
                                title: "渠道订单号",
                                dataIndex: "outOrderNo",
                                width: "230",
                                formType: "TEXT",
                            },
                            {
                                title: "出库单号",
                                dataIndex: ["customsInventory", "outboundNo"],
                                width: "230",
                                formType: "TEXT",
                            },
                            {
                                title: "清关企业",
                                dataIndex: ["customsInventory", "agentCompanyName"],
                                width: "230",
                                formType: "TEXT",
                            },
                            {
                                title: "电商平台",
                                dataIndex: "ebpName",
                                width: "230",
                                formType: "TEXT",
                            },
                            {
                                title: "电商企业名称",
                                dataIndex: ["customsInventory", "ebcName"],
                                width: "230",
                                formType: "TEXT",
                            },
                            {
                                title: "申报口岸",
                                dataIndex: ["customsInventory", "customs"],
                                width: "120",
                                formType: "TEXT",
                            },
                            {
                                title: "订单总金额",
                                dataIndex: ["customsInventory", "totalPrice"],
                                width: "120",
                                formType: "TEXT",
                            },
                            {
                                title: "总税金",
                                dataIndex: ["customsInventory", "taxPrice"],
                                width: "120",
                                formType: "TEXT",
                            },
                            {
                                title: "应征关税",
                                dataIndex: ["customsInventory", "customsTax"],
                                width: "120",
                                formType: "TEXT",
                            },
                            {
                                title: "应征增值税",
                                dataIndex: ["customsInventory", "valueAddedTax"],
                                width: "120",
                                formType: "TEXT",
                            },
                            {
                                title: "应征消费税",
                                dataIndex: ["customsInventory", "consumptionTax"],
                                width: "120",
                                formType: "TEXT",
                            },
                            {
                                title: "订购人姓名",
                                dataIndex: ["customsInventory", "buyerName"],
                                width: "120",
                                formType: "INPUT",
                            },
                            {
                                title: "订购人身份证号",
                                dataIndex: ["customsInventory", "buyerIdNumber"],
                                width: "200",
                                formType: "INPUT",
                            },
                            {
                                title: "收货(省)",
                                dataIndex: ["customsInventory", "consigneeProvince"],
                                width: "120",
                                formType: "INPUT",
                            },
                            {
                                title: "市",
                                dataIndex: ["customsInventory", "consigneeCity"],
                                width: "120",
                                formType: "INPUT",
                            },
                            {
                                title: "区",
                                dataIndex: ["customsInventory", "consigneeDistrict"],
                                width: "120",
                                formType: "INPUT",
                            },
                            {
                                title: "详细地址",
                                dataIndex: ["customsInventory", "consigneeAddress"],
                                width: "200",
                                formType: "INPUT",
                            },
                            {
                                title: "物流企业",
                                dataIndex: ["customsInventory", "logisticsCompanyName"],
                                width: "200",
                                formType: "TEXT",
                            },
                            {
                                title: "运单编号",
                                dataIndex: ["customsInventory", "logisticsNo"],
                                width: "200",
                                formType: "TEXT",
                            },
                        ],
                    },
                    {
                        tab: "商品信息",
                        type: "TABLE",
                        columns: [
                            {
                                title: "SKU",
                                dataIndex: "itemNo",
                                width: "200",
                                formType: "INPUT",
                            },
                            {
                                title: "料号",
                                dataIndex: "productId",
                                width: "200",
                                formType: "INPUT",
                            },
                            {
                                title: "金二序号",
                                dataIndex: "goodsSeqNo",
                                width: "200",
                                formType: "TEXT",
                            },
                            {
                                title: "备案名称",
                                dataIndex: "goodsName",
                                width: "600",
                                formType: "TEXT",
                            },
                            {
                                title: "条码",
                                dataIndex: "barCode",
                                width: "200",
                                formType: "TEXT",
                            },
                            {
                                title: "HS编码",
                                dataIndex: "hsCode",
                                width: "600",
                                formType: "SELECT",
                                extra: "/ccs/customs/listHs",
                            },
                            {
                                title: "净重",
                                dataIndex: "netWeight",
                                width: "200",
                                formType: "INPUTNUMBER",
                                numberType: "integer",
                            },
                            {
                                title: "毛重",
                                dataIndex: "grossWeight",
                                width: "200",
                                formType: "INPUTNUMBER",
                                numberType: "integer",
                            },
                            {
                                title: "原产国",
                                dataIndex: "originCountry",
                                width: "200",
                                formType: "SELECT",
                                extra: "/ccs/customs/listCountry",
                            },
                            {
                                title: "第一计量单位",
                                dataIndex: "firstUnit",
                                width: "120",
                                formType: "SELECT",
                                extra: "/ccs/customs/listUom",
                            },
                            {
                                title: "第一计量单位数量",
                                dataIndex: "firstUnitAmount",
                                width: "200",
                                formType: "TEXT",
                                numberType: "integer",
                            },
                            {
                                title: "第二计量单位",
                                dataIndex: "secondUnit",
                                width: "120",
                                formType: "SELECT",
                                extra: "/ccs/customs/listUom",
                            },
                            {
                                title: "第二计量单位数量",
                                dataIndex: "secondUnitAmount",
                                width: "200",
                                formType: "TEXT",
                                numberType: "integer",
                            },
                            {
                                title: "单价",
                                dataIndex: "unitPrice",
                                width: "120",
                                formType: "TEXT",
                                numberType: "decimal",
                            },
                            {
                                title: "数量",
                                dataIndex: "count",
                                width: "120",
                                formType: "TEXT",
                                numberType: "integer",
                            },
                            {
                                title: "总价",
                                dataIndex: "totalPrice",
                                width: "120",
                                formType: "TEXT",
                                numberType: "decimal",
                            },
                            {
                                title: "税金",
                                dataIndex: "taxPrice",
                                width: "120",
                                formType: "TEXT",
                                numberType: "decimal",
                            },
                        ],
                    },
                    {
                        tab: "轨迹日志",
                        type: "TABLE",
                        columns: [
                            // {
                            //   "title": "单据编号",
                            //   "dataIndex": "sn",
                            //   "width": "200",
                            //   "formType": "TEXT",
                            // },
                            // {
                            //   "title": "单据类型",
                            //   "dataIndex": "typeDesc",
                            //   "width": "120",
                            //   "formType": "TEXT",
                            // },
                            {
                                title: "修改前状态",
                                dataIndex: "oldStatusStr",
                                width: "120",
                                formType: "TEXT",
                            },
                            {
                                title: "修改后状态",
                                dataIndex: "newStatusStr",
                                width: "160",
                                formType: "TEXT",
                            },
                            {
                                title: "操作描述",
                                dataIndex: "operateDesStr",
                                width: "200",
                                formType: "TEXT",
                            },
                            {
                                title: "日志描述",
                                dataIndex: "logDes",
                                width: "280",
                                formType: "TEXT",
                                renderType: "func",
                                extra: "renderItem3",
                            },
                            {
                                title: "报文",
                                dataIndex: "",
                                width: "140",
                                formType: "TEXT",
                                renderType: "func",
                                extra: "renderItem",
                            },
                            // {
                            //     "title": "响应报文",
                            //     "dataIndex": "",
                            //     "width": "140",
                            //     "formType": "TEXT",
                            //     "renderType": "func",
                            //     "extra": "renderItem2"
                            // },
                            // {
                            //     "title": "操作人",
                            //     "dataIndex": "oper",
                            //     "width": "140",
                            //     "formType": "TEXT",
                            // },
                            {
                                title: "日志时间",
                                dataIndex: "createTime",
                                width: "160",
                                formType: "TEXT",
                                renderType: "js",
                                extra: "value => new Date(value).format('yyyy-MM-dd hh:mm:ss')",
                            },
                        ],
                    },
                ],
            },
            {
                tab: "订单申报",
                type: "TAB",
                tabsList: [
                    {
                        tab: "基础信息",
                        type: "TABLE",
                        columns: [
                            {
                                title: "渠道订单号",
                                dataIndex: "outOrderNo",
                                width: "200",
                                formType: "TEXT",
                            },
                            {
                                title: "出库单号",
                                dataIndex: "",
                                width: "200",
                                formType: "TEXT",
                            },
                            {
                                title: "报文传输企业",
                                dataIndex: ["order", "agentCompanyName"],
                                width: "200",
                                formType: "TEXT",
                            },
                            {
                                title: "电商平台",
                                dataIndex: "ebpName",
                                width: "200",
                                formType: "TEXT",
                            },
                            {
                                title: "电商企业名称",
                                dataIndex: ["order", "ebcName"],
                                width: "200",
                                formType: "TEXT",
                            },
                            {
                                title: "申报口岸",
                                dataIndex: ["order", "customs"],
                                width: "120",
                                formType: "TEXT",
                            },
                            {
                                title: "订单总金额",
                                dataIndex: ["order", "totalAmount"],
                                width: "140",
                                formType: "TEXT",
                            },
                            {
                                title: "实际支付金额",
                                dataIndex: ["order", "paidAmount"],
                                width: "140",
                                formType: "TEXT",
                            },
                            {
                                title: "总税金",
                                dataIndex: ["order", "tax"],
                                width: "140",
                                formType: "TEXT",
                            },
                            {
                                title: "订购人姓名",
                                dataIndex: ["order", "buyerName"],
                                width: "140",
                                formType: "INPUT",
                            },
                            {
                                title: "订购人身份证号",
                                dataIndex: ["order", "buyerIdNumber"],
                                width: "160",
                                formType: "INPUT",
                            },
                            {
                                title: "收货省",
                                dataIndex: ["order", "consigneeProvince"],
                                width: "120",
                                formType: "INPUT",
                            },
                            {
                                title: "市",
                                dataIndex: ["order", "consigneeCity"],
                                width: "120",
                                formType: "INPUT",
                            },
                            {
                                title: "区",
                                dataIndex: ["order", "consigneeDistrict"],
                                width: "120",
                                formType: "INPUT",
                            },
                            {
                                title: "详细地址",
                                dataIndex: ["order", "consigneeAddress"],
                                width: "200",
                                formType: "INPUT",
                            },
                            {
                                title: "物流企业",
                                dataIndex: ["order", "logisticsCompanyName"],
                                width: "200",
                                formType: "TEXT",
                            },
                        ],
                    },
                    {
                        tab: "商品信息",
                        type: "TABLE",
                        columns: [
                            {
                                title: "SKU",
                                dataIndex: "goodsNo",
                                width: "200",
                                formType: "INPUT",
                            },
                            {
                                title: "料号",
                                dataIndex: "recordNo",
                                width: "200",
                                formType: "INPUT",
                            },
                            // {
                            //     "title": "金二序号",
                            //     "dataIndex": "recordGnum",
                            //     "width": "200",
                            //     "formType": "INPUT",
                            // },
                            {
                                title: "备案名称",
                                dataIndex: "name",
                                width: "300",
                                formType: "TEXT",
                            },
                            {
                                title: "条码",
                                dataIndex: "barcode",
                                width: "200",
                                formType: "TEXT",
                            },
                            {
                                title: "HS编码",
                                dataIndex: "hsCode",
                                width: "600",
                                formType: "SELECT",
                                extra: "/ccs/customs/listHs",
                            },
                            {
                                title: "原产国",
                                dataIndex: "originCountry",
                                width: "200",
                                formType: "SELECT",
                                extra: "/ccs/customs/listCountry",
                            },
                            {
                                title: "单价",
                                dataIndex: "unitPrice",
                                width: "140",
                                formType: "TEXT",
                            },
                            {
                                title: "数量",
                                dataIndex: "goodsCount",
                                width: "140",
                                formType: "TEXT",
                            },
                            {
                                title: "总价",
                                dataIndex: "goodsAmount",
                                width: "140",
                                formType: "TEXT",
                            },
                        ],
                    },
                    {
                        tab: "轨迹日志",
                        type: "TABLE",
                        columns: [
                            {
                                title: "单据编号",
                                dataIndex: "sn",
                                width: "120",
                                formType: "TEXT",
                            },
                            {
                                title: "修改前状态",
                                dataIndex: "oldStatus",
                                width: "120",
                                formType: "TEXT",
                            },
                            {
                                title: "修改后状态",
                                dataIndex: "newStatus",
                                width: "160",
                                formType: "TEXT",
                            },
                            {
                                title: "操作描述",
                                dataIndex: "operDetail",
                                width: "140",
                                formType: "TEXT",
                            },
                            {
                                title: "日志描述",
                                dataIndex: "content",
                                width: "140",
                                formType: "TEXT",
                            },
                            {
                                title: "报文",
                                dataIndex: "",
                                width: "160",
                                formType: "TEXT",
                                renderType: "func",
                                extra: "renderItem",
                            },
                            {
                                title: "响应报文",
                                dataIndex: "",
                                width: "140",
                                formType: "TEXT",
                                renderType: "func",
                                extra: "renderItem2",
                            },
                            {
                                title: "操作人",
                                dataIndex: "oper",
                                width: "160",
                                formType: "TEXT",
                            },
                            {
                                title: "日志时间",
                                dataIndex: "createTime",
                                width: "140",
                                formType: "TEXT",
                                renderType: "js",
                                extra: "value => new Date(value).format('yyyy-MM-dd hh:mm:ss')",
                            },
                        ],
                    },
                ],
            },
            {
                tab: "运单申报",
                type: "TAB",
                columns: [],
                tabsList: [
                    {
                        tab: "轨迹日志",
                        type: "TABLE",
                        columns: [
                            {
                                title: "单据编号",
                                dataIndex: "sn",
                                width: "120",
                                formType: "TEXT",
                            },
                            {
                                title: "修改前状态",
                                dataIndex: "oldStatus",
                                width: "120",
                                formType: "TEXT",
                            },
                            {
                                title: "修改后状态",
                                dataIndex: "newStatus",
                                width: "160",
                                formType: "TEXT",
                            },
                            {
                                title: "操作描述",
                                dataIndex: "operDetail",
                                width: "140",
                                formType: "TEXT",
                            },
                            {
                                title: "日志描述",
                                dataIndex: "content",
                                width: "140",
                                formType: "TEXT",
                            },
                            {
                                title: "报文",
                                dataIndex: "",
                                width: "160",
                                formType: "TEXT",
                                renderType: "func",
                                extra: "renderItem",
                            },
                            {
                                title: "响应报文",
                                dataIndex: "",
                                width: "140",
                                formType: "TEXT",
                                renderType: "func",
                                extra: "renderItem2",
                            },
                            {
                                title: "操作人",
                                dataIndex: "oper",
                                width: "160",
                                formType: "TEXT",
                            },
                            {
                                title: "日志时间",
                                dataIndex: "createTime",
                                width: "140",
                                formType: "TEXT",
                                renderType: "js",
                                extra: "value => new Date(value).format('yyyy-MM-dd hh:mm:ss')",
                            },
                        ],
                    },
                ],
            },
            {
                tab: "支付单申报",
                type: "TAB",
                columns: [],
                tabsList: [
                    {
                        tab: "轨迹日志",
                        type: "TABLE",
                        columns: [
                            {
                                title: "单据编号",
                                dataIndex: "sn",
                                width: "160",
                                formType: "TEXT",
                            },
                            {
                                title: "修改前状态",
                                dataIndex: "oldStatus",
                                width: "140",
                                formType: "TEXT",
                            },
                            {
                                title: "修改后状态",
                                dataIndex: "newStatus",
                                width: "140",
                                formType: "TEXT",
                            },
                            {
                                title: "操作描述",
                                dataIndex: "operDetail",
                                width: "160",
                                formType: "TEXT",
                            },
                            {
                                title: "日志描述",
                                dataIndex: "content",
                                width: "200",
                                formType: "TEXT",
                            },
                            {
                                title: "报文",
                                width: "140",
                                formType: "TEXT",
                                renderType: "func",
                                extra: "renderItem",
                            },
                            {
                                title: "响应报文",
                                width: "140",
                                formType: "TEXT",
                                renderType: "func",
                                extra: "renderItem2",
                            },
                            {
                                title: "操作人",
                                dataIndex: "oper",
                                width: "140",
                                formType: "TEXT",
                            },
                            {
                                title: "日志时间",
                                dataIndex: "createTime",
                                width: "160",
                                formType: "TEXT",
                                renderType: "js",
                                extra: "value => new Date(value).format('yyyy-MM-dd hh:mm:ss')",
                            },
                        ],
                    },
                ],
            },
        ];
        this.state.updateConfigList = [
            {
                type: "RADIO",
                labelName: "清单状态",
                labelKey: "status",
                required: true,
                list: [],
                from: "/ccs/order/listActionStatus",
            },
        ];
        // this.state.updateCustomStatus = false;
        // this.state.updateCustomStatusConfigList = [
        //     {
        //         type: "RADIO",
        //         labelName: "海关状态",
        //         labelKey: "status",
        //         required: true,
        //         list: [],
        //         from: "/ccs/receiptMapping/modify/customsStat",
        //     },
        // ];
    }

    componentDidMount() {
        let { tabsList } = this.state;
        batchUpdateConfigsList(tabsList).then(res => {
            res.map(({ data, index }) => {
                tabsList[index].list = data;
            });
            this.setState({ tabsList });
        });
        let { updateConfigList } = this.state;
        batchUpdateConfigsList(updateConfigList).then(res => {
            res.map(({ data, index }) => {
                updateConfigList[index].list = data;
            });
            this.setState({ updateConfigList });
        });
        // let { updateCustomStatusConfigList } = this.state;
        // batchUpdateConfigsList(updateCustomStatusConfigList).then(res => {
        //     res.map(({ data, index }) => {
        //         updateCustomStatusConfigList[index].list = data;
        //     });
        //     this.setState({ updateCustomStatusConfigList });
        // });
        lib.request({
            url: "/ccs/route/listAction",
            needMask: true,
            success: res => {
                this.setState({
                    pushList: res,
                    pushType: res[0].value,
                });
            },
        });
        // this.getPre();
    }

    renderSpan(row) {
        return (
            <LogsModal
                row={row}
                declarationManageLogPush={this.state.buttons?.includes("DECLARATION-MANAGE-LOG-PUSH")}
            />
        );
    }

    getConfig() {
        return axios.get(getConfigDataUtils.getDataUrlByDataId(543)).then(res => res.data.data);
    }

    renderCallback(row) {
        return (
            <Tooltip title={row.customsInventory?.customsDetail}>
                <span>{row.customsInventory?.customsStatusDesc}</span>
            </Tooltip>
        );
    }

    renderExceptionType(row) {
        return (
            <Tooltip title={row.exceptionTypeStr?.describe}>
                <span>{row.exceptionTypeStr?.exceptionName}</span>
            </Tooltip>
        );
    }

    renderLogicCallback(row) {
        return (
            <Tooltip title={row.logistics?.customsDetail}>
                <span>{row.logistics?.customsDesc}</span>
            </Tooltip>
        );
    }

    customsReceiptFunc(row) {
        return this.state.buttons?.includes("customsReceipt") ? row.customsReceipt : "查看";
    }

    getCheckedRows() {
        return this.state.selectedRows;
    }

    // renderRightOperation() {
    //     return (
    //         // /ccs/order/export
    //         <Button onClick={() => {
    //             let { pagination, searchConditions } = this.state;
    //             lib.request({
    //                 url: "/ccs/order/export",
    //                 needMask: true,
    //                 data: { ...pagination, ...searchConditions },
    //                 success: (json) => {
    //                     lib.openPage('/download-center?page_title=下载中心')
    //                 }
    //             })
    //         }}>导出1<UploadOutlined /></Button>
    //     )
    // }

    renderLeftOperation() {
        return (
            <Space>
                <Button type="" onClick={() => this.pushAgain("DECLARE_INVENTORY")}>
                    清单重推
                </Button>
                <Button type="primary" onClick={() => this.pushAgain("DECLARE_ORDER")}>
                    订单重推
                </Button>
                <Button type="" onClick={() => this.pushAgain("DECLARE_PAYMENT")}>
                    支付单重推
                </Button>
                <Button type="primary" onClick={() => this.pushAgain("DECLARE_LOGISTICS")}>
                    运单重推
                </Button>
                <Button onClick={() => this.cancelFunc()}>取消订单</Button>
                {this.state.buttons?.includes("DECLARATION-MANAGE-DECLARE-PUSH") && (
                    <Button onClick={() => this.declarePushAgain()}>申报单重推</Button>
                )}
                {this.state.buttons?.includes("CHANGE-CONSIGNEE-INFO") && (
                    <Button onClick={() => this.changeConsigneeInfo()}>修改订单信息</Button>
                )}
                {this.state.buttons?.includes("ES-SYNC") && <Button onClick={() => this.esSync()}>es同步</Button>}
                {this.state.buttons?.includes("RE-DECLARE") && (
                    <Button onClick={() => this.reDeclare()}>重置申报</Button>
                )}
                {this.state.buttons?.includes("PENDING") && (
                    <Button
                        onClick={() => {
                            this.pending();
                        }}>
                        挂起
                    </Button>
                )}
                {this.state.buttons?.includes("CANCEL-PENDING") && (
                    <Button
                        onClick={() => {
                            this.cancelPending();
                        }}>
                        取消挂起
                    </Button>
                )}
                {this.state.buttons?.includes("UPDATE-CUSTOM-STATUS") && (
                    <UpdateStatusModal
                        selected={this.state.selectedRows}
                        success={() => {
                            this.load();
                        }}
                        type={"declaration"}
                    />
                )}
            </Space>
        );
    }

    renderTableTopView() {
        return (
            <Space>
                {this.state.buttons?.includes("UPDATE-CUMSTON_STATUS") && (
                    <Button
                        onClick={() => {
                            let list = this.getCheckedRows();
                            if (list.length === 0) return message.warn("请勾选数据");
                            this.setState({ updateStatus: true });
                        }}>
                        修改清单状态
                    </Button>
                )}
                {this.state.buttons?.includes("BATCH-DELECT") && (
                    <Button
                        onClick={() => {
                            this.batchDeleteOrReSubmit(false);
                        }}>
                        批量删除
                    </Button>
                )}
                {this.state.buttons?.includes("BATCH-DELECT-RE-SUBMIT") && (
                    <Button
                        onClick={() => {
                            this.batchDeleteOrReSubmit(true);
                        }}>
                        删除重推
                    </Button>
                )}
            </Space>
        );
    }

    batchDeleteOrReSubmit(reSubmit) {
        let list = this.getCheckedRows();
        if (list.length === 0) return message.warn("请勾选数据");
        const ids = list.map(item => item.id);
        Modal.confirm({
            title: !reSubmit ? "批量删除" : "删除重推",
            content: !reSubmit ? "确定删除申报单吗？" : "确定删除重推申报单吗",
            onOk: () => {
                lib.request({
                    url: !reSubmit ? "/ccs/order/deleteByIds" : "/ccs/order/deleteAndReSubmitByIds",
                    data: {
                        ids: ids.join(","),
                    },
                    needMask: true,
                    success: res => {
                        message.success("操作成功");
                        this.load();
                    },
                });
            },
        });
    }

    pending() {
        let list = this.getCheckedRows();
        if (list.length === 0) return message.warn("请勾选数据");
        const ids = list.map(item => item.id);
        lib.request({
            url: "/ccs/order/hangUp",
            data: {
                idList: ids,
            },
            needMask: true,
            success: res => {
                message.success("操作成功");
                this.load();
            },
        });
    }

    cancelPending() {
        let list = this.getCheckedRows();
        if (list.length === 0) return message.warn("请勾选数据");
        const ids = list.map(item => item.id);
        lib.request({
            url: "/ccs/order/cancelHangUp",
            data: {
                idList: ids,
            },
            needMask: true,
            success: res => {
                message.success("操作成功");
                this.load();
            },
        });
    }

    esSync() {
        let list = this.getCheckedRows();
        let snList = list.reduce((prev, curr) => [...prev, curr.sn], []);
        lib.request({
            url: "/ccs/order/syncEsData",
            data: {
                snList,
            },
            needMask: true,
            success: res => {
                message.success("es同步成功");
                this.load();
            },
        });
    }

    changeConsigneeInfo() {
        let list = this.getCheckedRows();
        if (list.length === 0) {
            message.warning("请选择一条数据!");
            return;
        }
        if (list.length > 1) {
            message.warning("只能勾选一条数据!");
            return;
        }
        this.setState({
            changeConsigneeInfoId: list[0].id,
            changeConsigneeInfoVisible: true,
            changeConsigneeInfoSn: list[0].sn,
        });
    }

    cancelFunc() {
        let list = this.getCheckedRows();
        if (list.length === 0) {
            message.warning("请选择数据");
            return;
        }
        Modal.confirm({
            cancelText: "取消",
            okText: "确定",
            title: "确定取消订单？",
            onOk: () => {
                this.cancelOrder();
            },
        });
    }

    declarePushAgain() {
        Modal.confirm({
            cancelText: "取消",
            okText: "确定",
            title: "申报单信息会重新创建，功能等同于上游重推，请谨慎操作！",
            onOk: () => {
                this.pushAgain("DECLARE_SUBMIT");
            },
        });
    }

    showDetail(row) {
        let { tabsList } = this.state;
        tabsList.map((item, index) => {
            item.dataList = [row];
        });
        return (
            <a
                onClick={() => {
                    lib.openPage(`/declaration-manage-detail?orderId=${row.id}&page_title=申报单详情`);
                }}>
                {row.declareOrderNo}
                <Row>
                    {row.delayTime && <Tag color="blue">{row.delayTime}</Tag>}
                    {Array.isArray(row.orderTagList) &&
                        row.orderTagList.map((item, index) => {
                            return (
                                <Tag color="blue" index={index}>
                                    {item}
                                </Tag>
                            );
                        })}
                    {Array.isArray(row.orderTodoTagList) &&
                        row.orderTodoTagList.map((item, index) => {
                            return (
                                <Tag color="red" index={index}>
                                    {item}
                                </Tag>
                            );
                        })}
                </Row>
            </a>
        );
    }

    // 确认修改
    tabSubmit(values) {}

    renderDetail() {
        const { isLog, tabsList } = this.state;
        let props = {
            tabsList,
            showSubmitBtn: false,
            id: this.state.id,
            tabSubmit: this.tabSubmit.bind(this),
        };
        return <BottomTag {...props} />;
    }

    // 导出数据
    exportList() {
        var strs = window.location.search.substring(1);
        var data = {};
        var b = strs.split("&");
        for (var i = 0; i < b.length; i++) {
            var [key, value] = b[i].split("=");
            if (["config_id", "title", "page_title", "refresh_event"].indexOf(key) == -1) {
                data[key] = value;
            }
        }
        data.currentPage = this.state.pagination.currentPage;
        data.pageSize = this.state.pagination.pageSize;
        var page = this.state.page;
        for (var key in this.state.page) {
            if (page[key].value !== "") {
                data[key] = page[key].value;
            }
        }
        lib.request({
            url: "/ccs/order/exportExcelByDownLoadCenter",
            data,
            needMask: true,
            success: res => {
                if (res) {
                    let url = `/download-center/${new Date().getTime()}?page_title=下载中心&config_id=1593580312171226&refresh_event=${new Date().getTime()}`;
                    window.indexProps.history.push(url);
                    event.emit("add-page", {
                        url,
                    });
                }
            },
        });
    }

    selectChange(e) {
        this.setState({
            pushType: e,
        });
    }

    // 取消申报单
    cancelOrder() {
        let list = this.getCheckedRows();
        if (list.length) {
            let idList = [];
            list.map(item => {
                idList.push(item.id);
            });
            lib.request({
                url: "/ccs/order/cancel",
                data: {
                    idList,
                },
                method: "POST",
                needMask: true,
                success: res => {
                    message.success("取消操作成功, 请在取消单栏目确认取消单的状态！");
                    this.load(true);
                },
            });
        } else {
            message.warning("请选择数据");
        }
    }

    // 重推
    pushAgain(action) {
        let list = this.getCheckedRows();
        if (list.length !== 0) {
            let idList = [];
            list.map(item => {
                idList.push(item.id);
            });
            lib.request({
                url: "/ccs/order/reDeclare",
                data: {
                    action,
                    idList,
                },
                method: "POST",
                needMask: true,
                success: res => {
                    message.success("重推成功");
                    this.load(true);
                },
                fail: err => {
                    console.log(err);
                },
            });
        } else {
            message.warning("请选择数据");
        }
    }

    declareProject(row) {
        return (
            <span>
                {this.status2icon(row.payment?.status)}
                {row.payment && row.payment.status !== 0 && <span title={row.payment?.statusDesc}>支付单申报</span>}

                {this.status2icon(row.order?.status)}
                {row.order && row.order.status !== 0 && <span title={row.order?.statusDesc}>订单申报</span>}

                {this.status2icon(row.logistics?.status)}
                {row.logistics && row.logistics.status !== 0 && <span title={row.logistics?.statusDesc}>运单申报</span>}

                {this.status2icon(row.customsInventory?.status)}
                {row.customsInventory && row.customsInventory.status !== 0 && (
                    <span title={row.customsInventory?.statusDesc}>清单申报</span>
                )}
            </span>
        );
    }

    renderStatus(row) {
        return <Tooltip title={row.exceptionTypeDesc && "申报异常"}>{row.statusDesc}</Tooltip>;
    }

    status2icon(status) {
        if (status === 10) {
            // 待申报
            return <i className="wait-icon"></i>;
        } else if (status === 20) {
            // 申报中
            return <i className="ing-icon"></i>;
        } else if (status === 100) {
            // 已放行
            return <i className="success-icon"></i>;
        } else if (status === -10) {
            // 取消申报
            return <i className="cancel-icon"></i>;
        } else if (status === -1) {
            // 申报失败
            return <i className="fail-icon"></i>;
        }
    }

    renderTooltip(row) {
        return (
            <Tooltip title={row.customsInventory?.customsDetail}>
                <span>{row.customsInventory?.customsStatusDesc}</span>
            </Tooltip>
        );
    }

    renderTooltip2(row) {
        return (
            <Tooltip title={row.order?.customsDetail}>
                <span>{row.order?.customsStatusDesc}</span>
            </Tooltip>
        );
    }

    status2desc(status) {
        return statusDist[status] ? statusDist[status] : "待申报";
    }
    // getPre = () => {
    //     console.log("getPre1");
    //     // loadScriptOnce("https://pfile.pddpic.com/galerie-go/open_sdk/pc.202102201613.js");
    // };
    renderModal() {
        return (
            <React.Fragment>
                <ChangeConsigneeInfo
                    visible={this.state.changeConsigneeInfoVisible}
                    id={this.state.changeConsigneeInfoId}
                    sn={this.state.changeConsigneeInfoSn}
                    onClose={success => {
                        this.setState({
                            changeConsigneeInfoId: null,
                            changeConsigneeInfoVisible: false,
                            changeConsigneeInfoSn: null,
                        });
                        if (success) {
                            this.load();
                        }
                    }}
                />
                <ReDeclareModal
                    auditModalVisible={this.state.reDeclareModalInfoVisible}
                    onClose={(success, action) => {
                        if (success) {
                            this.reDeclarePush(action);
                        }
                        this.setState({
                            reDeclareModalInfoVisible: false,
                        });
                    }}
                />
                {/* /ccs/order/listStatus */}
                <NewModal
                    title={"修改清单状态"}
                    visible={this.state.updateStatus}
                    configList={this.state.updateConfigList}
                    onOk={data => {
                        let list = this.getCheckedRows();
                        const ids = list.map(item => item.declareOrderNo);
                        this.batchUpdateCunstomStatus(data, ids);
                    }}
                    onCancel={() => {
                        this.setState({ updateStatus: false });
                    }}
                />
                {/* <NewModal
                    title={"修改海关状态"}
                    visible={this.state.updateCustomStatus}
                    configList={this.state.updateCustomStatusConfigList}
                    onOk={data => {
                        console.log("data:", data);
                    }}
                    onCancel={() => {
                        this.setState({ updateCustomStatus: false });
                    }}
                /> */}
            </React.Fragment>
        );
    }

    batchUpdateCunstomStatus(data, ids) {
        const declareOrderNoList = ids.map(item => {
            return {
                declareOrderNo: item,
                ...data,
            };
        });

        lib.request({
            url: "/ccs/order/updateInventoryStatus",
            data: {
                declareOrderNoList,
            },
            success: data => {
                message.success("修改成功");
                this.setState({ updateStatus: false });
                this.load();
            },
        });
    }

    reDeclare() {
        let list = this.getCheckedRows();
        if (list.length === 0) {
            message.warning("请至少选择一条数据");
            return;
        }
        this.setState({
            reDeclareModalInfoVisible: true,
        });
    }

    reDeclarePush(action) {
        let list = this.getCheckedRows();
        let snList = list.reduce((prev, curr) => [...prev, curr.sn], []);
        let idList = list.reduce((prev, curr) => [...prev, curr.id], []);
        lib.request({
            url: "/ccs/order/reDeclare",
            data: {
                idList,
                snList,
                action: action,
                forcePush: true,
            },
            needMask: true,
            success: res => {
                message.success("订单强制重推成功");
                this.load();
            },
        });
    }
}

export default App;
