import React from "react";
import { SearchList, getConfigDataUtils, lib } from "react-single-app";
import axios from "axios";
import { Button, Space, Modal, message } from "antd";

export default class CullList extends SearchList {
    constructor(props) {
        super(props);
    }

    getConfig() {
        return axios.get(getConfigDataUtils.getDataUrlByDataId(550)).then(res => res.data.data);
    }

    neglectCull(row) {
        let onOk = this.auditOk.bind(this, row.id);
        Modal.confirm({
            title: "提示",
            content: "确定要对此单设置不处理吗？",
            cancelText: "取消",
            okText: "确定",
            onOk() {
                return onOk();
            },
        });
    }

    batchNeglectCull() {
        let { selectedRows } = this.state;
        if (selectedRows.length === 0) {
            message.warning("请选择数据");
            return;
        }
        let ids = selectedRows.reduce((prev, curr) => [...prev, curr.id], []);
        let onOk = this.auditOk.bind(this, ids);
        Modal.confirm({
            title: "提示",
            content: "确定要批量设置不处理吗？",
            cancelText: "取消",
            okText: "确定",
            onOk() {
                return onOk(ids);
            },
        });
    }

    auditOk = id => {
        let ids = [];
        if (Array.isArray(id)) {
            ids = id;
        } else {
            ids.push(id);
        }
        return new Promise((resolve, reject) => {
            lib.request({
                url: "/ccs/cull/neglect",
                needMask: true,
                data: { ids },
                success: res => {
                    this.load();
                    message.success("忽略成功");
                    resolve(res);
                },
                fail: e => reject(e),
            });
        });
    };

    myOperation(row) {
        return (
            <Space>
                <span className="link" onClick={() => this.neglectCull(row)}>
                    忽略
                </span>
            </Space>
        );
    }

    renderLeftOperation() {
        return (
            <Space>
                <Button
                    onClick={() => {
                        this.batchNeglectCull();
                    }}
                    type="primary">
                    批量忽略
                </Button>
            </Space>
        );
    }

    renderModal() {
        return <React.Fragment></React.Fragment>;
    }
}
