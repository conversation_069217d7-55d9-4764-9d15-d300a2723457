import { Button, Input, message, Tabs } from "antd";
import React from "react";
import { ConfigCenter, lib, SearchList, getConfigDataUtils } from "react-single-app";
import moment from "moment";
import axios from "axios";

class App extends SearchList {
    constructor(props) {
        super(props);
    }

    getConfig() {
        return axios.get(getConfigDataUtils.getDataUrlByDataId(765)).then(res => res.data.data);
    }

    getCheckedRows() {
        const ids = selectedRows.map(item => item.id);
        return ids;
    }

    componentDidMount() {
        let toTime = moment().format("YYYY-MM-DD"),
            fromTime = moment().subtract(1, "months").format("YYYY-MM-DD");
        this.changeImmutable({
            createFrom: moment(fromTime + " 00:00:00.000").format("x"),
            createTo: moment(toTime + " 23:59:59.999").format("x"),
        });
    }

    renderDeclareOrderNo(row) {
        return (
            <a
                onClick={() => {
                    lib.openPage(`/declaration-manage-detail?orderId=${row.orderId}&page_title=申报单详情`);
                }}>
                {row.declareOrderNo}
            </a>
        );
    }

    renderLeftOperation() {
        return (
            <React.Fragment>
                <Button type="primary" onClick={() => this.pushAgain("DECLARE_ORDER")}>
                    订单重推
                </Button>
            </React.Fragment>
        );
    }
    pushAgain(action) {
        let list = this.getCheckedRows();
        if (list.length !== 0) {
            let ids = [];
            list.map(item => {
                ids += `${item.orderId},`;
            });
            lib.request({
                url: "/ccs/order/reDeclare",
                data: {
                    action,
                    ids: ids.slice(0, -1),
                },
                method: "POST",
                needMask: true,
                success: res => {
                    if (res.errorMessage === null) {
                        message.success("重推成功");
                        this.load(true);
                    } else {
                        message.error(res.errorMessage);
                    }
                },
            });
        } else {
            message.warning("请选择数据");
        }
    }
}
export default App;
