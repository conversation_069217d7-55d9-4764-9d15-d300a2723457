import React from "react";
import "./shop-good.less";
import NewModal from "../components/NewModal";
import { Checkbox, Button, Modal, Drawer, Input, Select, Form, message } from "antd";
import { ConfigCenter, lib } from "react-single-app";
import FormItem from "antd/lib/form/FormItem";
import TextArea from "antd/lib/input/TextArea";

// const TokenModal = Form.create({name: "tokenModal"})(
// class extends React.Component{
//     constructor(props) {
//         super(props)
//     }

//     render() {
//         let { editRow, visible, onOk, onCancel } = this.props;
//         const formItemLayout = {
//             labelCol: { span: 6 },
//             wrapperCol: { span: 14 },
//         }
//         const { getFieldDecorator } = this.props.form;

//         return <Modal
//             title="查看令牌"
//             visible={visible}
//             onOk={() => onOk(this.props.form)}
//             onCancel={() => onCancel(this.props.form)}
//         >
//             <Form >
//             <FormItem {...formItemLayout} label="APPID">
//                 {getFieldDecorator("appId", {
//                     rules: [{required: true, message: "请输入APPID"}],
//                     initialValue: editRow && editRow.appId
//                 })(
//                     <Input/>
//                 )}
//             </FormItem>
//             {editRow && editRow.channel === "alipay" && <React.Fragment>
//             <FormItem {...formItemLayout} label="PartnerId">
//                 {getFieldDecorator("partner", {
//                     rules: [{required: true, message: "请输入PartnerId"}],
//                     initialValue: editRow && editRow.partner
//                 })(
//                     <Input/>
//                 )}
//             </FormItem>
//             <FormItem {...formItemLayout} label="密钥">
//                 {getFieldDecorator("key", {
//                     rules: [{required: true, message: "请输入密钥"}],
//                     initialValue: editRow && editRow.key
//                 })(
//                     <Input/>
//                 )}
//             </FormItem>
//             </React.Fragment>}
//             {editRow && editRow.channel === "wechatpay" && <React.Fragment>
//             <FormItem {...formItemLayout} label="MCHID">
//                 {getFieldDecorator("mchId", {
//                     rules: [{required: true, message: "请输入MCHID"}],
//                     initialValue: editRow && editRow.mchId
//                 })(
//                     <Input/>
//                 )}
//             </FormItem>
//             <FormItem {...formItemLayout} label="密钥">
//                 {getFieldDecorator("partnerKey", {
//                     rules: [{required: true, message: "请输入密钥"}],
//                     initialValue: editRow && editRow.partnerKey
//                 })(
//                     <TextArea/>
//                 )}
//             </FormItem>
//             </React.Fragment>}
//             </Form>
//         </Modal>
//     }
// })

class App extends ConfigCenter {
    constructor(props) {
        super(props);
        this.state.upUrl = "/ccs/pay/payMerchantAccountChannel/upset";
        this.state.newAdd = false;
        this.state.configList = [
            {
                type: "SELECT",
                labelName: "收款渠道",
                labelKey: "channel",
                required: true,
                message: "请选择收款渠道",
                list: [
                    { name: "支付宝", value: "alipay" },
                    { name: "微信", value: "wechatpay" },
                    { name: "联动支付", value: "umf" },
                    { name: "通联支付", value: "tonglian" },
                ],
                onChange: e => {
                    let { configList } = this.state;
                    this.configTokenJsonView(configList, e);
                    this.setState({ configList });
                },
            },
            {
                type: "INPUT",
                labelName: "收款账户",
                labelKey: "recpAccount",
                required: true,
                message: "请输入收款账户",
                maxLength: 50,
            },
            {
                type: "INPUT",
                labelName: "社会信用代码",
                labelKey: "recpCode",
                required: true,
                message: "请输入社会信用代码",
                maxLength: 50,
            },
            {
                type: "INPUT",
                labelName: "企业工商备案名",
                labelKey: "recpName",
                required: true,
                message: "请输入企业工商备案名",
                maxLength: 50,
            },
            { type: "INPUT", labelName: "APPID", labelKey: "appId", required: true, message: "请输入APPID" },
            { type: "INPUT", labelName: "PartnerId", labelKey: "partner", required: true, message: "请输入PartnerId" },
            {
                type: "INPUT",
                labelName: "密钥",
                labelKey: "key",
                required: true,
                message: "请输入密钥",
                extra: <span>密钥加密展示,如需修改请先清空</span>,
            },
        ];
        this.state.lookList = [
            { type: "INPUT", labelName: "APPID", labelKey: "appId", required: true, message: "请输入APPID" },
            { type: "INPUT", labelName: "PartnerId", labelKey: "partner", required: true, message: "请输入PartnerId" },
            {
                type: "INPUT",
                labelName: "密钥",
                labelKey: "key",
                required: true,
                message: "请输入密钥",
                extra: <span>密钥加密展示,如需修改请先清空</span>,
            },
        ];
    }

    componentDidMount() {
        // lib.checkIsLogin()
    }

    renderRightOperation() {
        return (
            <React.Fragment>
                <Button
                    type="primary"
                    onClick={() =>
                        this.setState({
                            visible: true,
                            modalTitle: "新增收款渠道",
                        })
                    }>
                    新增收款渠道
                </Button>
            </React.Fragment>
        );
    }

    handleOk(values, modalForm) {
        if (this.state.editRow) {
            values.id = this.state.editRow.id;
        }
        values.merchantId = lib.getParam("merchantId");

        lib.request({
            url: this.state.upUrl,
            data: values,
            method: "POST",
            needMask: true,
            success: res => {
                if (res) {
                    this.setState({
                        visible: false,
                        editRow: null,
                    });
                    this.load(true);
                    //   modalForm.resetFields()
                }
            },
        });
    }

    handleCancel() {
        this.setState({
            visible: false,
            editRow: null,
        });
    }

    modalOk(values) {
        let { editRow } = this.state;
        values.id = editRow.id;
        values.channel = editRow.channel;
        lib.request({
            url: "/ccs/pay/payMerchantAccountChannel/upset",
            data: values,
            method: "POST",
            needMask: true,
            success: res => {
                if (res) {
                    this.setState({
                        editRow: {},
                        tokenJsonVisible: false,
                    });
                    message.success("修改成功");
                    this.load(true);
                }
            },
        });
    }

    modalCancel(form) {
        this.setState({
            editRow: null,
            tokenJsonVisible: false,
        });
        // form.resetFields();
    }

    renderModal() {
        let { visible, modalTitle, editRow, tokenJsonVisible } = this.state;
        //   const props = {
        //         visible,
        //         title: modalTitle,
        //         editRow,
        //         form: this.props.form,
        //         onOk: this.handleOk.bind(this),
        //         onCancel: this.handleCancel.bind(this),
        //         configList: this.state.configList,
        //       }
        // return (
        //     <React.Fragment>
        //         <NewModal {...props} />
        //     </React.Fragment>
        // )
        const props = {
            visible,
            title: modalTitle,
            editRow,
            onOk: this.handleOk.bind(this),
            onCancel: this.handleCancel.bind(this),
            configList: this.state.configList,
        };
        const tokenProps = {
            visible: tokenJsonVisible,
            editRow,
            onOk: this.modalOk.bind(this),
            onCancel: this.modalCancel.bind(this),
            configList: this.state.lookList,
        };

        return (
            <React.Fragment>
                <NewModal {...props} />
                <NewModal {...tokenProps} />
            </React.Fragment>
        );
    }

    modalTokenJson(editRow) {
        return (
            <span
                className="link"
                onClick={() => {
                    let { lookList } = this.state;
                    this.configTokenJsonView(lookList, editRow.channel);
                    this.setState({
                        tokenJsonVisible: true,
                        editRow,
                    });
                }}>
                查看
            </span>
        );
    }

    /**
     * 根据channel 设置不同渠道的item
     * @param configList
     * @param channel
     */
    configTokenJsonView(configList, channel) {
        function setPayLabelName(item, partner, key) {
            if (item.labelKey === "partner") {
                item.labelName = partner;
            }
            if (item.labelKey === "key") {
                item.labelName = key;
            }
        }
        function setAppIdDisplay(item, hide) {
            if (item.labelKey === "appId") {
                item.hide = hide;
            }
        }
        configList.map(item => {
            if (channel === "alipay") {
                setAppIdDisplay(item, false);
                setPayLabelName(item, "PartnerId", "密钥");
            } else if (channel === "wechatpay") {
                setAppIdDisplay(item, false);
                setPayLabelName(item, "微信商户号", "密钥");
            } else if (channel === "umf") {
                setAppIdDisplay(item, true);
                setPayLabelName(item, "商户id", "密钥");
            }
        });
    }

    myOperation(row) {
        return (
            <React.Fragment>
                <span
                    className="link"
                    onClick={() => {
                        let { configList } = this.state;
                        this.configTokenJsonView(configList, row.channel);
                        this.setState(
                            {
                                configList,
                            },
                            () => {
                                this.setState({
                                    editRow: row,
                                    visible: true,
                                    modalTitle: "编辑收款渠道",
                                });
                            },
                        );
                    }}>
                    编辑
                </span>
            </React.Fragment>
        );
    }
}

export default App;
// export default Form.create({name: "app"})(App)
