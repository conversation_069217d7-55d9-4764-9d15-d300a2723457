import React, { useState, useEffect } from "react";
import { Checkbox, Button, Modal, Drawer, Input, Select, Form, Icon, message, Tabs, Table, Space } from "antd";
import TextArea from "antd/lib/input/TextArea";
import { ConfigCenter, lib, Uploader } from "react-single-app";
import NewModal from "../components/NewModal";
import "./shop-good.less";
import "./declare-outbound-manage.less";

const FormItem = Form.Item;
const { TabPane } = Tabs;

class App extends ConfigCenter {
    constructor(props) {
        super(props);
        this.state.modalTitle = "新增核注清单";
        // 编辑/新建url
        this.state.upUrl = "/ccs/exportOrder/upset";
        this.state.modalData = {};
        this.state.modalVisible = false;
        this.state.result = {};
        this.state.configList = [
            // { type: 'SELECT', labelName: '清关企业', labelKey: 'declareCompanyId', list: [], xhr: '/xhr/company/listWithQualify?qualify=SBQY', required: true },
            // { type: 'SELECT', labelName: '账册编号', labelKey: 'accountBookId', list: [], xhr: '/xhr/customsBook/listAllInUseBookNo', required: true },
            // { type: 'SELECT', labelName: '快递名称', labelKey: 'expressIdList', list: [], xhr: '/xhr/express/listEnable', mode: "multiple", required: true },

            // {
            //     type: "SELECT",
            //     labelName: "清关企业",
            //     labelKey: "declareCompanyId",
            //     list: [],
            //     ccs: "/ccs/company/listWithSBQY",
            //     required: true,
            //     onChange: (e, form) => {
            //         lib.request({
            //             url: "/ccs/customsBook/listBookByAreaCompany",
            //             data: {
            //                 areaCompanyId: e,
            //             },
            //             success: data => {
            //                 this.state.configList[1].list = data;
            //                 this.setState({
            //                     configList: [...this.state.configList],
            //                 });
            //             },
            //         });
            //         form.setFieldValue("accountBookId", null);
            //     },
            // },
            // {
            //     type: "SELECT",
            //     labelName: "账册编号",
            //     labelKey: "accountBookId",
            //     list: [],
            //     // ccs: "/ccs/customsBook/effective/listBookNoByAccBookAuth",
            //     required: true,
            // },
            {
                type: "SELECT",
                labelName: "实体仓名称",
                labelKey: "entityWarehouseCode",
                list: [],
                ccs: "/ccs/entityWarehouse/entityWarehouse",
                required: true,
            },
            {
                type: "SELECT",
                labelName: "快递名称",
                labelKey: "expressIdList",
                list: [],
                ccs: "/ccs/express/listEnable",
                mode: "multiple",
                required: true,
            },
        ];
        this.uploader = React.createRef();
    }

    componentDidMount() {
        // lib.checkIsLogin()
    }

    getConfigList() {
        let { configList } = this.state;
        configList.map(item => {
            if (item.ccs) {
                lib.request({
                    url: item.ccs,
                    // data: { qualify: 'SBQY' },
                    needMask: false,
                    success: res => {
                        item.list = res || [];
                        this.setState({
                            configList,
                        });
                    },
                });
            }
        });
        this.setState({
            configList,
        });
    }

    sn(row) {
        return <a onClick={() => {
            lib.openPage(`/ccs/delcare-outbound-detail?pageTitle=申报出库单详情&id=${row.id}`)
        }}>{row.sn}</a>
    }

    // 关联单证号
    renderSns(row) {
        let arr = row.endorsementSns.split("/");
        return (
            <React.Fragment>
                {arr.map(item => (
                    <p style={{ lineHeight: "24px", marginBottom: 0 }} key={item}>
                        {item}
                    </p>
                ))}
            </React.Fragment>
        );
    }

    // 查看运单
    modalMailno(id) {
        lib.request({
            url: "/ccs/exportOrder/findMailById",
            data: {
                id,
            },
            needMask: true,
            success: res => {
                if (res) {
                    this.setState({
                        mailVisible: true,
                        modalData: res,
                    });
                }
            },
        });
    }
    export(row) {
        let { pagination, searchConditions } = this.state;
        lib.request({
            url: "/ccs/exportOrder/submit/export",
            needMask: true,
            data: { id: row.id },
            success: json => {
                Modal.confirm({
                    okText: "去下载中心",
                    cancelText: "取消",
                    icon: null,
                    content: "新建下载任务成功",
                    onOk() {
                        lib.openPage("/download-center?page_title=下载中心");
                    },
                });
            },
        });
    }

    myOperation(row) {
        return (
            <React.Fragment>
                <Space>
                    {/* {row.allowLoadMailNo && (
                        <a className="link" onClick={() => this.modalMailno(row.id)}>
                            查看运单
                        </a>
                    )} */}
                    {row.allowDiscard && (
                        <a className="link" onClick={() => this.cancelRow(row)}>
                            作废
                        </a>
                    )}
                    {row.allowDelete && (
                        <a className="link" onClick={() => this.deleteRow(row)}>
                            删除
                        </a>
                    )}
                    {row.allowImport ? (
                        <a
                            className="link import-link"
                            onClick={() => {
                                this.importFc(row);
                            }}>
                            导入
                        </a>
                    ) : (
                        <a className="link" onClick={() => this.export(row)}>
                            导出
                        </a>
                    )}
                    {row.allowImport && (
                        <a className="link import" onClick={() => this.showImportModal(row)}>
                            录入
                        </a>
                    )}
                    {
                        <a className="link import" onClick={() => {
                            Modal.confirm({
                                title: '生成核注单',
                                content: '是否生成核注单？',
                                onOk: () => {
                                    lib.request({
                                        url: '/ccs/exportOrder/generateEndorsement',
                                        data: {
                                            id: row.id,
                                        },
                                        success: () => {
                                            this.load();
                                        }
                                    })
                                }
                            })
                        }}>生成核注单</a>
                    }
                </Space>
            </React.Fragment >
        );
    }
    showImportModal(row) {
        this.setState({
            importModal: true,
            importId: row.id,
        });
    }
    importFc(row) {
        this.setState({
            fileId: row.id,
        });
        this.uploader.current.querySelector(".react-single-app-uploader input").click();
    }

    importFile({ src, name }) {
        let { fileId } = this.state;
        lib.request({
            url: "/ccs/exportOrder/preImport",
            data: {
                id: fileId,
                url: src,
            },
            needMask: true,
            success: res => {
                this.uploader.current.querySelector(".react-single-app-uploader input").value = "";
                this.setState({
                    modalVisible: true,
                    result: res,
                    importId: fileId,
                });
            },
        });
    }

    renderModal() {
        let props = {
            title: this.state.modalTitle,
            onOk: this.handleOk.bind(this),
            onCancel: this.handleCancel.bind(this),
            configList: this.state.configList,
            visible: this.state.visible,
            form: this.props.form,
            formItemLayout: {
                labelCol: { span: 8 },
                wrapperCol: { span: 14 },
            },
            modalStyle: { width: "800px" },
        };
        const columns1 = [
            {
                title: "序号",
                dataIndex: "idx",
            },
            {
                title: "运单号",
                dataIndex: "mailNo",
            },
            {
                title: "重量(KG)",
                dataIndex: "grossWeight",
            },
        ];
        const columns2 = [
            {
                title: "序号",
                dataIndex: "idx",
            },
            {
                title: "运单号",
                dataIndex: "mailNo",
            },
            {
                title: "错误信息",
                dataIndex: "errorMsg",
            },
        ];
        const { modalVisible, result, mailVisible, modalData, importModal, importText } = this.state;
        return (
            <React.Fragment>
                <div className="my-uploader" ref={this.uploader}>
                    <Uploader onChange={file => this.importFile(file)} />
                </div>
                {/* 新增弹窗 */}
                <NewModal {...props} />
                {/* 导入弹窗 */}
                <Modal
                    title="请输入单号，运单号+快递名称"
                    open={importModal}
                    onOk={() => this.showPreviewModal()}
                    onCancel={() => this.cancelImport()}>
                    <TextArea
                        value={importText}
                        rows={20}
                        onChange={e => this.setState({ importText: e.target.value })}
                    />
                </Modal>
                {/* 预览弹窗 */}
                <Modal
                    width="800px"
                    open={modalVisible}
                    title={`提交预览  (修改数量${result.totalCount})`}
                    onOk={() => this.submitHandler()}
                    okText="提交"
                    onCancel={() => this.cancelFunc()}>
                    <Tabs defaultActiveKey="0">
                        <TabPane tab={`校验成功(${result.successCount})`} key="0">
                            <Table dataSource={result.successRecordList} columns={columns1} rowKey="index"></Table>
                        </TabPane>
                        <TabPane tab={`校验失败(${result.failCount})`} key="1">
                            <Table dataSource={result.failRecordList} columns={columns2} rowKey="index"></Table>
                        </TabPane>
                    </Tabs>
                </Modal>
                {/* 查看运单弹窗 */}
                <Modal
                    width="700px"
                    title="查看运单"
                    open={mailVisible}
                    onOk={() => this.setState({ mailVisible: false })}
                    onCancel={() => this.setState({ mailVisible: false })}>
                    <div className="modal-header">
                        <div className="modal-title">工作台号：{modalData.stationNo}</div>
                        <div className="modal-title">包裹总数量({modalData.packageCount})个</div>
                        <div className="modal-title">累计总重量({modalData.packageWeight})KG</div>
                        <div className="modal-title">累计SKU种类({modalData.skuCount})个</div>
                    </div>
                    <Tabs defaultActiveKey="0">
                        {modalData.groupList &&
                            modalData.groupList.map((item, index) => {
                                return (
                                    <TabPane key={index} tab={`托盘号：${item.trayNo}`}>
                                        <pre className="modal-center">
                                            当前托盘包裹数量({item.packageCount})个 累计重量({item.packageWeight})KG
                                            SKU种类({item.skuCount})个
                                        </pre>
                                        <Table
                                            dataSource={item.itemList}
                                            columns={[
                                                {
                                                    title: "运单号",
                                                    dataIndex: "mailNo",
                                                    align: "center",
                                                },
                                                {
                                                    title: "重量(KG)",
                                                    dataIndex: "grossWeight",
                                                    align: "center",
                                                },
                                            ]}
                                            rowKey="idx"></Table>
                                    </TabPane>
                                );
                            })}
                    </Tabs>
                </Modal>
            </React.Fragment>
        );
    }
    // 作废
    cancelRow(row) {
        Modal.confirm({
            cancelText: "取消",
            okText: "确定",
            title: "提示",
            content: "请确认作废申报出库单？作废后不可生成核注清单",
            onOk: () => {
                lib.request({
                    url: "/ccs/exportOrder/discard",
                    data: {
                        id: row.id,
                    },
                    method: "POST",
                    needMask: true,
                    success: res => {
                        if (res) {
                            message.success("作废成功");
                            this.load(true);
                        }
                    },
                });
            },
        });
    }
    //     // 预览弹窗 确认方法
    //     submitHandler() {
    //         if (this.state.result.successCount === 0) {
    //             message.warning("暂无有效数据");
    //             return;
    //         }
    //         lib.request({
    //             url: "/ccs/exportOrder/discard",
    //             data: {
    //                 id: row.id,
    //             },
    //             method: "POST",
    //             needMask: true,
    //             success: (res) => {
    //                 if (res) {
    //                     message.success("作废成功");
    //                     this.load(true);
    //                 }
    //             },
    //         });
    //     },
    // });
    //   }

    // 删除
    deleteRow(row) {
        Modal.confirm({
            cancelText: "取消",
            okText: "确定",
            title: "提示",
            content: "请确认删除申报出库单？",
            onOk: () => {
                lib.request({
                    url: "/ccs/exportOrder/delete",
                    data: {
                        id: row.id,
                    },
                    method: "POST",
                    needMask: true,
                    success: res => {
                        if (res) {
                            message.success("删除成功");
                            this.load(true);
                        }
                    },
                });
            },
        });
    }

    newFunc() {
        this.setState({
            visible: true,
            modalTitle: "新增单据",
        });
        this.getConfigList();
    }

    // 渲染按钮
    renderRightOperation() {
        return (
            <React.Fragment>
                <Button type="primary" onClick={() => this.newFunc()}>
                    新增单据
                </Button>
            </React.Fragment>
        );
    }

    // 新增单据弹窗 确认方法
    handleOk(values, modalForm) {
        for (let i in values) {
            if (!values[i]) {
                delete values[i];
            }
        }
        lib.request({
            url: this.state.upUrl,
            data: values,
            method: "POST",
            needMask: true,
            success: res => {
                if (res) {
                    this.setState({
                        visible: false,
                    });
                    this.load(true);
                    modalForm && modalForm.resetFields();
                }
            },
        });
    }

    // 新增单据弹窗 关闭方法
    handleCancel() {
        this.setState({
            visible: false,
        });
    }

    cancelFunc() {
        this.setState(
            {
                modalVisible: false,
            },
            () => {
                setTimeout(() => {
                    this.setState({
                        result: {},
                    });
                }, 1000);
            },
        );
    }

    showPreviewModal() {
        let { importText, importId } = this.state;
        if (importText) {
            let content = importText
                .split("\n")
                .map(item =>
                    item
                        .split(" ")
                        .filter(ite => ite && ite.trim())
                        .join(" "),
                )
                .join("|");
            lib.request({
                url: "/ccs/exportOrder/pre/input-import",
                data: {
                    id: importId,
                    content,
                },
                method: "POST",
                needMask: true,
                success: res => {
                    this.setState({
                        modalVisible: true,
                        importModal: false,
                        result: res,
                        importText: "",
                    });
                },
            });
        } else {
            message.warning("请输入单号");
        }
    }

    cancelImport() {
        this.setState({
            importModal: false,
            importText: "",
        });
    }

    // 预览弹窗 确认方法
    submitHandler() {
        // if (this.state.result.successCount === 0) {
        //     message.warning("暂无有效数据")
        //     return
        // }
        // lib.request({
        //     url: "/ccs/exportOrder/submit/import",
        //     data: {
        //         id: this.state.importId,
        //         recordList: this.state.result.successRecordList,
        //     },
        //     needMask: true,
        //     success: res => {
        //         if (!res.errorMessage) {
        //             message.success("导入成功")
        //             this.setState({
        //                 modalVisible: false,
        //                 result: {},
        //             })
        //             this.load(true)
        //         }
        //     }
        // })
        message.success("导入成功");
        this.setState({
            modalVisible: false,
            result: {},
        });
        this.load(true);
    }
}

export default App;
