import { DTEditFormConfigs, DTEditFormRefs } from "@dt/components";
import React from "react";
import { Button, Space, Form, Input, message } from "antd";
import { DDYObject } from "react-single-app";
import { workOrderStatus } from "./type";
type configsFnType = (props: DDYObject) => DTEditFormConfigs;
const configsFn: configsFnType = props => {
    const { ref } = props;
    return {
        baseInfo: {
            title: <h3 style={{ fontSize: "24px" }}>基础信息</h3>,
            renderHeadRight: ({ configItem, detail }) => {
                return (
                    <div style={{ display: "flex", flexDirection: "row-reverse" }}>
                        {detail?.status === workOrderStatus.CREATED && (
                            <Space>
                                <Button
                                    onClick={() => {
                                        props.changeMode({ name: "finish" });
                                    }}>
                                    完成运输
                                </Button>
                            </Space>
                        )}
                    </div>
                );
            },
            configs: [
                {
                    type: "TEXT",
                    fProps: {
                        label: "运输作业单号",
                        name: "transNo",
                    },
                },
                {
                    type: "TEXT",
                    fProps: {
                        label: "作业单类型",
                        name: "orderTypeDesc",
                    },
                },
                {
                    type: "TEXT",
                    fProps: {
                        label: "作业单状态",
                        name: "statusDesc",
                    },
                },
                {
                    type: "TEXT",
                    fProps: {
                        label: "港到仓资源编码",
                        name: ["baseInfo", "resourceCode"],
                    },
                },
                {
                    type: "TEXT",
                    fProps: {
                        label: "干线运输资源编码",
                        name: ["baseInfo", "mainLineResourceCode"],
                    },
                },
                {
                    type: "TEXT",
                    fProps: {
                        label: "创建时间",
                        name: "createTime",
                    },
                },
                {
                    type: "TEXT",
                    fProps: {
                        label: "外部单号",
                        name: "outOrderNo",
                    },
                },
                {
                    type: "TEXT",
                    fProps: {
                        label: "货主",
                        name: "ownerName",
                    },
                },
                {
                    type: "TEXT",
                    fProps: {
                        label: "实体仓",
                        name: "warehouseName",
                    },
                },
                {
                    type: "TEXT",
                    fProps: {
                        label: "完成时间",
                        name: "finishTime",
                    },
                },
                {
                    type: "TEXT",
                    fProps: {
                        label: "进境口岸",
                        name: ["baseInfo", "entryPort"],
                    },
                },
                {
                    type: "TEXT",
                    fProps: {
                        label: "港到仓装车方式",
                        name: ["baseInfo", "loadingTypePortToWh"],
                    },
                },
                {
                    type: "TEXT",
                    fProps: {
                        label: "起运国/地区",
                        name: ["baseInfo", "fromRegion"],
                    },
                },
                {
                    type: "TEXT",
                    fProps: {
                        label: "起运港（始发机场）",
                        name: ["baseInfo", "fromLocation"],
                    },
                },
                {
                    type: "TEXT",
                    fProps: {
                        label: "起运仓",
                        name: ["baseInfo", "fromWarehouse"],
                    },
                },
                {
                    type: "TEXT",
                    fProps: {
                        label: "目的国/地区",
                        name: ["baseInfo", "toRegion"],
                    },
                },
                {
                    type: "TEXT",
                    fProps: {
                        label: "目的港",
                        name: ["baseInfo", "toLocation"],
                    },
                },
                {
                    type: "TEXT",
                    fProps: {
                        label: "目的仓",
                        name: ["baseInfo", "toWarehouse"],
                    },
                },
                {
                    type: "TEXT",
                    fProps: {
                        label: "起始地址",
                        name: ["baseInfo", "mainLineFromLocation"],
                    },
                },
                {
                    type: "TEXT",
                    fProps: {
                        label: "目的地址",
                        name: ["baseInfo", "mainLineToLocation"],
                    },
                },
                {
                    type: "TEXT",
                    fProps: {
                        label: "备注",
                        name: "remark",
                    },
                },
            ],
        },
        serverInfo: {
            title: <h3 style={{ fontSize: "24px", marginBottom: 0 }}>服务商填写信息</h3>,
            renderHeadRight: ({ configItem, detail }) => {
                return (
                    <Space style={{ display: "flex", flexDirection: "row-reverse" }}>
                        {detail?.status === workOrderStatus.CREATED && (
                            <>
                                {configItem.moduleMode === "read" && (
                                    <Button
                                        onClick={() => {
                                            props.changeMode({ name: "addServer" });
                                        }}>
                                        添加车辆信息
                                    </Button>
                                )}
                                <Button
                                    onClick={() => {
                                        if (configItem.moduleMode === "edit") {
                                            props.changeMode({ name: "serverInfo", configItem: configItem });
                                        } else {
                                            configItem.moduleMode = "edit";
                                            ref.current.setConfigModuleItem("serverInfo", configItem);
                                        }
                                    }}>
                                    {configItem.moduleMode === "edit" ? "保存" : "编辑"}
                                </Button>
                                {configItem.moduleMode === "read" && (
                                    <Button
                                        onClick={() => {
                                            if (!detail?.vehicleResourceCode) {
                                                message.error("请先添加车辆资源编码");
                                                return;
                                            }
                                            configItem.configs.map(item => {
                                                const bol = ["actDepTime", "actArrTime", "actSignTime"].includes(
                                                    item?.fProps.name as string,
                                                );
                                                // item.editable = !bol;
                                                if (bol) {
                                                    item.editable = false;
                                                }
                                                if (item?.fProps.name === "vehiclePlate") {
                                                    item.editable = true;
                                                }
                                            });
                                            configItem.configs = [...configItem.configs];
                                            configItem.moduleMode = "edit";
                                            ref.current.setConfigModuleItem("serverInfo", configItem);
                                        }}>
                                        编辑车牌
                                    </Button>
                                )}
                            </>
                        )}
                    </Space>
                );
            },
            moduleMode: "read",
            configs: [
                {
                    // /^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领A-Z]{1}[A-Z]{1}[A-Z0-9]{4}[A-Z0-9挂学警港澳]{1}$/
                    // type: "SELECT",
                    type: "INPUT",
                    fProps: {
                        label: "车牌号",
                        name: "vehiclePlate",
                        rules: [
                            { required: true },
                            {
                                validator(rule, value, callback) {
                                    if (!value) return Promise.resolve("");
                                    if (value.length !== 7 || value.length !== 8) {
                                        return Promise.resolve("车牌号长度不符合7-8位");
                                    }
                                    return Promise.resolve("");
                                },
                            },
                        ],
                    },
                    cProps: {
                        // disabled: true,
                    },
                    // list: [],
                    // dataUrl: "/ccs/vehicleResource/listVehiclePlate",
                    // editable: true,
                },
                {
                    type: "INPUT",
                    fProps: {
                        label: "车辆资源编码",
                        name: "vehicleResourceCode",
                        rules: [{ required: true }],
                    },
                    cProps: {
                        disabled: true,
                    },
                    editable: true,
                },
                {
                    type: "INPUT",
                    fProps: {
                        label: "车型名称",
                        name: "vehicleTypeName",
                    },
                },
                {
                    type: "INPUT",
                    fProps: {
                        label: "历史运输次数",
                        name: "historyShipmentsNum",
                    },
                },
                {
                    type: "INPUT",
                    fProps: {
                        label: "本单运次编号",
                        name: "shipmentNo",
                        rules: [{ required: true }],
                    },
                    cProps: {
                        disabled: true,
                    },
                    editable: true,
                },

                {
                    type: "INPUT",
                    fProps: {
                        label: "可装载集装箱类型",
                        name: "containerTypeDesc",
                    },
                },
                {
                    type: "DATE",
                    fProps: {
                        label: "实际发车时间",
                        name: "actDepTime",
                        rules: [{ required: true }],
                    },
                    cProps: {
                        showTime: true,
                    },
                    editable: true,
                    showTextFormat: "YYYY-MM-DD HH:mm:ss",
                },
                {
                    type: "DATE",
                    fProps: {
                        label: "实际到达时间",
                        name: "actArrTime",
                        rules: [{ required: true }],
                    },
                    cProps: {
                        showTime: true,
                    },
                    editable: true,
                    showTextFormat: "YYYY-MM-DD HH:mm:ss",
                },

                {
                    type: "DATE",
                    fProps: {
                        label: "实际签收时间",
                        name: "actSignTime",
                        rules: [{ required: true }],
                    },
                    cProps: {
                        showTime: true,
                    },
                    editable: true,
                    showTextFormat: "YYYY-MM-DD HH:mm:ss",
                },
            ],
        },
        aptitudeTitle: {
            title: <h3 style={{ fontSize: "24px" }}>资质相关信息</h3>,
            configs: [],
            useChildren: true,
        },
        shipperInfo: {
            title: "发货人",
            configs: [
                {
                    type: "TEXT",
                    fProps: {
                        label: "姓名",
                        name: ["shipperInfo", "name"],
                    },
                },
                {
                    type: "TEXT",
                    fProps: {
                        label: "电话",
                        name: ["shipperInfo", "phone"],
                    },
                },
                {
                    type: "TEXT",
                    fProps: {
                        label: "邮箱",
                        name: ["shipperInfo", "email"],
                    },
                },
                {
                    type: "TEXT",
                    fProps: {
                        label: "公司名称",
                        name: ["shipperInfo", "companyName"],
                    },
                },
                {
                    type: "TEXT",
                    fProps: {
                        label: "地址",
                        name: ["shipperInfo", "address"],
                    },
                },
                {
                    type: "TEXT",
                    fProps: {
                        label: "信用代码",
                        name: ["shipperInfo", "socialCreditCode"],
                    },
                },
            ],
        },
        consigneeInfo: {
            title: "收货人",
            configs: [
                {
                    type: "TEXT",
                    fProps: {
                        label: "姓名",
                        name: ["consigneeInfo", "name"],
                    },
                },
                {
                    type: "TEXT",
                    fProps: {
                        label: "电话",
                        name: ["consigneeInfo", "phone"],
                    },
                },
                {
                    type: "TEXT",
                    fProps: {
                        label: "邮箱",
                        name: ["consigneeInfo", "email"],
                    },
                },
                {
                    type: "TEXT",
                    fProps: {
                        label: "公司名称",
                        name: ["consigneeInfo", "companyName"],
                    },
                },
                {
                    type: "TEXT",
                    fProps: {
                        label: "地址",
                        name: ["consigneeInfo", "address"],
                    },
                },
                {
                    type: "TEXT",
                    fProps: {
                        label: "信用代码",
                        name: ["consigneeInfo", "socialCreditCode"],
                    },
                },
            ],
        },
        notifyPersonInfo: {
            title: "通知人",
            configs: [
                {
                    type: "TEXT",
                    fProps: {
                        label: "姓名",
                        name: ["notifyPersonInfo", "name"],
                    },
                },
                {
                    type: "TEXT",
                    fProps: {
                        label: "电话",
                        name: ["notifyPersonInfo", "phone"],
                    },
                },
                {
                    type: "TEXT",
                    fProps: {
                        label: "邮箱",
                        name: ["notifyPersonInfo", "email"],
                    },
                },
                {
                    type: "TEXT",
                    fProps: {
                        label: "公司名称",
                        name: ["notifyPersonInfo", "companyName"],
                    },
                },
                {
                    type: "TEXT",
                    fProps: {
                        label: "地址",
                        name: ["notifyPersonInfo", "address"],
                    },
                },
                {
                    type: "TEXT",
                    fProps: {
                        label: "信用代码",
                        name: ["notifyPersonInfo", "socialCreditCode"],
                    },
                },
            ],
        },
        bookingPartyInfo: {
            title: "商家联系人信息",
            configs: [
                {
                    type: "TEXT",
                    fProps: {
                        label: "姓名",
                        name: ["bookingPartyInfo", "name"],
                    },
                },
                {
                    type: "TEXT",
                    fProps: {
                        label: "电话",
                        name: ["bookingPartyInfo", "phone"],
                    },
                },
                {
                    type: "TEXT",
                    fProps: {
                        label: "邮箱",
                        name: ["bookingPartyInfo", "email"],
                    },
                },
                {
                    type: "TEXT",
                    fProps: {
                        label: "公司名称",
                        name: ["bookingPartyInfo", "companyName"],
                    },
                },
                {
                    type: "TEXT",
                    fProps: {
                        label: "地址",
                        name: ["bookingPartyInfo", "address"],
                    },
                },
                {
                    type: "TEXT",
                    fProps: {
                        label: "信用代码",
                        name: ["bookingPartyInfo", "socialCreditCode"],
                    },
                },
            ],
        },
        supplierInfo: {
            title: "供应商",
            configs: [
                {
                    type: "TEXT",
                    fProps: {
                        label: "姓名",
                        name: ["supplierInfo", "name"],
                    },
                },
                {
                    type: "TEXT",
                    fProps: {
                        label: "电话",
                        name: ["supplierInfo", "phone"],
                    },
                },
                {
                    type: "TEXT",
                    fProps: {
                        label: "邮箱",
                        name: ["supplierInfo", "email"],
                    },
                },
                {
                    type: "TEXT",
                    fProps: {
                        label: "公司名称",
                        name: ["supplierInfo", "companyName"],
                    },
                },
                {
                    type: "TEXT",
                    fProps: {
                        label: "地址",
                        name: ["supplierInfo", "address"],
                    },
                },
                {
                    type: "TEXT",
                    fProps: {
                        label: "信用代码",
                        name: ["supplierInfo", "socialCreditCode"],
                    },
                },
            ],
        },
        goodsInfo: {
            title: <h3 style={{ fontSize: "24px", marginBottom: 0 }}>商品明细</h3>,
            configs: [],
            renderHeadRight: ({ detail }) => {
                return (
                    <div style={{ display: "flex", flexDirection: "row-reverse" }}>
                        {detail?.status === workOrderStatus.CREATED && detail?.orderType === "TOB_TRANSPORT" && (
                            <Button
                                onClick={() => {
                                    props.changeMode({ name: "goodsInfo" });
                                }}>
                                保存
                            </Button>
                        )}
                    </div>
                );
            },
            useChildren: true,
        },
        packageInfo: {
            title: <h3 style={{ fontSize: "24px" }}>包裹信息</h3>,
            configs: [
                {
                    type: "TEXT",
                    fProps: {
                        label: "总体积（立方米)",
                        name: ["packageInfo", "totalVolume"],
                    },
                },
                {
                    type: "TEXT",
                    fProps: {
                        label: "总体积（立方米)",
                        name: ["packageInfo", "totalWeight"],
                    },
                },
            ],
        },
        packageItems: {
            title: "包装明细",
            configs: [],
            useChildren: true,
        },
        packagingInfo: {
            title: <h3 style={{ fontSize: "24px" }}>装箱信息</h3>,
            configs: [],
            useChildren: true,
        },
        packagingBaseInfo: {
            title: "基本信息",
            configs: [
                {
                    type: "TEXT",
                    fProps: {
                        label: "货品类型",
                        name: ["loadInfo", "productTypeDesc"],
                    },
                },
                {
                    type: "TEXT",
                    fProps: {
                        label: "货品描述",
                        name: ["loadInfo", "productDesc"],
                    },
                },
                {
                    type: "TEXT",
                    fProps: {
                        label: "装箱方式",
                        name: ["loadInfo", "loadingTypeDesc"],
                    },
                },
                {
                    type: "TEXT",
                    fProps: {
                        label: "商品温度要求（℃）",
                        name: ["loadInfo", "temperature"],
                    },
                },
                {
                    type: "TEXT",
                    fProps: {
                        label: "柜型与数量",
                        name: ["loadInfo", "containerTypes"],
                    },
                },
            ],
        },
        packagingHeadInfo: {
            title: "头程承运信息",
            configs: [
                {
                    type: "TEXT",
                    fProps: {
                        label: "运输方式",
                        name: ["carrierInfo", "transportModeDesc"],
                    },
                },
                {
                    type: "TEXT",
                    fProps: {
                        label: "主提单号",
                        name: ["carrierInfo", "mblNo"],
                    },
                },
                {
                    type: "TEXT",
                    fProps: {
                        label: "分提单号",
                        name: ["carrierInfo", "hblNo"],
                    },
                },
                {
                    type: "TEXT",
                    fProps: {
                        label: "承运商",
                        name: ["carrierInfo", "carrierName"],
                    },
                },
                {
                    type: "TEXT",
                    fProps: {
                        label: "船名",
                        name: ["carrierInfo", "shippingName"],
                    },
                },
                {
                    type: "TEXT",
                    fProps: {
                        label: "船次",
                        name: ["carrierInfo", "flightNo"],
                    },
                },
                {
                    type: "TEXT",
                    fProps: {
                        label: "航班日期",
                        name: ["carrierInfo", "flightDate"],
                    },
                },
                {
                    type: "TEXT",
                    fProps: {
                        label: "期望到港时间",
                        name: ["carrierInfo", "expectArrivePortDate"],
                    },
                },
                {
                    type: "TEXT",
                    fProps: {
                        label: "集装箱清单",
                        name: ["carrierInfo", "containerCodes"],
                    },
                },
            ],
        },
        packagingItems: {
            title: "装箱明细",
            useChildren: true,
            configs: [],
            renderHeadRight: ({ detail }) => {
                return (
                    <div style={{ display: "flex", flexDirection: "row-reverse" }}>
                        {detail?.status === workOrderStatus.CREATED &&
                            detail?.orderType === "TOB_PORT_TO_WAREHOUSE" && (
                                <Button
                                    onClick={() => {
                                        props.changeMode({ name: "packagingInfo" });
                                    }}>
                                    保存
                                </Button>
                            )}
                    </div>
                );
            },
        },
        filesInfo: {
            title: <h3 style={{ fontSize: "24px" }}>文件信息</h3>,
            configs: [],
            useChildren: true,
        },
    };
};

export const packageColumns = [
    {
        title: "行号",
        render: (_, record, index) => {
            return index + 1;
        },
    },
    {
        title: "包装单位",
        dataIndex: "packageTypeDesc",
        key: "packageTypeDesc",
    },
    {
        title: "包装数量",
        dataIndex: "qty",
        key: "qty",
    },
    {
        title: "宽度（厘米）",
        dataIndex: "width",
        key: "width",
    },
    {
        title: "长度（厘米）",
        dataIndex: "length",
        key: "length",
    },
    {
        title: "高度（厘米）",
        dataIndex: "height",
        key: "height",
    },
    {
        title: "是否叠托",
        dataIndex: "overlayTrayDesc",
        key: "overlayTrayDesc",
    },
    {
        title: "托盘材质",
        dataIndex: "trayTypeDesc",
        key: "trayTypeDesc",
    },
];

export const packagingColumns = [
    {
        title: "行号",
        render: (_, record, index) => {
            return index + 1;
        },
    },
    {
        title: "箱号",
        dataIndex: "boxNo",
        key: "boxNo",
    },
    {
        title: "装箱明细行号",
        dataIndex: "packagingItemLineNo",
        key: "packagingItemLineNo",
    },
    {
        title: "托盘编号",
        dataIndex: "palletNo",
        key: "palletNo",
    },
    {
        title: "商品编码",
        dataIndex: "itemCode",
        key: "itemCode",
    },
    {
        title: "商品数量",
        dataIndex: "itemQuantity",
        key: "itemQuantity",
    },
    {
        title: "实际发货数量",
        width: 140,
        dataIndex: "actDeliveryQty",
        key: "actDeliveryQty",
        render: (text, record, index) => {
            return (
                <Form.Item name={["packagingInfo", index, "actDeliveryQty"]} rules={[{ required: true }]}>
                    <Input
                        onChange={e => {
                            //@ts-ignore
                            // dataSource[index].actDeliveryQty = e.target.value;
                        }}
                    />
                </Form.Item>
            );
        },
    },
    {
        title: "实际签收数量",
        dataIndex: "actSignQty",
        key: "actSignQty",
        width: 150,
        render: (text, record, index) => {
            return (
                <Form.Item name={["packagingInfo", index, "actSignQty"]} rules={[{ required: true }]}>
                    <Input
                        onChange={e => {
                            //@ts-ignore
                            // dataSource[index].actSignQty = e.target.value;
                        }}
                    />
                </Form.Item>
            );
        },
    },
];

export const filesColumns = [
    {
        title: "文件类型",
        dataIndex: "attachmentTypeDesc",
        key: "attachmentTypeDesc",
    },
    {
        title: "文件名称",
        dataIndex: "name",
        key: "name",
    },
    {
        title: "操作",
        render: (_, record, index) => {
            return (
                <Space>
                    <a target="_blank" href={record.url}>
                        预览
                    </a>
                    <a
                        onClick={() => {
                            window.open(record.url);
                        }}>
                        下载
                    </a>
                </Space>
            );
        },
    },
];

export const goodsColumns = [
    {
        title: "行号",
        width: 80,
        dataIndex: "id",
        key: "id",
        render: (text: any, _record: any, index: number) => {
            return (
                <Form.Item name={["goodsInfo", index, "id"]} rules={[{ required: true }]}>
                    {index + 1}
                </Form.Item>
            );
        },
    },
    {
        title: "明细行号",
        width: 100,
        dataIndex: "orderLineNo",
        key: "orderLineNo",
    },
    {
        title: "商品编码",
        width: 100,
        dataIndex: "itemCode",
        key: "itemCode",
    },
    {
        title: "商品名称",
        width: 100,
        dataIndex: "itemName",
        key: "itemName",
    },
    {
        title: "货品数量",
        width: 100,
        dataIndex: "qty",
        key: "qty",
    },
    {
        title: "实际发货数量",
        width: 140,
        dataIndex: "actDeliveryQty",
        key: "actDeliveryQty",
        render: (text, record, index) => {
            return (
                <Form.Item name={["goodsInfo", index, "actDeliveryQty"]} rules={[{ required: true }]}>
                    <Input
                        onChange={e => {
                            //@ts-ignore
                            // dataSource[index].actDeliveryQty = e.target.value;
                        }}
                    />
                </Form.Item>
            );
        },
    },
    {
        title: "实际签收数量",
        dataIndex: "actSignQty",
        key: "actSignQty",
        width: 150,
        render: (text, record, index) => {
            return (
                <Form.Item name={["goodsInfo", index, "actSignQty"]} rules={[{ required: true }]}>
                    <Input
                        onChange={e => {
                            //@ts-ignore
                            // dataSource[index].actSignQty = e.target.value;
                        }}
                    />
                </Form.Item>
            );
        },
    },
    {
        title: "原产国",
        dataIndex: "originRegion",
        key: "originRegion",
        width: 100,
    },
    {
        title: "采购单价",
        width: 100,
        dataIndex: "purchasePrice",
        key: "purchasePrice",
    },
    {
        title: "币种",
        width: 100,
        dataIndex: "currency",
        key: "currency",
    },
    {
        title: "总价",
        width: 100,
        dataIndex: "totalPrice",
        key: "totalPrice",
    },
    {
        title: "合计体积 (立方米）",
        dataIndex: "volume",
        width: 100,
        key: "volume",
    },
    {
        title: "合计重量 (kg）",
        width: 100,
        dataIndex: "weight",
        key: "weight",
    },
    {
        title: "生产批号",
        width: 100,
        dataIndex: "produceCode",
        key: "produceCode",
    },
    {
        title: "生产日期",
        width: 150,
        dataIndex: "productDate",
        key: "productDate",
    },
    {
        title: "失效日期",
        width: 150,
        dataIndex: "expiryDate",
        key: "expiryDate",
    },
];

export default configsFn;
