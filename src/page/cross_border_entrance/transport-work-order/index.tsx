import { SearchList } from "@dt/components";
import React, { useRef } from "react";
//@ts-ignore
import { getConfigDataUtils, lib, hooks } from "react-single-app";
import { Button, Modal, message, Tabs } from "antd";
import axios from "axios";
import { workOrderStatus } from "./type";
const { useGetAuthButtons } = hooks;
export default () => {
    const SearchListRef = useRef();
    const [buttons] = useGetAuthButtons({ systemCode: "CCS_ADMIN", pagePath: "/transport-work-order" });
    const getConfig = async () => {
        let url = getConfigDataUtils.getDataUrlByDataId(838);
        const res = await axios.get(url);
        return res.data.data;
    };

    const finishTransport = row => {
        Modal.confirm({
            title: "确认",
            content: "确认回告运输完成吗？",
            onOk: () => {
                lib.request({
                    url: "/ccs/transwork/finishTransWork",
                    data: {
                        id: row.id,
                    },
                    success(data) {
                        message.success("操作成功");
                        //@ts-ignore
                        SearchListRef.current.load();
                    },
                });
            },
        });
    };
    return (
        <SearchList
            ref={SearchListRef}
            searchConditionConfig={{
                size: "middle",
            }}
            headerMode={"sticky"}
            getConfig={getConfig}
            renderOperationTopView={() => (
                <>
                    <Tabs
                        items={[
                            {
                                label: "全部",
                                key: null,
                            },
                            {
                                label: "已创建",
                                //@ts-ignore
                                key: 100,
                            },
                            {
                                label: "已完成",
                                //@ts-ignore
                                key: 200,
                            },
                            {
                                label: "已取消",
                                //@ts-ignore
                                key: 300,
                            },
                        ]}
                        onChange={e => {
                            //@ts-ignore
                            SearchListRef.current?.changeImmutable({ status: e });
                        }}
                    />
                </>
            )}
            tableCustomFun={{
                operateFn: (row, index) => {
                    return (
                        <>
                            {row.status === workOrderStatus.CREATED && buttons.includes("order-finish-transport") && (
                                <Button type="link" onClick={() => finishTransport(row)}>
                                    完成运输
                                </Button>
                            )}
                        </>
                    );
                },
                orderNoFn: (row, index) => {
                    return (
                        <>
                            <a
                                onClick={() => {
                                    lib.openPage("/transport-work-order-detail?page_title=运输作业单详情&id=" + row.id);
                                }}>
                                {row.transNo}
                            </a>
                        </>
                    );
                },
            }}
        />
    );
};
