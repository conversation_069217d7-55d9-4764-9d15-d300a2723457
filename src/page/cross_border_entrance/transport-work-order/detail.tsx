import React, { useEffect, useRef, useState } from "react";
import { DTEditForm, DTEditFormRefs, DTEditFormConfigs } from "@dt/components";
import configsFn, { filesColumns, goodsColumns, packageColumns, packagingColumns } from "./configs";
import { message, Table, Modal } from "antd";
import moment from "moment";
import GoodsTable from "./components/goods-table";
import { DDYObject, lib } from "react-single-app";
import AddModal from "./components/add-modal";
export default () => {
    const ref = useRef<DTEditFormRefs>();
    const [detail, setDetail] = useState<DDYObject>();
    const [addOpen, setAddOpen] = useState(false);
    const configs = configsFn({
        ref: ref,
        changeMode: ({ name, configItem }) => {
            if (name === "finish") {
                // todo
                Modal.confirm({
                    title: "确认",
                    content: "确认回告运输完成吗？",
                    onOk: () => {
                        lib.request({
                            url: "/ccs/transwork/finishTransWork",
                            data: {
                                id: lib.getParam("id"),
                            },
                            success(data) {
                                message.success("操作成功");
                                getDetail();
                            },
                        });
                    },
                });
            }

            if (name === "serverInfo") {
                /// 车牌号可编辑时
                if (configItem.configs[0].editable) {
                    ref.current.form.validateFields(["vehiclePlate", "vehicleResourceCode"]).then(res => {
                        lib.request({
                            url: "/ccs/transwork/addTruckNo",
                            data: {
                                id: lib.getParam("id"),
                                vehiclePlate: res.vehiclePlate,
                                truckResourceCode: res.vehicleResourceCode,
                            },
                            success(data) {
                                message.info("保存成功");
                                configItem.configs.map(item => {
                                    const bol = ["actDepTime", "actArrTime", "actSignTime"].includes(
                                        item?.fProps.name as string,
                                    );
                                    item.editable = bol;
                                    if (item?.fProps.name === "vehiclePlate") {
                                        item.editable = false;
                                    }
                                });
                                configItem.configs = [...configItem.configs];
                                configItem.moduleMode = "read";
                                ref.current.setConfigModuleItem("serverInfo", configItem);
                                getDetail();
                            },
                        });
                    });
                } else {
                    ref.current.form.validateFields(["actArrTime", "actDepTime", "actSignTime"]).then(res => {
                        lib.request({
                            url: "/ccs/transwork/editTimeInfo",
                            data: {
                                id: lib.getParam("id"),
                                actArrTime: res.actArrTime && res.actArrTime.valueOf(),
                                actDepTime: res.actDepTime && res.actDepTime.valueOf(),
                                actSignTime: res.actSignTime && res.actSignTime.valueOf(),
                            },
                            success(data) {
                                message.info("保存成功");
                                configItem.moduleMode = "read";
                                ref.current.setConfigModuleItem(name, configItem);
                                getDetail();
                            },
                        });
                    });
                }
            }

            // 商品明细保存
            if (name === "goodsInfo") {
                ref.current.form.validateFields(["goodsInfo"]).then(res => {
                    lib.request({
                        url: "/ccs/transwork/item/edit",
                        data: {
                            items: res.goodsInfo,
                        },
                        success(data) {
                            message.success("保存成功");
                            getDetail();
                        },
                    });
                });
            }

            //
            if (name === "packagingInfo") {
                ref.current.form.validateFields(["packagingInfo"]).then(res => {
                    lib.request({
                        url: "/ccs/transwork/packagingItemEdit",
                        data: {
                            items: res.packagingInfo,
                        },
                        success(data) {
                            message.success("保存成功");
                            getDetail();
                        },
                    });
                });
            }

            //addServer
            if (name === "addServer") {
                setAddOpen(true);
            }
        },
    });

    const getDetail = () => {
        lib.request({
            url: "/ccs/transwork/detail",
            data: {
                id: lib.getParam("id"),
            },
            success(data) {
                setDetail(data);
            },
        });
    };

    const fn = data => {
        ref.current.form.setFieldsValue(data);
    };
    useEffect(() => {
        getDetail();
    }, []);
    return (
        <>
            <DTEditForm
                totalMode="read"
                configs={configs}
                ref={ref}
                detail={detail}
                layout={{
                    mode: "appoint",
                    colNum: 3,
                }}
                beforeMergeForm={detail => {
                    return {
                        ...detail,
                        actArrTime: detail.actArrTime && moment(detail.actArrTime),
                        actDepTime: detail.actDepTime && moment(detail.actDepTime),
                        actSignTime: detail.actSignTime && moment(detail.actSignTime),
                    };
                }}>
                {({ moduleType, colProps, layout, formItemColProps, detail }) => (
                    <>
                        {moduleType === "goodsInfo" && (
                            <GoodsTable
                                setValues={data => {
                                    fn({ goodsInfo: data });
                                }}
                                columns={goodsColumns}
                                pageUrl="/ccs/transwork/item/paging"
                            />
                        )}
                        {moduleType === "packageItems" && (
                            <Table
                                columns={packageColumns}
                                pagination={false}
                                dataSource={detail?.packageInfo?.packageInfoItems || []}
                                scroll={{ x: "100%", y: "400px" }}
                            />
                        )}
                        {moduleType === "packagingItems" && (
                            <GoodsTable
                                setValues={data => {
                                    fn({ packagingInfo: data });
                                }}
                                columns={packagingColumns}
                                pageUrl="/ccs/transwork/getPackagingItems"
                            />
                        )}
                        {moduleType === "filesInfo" && (
                            <Table
                                columns={filesColumns}
                                pagination={false}
                                dataSource={detail?.attachments || []}
                                scroll={{ x: "400px", y: "400px" }}
                            />
                        )}
                    </>
                )}
            </DTEditForm>
            <AddModal
                open={addOpen}
                params={{
                    vehiclePurpose: detail && detail.orderType,
                }}
                onClose={data => {
                    if (data) {
                        lib.request({
                            url: "/ccs/transwork/addTruckInfo",
                            data: {
                                vehicleId: data.id,
                                id: lib.getParam("id"),
                            },
                            success(res) {
                                message.success("添加成功");
                                ref.current.form.setFieldsValue({ ...data });
                                getDetail();
                            },
                        });
                    }
                    setAddOpen(false);
                }}
            />
        </>
    );
};
