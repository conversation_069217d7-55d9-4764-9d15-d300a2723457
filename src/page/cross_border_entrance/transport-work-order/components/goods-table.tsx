import React, { useEffect, useState } from "react";
import { Table, message, Form, Input } from "antd";
import { lib } from "react-single-app";
import { goodsColumns } from "../configs";
export default (props: { setValues: any; pageUrl: string; columns: any[] }) => {
    const [pagination, setPaginationn] = useState({
        current: 1,
        pageSize: 10,
        total: 0,
    });
    const [dataSource, setDataSource] = useState([]);

    function getTableData(current: number, pageSize: number) {
        lib.request({
            // url: "/ccs/transwrok/item/paging",
            url: props.pageUrl,
            needMask: false,
            data: {
                id: lib.getParam("id"),
                currentPage: current,
                pageSize: pageSize,
            },
            success: data => {
                if (data.dataList) {
                    setDataSource(data.dataList);
                    props.setValues(data.dataList);
                } else {
                    setDataSource(data);
                    props.setValues(data);
                }

                data.page &&
                    setPaginationn({
                        current: data.page.currentPage,
                        pageSize: data.page.pageSize,
                        total: data.page.totalCount,
                    });
            },
            fail: (code: number, msg: string) => {
                message.error(msg);
            },
        });
    }

    useEffect(() => {
        getTableData(0, 50);
    }, []);

    return (
        <>
            <Table
                columns={props.columns}
                pagination={{
                    current: pagination.current,
                    pageSize: pagination.pageSize,
                    total: pagination.total,
                    onChange(page, pageSize) {
                        getTableData(page, pageSize);
                    },
                }}
                //@ts-ignore
                dataSource={dataSource}
                scroll={{ x: "1000px", y: "400px" }}
            />
        </>
    );
};
