import { Modal, Form, Select, Input, Space, Button, Table, Row, Col, message } from "antd";
import React, { useEffect, useState } from "react";
import { lib } from "react-single-app";
export default ({ open, onClose, params }) => {
    const [form] = Form.useForm();
    const [carNos, setCarNos] = useState([]);
    const [dataSource, setDataSource] = useState([]);
    const [pagination, setPagination] = useState({
        pageSize: 10,
        currentPage: 1,
        totalCount: 1,
    });
    const [selected, setSelected] = useState([]);

    const onSearch = (page, pageSize) => {
        const values = form.getFieldsValue();
        lib.request({
            url: "/ccs/vehicleResource/pagingEnable",
            data: {
                ...values,
                currentPage: page,
                pageSize,
                ...params,
            },
            success(data) {
                setDataSource(data.dataList);
                setPagination(data.page);
            },
        });
    };

    useEffect(() => {
        // /vehicleResource/listVehiclePlate
        if (open) {
            onSearch(1, 10);
            lib.request({
                url: "/ccs/vehicleResource/listVehiclePlate",
                // data:{}
                success(data) {
                    setCarNos(data);
                },
            });
        }
    }, [open]);

    const columns = [
        {
            title: "车牌号",
            dataIndex: "vehiclePlate",
            key: "vehiclePlate",
        },
        {
            title: "车辆资源code",
            dataIndex: "vehicleResourceCode",
            key: "vehicleResourceCode",
            render: value => {
                return <span style={{ whiteSpace: "wrap", display: "inline-block", maxWidth: "100px" }}>{value}</span>;
            },
        },
        {
            title: "车型名称",
            dataIndex: "vehicleTypeName",
            key: "vehicleTypeName",
        },
        {
            title: "车辆类型",
            dataIndex: "vehicleTypeDesc",
            key: "vehicleTypeDesc",
        },
    ];
    const onSelectChange = (newSelectedRowKeys: React.Key[]) => {
        setSelected(newSelectedRowKeys);
    };

    return (
        <>
            <Modal
                title={"添加"}
                open={open}
                width={900}
                onCancel={() => {
                    setSelected([]);
                    onClose();
                }}
                onOk={() => {
                    const data = selected.filter(item => !!item);
                    if (data.length !== 1) {
                        message.error("仅允许添加1条数据");
                        return;
                    }
                    const result = dataSource.filter(item => item.id === selected[0]);
                    onClose(result[0]);
                }}>
                <>
                    <Form form={form}>
                        <Row gutter={24}>
                            <Col span={8}>
                                <Form.Item label="车牌号" name="vehiclePlate">
                                    <Select
                                        showSearch
                                        filterOption={(input, option) => {
                                            return (
                                                option.props.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
                                            );
                                        }}>
                                        {carNos.map(item => {
                                            return (
                                                <Select.Option value={item.id} key={item.id}>
                                                    {item.name}
                                                </Select.Option>
                                            );
                                        })}
                                    </Select>
                                </Form.Item>
                            </Col>
                            <Col span={8}>
                                <Form.Item label="车辆资源code" name="vehicleResourceCode">
                                    <Input />
                                </Form.Item>
                            </Col>
                            <Col span={8}>
                                <Form.Item label="车型名称" name="vehicleTypeName">
                                    <Input />
                                </Form.Item>
                            </Col>
                            <Col span={8}>
                                <Form.Item>
                                    <Space>
                                        <Button
                                            onClick={() => {
                                                onSearch(1, 10);
                                            }}>
                                            查询
                                        </Button>
                                        <Button
                                            onClick={() => {
                                                form.resetFields();
                                                onSearch(1, 10);
                                            }}>
                                            重置
                                        </Button>
                                    </Space>
                                </Form.Item>
                            </Col>
                        </Row>
                    </Form>
                    <Table
                        columns={columns}
                        dataSource={dataSource}
                        rowKey={"id"}
                        pagination={{
                            ...pagination,
                            total: pagination.totalCount,
                            onChange(page, pageSize) {
                                onSearch(page, pageSize);
                            },
                        }}
                        rowSelection={{
                            onChange: onSelectChange,
                            selectedRowKeys: selected,
                            // selections: selected
                            type: "radio",
                        }}
                    />
                </>
            </Modal>
        </>
    );
};
