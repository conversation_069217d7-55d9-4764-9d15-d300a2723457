import React from "react";
import { Space, Form, Tag } from "antd";

export function detailCollaborateOrderConfig(prop) {
    return {
        baseInfo: {
            children: [
                {
                    label: "货主",
                    editEnable: true,
                    name: "ownerName",
                    type: "text",
                },
                {
                    label: "实体仓名称",
                    editEnable: false,
                    name: "entityWarehouseName",
                    type: "text",
                },
                {
                    label: "转入实体仓",
                    editEnable: false,
                    name: "entityInWarehouseName",
                    type: "textInput",
                },
                {
                    label: "转出实体仓",
                    editEnable: false,
                    name: "entityOutWarehouseName",
                    type: "textInput",
                },
                {
                    label: "申报企业",
                    editEnable: true,
                    type: "text",
                    name: "customsEntryCompanyName",
                },
                {
                    label: "预计到货数量",
                    editEnable: false,
                    name: "estimatedQty",
                    type: "text",
                },
                {
                    label: "预计到货毛重",
                    editEnable: false,
                    name: "estimatedWeight",
                    type: "text",
                },
                {
                    label: "预计到货净重",
                    editEnable: true,
                    name: "estimatedNewWeight",
                    type: "text",
                },
            ],
            label: "基本信息",
            name: "baseInfo",
            isGroup: true,
            className: "baseInfo",
        },
        customsClearanceInfo: {
            children: [
                {
                    label: "清关单号",
                    editEnable: true,
                    type: "text",
                    name: "inveCustomsSn",
                },
                {
                    label: "关联清关单号",
                    editEnable: true,
                    type: "text",
                    name: "associatedInveCustomsSn",
                },
                {
                    label: "业务类型",
                    editEnable: true,
                    type: "text",
                    name: "inveBusinessTypeDesc",
                },
                {
                    label: "区内账册编号",
                    name: "areaBookNo",
                    editEnable: true,
                    type: "text",
                },
                {
                    label: "进出标志",
                    name: "inOrOutFlag",
                    editEnable: true,
                    type: "text",
                },
                {
                    label: "运输方式",
                    editEnable: false,
                    name: "transportModeName",
                    type: "text",
                },
                {
                    label: "核注清单编号",
                    editEnable: true,
                    name: "refHzInveNo",
                    type: "text",
                },
                {
                    label: "启运国",
                    editEnable: true,
                    name: "shipmentCountryName",
                    type: "text",
                },
                {
                    label: "报关单号",
                    editEnable: false,
                    name: "customsEntryNo",
                    type: "text",
                },
                {
                    label: "清关完成时间",
                    editEnable: false,
                    name: "customsFinishTime",
                    type: "text",
                },
                {
                    label: "提单号",
                    editEnable: false,
                    name: "pickUpNo",
                    type: "textInput",
                },
                {
                    label: "货代公司",
                    editEnable: false,
                    name: "forwardingCompany",
                    type: "textInput",
                },
                {
                    label: "集装箱号",
                    editEnable: false,
                    name: "conNo",
                    type: "textInput",
                },
                {
                    label: "预计到港日期",
                    editEnable: false,
                    name: "expectedToPortTime",
                    type: "dateInput",
                },
                {
                    label: "实际到港日期",
                    name: "actualArrivalDate",
                    editEnable: false,
                    type: "dateInput",
                },
                {
                    label: "到货港口/机场",
                    name: "arrivalPort",
                    editEnable: false,
                    type: "textInput",
                },
                {
                    label: "品名",
                    name: "productName",
                    editEnable: false,
                    type: "textInput",
                },
                {
                    label: "类目",
                    name: "category",
                    editEnable: false,
                    type: "textInput",
                    wrapperCol: { span: 14 },
                },
                {
                    label: "备注",
                    name: "textInput",
                    editEnable: false,
                    type: "text",
                },
            ],
            label: "清关信息",
            name: "baseInfo",
            isGroup: true,
            className: "declareInfo",
        },
        warehouseInfo: {
            children: [
                {
                    label: "入库/出库单号",
                    editEnable: false,
                    type: "text",
                    name: "inOutOrderNo",
                },
                {
                    label: "运输费",
                    editEnable: false,
                    type: "textInput",
                    name: "shippingFee",
                },
                {
                    label: "理货报告号",
                    editEnable: false,
                    type: "text",
                    name: "tallyOrderNo",
                },
                {
                    label: "实际理货数量",
                    editEnable: false,
                    type: "text",
                    name: "actualTallyQty",
                },
                {
                    label: "仓库托数",
                    editEnable: false,
                    type: "textInput",
                    name: "fightQty",
                },
                {
                    label: "理货完成时间",
                    editEnable: false,
                    type: "text",
                    name: "tallyFinishTime",
                },
                {
                    label: "实际到库时间",
                    editEnable: false,
                    type: "text",
                    name: "warehouseTime",
                },
            ],
            label: "仓库信息",
            name: "extraInfo",
            isGroup: true,
            className: "baseInfo",
        },
        skuTableInfo: {
            type: "tableInput",
            name: ["detailsVOS"],
            columns: [
                {
                    title: "货品名称",
                    name: "goodsName",
                    width: 150,
                    type: "text",
                    // }, {
                    //     title: "货品ID",
                    //     name: 'goodsId',
                    //     width: 150,
                    //     type: "text",
                },
                {
                    title: "SKU",
                    name: "sku",
                    width: 150,
                    type: "text",
                },
                {
                    title: "统一料号",
                    name: "collaborateLabel",
                    width: 245,
                    type: "renderContainer",
                    render: (item, disabled, fieldValue, name) => {
                        const TagText = ({ value }) => {
                            return (
                                <Space>
                                    <span>{value?.productId}</span>
                                    {value?.tag === "add" && <Tag color={"blue"}>新增</Tag>}
                                </Space>
                            );
                        };
                        return (
                            <Form.Item name={name} noStyle>
                                <TagText />
                            </Form.Item>
                        );
                    },
                },
                {
                    title: "海关备案料号",
                    name: "customsRecordProductId",
                    width: 150,
                    type: "text",
                },
                {
                    title: "条形码",
                    name: "barCode",
                    width: 150,
                    type: "text",
                },
                {
                    title: "最终申报数量",
                    name: "declareQty",
                    width: 180,
                    type: "text",
                },
                {
                    title: "理货数量",
                    name: "tallyQty",
                    width: 180,
                    type: "text",
                    // }, {
                    //     title: "申报料号",
                    //     name: 'collaborateLabel',
                    //     width: 245,
                    //     type: "renderContainer",
                    //     render: (item, disabled, fieldValue, name) => {
                    //         const TagText = ({value}) => {
                    //             return <Space><span>{value?.productId}</span>
                    //                 {value?.tag === "delete" &&
                    //                 <Tag color={'red'}>已删</Tag>
                    //                 }
                    //                 {value?.tag === "add" &&
                    //                 <Tag color={'blue'}>新增</Tag>
                    //                 }
                    //             </Space>
                    //         }
                    //         return (
                    //             <Form.Item name={name} noStyle>
                    //                 <TagText/>
                    //             </Form.Item>

                    //         )

                    //     }
                },
                {
                    title: "备案序号",
                    name: "goodsSeqNo",
                    width: 180,
                    type: "text",
                },
                {
                    title: "净重",
                    name: "netWeight",
                    width: 180,
                    type: "text",
                },
                {
                    title: "毛重",
                    name: "grossWeight",
                    width: 180,
                    type: "text",
                },
                {
                    title: "差异类型",
                    name: "diffTypeDesc",
                    width: 180,
                    type: "text",
                },
                {
                    title: "差异数量",
                    name: "diffQty",
                    width: 180,
                    type: "text",
                },
            ],
            label: "货品信息",
            isGroup: false,
            className: "customsWarehouseInfo",
        },
    };
}
