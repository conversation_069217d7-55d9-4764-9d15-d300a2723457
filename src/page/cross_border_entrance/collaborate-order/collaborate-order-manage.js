import React from "react";
import { lib, getConfigDataUtils, SearchList, event } from "react-single-app";
import { message } from "antd";
import axios from "axios";
import { StatusTabs } from "./status-tab";

export default class CollaborateOrderManage extends SearchList {
    constructor(props) {
        super(props);
        this.statusTabRef = React.createRef();
        this.onSearchReset = this.onSearchReset.bind(this);
    }

    getConfig() {
        let url = getConfigDataUtils.getDataUrlByDataId(488);
        return axios.get(url).then(res => res.data.data);
    }

    componentDidMount() {
        event.on("onSearchReset", this.onSearchReset);
    }

    componentWillUnmount() {
        event.off("onSearchReset", this.onSearchReset);
    }

    onSearchReset() {
        this.statusTabRef.current.setActiveKey(1);
        this.changeImmutable({ searchType: 1 });
    }

    onSearch(search) {
        if (!search.searchType) {
            search.searchType = 1;
        }
        this.statusTabRef.current.getDetail(search);
    }

    renderOperationTopView() {
        return (
            <StatusTabs
                ref={this.statusTabRef}
                tabOnChange={e => {
                    this.changeImmutable({ searchType: Number(e) });
                }}
            />
        );
    }

    collaborateSnClick(row) {
        if (!row.inveBusinessType) {
            message.warn(`协同单查看失败：关联清关单${row.inveCustomsSn}未确认`);
        } else {
            lib.openPage(
                `/collaborate-order-detail?page_title=协同单详情&collaborateOrderId=${row.id}&inveCustomsSn=${row.inveCustomsSn}`,
            );
        }
    }

    collaborateSnFunc(row) {
        return (
            <a
                className={"link"}
                onClick={() => {
                    this.collaborateSnClick(row);
                }}>
                {row.collaborateSn}
            </a>
        );
    }
}
