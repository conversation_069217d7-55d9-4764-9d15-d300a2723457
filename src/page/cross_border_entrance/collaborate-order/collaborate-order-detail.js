import React, { useEffect, useState, useRef } from "react";
import { lib, ConfigForm<PERSON>enter } from "react-single-app";
import { detailCollaborateOrderConfig } from "./view-config";
import { Row, Button, message, Steps } from "antd";
//   BUSSINESS_EMPTY("EMPTY", "空", InventoryInOutEnum.NULL),
//     BUSSINESS_REFUND_INAREA("REFUND_INAREA", "退货入区", InventoryInOutEnum.IN),
//     BUSSINESS_SECTION_OUT("SECTION_OUT", "区间流转(出)", InventoryInOutEnum.OUT),
//     BUSSINESS_SECTION_IN("SECTION_IN", "区间流转(入)", InventoryInOutEnum.IN),
//     BUSSINESS_SECTIONINNER_OUT("SECTIONINNER_OUT", "区内流转(出)", InventoryInOutEnum.OUT),
//     BUSSINESS_SECTIONINNER_IN("SECTIONINNER_IN", "区内流转(入)", InventoryInOutEnum.IN),
//     BUSSINESS_ONELINE_IN("ONELINE_IN", "一线入境", InventoryInOutEnum.IN),
//     BUSSINESS_DESTORY("DESTORY", "销毁", InventoryInOutEnum.OUT);
const { Step } = Steps;
export default function CollaborateOrderDetail() {
    const ref = useRef();
    const [disableEdit, setDisableEdit] = useState(true);
    const [detailData, setDetailData] = useState();
    const collaborateOrderId = lib.getParam("collaborateOrderId"),
        inveCustomsSn = lib.getParam("inveCustomsSn");
    useEffect(() => {
        getDetail();
    }, []);

    const configData = detailCollaborateOrderConfig({});

    function getDetail() {
        ref.current.getDetail({
            url: "/ccs/collaborateDetails/getByCollaborateId",
            data: { collaborateOrderId: collaborateOrderId, inveCustomsSn: inveCustomsSn },
        });
    }

    const beforeSubmit = data => {
        data.id = detailData?.inveOrderId;
        if (data.expectedToPortTime) {
            data.expectedToPortTime = data.expectedToPortTime.valueOf();
        }
        if (data.actualArrivalDate) {
            data.actualArrivalDate = data.actualArrivalDate.valueOf();
        }
        delete data.detailsVOS;
        return data;
    };
    const onConfigLoadSuccess = config => {};
    function setItem(data, item) {
        let show = false;
        //区间
        let isWareHoseBetween =
            data?.inveBusinessType === "SECTION_OUT" ||
            data?.inveBusinessType === "SECTION_IN" ||
            data?.inveBusinessType === "SECTIONINNER_OUT" ||
            data?.inveBusinessType === "SECTIONINNER_IN";
        switch (item.name) {
            //区内区间
            case "entityOutWarehouseName": //转出实体仓
                show = isWareHoseBetween;
                item.disabled = !(
                    data?.inveBusinessType === "SECTION_IN" || data?.inveBusinessType === "SECTIONINNER_IN"
                );
                item.hidden = !show;
                break;
            case "entityInWarehouseName": //转入实体仓
                item.disabled = !(
                    data?.inveBusinessType === "SECTIONINNER_OUT" || data?.inveBusinessType === "SECTION_OUT"
                );
                show = isWareHoseBetween;
                item.hidden = !show;
                break;
            case "shippingFee": //运输费
                show = isWareHoseBetween;
                item.hidden = !show;
                break;
            case "associatedInveCustomsSn": //关联清关单
                item.hidden = !data?.associatedInveCustomsSn;
                break;
            //一线入境
            case "entityWarehouseName": //实体仓名称
            case "customsEntryCompany": //预计到货数量
            case "estimatedNewWeight": // 预计到货毛重
            case "estimatedWeight": // 预计到货净重：
            case "shipmentCountryName": //启运国
            case "customsEntryNo": //报关单号
            case "pickUpNo": //提单号
            case "conNo": //集装箱号
            case "expectedToPortTime": // 预计到港日期：
            case "actualArrivalDate": //实际到港日期：
            case "arrivalPort": //到货港口/机场
            case "warehouseTime": //实际到库时间
                show = data?.inveBusinessType === "ONELINE_IN";
                item.hidden = !show;
                break;
            default:
                break;
        }
    }
    const beforeSetDetail = data => {
        if (!data.expectedToPortTime) {
            delete data.expectedToPortTime;
        }
        if (!data.actualArrivalDate) {
            delete data.actualArrivalDate;
        }
        ref.current.config.baseInfo.children.map(item => {
            setItem(data, item);
        });
        ref.current.config.customsClearanceInfo.children.map(item => {
            setItem(data, item);
        });
        ref.current.config.warehouseInfo.children.map(item => {
            setItem(data, item);
        });

        setDetailData(data);
        return data;
    };
    const onSubmitSuccess = () => {
        getDetail();
        setDisableEdit(true);
        message.success("保存成功");
    };

    const editOrSave = () => {
        if (disableEdit) {
            setDisableEdit(false);
        } else {
            ref.current.submitForm();
        }
    };
    return (
        <div className="goods-record-check-detail">
            {detailData && detailData.stateRes.flowStateList && (
                <Steps
                    current={detailData.stateRes.currNode}
                    status={detailData.stateRes.flowStateList[detailData.stateRes.currNode - 1]?.status}
                    labelPlacement={"vertical"}
                    style={{ margin: "15px 0" }}>
                    {detailData.stateRes.flowStateList.map((item, index) => {
                        return <Step title={item.title} key={index} status={item.status} />;
                    })}
                </Steps>
            )}
            {/*<Row justify={'end'} style={{marginRight: '40px'}}>*/}
            {/*    <Button onClick={() => {*/}
            {/*        editOrSave()*/}
            {/*    }}>{disableEdit ? "编辑" : "保存"}</Button>*/}
            {/*</Row>*/}
            <ConfigFormCenter
                ref={ref}
                disableEdit={disableEdit}
                submitUrl={"/ccs/invenorder/editInventoryHead"}
                beforeSubmit={beforeSubmit}
                onSubmitSuccess={onSubmitSuccess}
                confData={configData}
                onConfigLoadSuccess={onConfigLoadSuccess}
                beforeSetDetail={beforeSetDetail}
            />
        </div>
    );
}
