import React, { useState, useEffect, useImperativeHandle } from "react";
import { Tabs } from "antd";
import { lib } from "react-single-app";

export const StatusTabs = React.forwardRef(({ tabOnChange, type }, ref) => {
    const [statusCount, setStatusCount] = useState();
    const [activeKey, setActiveKey] = useState();
    const [allCount, setAllCount] = useState(0);
    const [oneLineInCount, setOneLineInCount] = useState(0);
    const [notOneLineCount, setNotOneLineCount] = useState(0);
    useEffect(() => {}, []);
    function getDetail(search) {
        lib.request({
            url: "/ccs/collaborateOrder/inveBusinessTypeCount",
            data: search,
            success: res => {
                // setStatusCount(res)
                console.log(res);
                setAllCount(res.allCount);
                setOneLineInCount(res.oneLineInCount);
                setNotOneLineCount(res.notOneLineCount);
            },
        });
    }
    useImperativeHandle(ref, () => ({
        getDetail: getDetail,
        setActiveKey: setActiveKey,
    }));
    return (
        <Tabs
            defaultActiveKey={"0"}
            activeKey={activeKey}
            onChange={e => {
                setActiveKey(e);
                tabOnChange(e);
            }}>
            <Tabs.TabPane tab={`全部（${allCount}）`} key={1} />
            <Tabs.TabPane tab={`一线入区（${oneLineInCount}）`} key={2} />
            <Tabs.TabPane tab={`非一线进出（${notOneLineCount}）`} key={3} />
        </Tabs>
    );
});
