import React, { useEffect, useRef, useState } from "react";
import { DTEditForm, DTEditFormRefs, DTEditFormConfigs } from "@dt/components";
import { Table, Button, Space, Modal, message, Tabs, Card, Descriptions } from "antd";
import { DDYObject, lib } from "react-single-app";
import moment from "moment";

const { TabPane } = Tabs;

export default () => {
    const ref = useRef<DTEditFormRefs>();
    const [detail, setDetail] = useState<DDYObject>({});
    const [loading, setLoading] = useState(false);
    const [tableData, setTableData] = useState([]);
    const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);

    // DTEditForm 配置
    const configs: DTEditFormConfigs = [
        {
            type: "INPUT",
            fProps: {
                label: "申报单号",
                name: "declareNo",
                rules: [{ required: true, message: "请输入申报单号" }],
            },
        },
        {
            type: "SELECT",
            fProps: {
                label: "业务类型",
                name: "businessType",
                rules: [{ required: true, message: "请选择业务类型" }],
            },
            list: [
                { label: "一般贸易", value: "GENERAL_TRADE" },
                { label: "保税物流", value: "BONDED_LOGISTICS" },
                { label: "跨境电商", value: "CROSS_BORDER_ECOMMERCE" },
            ],
        },
        {
            type: "INPUT",
            fProps: {
                label: "申报企业",
                name: "declareCompany",
                rules: [{ required: true, message: "请输入申报企业" }],
            },
        },
        {
            type: "DATEPICKER",
            fProps: {
                label: "申报日期",
                name: "declareDate",
                rules: [{ required: true, message: "请选择申报日期" }],
            },
        },
        {
            type: "SELECT",
            fProps: {
                label: "申报状态",
                name: "status",
            },
            list: [
                { label: "草稿", value: "DRAFT" },
                { label: "已提交", value: "SUBMITTED" },
                { label: "审核中", value: "REVIEWING" },
                { label: "已通过", value: "APPROVED" },
                { label: "已拒绝", value: "REJECTED" },
            ],
        },
        {
            type: "TEXTAREA",
            fProps: {
                label: "备注",
                name: "remark",
                rows: 3,
            },
        },
    ];

    // 表格列配置
    const columns = [
        {
            title: "序号",
            dataIndex: "index",
            width: 80,
            render: (_, __, index) => index + 1,
        },
        {
            title: "商品编码",
            dataIndex: "goodsCode",
            width: 150,
        },
        {
            title: "商品名称",
            dataIndex: "goodsName",
            width: 200,
        },
        {
            title: "规格型号",
            dataIndex: "specification",
            width: 150,
        },
        {
            title: "申报数量",
            dataIndex: "declareQty",
            width: 120,
        },
        {
            title: "申报单位",
            dataIndex: "declareUnit",
            width: 100,
        },
        {
            title: "申报单价",
            dataIndex: "declarePrice",
            width: 120,
        },
        {
            title: "申报总价",
            dataIndex: "declareTotalPrice",
            width: 120,
        },
        {
            title: "币制",
            dataIndex: "currency",
            width: 80,
        },
        {
            title: "原产国",
            dataIndex: "originCountry",
            width: 100,
        },
        {
            title: "操作",
            width: 150,
            fixed: "right",
            render: (_, record, index) => (
                <Space>
                    <Button type="link" size="small" onClick={() => handleEdit(record, index)}>
                        编辑
                    </Button>
                    <Button type="link" size="small" danger onClick={() => handleDelete(record, index)}>
                        删除
                    </Button>
                </Space>
            ),
        },
    ];

    // 获取详情数据
    const getDetail = () => {
        setLoading(true);
        lib.request({
            url: "/ccs/declare-outbound/detail",
            data: {
                id: lib.getParam("id"),
            },
            success: (data) => {
                setDetail(data);
                setTableData(data.itemList || []);
            },
            complete: () => {
                setLoading(false);
            },
        });
    };

    // 保存表单数据
    const handleSave = () => {
        ref.current?.form.validateFields().then((values) => {
            const submitData = {
                ...values,
                declareDate: values.declareDate ? moment(values.declareDate).valueOf() : null,
                id: lib.getParam("id"),
                itemList: tableData,
            };

            lib.request({
                url: "/ccs/declare-outbound/save",
                data: submitData,
                success: () => {
                    message.success("保存成功");
                    getDetail();
                },
            });
        });
    };

    // 提交申报
    const handleSubmit = () => {
        Modal.confirm({
            title: "确认提交",
            content: "确定要提交申报吗？提交后将无法修改。",
            onOk: () => {
                lib.request({
                    url: "/ccs/declare-outbound/submit",
                    data: {
                        id: lib.getParam("id"),
                    },
                    success: () => {
                        message.success("提交成功");
                        getDetail();
                    },
                });
            },
        });
    };

    // 编辑表格行
    const handleEdit = (record, index) => {
        // 这里可以打开编辑弹窗或跳转到编辑页面
        console.log("编辑", record, index);
    };

    // 删除表格行
    const handleDelete = (record, index) => {
        Modal.confirm({
            title: "确认删除",
            content: "确定要删除这条记录吗？",
            onOk: () => {
                const newData = [...tableData];
                newData.splice(index, 1);
                setTableData(newData);
                message.success("删除成功");
            },
        });
    };

    // 批量删除
    const handleBatchDelete = () => {
        if (selectedRowKeys.length === 0) {
            message.warning("请选择要删除的记录");
            return;
        }

        Modal.confirm({
            title: "确认删除",
            content: `确定要删除选中的 ${selectedRowKeys.length} 条记录吗？`,
            onOk: () => {
                const newData = tableData.filter((_, index) => !selectedRowKeys.includes(index));
                setTableData(newData);
                setSelectedRowKeys([]);
                message.success("删除成功");
            },
        });
    };

    useEffect(() => {
        getDetail();
    }, []);

    return (
        <div style={{ padding: "24px" }}>
            {/* 上方：DTEditForm 表单区域 */}
            <Card title="申报基本信息" style={{ marginBottom: "24px" }}>
                <DTEditForm
                    ref={ref}
                    configs={configs}
                    detail={detail}
                    layout={{
                        mode: "appoint",
                        colNum: 3,
                    }}
                    beforeMergeForm={(data) => ({
                        ...data,
                        declareDate: data.declareDate ? moment(data.declareDate) : null,
                    })}
                />

                <div style={{ marginTop: "16px", textAlign: "right" }}>
                    <Space>
                        <Button onClick={() => ref.current?.form.resetFields()}>
                            重置
                        </Button>
                        <Button type="primary" onClick={handleSave}>
                            保存
                        </Button>
                        {detail.status === "DRAFT" && (
                            <Button type="primary" onClick={handleSubmit}>
                                提交申报
                            </Button>
                        )}
                    </Space>
                </div>
            </Card>

            {/* 下方：Antd 组件区域 */}
            <Card title="申报商品明细">
                <Tabs defaultActiveKey="1">
                    <TabPane tab="商品列表" key="1">
                        <div style={{ marginBottom: "16px" }}>
                            <Space>
                                <Button type="primary">
                                    新增商品
                                </Button>
                                <Button onClick={handleBatchDelete}>
                                    批量删除
                                </Button>
                                <Button>
                                    导入
                                </Button>
                                <Button>
                                    导出
                                </Button>
                            </Space>
                        </div>

                        <Table
                            loading={loading}
                            dataSource={tableData}
                            columns={columns}
                            rowKey={(record, index) => index}
                            rowSelection={{
                                selectedRowKeys,
                                onChange: setSelectedRowKeys,
                            }}
                            scroll={{ x: 1200, y: 400 }}
                            pagination={{
                                showSizeChanger: true,
                                showQuickJumper: true,
                                showTotal: (total) => `共 ${total} 条记录`,
                            }}
                        />
                    </TabPane>

                    <TabPane tab="申报统计" key="2">
                        <Descriptions bordered column={2}>
                            <Descriptions.Item label="商品总数">
                                {tableData.length} 项
                            </Descriptions.Item>
                            <Descriptions.Item label="申报总价">
                                {tableData.reduce((sum, item) => sum + (item.declareTotalPrice || 0), 0).toFixed(2)}
                            </Descriptions.Item>
                            <Descriptions.Item label="申报总量">
                                {tableData.reduce((sum, item) => sum + (item.declareQty || 0), 0)}
                            </Descriptions.Item>
                            <Descriptions.Item label="创建时间">
                                {detail.createTime ? moment(detail.createTime).format("YYYY-MM-DD HH:mm:ss") : "-"}
                            </Descriptions.Item>
                        </Descriptions>
                    </TabPane>

                    <TabPane tab="操作日志" key="3">
                        <Table
                            dataSource={detail.logList || []}
                            columns={[
                                { title: "操作时间", dataIndex: "operateTime", render: (time) => moment(time).format("YYYY-MM-DD HH:mm:ss") },
                                { title: "操作人", dataIndex: "operator" },
                                { title: "操作类型", dataIndex: "operateType" },
                                { title: "操作说明", dataIndex: "operateDesc" },
                            ]}
                            pagination={false}
                        />
                    </TabPane>
                </Tabs>
            </Card>
        </div>
    );
};