import React, { useEffect, useRef, useState } from "react";
import { DTEditForm, DTEditFormRefs, DTEditFormConfigs, DTEditFormListConfigs } from "@dt/components";
import { Table, Button, Space, Modal, message, Tabs, Card, Descriptions, Form, Input } from "antd";
import { DDYObject, lib } from "react-single-app";
import moment from "moment";

const { TabPane } = Tabs;

export default () => {
    const ref = useRef<DTEditFormRefs>();
    const [detail, setDetail] = useState<DDYObject>({});
    const [loading, setLoading] = useState(false);
    const [tableData, setTableData] = useState([]);
    const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
    const [endorsementList, setEndorsementList] = useState([]);
    const [endorsementLoading, setEndorsementLoading] = useState(false);

    // DTEditForm 配置
    const configs: DTEditFormListConfigs = [
        {
            type: "INPUT",
            fProps: {
                label: "申报出库单",
                name: "sn",
                rules: [{ required: true, message: "请输入申报单号" }],
            },
        },
        {
            type: "INPUT",
            fProps: {
                label: "申报出库单状态",
                name: "statusDesc",
                rules: [{ required: true, message: "请输入申报出库单状态" }],
            },
        },
        {
            type: "INPUT",
            fProps: {
                label: "实体仓名称",
                name: "entityWarehouseName",
                rules: [{ required: true, message: "请选择实体仓名称" }],
            },
        },
        {
            type: "INPUT",
            fProps: {
                label: "快递公司",
                name: "expressNames",
            },
        },
        {
            type: "INPUT",
            fProps: {
                label: "创建时间",
                name: "createTimeStr",
            },
        },
        {
            type: "INPUT",
            fProps: {
                label: "操作人",
                name: "createByName",
            },
        },
        {
            type: "INPUT",
            fProps: {
                label: "总毛重",
                name: "totalGrossWeight",
            },
        },
        {
            type: "INPUT",
            fProps: {
                label: "总净重",
                name: "totalNetWeight",
            },
        },
    ];

    // 表格列配置
    const columns = [
        {
            title: "申报单号",
            dataIndex: "declareOrderNo",
            width: 150,
        },
        {
            title: "运单号",
            dataIndex: "mailNo",
            width: 200,
        },
        {
            title: "快递公司",
            dataIndex: "expressName",
            width: 150,
        },
        {
            title: "清单编号",
            dataIndex: "inventoryNo",
            width: 120,
        },
        {
            title: "账册编码",
            dataIndex: "accountBookNo",
            width: 100,
        },
        {
            title: "操作",
            width: 100,
            fixed: "right",
        },
    ];

    // 核注单列表列配置
    const endorsementColumns = [
        {
            title: "核注企业单号",
            dataIndex: "sn",
            width: 180,
        },
        {
            title: "预录入核注编号",
            dataIndex: "preOrderNo",
            width: 180,
        },
        {
            title: "核注清单编号",
            dataIndex: "realEndorsementOrderNo",
            width: 180,
        },
        {
            title: "核注单状态",
            dataIndex: "statusDesc",
            width: 120,
        },
        {
            title: "账册编码",
            dataIndex: "customsBookNo",
            width: 120,
        },
        {
            title: "创建时间",
            dataIndex: "createTime",
            width: 160,
        },
        {
            title: "企业核放单号",
            dataIndex: "checklistSn",
            width: 150,
        },
        {
            title: "操作",
            width: 100,
            fixed: "right",
            render: (_, record) => (
                <Space>
                    <Button type="link" size="small" onClick={() => handleViewEndorsement(record)}>
                        查看
                    </Button>
                </Space>
            ),
        },
    ];

    // 获取详情数据
    const getDetail = () => {
        setLoading(true);
        lib.request({
            url: "/ccs/exportOrder/detail",
            data: {
                id: lib.getParam("id"),
            },
            success: data => {
                setDetail(data);
                // setTableData(data.itemList || []);
            },
            fail: () => {
                setLoading(false);
            },
        });
    };

    // 获取核注单列表
    const getEndorsementList = () => {
        setEndorsementLoading(true);
        lib.request({
            url: "/ccs/exportOrder/listEndorsementById",
            data: {
                id: lib.getParam("id"),
            },
            success: data => {
                setEndorsementList(data || []);
                setEndorsementLoading(false);
            },
            fail: () => {
                setEndorsementLoading(false);
            },
        });
    };

    // 查看核注单详情
    const handleViewEndorsement = record => {
        // lib.openPage(`/endorsement-detail?id=${record.id}&page_title=核注单详情`);
        lib.openPage(`/ccs/nuclear-note-detail?page_title=核注单详情&id=${record.id}`);
    };

    const search = (page = 1, pageSize = 10) => {
        form.validateFields().then(values => {
            lib.request({
                url: "/ccs/exportOrder/pagingItems",
                data: {
                    currentPage: page,
                    pageSize,
                    ...values,
                    exportOrderId: +lib.getParam("id"),
                },
                success(data) {
                    setTableData(data.dataList);
                    setLoading(false);
                },
                fail(code, msg, data) {
                    setLoading(false);
                },
            });
        });
    };

    const exportData = () => {
        form.validateFields().then(values => {
            lib.request({
                url: "/ccs/exportOrder/exportItems",
                data: {
                    accountBookNo: values.accountBookNo,
                    exportOrderId: +lib.getParam("id"),
                },
                success(data) {
                    lib.openPage("/excel/download-center?page_title=下载中心");
                },
            });
        });
    };

    const [form] = Form.useForm();

    useEffect(() => {
        getDetail();
        getEndorsementList();
        search();
    }, []);

    return (
        <div style={{ padding: "24px" }}>
            {/* 上方：DTEditForm 表单区域 */}
            <Card title="基本信息" style={{ marginBottom: "24px" }}>
                <DTEditForm
                    ref={ref}
                    configs={configs}
                    detail={detail}
                    layout={{
                        mode: "appoint",
                        colNum: 3,
                    }}
                    totalMode="read"
                    beforeMergeForm={data => ({
                        ...data,
                        declareDate: data.declareDate ? moment(data.declareDate) : null,
                    })}
                />
            </Card>

            {/* 下方：Antd 组件区域 */}
            <Tabs defaultActiveKey="1">
                <TabPane tab="关联包裹" key="1">
                    <div style={{ marginBottom: "16px" }}>
                        <Space>
                            <Form form={form}>
                                <Form.Item label={"账册编号"} name={"accountBookNo"}>
                                    <Input />
                                </Form.Item>
                            </Form>
                            <Button
                                onClick={() => {
                                    search();
                                }}
                                style={{ marginBottom: "16px" }}>
                                查询
                            </Button>
                            <Button
                                style={{ marginBottom: "16px" }}
                                onClick={() => {
                                    form.resetFields();
                                    search();
                                }}>
                                重置
                            </Button>
                            <Button onClick={exportData} style={{ marginBottom: "16px" }}>
                                导出
                            </Button>
                        </Space>
                    </div>

                    <Table
                        loading={loading}
                        dataSource={tableData}
                        columns={columns}
                        rowKey={(record, index) => index}
                        rowSelection={{
                            selectedRowKeys,
                            onChange: setSelectedRowKeys,
                        }}
                        scroll={{ x: 1200, y: 400 }}
                        pagination={{
                            showSizeChanger: true,
                            showQuickJumper: true,
                            showTotal: total => `共 ${total} 条记录`,
                        }}
                    />
                </TabPane>

                <TabPane tab="核注单列表" key="3">
                    <Table
                        loading={endorsementLoading}
                        dataSource={endorsementList}
                        columns={endorsementColumns}
                        rowKey="id"
                        scroll={{ x: 1000 }}
                        pagination={{
                            showSizeChanger: true,
                            showQuickJumper: true,
                            showTotal: total => `共 ${total} 条记录`,
                        }}
                    />
                </TabPane>
            </Tabs>
        </div>
    );
};
