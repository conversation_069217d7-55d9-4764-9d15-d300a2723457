import React, { useEffect, useState } from "react";
import { Modal, message, Radio, Space } from "antd";
import { lib } from "react-single-app";
export default ({ open, closeFn, ids }) => {
    const [value, setValue] = useState("");
    const [plainOptions, setplainOptions] = useState("");
    // const plainOptions = [
    //     { label: "已创建", value: "CREATED" },
    //     { label: "已备案", value: "DECALRING" },
    //     { label: "已结案", value: "AUDITING" },
    //     { label: "已暂停", value: "Apple" },
    //     { label: "备案异常", value: "Pear" },
    //     { label: "结案异常", value: "Orange" },
    // ];
    const onChange = e => {
        console.log("e:", e);
        setValue(e.target.value);
    };
    const submit = () => {
        console.log("ids", ids);
        lib.request({
            url: "/ccs/bizDeclareForm/manualUpdStatus",
            data: { idList: ids, status: value },
            success(data) {
                message.success("提交成功");
                closeFn(true);
            },
        });
    };

    useEffect(() => {
        lib.request({
            url: "/ccs/bizDeclareForm/listStatus",
            success(data) {
                console.log("data:", data);
                data.map(item => {
                    item.value = item.id;
                    item.label = item.name;
                });
                setplainOptions(data);
            },
        });
    }, []);
    // console.log("ids:", ids);
    return (
        <Modal
            open={open}
            title={"修改申报表状态"}
            onCancel={() => {
                closeFn();
            }}
            width={330}
            onOk={() => {
                submit();
            }}>
            <Space>
                <Radio.Group
                    //@ts-ignore
                    options={plainOptions}
                    onChange={onChange}
                    value={value}
                />
            </Space>
        </Modal>
    );
};
