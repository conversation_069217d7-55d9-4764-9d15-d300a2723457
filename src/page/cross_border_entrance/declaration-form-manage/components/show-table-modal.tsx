import React, { useEffect, useState } from "react";
import { Modal, Table } from "antd";
import { lib } from "react-single-app";
export default ({ open, closeFn, row }) => {
    const [dataSource, setDataSource] = useState([]);
    const getData = () => {
        lib.request({
            url: "/ccs/bizDeclareForm/getRefInventoryOrderList",
            data: { declareFormNo: row.declareFormNo },
            success(data) {
                setDataSource(data);
            },
        });
    };
    const columns = [
        {
            title: "清关单号",
            dataIndex: "inventoryOrderSn",
            key: "inventoryOrderSn",
            width: 150,
        },
        {
            title: "创建时间",
            dataIndex: "createTime",
            key: "createTime",
            width: 150,
        },
        {
            title: "清关状态",
            dataIndex: "statusDesc",
            key: "statusDesc",
            width: 80,
        },
        {
            title: "核注清单编号",
            dataIndex: "refHzInveNo",
            key: "refHzInveNo",
            width: 150,
        },
        {
            title: "涉及品数",
            dataIndex: "involvedProductNum",
            key: "involvedProductNum",
            width: 80,
        },
    ];
    useEffect(() => {
        if (open) {
            getData();
        }
    }, [open]);
    return (
        <>
            <Modal
                width={800}
                title="关联清关单"
                open={open}
                onOk={() => {
                    closeFn();
                }}
                onCancel={() => {
                    closeFn();
                }}>
                <Table columns={columns} dataSource={dataSource} />
            </Modal>
        </>
    );
};
