import React, { useEffect, useRef } from "react";
import { DTEditForm, DTEditFormRefs, DTEditFormConfigs } from "@dt/components";
import { Modal, message } from "antd";
// import { depFn } from "../../warehouse-address-manage/component/add-modal";
import { lib } from "react-single-app";
export default ({ open, closeFn }) => {
    const configs: DTEditFormConfigs = [
        {
            type: "SELECT",
            fProps: {
                label: "业务类型",
                name: "businessType",
                // labelCol: { span: 24 },
                rules: [{ required: true, message: "请选择业务类型" }],
            },
            list: [],
            dataUrl: "/ccs/bizDeclareForm/listBusinessType",
            selectShowMode: "together",
        },
        {
            type: "SELECT",
            fProps: {
                label: "区内企业",
                name: "areaCompanyId",
                // labelCol: { span: 24 },
                rules: [{ required: true, message: "请选择区内企业" }],
            },
            list: [],
            dataUrl: "/ccs/company/listWithQNQYAll",
        },
        {
            type: "SELECT",
            fProps: {
                label: "账册编号",
                name: "bookId",
                // labelCol: { span: 24 },
                rules: [{ required: true, message: "请选择账册编号" }],
            },
            relys: [{ from: "areaCompanyId" }],
            list: [],
            dataUrl: "/ccs/customsBook/listBookByAreaCompany",
        },
        {
            type: "INPUT",
            fProps: {
                label: "申请人",
                name: "applicant",
                // labelCol: { span: 24 },
                rules: [{ required: true, message: "请输入申请人" }, { max: 64 }],
            },
        },
    ];
    const ref = useRef<DTEditFormRefs>();

    const submit = () => {
        ref.current.form.validateFields().then(values => {
            lib.request({
                url: "/ccs/bizDeclareForm/create",
                data: values,
                success(data) {
                    message.success("新增成功");
                    closeFn(true);
                },
            });
        });
    };
    useEffect(() => {
        if (open) {
            ref.current.form.resetFields();
        }
    }, [open]);

    return (
        <Modal
            open={open}
            title={"新增申报表"}
            onCancel={() => {
                ref.current.form.resetFields();
                closeFn();
            }}
            onOk={() => {
                submit();
            }}>
            <DTEditForm
                configs={configs}
                layout={{
                    mode: "appoint",
                    colNum: 1,
                }}
                ref={ref}
            />
        </Modal>
    );
};
