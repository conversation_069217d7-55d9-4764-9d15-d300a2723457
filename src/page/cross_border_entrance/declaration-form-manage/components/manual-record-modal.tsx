import React, { useEffect, useRef } from "react";
import { DTEditForm, DTEditFormRefs, DTEditFormConfigs } from "@dt/components";
import { Modal, message } from "antd";
// import { depFn } from "../../warehouse-address-manage/component/add-modal";
import { lib } from "react-single-app";
export default ({ open, closeFn, ids }) => {
    const configs: DTEditFormConfigs = [
        {
            type: "INPUT",
            fProps: {
                label: "请输入申报表编号，手动备案",
                // labelAlign:""
                name: "declareFormNo",
                labelCol: { span: 24 },
                wrapperCol: { span: 24 },
                rules: [{ required: true, message: "请输入申报表编号" }],
            },
        },
    ];
    const ref = useRef<DTEditFormRefs>();

    const submit = () => {
        ref.current.form.validateFields().then(values => {
            lib.request({
                url: "/ccs/bizDeclareForm/manualRecord",
                data: { ...values, id: ids[0] },
                success(data) {
                    message.success("提交成功");
                    closeFn(true);
                },
            });
        });
    };

    useEffect(() => {
        if (open) {
            ref.current.form.resetFields();
        }
    }, [open]);

    return (
        <Modal
            open={open}
            title={"手动备案"}
            width={300}
            onCancel={() => {
                ref.current.form.resetFields();
                closeFn();
            }}
            onOk={() => {
                submit();
            }}>
            <DTEditForm
                configs={configs}
                layout={{
                    mode: "appoint",
                    colNum: 1,
                }}
                ref={ref}
            />
        </Modal>
    );
};
