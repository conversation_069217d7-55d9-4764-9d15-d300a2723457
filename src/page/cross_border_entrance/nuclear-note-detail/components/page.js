import React from "react";
import { lib, SearchList, getConfigDataUtils } from "react-single-app";
import axios from "axios";
import { Button, Tabs, Modal, Table, Form, message } from "antd";
import NewModal from "../../../../components/NewModal";
import { eliminateColumns, previewColumns, eliminateMailColumns, addConfigList } from "../config";

const { TabPane } = Tabs;
const FormItem = Form.Item;

export default class CrossBorderList extends SearchList {
    constructor(props) {
        super(props);
        this.params = { withDetails: false, lastTime: "2023-06-27 09:45:07", lastTimestamp: 1, pageIgnore: "1" };
        this.state.deleteModalVisible = false;
        this.state.deleteBatchModalVisible = false;
        this.state.addModalVisible = false;
        this.state.eliminateModalVisible = false;
        this.state.previewModalVisible = false;
        this.state.eliminateData = {};
        this.state.previewData = {};
        this.state.customsInventorySnList = [];
        this.state.isShow = props.status === "INIT" || props.status === "EXCEPTION";
        this.state.isException = props.status === "EXCEPTION";

        this.state.queryType = "";
        this.state.queryInfo = "";
    }

    getConfig() {
        let url = getConfigDataUtils.getDataUrlByDataId(764);
        return axios.get(url).then(res => res.data.data);
    }

    renderRightOperation() {
        const { isShow, isException } = this.state;
        return (
            <>
                <Button onClick={() => this.singleExport()}>导出表体</Button>
                {isShow && <Button onClick={() => this.addEbInvBatch()}>批量增加</Button>}
                {isShow && <Button onClick={() => this.deleteEbInvBatch()}>批量删除</Button>}
                {isException && <Button onClick={() => this.eliminateException()}>剔除异常</Button>}
            </>
        );
    }

    renderModal() {
        const {
            eliminateModalVisible,
            eliminateData,
            addModalVisible,
            deleteBatchModalVisible,
            deleteModalVisible,
            previewModalVisible,
            previewData,
            editRow,
        } = this.state;
        let addProps = {
            title: "批量增加",
            onOk: this.addBatchFunc.bind(this),
            onCancel: data => {
                this.setState({
                    addModalVisible: false,
                    editRow: {},
                });
            },
            configList: addConfigList,
            editRow,
            visible: addModalVisible,
            formItemLayout: {
                labelCol: { span: 8 },
                wrapperCol: { span: 14 },
            },
        };
        return (
            <React.Fragment>
                <Modal
                    title="剔除异常"
                    visible={eliminateModalVisible}
                    onOk={() => this.setState({ eliminateModalVisible: false, eliminateData: {} })}
                    onCancel={() => this.setState({ eliminateModalVisible: false, eliminateData: {} })}>
                    <Tabs defaultActiveKey="0">
                        <TabPane tab="表体" key="0">
                            <Table
                                dataSource={eliminateData?.exceptionItem}
                                columns={eliminateColumns}
                                pagination={false}
                                scroll={{ y: 400 }}
                            />
                        </TabPane>
                        <TabPane tab="运单号" key="1">
                            <Table
                                dataSource={eliminateData?.exceptionMailNos}
                                columns={eliminateMailColumns}
                                pagination={false}
                                scroll={{ y: 400 }}
                            />
                        </TabPane>
                    </Tabs>
                </Modal>
                <Modal
                    title={`提交预览  (修改数量${previewData?.totalCount || 0})`}
                    visible={previewModalVisible}
                    onOk={() => this.previewOk()}
                    onCancel={() => this.setState({ previewModalVisible: false, previewData: {} })}
                    width="800px">
                    <Tabs defaultActiveKey="0">
                        <TabPane tab={`新增成功(${previewData?.addSuccessCount || 0})`} key="0">
                            <Table
                                dataSource={previewData?.addSuccessRes}
                                columns={previewColumns.success}
                                pagination={false}
                                scroll={{ y: 400 }}
                            />
                        </TabPane>
                        <TabPane tab={`新增失败(${previewData?.addFailCount || 0})`} key="1">
                            <Table
                                dataSource={previewData?.addFailRes}
                                columns={previewColumns.fail}
                                pagination={false}
                                scroll={{ y: 400 }}
                            />
                        </TabPane>
                        <TabPane tab={`删除成功(${previewData?.delSuccessCount || 0})`} key="2">
                            <Table
                                dataSource={previewData?.delSuccessRes}
                                columns={previewColumns.success}
                                pagination={false}
                                scroll={{ y: 400 }}
                            />
                        </TabPane>
                        <TabPane tab={`删除失败(${previewData?.delFailCount || 0})`} key="3">
                            <Table
                                dataSource={previewData?.delFailRes}
                                columns={previewColumns.fail}
                                pagination={false}
                                scroll={{ y: 400 }}
                            />
                        </TabPane>
                    </Tabs>
                </Modal>
                <Modal
                    title="批量删除"
                    visible={deleteBatchModalVisible}
                    onOk={() => this.deleteBatchFunc()}
                    onCancel={() => this.setState({ deleteBatchModalVisible: false })}>
                    是否确认删除？
                </Modal>
                <Modal
                    title="删除"
                    visible={deleteModalVisible}
                    onOk={() => this.deleteFunc()}
                    onCancel={() => this.setState({ deleteModalVisible: false })}>
                    是否确认删除？
                </Modal>
                <NewModal {...addProps} />
            </React.Fragment>
        );
    }

    singleExport() {
        lib.request({
            url: "/ccs/endorsement/singleExport",
            data: {
                id: lib.getParam("id"),
            },
            needMask: true,
            success: () => {
                message.success("导出成功");
                lib.openPage("/excel/download-center?page_title=下载中心");
                // this.load(true);
            },
        });
    }

    addEbInvBatch() {
        this.setState({ addModalVisible: true, editRow: { queryType: "logisticsNo" } });
    }

    deleteEbInvBatch() {
        const { selectedRows } = this.state;
        if (selectedRows.length) {
            this.setState({ deleteBatchModalVisible: true });
        }
    }

    deleteBatchFunc() {
        const { selectedRows } = this.state;
        let customsInventorySnList = selectedRows.map(item => item.customsInventorySn);
        if (customsInventorySnList.length) {
            lib.request({
                url: "/ccs/endorsement/deleteEbInvBatch/preview",
                data: {
                    id: lib.getParam("id"),
                    customsInventorySnList,
                },
                needMask: true,
                success: res => {
                    this.setState({
                        deleteBatchModalVisible: false,
                        customsInventorySnList,
                        previewModalVisible: true,
                        previewData: res,
                    });
                },
            });
        }
    }

    eliminateException() {
        lib.request({
            url: "/ccs/endorsement/eliminateException",
            data: {
                id: lib.getParam("id"),
            },
            needMask: true,
            success: res => {
                message.success("剔除异常成功");
                let exceptionItem = [];
                Object.entries(res.exceptionItem).map(([key, value]) => {
                    exceptionItem.push({ key, value });
                });
                this.setState({
                    eliminateModalVisible: true,
                    eliminateData: {
                        exceptionMailNos: res.exceptionMailNos || [],
                        exceptionItem,
                    },
                });
                this.load(true);
            },
        });
    }

    addBatchFunc(data) {
        this.setState({
            queryType: data.queryType,
            queryInfo: data.queryInfo,
        });
        lib.request({
            url: "/ccs/endorsement/addEbInvBatch/preview",
            data: {
                id: lib.getParam("id"),
                queryType: data.queryType,
                queryInfo: data.queryInfo,
            },
            needMask: true,
            success: res => {
                this.setState({
                    addModalVisible: false,
                    previewModalVisible: true,
                    previewData: res,
                });
            },
        });
    }

    previewOk() {
        const { customsInventorySnList } = this.state;
        if (customsInventorySnList.length) {
            this.delPreviewOk();
        } else {
            this.addPreviewOk();
        }
    }
    addPreviewOk(data) {
        const { queryType, queryInfo } = this.state;
        lib.request({
            url: "/ccs/endorsement/addEbInvBatch",
            data: {
                id: lib.getParam("id"),
                queryType: queryType,
                queryInfo: queryInfo,
            },
            needMask: true,
            success: res => {
                this.setState({
                    previewModalVisible: false,
                    previewData: {},
                });
                this.load(true);
            },
        });
    }

    delPreviewOk() {
        const { customsInventorySnList } = this.state;

        lib.request({
            url: "/ccs/endorsement/deleteEbInvBatch",
            data: {
                id: lib.getParam("id"),
                customsInventorySnList,
            },
            needMask: true,
        });
        this.setState({
            customsInventorySnList: [],
            previewModalVisible: false,
            previewData: {},
        });
        this.load(true);
    }

    myOperation(row) {
        const { isShow } = this.state;
        return (
            <>
                {isShow && (
                    <a
                        onClick={e =>
                            this.setState({
                                deleteModalVisible: true,
                                customsInventorySnList: [row.customsInventorySn],
                            })
                        }>
                        删除
                    </a>
                )}
            </>
        );
    }

    deleteFunc() {
        const { customsInventorySnList } = this.state;
        lib.request({
            url: "/ccs/endorsement/deleteEbInvBatch/preview",
            data: {
                id: lib.getParam("id"),
                customsInventorySnList,
            },
            needMask: true,
            success: res => {
                this.setState({
                    deleteModalVisible: false,
                    previewModalVisible: true,
                    previewData: res,
                });
            },
        });
    }
}
