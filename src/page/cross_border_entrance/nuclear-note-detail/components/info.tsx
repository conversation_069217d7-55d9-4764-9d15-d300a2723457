import React, { useState, useEffect, useRef } from "react";
//@ts-ignore
import { ConfigFormCenter, DDYObject, lib } from "react-single-app";
import { CONFIG_DATA, log_column, addNuclearReleaseConfig } from "../config";
import { Button, Space, Table, message, Modal, Tag } from "antd";
import NewModal from "@/components/NewModal";
import { NuclearNoteDetailStatus } from "../type";
import { BussinessType } from "../../../customs-clearance-manage/customs-clearance-detail2";

export default ({ detail, update }) => {
    const configFormCenterRef = useRef();
    const newModalRef = useRef<any>();
    const [disableEdit, setDisableEdit] = useState(false);
    const [open, setOpen] = useState(false);
    const [configs, setConfigs] = useState(addNuclearReleaseConfig);

    const body_column = [
        {
            title: "序号",
            dataIndex: "serializeNo",
            key: "serializeNo",
            width: 50,
        },
        {
            title: "报关单商品序号",
            dataIndex: "declareCustomsGoodsSeqNo",
            key: "declareCustomsGoodsSeqNo",
            width: 50,
        },
        {
            title: "是否新品",
            dataIndex: "isNew",
            key: "isNew",
            width: 70,
        },
        {
            title: "备案序号",
            dataIndex: "goodsSeqNo",
            key: "goodsSeqNo",
            width: 70,
        },
        {
            title: "商品料号",
            dataIndex: "productId",
            key: "productId",
            width: 150,
            render: (value, record) => {
                return (
                    <>
                        {value}
                        <Space>
                            {record.tagList &&
                                record.tagList.map((item, index) => {
                                    return (
                                        <Tag color="blue" key={index}>
                                            {item}
                                        </Tag>
                                    );
                                })}
                        </Space>
                    </>
                );
            },
        },
        {
            title: "申报表序号",
            dataIndex: "declareFormItemSeqNo",
            key: "declareFormItemSeqNo",
            width: 120,
        },
        {
            title: "商品编码",
            dataIndex: "hsCode",
            key: "hsCode",
            width: 120,
        },
        {
            title: "商品名称",
            dataIndex: "goodsName",
            key: "goodsName",
            width: 250,
        },
        {
            title: "规格型号",
            dataIndex: "goodsModel",
            key: "goodsModel",
            width: 250,
        },
        {
            title: "原产国（地区）",
            dataIndex: "originCountry",
            key: "originCountry",
            width: 100,
        },
        {
            title: "最终目的国（地区）",
            dataIndex: "destinationCountry",
            key: "destinationCountry",
            width: 120,
        },
        {
            title: "申报计量单位",
            dataIndex: "unit",
            key: "unit",
            width: 100,
        },
        {
            title: "申报数量",
            dataIndex: "declareUnitQfy",
            key: "declareUnitQfy",
            width: 80,
        },
        {
            title: "申报单价",
            dataIndex: "declarePrice",
            key: "declarePrice",
            width: 80,
        },
        {
            title: "申报总价",
            dataIndex: "declareTotalPrice",
            key: "declareTotalPrice",
            width: 80,
        },
        {
            title: "币制",
            dataIndex: "currency",
            key: "currency",
            width: 60,
        },
        {
            title: "法定计量单位",
            dataIndex: "firstUnit",
            key: "firstUnit",
            width: 120,
        },
        {
            title: "法定第二计量单位",
            dataIndex: "secondUnit",
            key: "secondUnit",
            width: 120,
        },
        {
            title: "法定数量",
            dataIndex: "firstUnitQfy",
            key: "firstUnitQfy",
            width: 80,
        },
        {
            title: "第二法定数量",
            dataIndex: "secondUnitQfy",
            key: "secondUnitQfy",
            width: 120,
        },
        {
            title: "毛重（kg）",
            dataIndex: "grossWeight",
            key: "grossWeight",
            width: 100,
        },
        {
            title: "净重（kg）",
            dataIndex: "netWeight",
            key: "netWeight",
            width: 100,
        },
        {
            title: "修改标志",
            dataIndex: "modfMark",
            key: "modfMark",
            width: 120,
        },
        {
            title: "危化品标志",
            dataIndex: "dangerousFlag",
            key: "dangerousFlag",
            width: 100,
        },
        {
            title: "记账金二序号",
            dataIndex: "customsCallBackSeqNo",
            key: "customsCallBackSeqNo",
            width: 120,
        },
        {
            title: "报关单商品序号",
            dataIndex: "declareCustomsGoodsSeqNo",
            key: "declareCustomsGoodsSeqNo",
            width: 120,
        },
        {
            title: "申报表序号",
            dataIndex: "declareFormItemSeqNo",
            key: "declareFormItemSeqNo",
            width: 100,
        },
        {
            title: "征免方式",
            dataIndex: "avoidTaxMethod",
            key: "avoidTaxMethod",
            width: 100,
        },
        {
            title: "单耗版本号",
            dataIndex: "version",
            key: "version",
            width: 120,
        },
        {
            title: "备注",
            dataIndex: "remark",
            key: "remark",
            width: 150,
        },
        {
            title: "操作",
            width: 100,
            fixed: "right",
            render: (_, record) => {
                return (
                    <>
                        {detail.bussinessType === "SECONDE_OUT" && (
                            <>
                                <Button
                                    type="link"
                                    onClick={() => {
                                        Modal.confirm({
                                            title: `关联的包裹运单号(运单数：${record.packageMailNoList?.length || 0})`,
                                            icon: null,
                                            content: (
                                                <div className="content">
                                                    {Array.isArray(record.packageMailNoList) &&
                                                        record.packageMailNoList.map(item => {
                                                            return <div>{item}</div>;
                                                        })}
                                                </div>
                                            ),
                                            okText: "确认",
                                            cancelText: "取消",
                                            // bodyStyle: { width: '600px' },
                                            // style: { width: "600px", boxSizing: 'content-box' },
                                            width: 600,
                                            bodyStyle: { maxHeight: "500px", overflow: "scroll" },
                                            wrapClassName: "nuclear-note-detail-modal",
                                        });
                                    }}>
                                    查看
                                </Button>
                            </>
                        )}
                    </>
                );
            },
        },
    ];

    const beforeSubmit = () => {};

    const onConfigLoadSuccess = config => {
        config.secondInfo.children.map(item => {
            //@ts-ignore
            configFormCenterRef.current?.initSelect(item);
        });
    };

    const onSubmitSuccess = () => {};

    const edit = () => {
        //@ts-ignore
        const form = configFormCenterRef.current?.getForm;
        form.validateFields()
            .then(res => {
                lib.request({
                    url: "/ccs/endorsement/detail/edit",
                    data: {
                        id: lib.getParam("id"),
                        invtType: res.customsInvtType,
                        supvMode: res.supvMode,
                        inputerIcCard: res.inputerIcCard,
                        remark: res.remark,
                    },
                    success: data => {
                        update();
                        message.success("编辑成功");
                        setDisableEdit(false);
                    },
                });
            })
            .catch(err => {});
    };

    useEffect(() => {
        update();
    }, []);

    useEffect(() => {
        if (detail) {
            //@ts-ignore
            configFormCenterRef.current?.setMergeDetail(detail);
            // getConfigDatas();
        }
    }, [detail]);

    const getConfigDatas = () => {
        const arr = [...configs];
        arr.map((item, index) => {
            if (item.from) {
                const params = {};
                if (item.labelKey === "type") {
                    params["endorsementId"] = Number(lib.getParam("id"));
                }
                Array.isArray(item.fromParams) &&
                    item.fromParams.map((val: string) => {
                        params[val] = detail[val];
                    });

                lib.request({
                    url: item.from,
                    data: params,
                    success(data) {
                        item.list = data;
                        setConfigs([...arr]);
                        if (item.labelKey === "type") {
                            newModalRef.current?.setFormValue([
                                {
                                    name: item.labelKey,
                                    value: data?.[0].id,
                                },
                            ]);
                        }
                    },
                });
            }
        });
        // 给车辆信息添加自定义change事件，做联动操作
        arr[4].onChange = e => {
            e = String(e);
            if (e.indexOf("_") !== -1) {
            } else {
                lib.request({
                    url: "/ccs/checklist/getVehicleById",
                    data: {
                        id: Number(e),
                    },
                    needMask: true,
                    success: res => {
                        //@ts-ignore
                        newModalRef.current?.form.setFields([
                            // {
                            //     name: "carWeight",
                            //     value: res.vehicleWeight,
                            // },
                            {
                                name: "licenseFrame",
                                value: res.vehicleFrameNo,
                            },
                            // {
                            //     name: "frameWeight",
                            //     value: res.vehicleFrameWeight,
                            // },
                        ]);
                    },
                });
            }
        };
    };

    const addNuclearRelease = data => {
        lib.request({
            url: "/ccs/endorsement/createChecklist",
            data: {
                ...data,
                licensePlate: String(data.licensePlate),
                declareCompanyID: detail?.declareCompanyId,
                endorsementIdList: [lib.getParam("id")],
            },
            success(data) {
                message.success("新增成功");
                update();
                setOpen(false);
            },
        });
    };

    const push = () => {
        lib.request({
            url: "/ccs/endorsement/push",
            data: {
                idList: [Number(lib.getParam("id"))],
            },
            needMask: true,
            success: res => {
                if (!res.errorMessage) {
                    message.success("推送核注成功");
                    update();
                } else {
                    message.error(res.errorMessage);
                }
            },
        });
    };

    return (
        <div className="nuclear-note-detail-info">
            <Space className="right-btn">
                {[
                    NuclearNoteDetailStatus.INIT,
                    NuclearNoteDetailStatus.STORAGED,
                    NuclearNoteDetailStatus.EXCEPTION,
                    NuclearNoteDetailStatus.STORAGE_EXCEPTION,
                ].includes(detail?.status) &&
                    (!disableEdit ? (
                        <Button
                            onClick={() => {
                                setDisableEdit(true);
                            }}>
                            编辑
                        </Button>
                    ) : (
                        <Button
                            onClick={() => {
                                edit();
                            }}>
                            完成
                        </Button>
                    ))}
                {[
                    NuclearNoteDetailStatus.INIT,
                    NuclearNoteDetailStatus.STORAGED,
                    NuclearNoteDetailStatus.EXCEPTION,
                    NuclearNoteDetailStatus.STORAGE_EXCEPTION,
                ].includes(detail?.status) && (
                    <Button
                        onClick={() => {
                            push();
                        }}>
                        推送核注
                    </Button>
                )}
                {[NuclearNoteDetailStatus.EXAMINE].includes(detail?.status) &&
                    ![
                        BussinessType.INVENTORY_PROFIT,
                        BussinessType.RANDOM_INSPECTION_DECLARATION,
                        BussinessType.BUSSINESS_SECTIONINNER_OUT,
                    ].includes(detail?.bussinessType) && (
                        <Button
                            onClick={() => {
                                setOpen(true);
                                getConfigDatas();
                            }}>
                            创建核放单
                        </Button>
                    )}
            </Space>
            <ConfigFormCenter
                ref={configFormCenterRef}
                // onSinglesSelectChange={onSinglesSelectChange}
                disableEdit={!disableEdit}
                submitUrl={"/ccs/fbChecklist/saveHead"}
                beforeSubmit={beforeSubmit}
                // beforeSetDetail={beforeSetDetail}
                // code={code}
                confData={CONFIG_DATA}
                onConfigLoadSuccess={onConfigLoadSuccess}
                // onSetDetailSuccess={onSetDetailSuccess}
                // submitUrl={'/ccs/checklist/upset'}
                onSubmitSuccess={onSubmitSuccess}
            />
            <div className="title">
                <div className="subtitle">核注单表体</div>
            </div>
            <Table
                dataSource={detail?.endorsementItemDetailVOList}
                //@ts-ignore
                columns={body_column}
                pagination={{
                    pageSize: 50,
                }}
                scroll={{ x: 4500, y: 1000 }}
            />
            {detail?.bussinessType === "SIMPLE_PROCESSING" ? (
                <>
                    <div className="title">
                        <div className="subtitle">料件表体</div>
                    </div>
                    <Table
                        dataSource={detail?.endorsementItemGoodsDetailVOList}
                        //@ts-ignore
                        columns={body_column}
                        pagination={{
                            pageSize: 50,
                        }}
                        scroll={{ x: 4500, y: 1000 }}
                    />
                </>
            ) : null}
            <div className="tips">
                <div className="tipsText">
                    每页显示<a style={{ color: "#02a7f0" }}>50</a>条
                </div>
            </div>
            <div className="title">
                <div className="subtitle">核注单日志</div>
            </div>
            <Table
                dataSource={detail?.endorsementTrackLogVOList}
                columns={log_column}
                pagination={{
                    pageSize: 50,
                }}
                style={{ width: "70%" }}
                // scroll={{ x: 600, y: 1000 }}
            />
            <NewModal
                title={"新增核放单"}
                visible={open}
                configList={configs}
                ref={newModalRef}
                onOk={data => {
                    addNuclearRelease(data);
                }}
                onCancel={() => {
                    setOpen(false);
                }}
            />
        </div>
    );
};
