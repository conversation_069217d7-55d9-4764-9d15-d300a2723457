import { But<PERSON>, Modal } from "antd";
import React from "react";
export const CONFIG_DATA = {
    baseInfo: {
        children: [
            {
                label: "企业内部编码",
                name: "sn",
                editEnable: false,
                type: "text",
                labelCol: { span: 12 },
                customConfig: { style: { width: "33.3%" } },
            },
            {
                label: "预录入核注编号",
                name: "preOrderNo",
                editEnable: false,
                labelCol: { span: 12 },
                type: "text",
                customConfig: { style: { width: "33.3%" } },
            },
            {
                label: "核注清单编号",
                name: "realOrderNo",
                editEnable: false,
                labelCol: { span: 12 },
                type: "text",
                customConfig: { style: { width: "33.3%" } },
            },
            {
                label: "业务类型",
                name: "bussinessTypeDesc",
                editEnable: false,
                type: "text",
                labelCol: { span: 12 },
                customConfig: { style: { width: "33.3%" } },
            },
            {
                label: "进出标志",
                name: "ieFlag",
                editEnable: false,
                labelCol: { span: 12 },
                type: "text",
                customConfig: { style: { width: "33.3%" } },
            },
            {
                label: "清关单号",
                name: "inventoryOrderSn",
                editEnable: false,
                labelCol: { span: 12 },
                type: "text",
                customConfig: { style: { width: "33.3%" } },
            },
            {
                label: "申报出库单号",
                name: "exportOrderSn",
                editEnable: false,
                type: "text",
                labelCol: { span: 12 },
                customConfig: { style: { width: "33.3%" } },
            },
            {
                label: "账册编号",
                name: "customsBookNo",
                editEnable: false,
                labelCol: { span: 12 },
                type: "text",
                customConfig: { style: { width: "33.3%" } },
            },
            {
                label: "核放企业单号",
                name: "checklistSnList",
                editEnable: false,
                labelCol: { span: 12 },
                type: "text",
                customConfig: { style: { width: "33.3%" } },
            },
            {
                label: "创建时间",
                name: "createTime",
                editEnable: false,
                type: "text",
                labelCol: { span: 12 },
                customConfig: { style: { width: "33.3%" } },
            },
            {
                label: "更新时间",
                name: "updateTime",
                editEnable: false,
                labelCol: { span: 12 },
                type: "text",
                customConfig: { style: { width: "33.3%" } },
            },
            {
                label: "完成时间",
                name: "finishTime",
                editEnable: false,
                labelCol: { span: 12 },
                type: "text",
                customConfig: { style: { width: "33.3%" } },
            },
            {
                label: "核注单状态",
                name: "statusDesc",
                editEnable: false,
                type: "text",
                labelCol: { span: 12 },
                customConfig: { style: { width: "33.3%" } },
            },
            {
                label: "海关回执",
                name: "customsStatusDesc",
                editEnable: false,
                labelCol: { span: 12 },
                type: "text",
                customConfig: { style: { width: "33.3%" } },
            },
            {
                label: "是否核扣账册",
                name: "stockChangeFlag",
                editEnable: false,
                labelCol: { span: 12 },
                type: "text",
                customConfig: { style: { width: "33.3%" } },
            },
            {
                label: "报关单统一编号",
                name: "customsEntrySeqNo",
                editEnable: false,
                labelCol: { span: 12 },
                type: "text",
                customConfig: { style: { width: "33.3%" } },
            },
            {
                label: "报关单生成状态",
                name: "generateDeclareStatusDesc",
                editEnable: false,
                labelCol: { span: 12 },
                type: "text",
                customConfig: { style: { width: "33.3%" } },
            },
            {
                label: "操作人",
                name: "createBy",
                editEnable: false,
                labelCol: { span: 12 },
                type: "text",
                customConfig: { style: { width: "33.3%" } },
            },
        ],
        label: "基本信息",
        name: "changeConsigneeInfo",
        isGroup: true,
        className: "declaration-manage changeConsigneeInfo",
    },
    secondInfo: {
        children: [
            {
                label: "清单类型",
                name: "customsInvtType",
                editEnable: false,
                labelCol: { span: 12 },
                type: "single-select",
                rules: [{ required: true }],
                from: "/ccs/dictionary/listCustomsInvtType",
                customConfig: { style: { width: "33.3%" }, together: true },
            },
            {
                label: "总件数",
                name: "totalDeclareQty",
                editEnable: false,
                labelCol: { span: 12 },
                type: "text",
                customConfig: { style: { width: "33.3%" } },
            },
            {
                label: "运输方式",
                name: "transportModeDesc",
                editEnable: false,
                labelCol: { span: 12 },
                type: "text",
                customConfig: { style: { width: "33.3%" } },
            },
            {
                label: "监管方式",
                name: "supvMode",
                editEnable: false,
                labelCol: { span: 12 },
                type: "single-select",
                rules: [{ required: true }],
                from: "/ccs/dictionary/listSupvMode",
                customConfig: { style: { width: "33.3%" }, together: true },
            },
            {
                label: "总净重（千克）",
                name: "totalNetWeight",
                editEnable: false,
                labelCol: { span: 12 },
                type: "text",
                customConfig: { style: { width: "33.3%" } },
            },
            {
                label: "总毛重（千克）",
                name: "totalGrossWeight",
                editEnable: false,
                labelCol: { span: 12 },
                type: "text",
                customConfig: { style: { width: "33.3%" } },
            },
            {
                label: "进出境关别",
                name: "entryExitCustoms",
                editEnable: false,
                labelCol: { span: 12 },
                type: "text",
                customConfig: { style: { width: "33.3%" } },
            },
            {
                label: "主管海关",
                name: "customsCode",
                editEnable: false,
                labelCol: { span: 12 },
                type: "text",
                customConfig: { style: { width: "33.3%" } },
            },
            {
                label: "启运/运抵国",
                name: "shipmentCountry",
                editEnable: false,
                labelCol: { span: 12 },
                type: "text",
                customConfig: { style: { width: "33.3%" } },
            },
            {
                label: "关联核注清单编号",
                name: "rltEndorsementNo",
                editEnable: false,
                labelCol: { span: 12 },
                type: "text",
                customConfig: { style: { width: "33.3%" } },
            },
            {
                label: "关联账册备案号",
                name: "rltCustomsBookNo",
                editEnable: false,
                labelCol: { span: 12 },
                type: "text",
                customConfig: { style: { width: "33.3%" } },
            },
            {
                label: "报关单号",
                name: "customsEntryNo",
                editEnable: false,
                labelCol: { span: 12 },
                type: "text",
                customConfig: { style: { width: "33.3%" } },
            },
            {
                label: "报关标志",
                name: "customsFlag",
                editEnable: false,
                labelCol: { span: 12 },
                type: "text",
                customConfig: { style: { width: "33.3%" } },
            },
            {
                label: "是否生成报关单",
                name: "declarationFlag",
                editEnable: false,
                labelCol: { span: 12 },
                type: "text",
                customConfig: { style: { width: "33.3%" } },
            },
            {
                label: "报关单类型",
                name: "customsEntryType",
                editEnable: false,
                labelCol: { span: 12 },
                type: "text",
                customConfig: { style: { width: "33.3%" } },
            },
            {
                label: "报关类型",
                name: "customsType",
                editEnable: false,
                labelCol: { span: 12 },
                type: "text",
                customConfig: { style: { width: "33.3%" } },
            },
            {
                label: "录入员IC卡号",
                name: "inputerIcCard",
                editEnable: false,
                labelCol: { span: 12 },
                type: "textInput",
                rules: [{ required: true }],
                customConfig: { style: { width: "33.3%" } },
            },
            {
                label: "备注",
                name: "remark",
                editEnable: false,
                labelCol: { span: 12 },
                type: "textInput",
                rules: [
                    {
                        validator: (rule, value, callback) => {
                            if (value != null && value.length > 4000) {
                                callback("长度需要小于4000");
                            }
                            callback();
                        },
                    },
                ],
                customConfig: { style: { width: "33.3%" } },
            },
            {
                label: "经营单位名称",
                name: "tradeCompanyName",
                editEnable: false,
                labelCol: { span: 12 },
                type: "text",
                customConfig: { style: { width: "33.3%" } },
            },
            {
                label: "经营单位USCC",
                name: "tradeCompanyUSCC",
                editEnable: false,
                labelCol: { span: 12 },
                type: "text",
                customConfig: { style: { width: "33.3%" } },
            },
            {
                label: "经营单位编码",
                name: "tradeCompanyCode",
                editEnable: false,
                labelCol: { span: 12 },
                type: "text",
                customConfig: { style: { width: "33.3%" } },
            },
            {
                label: "加工单位名称",
                name: "processCompanyName",
                editEnable: false,
                labelCol: { span: 12 },
                type: "text",
                customConfig: { style: { width: "33.3%" } },
            },
            {
                label: "加工单位USCC",
                name: "processCompanyUSCC",
                editEnable: false,
                labelCol: { span: 12 },
                type: "text",
                customConfig: { style: { width: "33.3%" } },
            },
            {
                label: "加工单位编码",
                name: "processCompanyCode",
                editEnable: false,
                labelCol: { span: 12 },
                type: "text",
                customConfig: { style: { width: "33.3%" } },
            },
            {
                label: "申报单位名称",
                name: "declareCompanyName",
                editEnable: false,
                labelCol: { span: 12 },
                type: "text",
                customConfig: { style: { width: "33.3%" } },
            },
            {
                label: "申报单位USCC",
                name: "declareCompanyUSCC",
                editEnable: false,
                labelCol: { span: 12 },
                type: "text",
                customConfig: { style: { width: "33.3%" } },
            },
            {
                label: "申报单位编码",
                name: "declareCompanyCode",
                editEnable: false,
                labelCol: { span: 12 },
                type: "text",
                customConfig: { style: { width: "33.3%" } },
            },
            {
                label: "录入单位名称",
                name: "inputCompanyName",
                editEnable: false,
                labelCol: { span: 12 },
                type: "text",
                customConfig: { style: { width: "33.3%" } },
            },
            {
                label: "录入单位USCC",
                name: "inputCompanyUSCC",
                editEnable: false,
                labelCol: { span: 12 },
                type: "text",
                customConfig: { style: { width: "33.3%" } },
            },
            {
                label: "录入单位编码",
                name: "inputCompanyCode",
                editEnable: false,
                labelCol: { span: 12 },
                type: "text",
                customConfig: { style: { width: "33.3%" } },
            },
            {
                label: "对应报关单申报单位名称",
                name: "corrCusDeclareCompanyName",
                editEnable: false,
                labelCol: { span: 12 },
                type: "text",
                customConfig: { style: { width: "33.3%" } },
            },
            {
                label: "对应报关单申报单位USCC",
                name: "corrCusDeclareCompanyUSCC",
                editEnable: false,
                labelCol: { span: 12 },
                type: "text",
                customConfig: { style: { width: "33.3%" } },
            },
            {
                label: "对应报关单申报单位编码",
                name: "corrCusDeclareCompanyCode",
                editEnable: false,
                labelCol: { span: 12 },
                type: "text",
                customConfig: { style: { width: "33.3%" } },
            },
            {
                label: "关联报关单境内收发货人名称",
                name: "rltCusInnerSFHRCompanyName",
                editEnable: false,
                labelCol: { span: 12 },
                type: "text",
                customConfig: { style: { width: "33.3%" } },
            },
            {
                label: "关联报关单境内收发货人USCC",
                name: "rltCusInnerSFHRCompanyUSCC",
                editEnable: false,
                labelCol: { span: 12 },
                type: "text",
                customConfig: { style: { width: "33.3%" } },
            },
            {
                label: "关联报关单境内收发货人编码",
                name: "rltCusInnerSFHRCompanyCode",
                editEnable: false,
                labelCol: { span: 12 },
                type: "text",
                customConfig: { style: { width: "33.3%" } },
            },
            {
                label: "关联报关单消费使用单位名称",
                name: "rltCusXFDYCompanyName",
                editEnable: false,
                labelCol: { span: 12 },
                type: "text",
                customConfig: { style: { width: "33.3%" } },
            },
            {
                label: "关联报关单消费使用单位USCC",
                name: "rltCusXFDYCompanyUSCC",
                editEnable: false,
                labelCol: { span: 12 },
                type: "text",
                customConfig: { style: { width: "33.3%" } },
            },
            {
                label: "关联报关单消费使用单位编码",
                name: "rltCusXFDYCompanyCode",
                editEnable: false,
                labelCol: { span: 12 },
                type: "text",
                customConfig: { style: { width: "33.3%" } },
            },
            {
                label: "关联报关单申报单位名称",
                name: "rltCusDeclareCompanyName",
                editEnable: false,
                labelCol: { span: 12 },
                type: "text",
                customConfig: { style: { width: "33.3%" } },
            },
            {
                label: "关联报关单申报单位USCC",
                name: "rltCusDeclareCompanyUSCC",
                editEnable: false,
                labelCol: { span: 12 },
                type: "text",
                customConfig: { style: { width: "33.3%" } },
            },
            {
                label: "关联报关单申报单位编码",
                name: "rltCusDeclareCompanyCode",
                editEnable: false,
                labelCol: { span: 12 },
                type: "text",
                customConfig: { style: { width: "33.3%" } },
            },
        ],
        label: "核注单表头",
        name: "changeConsigneeInfo2",
        isGroup: true,
    },
};

export const log_column = [
    {
        title: "核注单状态",
        dataIndex: "statusDesc",
        key: "statusDesc",
        width: 100,
    },
    {
        title: "日志描述",
        dataIndex: "logInfo",
        key: "logInfo",
        width: 300,
    },
    {
        title: "操作时间",
        dataIndex: "createTime",
        key: "createTime",
        width: 100,
    },
    {
        title: "操作人",
        dataIndex: "operator",
        key: "operator",
        width: 100,
    },
    {
        title: "操作",
        dataIndex: "callbackDetail",
        key: "callbackDetail",
        width: 100,
        render: (_, record) => {
            return (
                <>
                    {record.callbackDetail && (
                        <Button
                            type="link"
                            onClick={() => {
                                Modal.confirm({
                                    title: "报文",
                                    icon: null,
                                    content: record.callbackDetail,
                                    okText: "确认",
                                    cancelText: "取消",
                                    width: 900,
                                });
                            }}>
                            查看报文
                        </Button>
                    )}
                </>
            );
        },
    },
];

export const addNuclearReleaseConfig = [
    {
        type: "SELECT",
        labelName: "核放单类型",
        labelKey: "type",
        required: true,
        disabled: true,
        list: [],
        from: "/ccs/endorsement/listChecklistTypeByEndorsementId",
        fromParams: ["bussinessType"],
    },
    {
        type: "SELECT",
        labelName: "绑定类型",
        labelKey: "bindType",
        required: true,
        list: [],
        from: "/ccs/checklist/bindType",
    },
    {
        type: "INPUT",
        labelName: "申请人",
        labelKey: "applicant",
        required: true,
    },
    {
        type: "SELECT",
        labelName: "清关企业",
        labelKey: "declareCompanyID",
        required: true,
        list: [],
        from: "/ccs/company/listWithSBQY",
    },
    {
        type: "SELECT",
        labelName: "车辆信息",
        labelKey: "licensePlate",
        required: true,
        list: [],
        from: "/ccs/checklist/listVehicle",
    },
    {
        type: "INPUT",
        labelName: "车辆自重（KG）",
        labelKey: "carWeight",
        required: true,
    },
    {
        type: "INPUT",
        labelName: "车架号",
        labelKey: "licenseFrame",
        required: true,
    },
    {
        type: "INPUT",
        labelName: "车架重（KG）",
        labelKey: "frameWeight",
        required: true,
    },
];

// page.js
export const eliminateColumns = [
    {
        title: "商品序号",
        dataIndex: "key",
    },
    {
        title: "料号",
        dataIndex: "value",
    },
];

export const previewColumns = {
    success: [
        {
            title: "序号",
            dataIndex: "seqNo",
        },
        {
            title: "运单号",
            dataIndex: "orderNo",
        },
    ],
    fail: [
        {
            title: "序号",
            dataIndex: "seqNo",
        },
        {
            title: "运单号",
            dataIndex: "orderNo",
        },
        {
            title: "失败原因",
            dataIndex: "failReason",
        },
    ],
};

export const eliminateMailColumns = [
    {
        title: "运单号",
        render: (text, record) => record,
    },
];

export const addConfigList = [
    {
        type: "SELECT",
        labelName: "单据类型",
        labelKey: "queryType",
        required: true,
        message: "运单号",
        list: [
            { id: "declareOrderNo", name: "申报单号" },
            { id: "inventoryNo", name: "清单编号" },
            { id: "logisticsNo", name: "运单号" },
        ],
    },
    {
        type: "TEXTAREA",
        labelName: "单号",
        labelKey: "queryInfo",
        required: true,
        // message: "每个ID占一行",
        list: [
            { id: "declareOrderNo", name: "申报单号" },
            { id: "inventoryNo", name: "清单编号" },
            { id: "logisticsNo", name: "运单号" },
        ],
    },
];
