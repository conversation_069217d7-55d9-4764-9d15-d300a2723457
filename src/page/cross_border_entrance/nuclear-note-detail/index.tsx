import React, { useState, useEffect } from "react";
import "./index.less";
import { Tabs } from "antd";

import CrossBorderList from "./components/page";
import Info from "./components/info";
import { DDYObject, lib } from "react-single-app";
export default () => {
    const [detail, setDetail] = useState<DDYObject>(null);
    const getDetail = () => {
        lib.request({
            url: "/ccs/endorsement/detail",
            data: {
                id: lib.getParam("id"),
            },
            success: data => {
                setDetail(data);
            },
        });
    };
    return (
        <div>
            <Tabs
                destroyInactiveTabPane={true}
                onChange={() => {
                    getDetail();
                }}>
                <Tabs.TabPane tab="核注单信息" key={"1"}>
                    <Info detail={detail} update={getDetail} />
                </Tabs.TabPane>
                {detail?.bussinessType === "SECONDE_OUT" && (
                    <Tabs.TabPane tab="跨境电商清单" key={"2"}>
                        <div style={{ height: "500px" }}>
                            <CrossBorderList status={detail?.status} />
                        </div>
                    </Tabs.TabPane>
                )}
            </Tabs>
        </div>
    );
};
