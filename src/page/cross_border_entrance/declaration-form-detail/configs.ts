import { DTEditForm, DTEditFormRefs, DTEditFormListConfigs, DTEditFormItemProps } from "@dt/components";
import { lib } from "react-single-app";
export const DetailBodyHeadConfigs: DTEditFormItemProps[] = [
    {
        type: "INPUT",
        fProps: {
            label: "申报序号",
            name: "seqNo",
        },
        cProps: {
            disabled: true,
        },
    },
    {
        type: "SELECT",
        fProps: {
            label: "申报表料件成品标志",
            name: "mtpckEndprdMarkCd",
        },
        list: [
            { id: "I", name: "料件/半成品" },
            { id: "E", name: "成品/残次品" },
        ],
        cProps: {
            disabled: true,
        },
        dataUrl: "/ccs/bizDeclareForm/listMtpckEndprdType",
    },
    {
        type: "INPUT",
        fProps: {
            label: "底账商品序号",
            name: "goodsSeqNo",
            // rules: [{ required: true, message: '请填写底账商品序号' }]
        },
        cProps: {
            disabled: true,
        },
    },
    {
        type: "SELECT",
        fProps: {
            label: "商品编码",
            name: "goodsCode",
            rules: [{ required: true, message: "请填写商品编码" }],
        },
        list: [],
        selectShowMode: "together",
        dataUrl: "/ccs/customs/listHs",
    },
    {
        type: "INPUT",
        fProps: {
            label: "商品名称",
            name: "goodsName",
            rules: [{ required: true, message: "请填写商品名称" }],
        },
    },
    {
        type: "INPUT",
        fProps: {
            label: "商品规格型号",
            name: "goodsModel",
            rules: [{ required: true, message: "请填写商品规格型号" }],
        },
    },
    {
        type: "INPUT",
        fProps: {
            label: "料号",
            name: "productId",
            rules: [{ required: true, message: "请填写料号" }],
        },
    },
    {
        type: "SELECT",
        fProps: {
            label: "法定计量单位",
            name: "lawFirstUnit",
            rules: [{ required: true, message: "请选择法定计量单位" }],
        },
        list: [],
        dataUrl: "/ccs/customs/listUom",
        cProps: {
            disabled: true,
        },
    },
    {
        type: "SELECT",
        fProps: {
            label: "法定第二计量单位",
            name: "lawSecondUnit",
            // rules: [{ required: true, message: '请选择法定第二计量单位' }]
        },
        list: [],
        dataUrl: "/ccs/customs/listUom",
        cProps: {
            disabled: true,
        },
    },
    {
        type: "SELECT",
        fProps: {
            label: "申报单位",
            name: "declareUnit",
            rules: [{ required: true, message: "请选择申报单位" }],
        },
        list: [],
        selectShowMode: "together",
        dataUrl: "/ccs/customs/listUom",
    },
    {
        type: "INPUT",
        fProps: {
            label: "数量",
            name: "qty",
            rules: [
                { required: true, message: "请填写数量" },
                {
                    pattern: RegExp("^[1-9]\\d*$"),
                    message: "请输入大于0的整数",
                },
            ],
        },
    },
    {
        type: "SELECT",
        fProps: {
            label: "币制",
            name: "currency",
            rules: [{ required: true, message: "请选择币制" }],
        },
        // cProps:{},
        selectShowMode: "together",
        list: [],
        dataUrl: "/ccs/customs/listCurrency",
    },
    {
        type: "INPUT",
        fProps: {
            label: "单价",
            name: "price",
            rules: [{ required: true, message: "请填写单价" }],
        },
    },
    {
        type: "INPUT",
        fProps: {
            label: "总价",
            name: "totalPrice",
        },
        cProps: {
            disabled: true,
        },
    },
    {
        type: "INPUT",
        fProps: {
            label: "许可证编号",
            name: "licenseNumber",
            rules: [
                {
                    pattern: /^[\u4e00-\u9fa5a-zA-Z0-9]{1,64}$/,
                    message: "请输入中英文数字，不含空格特殊字符，不超过64字符",
                },
            ],
        },
    },
    {
        type: "DATE",
        fProps: {
            label: "许可证有效期",
            name: "licenceValidityDate",
        },
    },
    {
        type: "SELECT",
        fProps: {
            label: "商品标记",
            name: "goodsMark",
        },
        list: [],
        dataUrl: "/ccs/bizDeclareForm/listGoodsMark",
        selectShowMode: "together",
    },
    {
        type: "INPUT",
        fProps: {
            label: "商品备注",
            name: "goodsRemark",
            rules: [
                {
                    pattern: /^[\u4e00-\u9fa5a-zA-Z0-9]{1,64}$/,
                    message: "请输入中英文数字，不含空格特殊字符，不超过64字符",
                },
            ],
        },
    },
    {
        type: "SELECT",
        fProps: {
            label: "成品类型",
            name: "endProductType",
        },
        list: [],
        dataUrl: "/ccs/bizDeclareForm/listItemEndprdType",
        cProps: {
            disabled: true,
        },
    },
    {
        type: "SELECT",
        fProps: {
            label: "重点商品标识",
            name: "importantGoodsMark",
            rules: [{ required: true, message: "请选择重点商品标识" }],
        },
        list: [],
        dataUrl: "/ccs/bizDeclareForm/listImportantGoodsMark",
        selectShowMode: "together",
    },
    {
        type: "SELECT",
        fProps: {
            label: "国别(地区)",
            name: "country",
            rules: [{ required: true, message: "请选择国别(地区)" }],
        },
        list: [],
        dataUrl: "/ccs/customs/listCountry",
        selectShowMode: "together",
    },
    {
        type: "SELECT",
        fProps: {
            label: "修改标志",
            name: "editMark",
        },
        list: [],
        dataUrl: "/ccs/bizDeclareForm/listItemEditMark",
        cProps: {
            disabled: true,
        },
    },
    {
        type: "INPUT",
        fProps: {
            label: "备注",
            name: "remark",
            rules: [{ max: 255 }],
        },
    },
];

export enum DeclareFormStatus {
    /**
     * 已创建
     */
    CREATED = "CREATED", // "已创建"),
    /**
     * 备案中
     */
    RECORDING = "RECORDING", // "备案中"),
    /**
     * 已备案
     */
    RECORDED = "RECORDED", //"已备案"),
    /**
     * 结案中
     */
    FINISHING = "FINISHING", //"结案中"),
    /**
     * 已结案
     */
    FINISHED = "FINISHED", //"已结案"),
    /**
     * 已暂停
     */
    PAUSED = "PAUSED", //"已暂停"),
    /**
     * 备案异常
     */
    RECORD_EXCEPTION = "RECORD_EXCEPTION", //"备案异常"),
    /**
     * 结案异常
     */
    FINISH_EXCEPTION = "FINISH_EXCEPTION", //"结案异常"),
    /**
     * 已作废
     */
    DISCARD = "DISCARD", //"已作废"),
}
