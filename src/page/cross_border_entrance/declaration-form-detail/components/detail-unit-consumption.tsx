import React, { useEffect, useRef, useState } from "react";
import { Table, Input, Button, Form, Space, Row, message, Modal } from "antd";
import { lib } from "react-single-app";
import UnitConsumptionModal from "./unit-consumption-modal";
import { DeclareFormStatus } from "../configs";

const App = ({ detail }) => {
    const [form] = Form.useForm();
    const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
    // const [select]
    const selectedRows = useRef<any[]>([]);
    const [dataSource, setDataSource] = useState<any[]>([]);
    const dataCache = useRef([]);
    const [modalBol, setModalBol] = useState(false);
    const [row, setRow] = useState();
    const [mode, setMode] = useState("add");
    const onSelectChange = (newSelectedRowKeys: React.Key[], rows) => {
        console.log("selectedRowKeys changed: ", newSelectedRowKeys);
        setSelectedRowKeys(newSelectedRowKeys);
        selectedRows.current = rows;
    };
    const rowSelection = {
        selectedRowKeys,
        onChange: onSelectChange,
    };

    // 查询条件
    const [query, setQuery] = useState({
        endprdSeqNo: "",
        endprdGoodsName: "",
        mtpckSeqNo: "",
        mtpckGoodsName: "",
    });

    // 处理查询
    const handleSearch = values => {
        console.log("values:", values);
        setQuery(values);
    };

    // 重置查询条件
    const handleReset = () => {
        form.resetFields();
        setQuery({
            endprdSeqNo: "",
            endprdGoodsName: "",
            mtpckSeqNo: "",
            mtpckGoodsName: "",
        });
        // getData();
        // dataCache.current =
        setDataSource([...dataCache.current]);
    };

    // 过滤数据
    const filteredData = dataSource.filter(item => {
        if (!query?.endprdSeqNo && !query?.endprdGoodsName && !query?.mtpckSeqNo && !query?.mtpckGoodsName) {
            return true;
        }
        return (
            (item?.endprdSeqNo + "")?.includes(query.endprdSeqNo) ||
            (item?.endprdGoodsName + "")?.includes(query.endprdGoodsName) ||
            (item?.mtpckSeqNo + "")?.includes(query.mtpckSeqNo) ||
            (item?.mtpckGoodsName + "")?.includes(query.mtpckGoodsName)
        );
    });
    // console.log("filteredData:", filteredData);
    // 表格列定义
    const columns = [
        {
            title: "成品申报序号",
            dataIndex: "endprdSeqNo",
            key: "endprdSeqNo",
        },
        {
            title: "成品商品名称",
            dataIndex: "endprdGoodsName",
            key: "endprdGoodsName",
        },
        {
            title: "料件申报序号",
            dataIndex: "mtpckSeqNo",
            key: "mtpckSeqNo",
        },
        {
            title: "料件商品名称",
            dataIndex: "mtpckGoodsName",
            key: "mtpckGoodsName",
        },
        {
            title: "净耗",
            dataIndex: "netConsumption",
            key: "netConsumption",
        },
        {
            title: "损耗率",
            dataIndex: "consumptionRate",
            key: "consumptionRate",
        },
        {
            title: "修改标识",
            dataIndex: "editMarkDesc",
            key: "editMarkDesc",
        },
        {
            title: "操作",
            key: "操作",
            render: (row, _, index) =>
                ![
                    DeclareFormStatus.RECORDING,
                    DeclareFormStatus.FINISHING,
                    DeclareFormStatus.FINISHED,
                    DeclareFormStatus.DISCARD,
                ].includes(detail.status) && (
                    <Button
                        type="primary"
                        onClick={() => {
                            setMode("edit");
                            setModalBol(true);
                            row.index = index;
                            setRow(row);
                        }}>
                        编辑
                    </Button>
                ),
        },
    ];

    const getData = () => {
        lib.request({
            url: "/ccs/bizDeclareForm/listUnitConsumptionsById",
            data: {
                id: lib.getParam("id"),
            },
            success(data) {
                setDataSource(data);
                dataCache.current = [...data];
            },
        });
    };

    const add = () => {
        setMode("add");
        setModalBol(true);
    };
    const importData = () => {
        let arr = [];
        const params = { bizDeclareFormId: lib.getParam("id") };
        for (let i in params) {
            arr.push(i + "=" + params[i]);
        }
        const str = arr.join("&");
        let importExtendParamBase64 = window.btoa(str + "");
        lib.openPage(
            `/excel/import-data?page_title=申报表单耗导入&code=IMPORT_BIZ_DECLARE_FORM_UC&importExtendParam=${encodeURIComponent(
                importExtendParamBase64,
            )}`,
            () => {
                getData();
            },
        );
    };

    const del = () => {
        // /bizDeclareForm/batchDeleteItems
        if (selectedRowKeys.length === 0) {
            return message.error("请选择删除的数据");
        }
        const idList = selectedRows.current.map(item => item.id);
        Modal.confirm({
            title: "确认",
            content: "确定删除申报表单耗吗？",
            onOk: () => {
                lib.request({
                    url: "/ccs/bizDeclareForm/batchDeleteUnitConsumptions",
                    data: {
                        bizDeclareFormId: lib.getParam("id"),
                        idList: idList,
                    },
                    success(data) {
                        message.success("删除成功");
                        getData();
                        // handleReset();
                    },
                });
            },
        });
    };

    const save = () => {
        lib.request({
            url: "/ccs/bizDeclareForm/saveUnitConsumptions",
            data: {
                bizDeclareFormId: lib.getParam("id"),
                unitConsumptionList: dataSource,
            },
            success(data) {
                message.success("保存成功");
                getData();
                // handleReset();
            },
        });
    };

    useEffect(() => {
        getData();
    }, []);

    return (
        <div style={{}}>
            <Space style={{ paddingBottom: "20px" }}>
                <Button
                    type="primary"
                    onClick={() => {
                        add();
                    }}>
                    新增
                </Button>
                <Button
                    onClick={() => {
                        importData();
                    }}>
                    导入
                </Button>
                <Button
                    onClick={() => {
                        del();
                    }}>
                    批量删除
                </Button>
                <Button
                    onClick={() => {
                        save();
                    }}>
                    保存
                </Button>
            </Space>
            {/* 查询表单 */}
            <Form form={form} onFinish={handleSearch}>
                {/* <h2>查询条件</h2> */}
                <Space>
                    <Form.Item name="endprdSeqNo" label="成品申报序号">
                        <Input placeholder="成品申报序号" style={{ width: "150px" }} />
                    </Form.Item>
                    <Form.Item name="endprdGoodsName" label="成品商品名称">
                        <Input placeholder="成品商品名称" style={{ width: "150px" }} />
                    </Form.Item>
                </Space>
                <br />
                <Space>
                    <Form.Item name="mtpckSeqNo" label="料件申报序号">
                        <Input placeholder="料件申报序号" style={{ width: "150px" }} />
                    </Form.Item>
                    <Form.Item name="mtpckGoodsName" label="料件商品名称">
                        <Input placeholder="料件商品名称" style={{ width: "150px" }} />
                    </Form.Item>
                    <Form.Item>
                        <Space>
                            <Button type="primary" htmlType="submit">
                                查询
                            </Button>
                            <Button onClick={handleReset}>重置</Button>
                        </Space>
                    </Form.Item>
                </Space>
            </Form>

            {/* 数据表格 */}
            <div style={{ marginTop: "20px" }}>
                <Table
                    columns={columns}
                    dataSource={filteredData}
                    rowSelection={rowSelection}
                    bordered
                    rowKey={(record, index) => {
                        return record.endprdSeqNo + "-" + record.mtpckSeqNo;
                    }}
                    pagination={false} // 暂时禁用分页
                />
            </div>
            <UnitConsumptionModal
                open={modalBol}
                updateBody={({ values, type, index }) => {
                    console.log("values, type, index", values, type, index);
                    if (type === "add") {
                        if (
                            dataCache.current.filter(
                                item =>
                                    `${item.endprdSeqNo}-${item.mtpckSeqNo}` ===
                                    `${values.endprdSeqNo}-${values.mtpckSeqNo}`,
                            ).length
                        ) {
                            message.error("成品和料件组合已存在");
                            return;
                        }
                        dataSource.unshift(values);
                        setRow(null);
                        setDataSource([...dataSource]);
                        dataCache.current.unshift(values);
                    } else {
                        // console.log(values, type, index);
                        dataSource[index] = values;
                        setRow(null);
                        setDataSource([...dataSource]);
                        // const
                        const i = dataCache.current.findIndex(item => item.id === values.id);
                        dataCache.current[i] = values;
                    }
                }}
                row={row}
                closeFn={() => {
                    setModalBol(false);
                }}
                mode={mode}
            />
        </div>
    );
};

export default App;
