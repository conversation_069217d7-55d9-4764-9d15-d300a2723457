import React, { useEffect, useRef } from "react";
import { DTEditForm, DTEditFormRefs, DTEditFormConfigs } from "@dt/components";
import { Modal, message } from "antd";
// import { depFn } from "../../warehouse-address-manage/component/add-modal";
import { lib } from "react-single-app";
import { Button } from "antd";
import moment from "moment";
import { DeclareFormStatus } from "../configs";
export default ({ detail, load }) => {
    const ref = useRef<DTEditFormRefs>();
    const configs: DTEditFormConfigs = {
        head: {
            moduleMode: "read",
            title: " ",
            renderHeadRight: ({ totalModal, configItem, detail }) => {
                if (
                    [
                        DeclareFormStatus.FINISHED,
                        DeclareFormStatus.DISCARD,
                        DeclareFormStatus.FINISHING,
                        DeclareFormStatus.RECORDING,
                    ].includes(detail?.status)
                ) {
                    return null;
                }
                return (
                    <div style={{ display: "flex", justifyContent: "flex-end" }}>
                        {configItem.moduleMode === "read" ? (
                            <Button
                                onClick={() => {
                                    configItem.moduleMode = "edit";
                                    ref.current.setConfigModuleItem("head", configItem);
                                }}>
                                编辑
                            </Button>
                        ) : (
                            <Button
                                onClick={() => {
                                    ref.current.form.validateFields().then(values => {
                                        configItem.moduleMode = "read";
                                        submit(() => {
                                            ref.current.setConfigModuleItem("head", configItem);
                                        });
                                        // ref.current.setConfigModuleItem("head", configItem);
                                    });
                                }}>
                                保存
                            </Button>
                        )}
                    </div>
                );
            },
            configs: [
                {
                    type: "INPUT",
                    fProps: {
                        label: "企业内部编号",
                        name: "sn",
                    },
                },
                {
                    type: "INPUT",
                    fProps: {
                        label: "预录入统一编号",
                        name: "preNo",
                    },
                },
                {
                    type: "INPUT",
                    fProps: {
                        label: "申报表编号",
                        name: "declareFormNo",
                    },
                },
                {
                    type: "INPUT",
                    fProps: {
                        label: "主管关区",
                        name: "masterCustomsName",
                    },
                },
                {
                    type: "INPUT",
                    fProps: {
                        label: "账册编号",
                        name: "bookNo",
                    },
                },
                {
                    type: "SELECT",
                    fProps: {
                        label: "业务类型",
                        name: "businessType",
                    },
                    list: [],
                    dataUrl: "/ccs/bizDeclareForm/listBusinessType",
                },
                {
                    type: "INPUT",
                    fProps: {
                        label: "创建时间",
                        name: "createTime",
                    },
                },
                {
                    type: "INPUT",
                    fProps: {
                        label: "更新时间",
                        name: "updateTime",
                    },
                },
                {
                    type: "SELECT",
                    fProps: {
                        label: "区内企业编码",
                        name: "areaCompanyCode",
                    },
                    list: [],
                    dataUrl: "/ccs/company/listWithQNQYAll",
                },
                {
                    type: "INPUT",
                    fProps: {
                        label: "区内企业USCC",
                        name: "areaCompanyUSCC",
                        // labelCol: { span: 24 },
                        // rules: [{ required: true, message: "请输入企业内部编号" }],
                    },
                    // editable: true,
                },
                {
                    type: "INPUT",
                    fProps: {
                        label: "区内企业名称",
                        name: "areaCompanyName",
                    },
                },
                {
                    type: "INPUT",
                    fProps: {
                        label: "保证金征收编号",
                        name: "depositCollectionNo",
                    },
                    editable: true,
                },
                {
                    type: "INPUT",
                    fProps: {
                        label: "申报单位编码",
                        name: "declareCompanyCode",
                    },
                },
                {
                    type: "INPUT",
                    fProps: {
                        label: "申报单位USCC",
                        name: "declareCompanyUSCC",
                    },
                },
                {
                    type: "INPUT",
                    fProps: {
                        label: "申报单位名称",
                        name: "declareCompanyName",
                    },
                },
                {
                    type: "SELECT",
                    fProps: {
                        label: "货物流向",
                        name: "goodsDirection",
                        // labelCol: { span: 24 },
                        rules: [{ required: true, message: "请选择货物流向" }],
                    },
                    editable: true,
                    selectShowMode: "together",
                    list: [],
                    dataUrl: "/ccs/bizDeclareForm/listGoodsDirection",
                },
                {
                    type: "INPUT",
                    fProps: {
                        label: "录入单位编码",
                        name: "inputCompanyCode",
                    },
                },
                {
                    type: "INPUT",
                    fProps: {
                        label: "录入单位USCC",
                        name: "inputCompanyUSCC",
                    },
                },
                {
                    type: "INPUT",
                    fProps: {
                        label: "录入单位名称",
                        name: "inputCompanyName",
                    },
                },
                {
                    type: "INPUT",
                    fProps: {
                        label: "展示地",
                        name: "exhibitionPlace",
                        // labelCol: { span: 24 },
                        rules: [{ max: 512 }],
                    },
                    editable: true,
                },
                {
                    type: "DATE",
                    fProps: {
                        label: "申报表有效期",
                        name: "validTime",
                        // labelCol: { span: 24 },
                        rules: [{ required: true, message: "请选择申报表有效期" }],
                    },
                    editable: true,
                },
                {
                    type: "SELECT",
                    fProps: {
                        label: "底账料件成品标志",
                        name: "bookMtpckEndprdTypecd",
                        // labelCol: { span: 24 },
                        rules: [{ required: true, message: "请选择底账料件成品标志" }],
                    },
                    list: [],
                    editable: true,
                    selectShowMode: "together",
                    dataUrl: "/ccs/bizDeclareForm/listBookMtpckEndprdType",
                },
                {
                    type: "SELECT",
                    fProps: {
                        label: "边角料集报标志",
                        name: "scrapCollectionMark",
                    },
                    list: [],
                    editable: true,
                    dataUrl: "/ccs/bizDeclareForm/listScrapCollectionMark",
                },
                {
                    type: "INPUT",
                    fProps: {
                        label: "担保征收比例%",
                        name: "guaranteeCollectionRate",
                    },
                },
                {
                    type: "INPUT",
                    fProps: {
                        label: "备案审批日期",
                        name: "recordAuditTime",
                        // labelCol: { span: 24 },
                        // rules: [{ required: true, message: "请选择备案审批日期" }],
                    },
                    // editable: true,
                },
                {
                    type: "INPUT",
                    fProps: {
                        label: "变更审批日期",
                        name: "editAuditTime",
                        // labelCol: { span: 24 },
                        // rules: [{ required: true, message: "请选择变更审批日期" }],
                    },
                    // editable: true,
                },
                {
                    type: "INPUT",
                    fProps: {
                        label: "结案审批日期",
                        name: "finishAuditTime",
                        // labelCol: { span: 24 },
                        // rules: [{ required: true, message: "请输入企业内部编号" }],
                    },
                },
                {
                    type: "INPUT",
                    fProps: {
                        label: "申请人",
                        name: "applicant",
                        // labelCol: { span: 24 },
                        rules: [{ required: true, message: "请输入企业内部编号" }, { max: 64 }],
                    },
                    editable: true,
                },
                {
                    type: "SELECT",
                    fProps: {
                        label: "保税区内销标志",
                        name: "freeDomesticMark",
                        // labelCol: { span: 24 },
                        // rules: [{ required: true, message: "请输入企业内部编号" }],
                    },
                    list: [],
                    dataUrl: "/ccs/bizDeclareForm/listFreeDomestic",
                    editable: true,
                },
                {
                    type: "INPUT",
                    fProps: {
                        label: "备注",
                        name: "remark",
                        // labelCol: { span: 24 },
                        rules: [{ max: 255, message: "长度最长位255" }],
                    },

                    editable: true,
                },
            ],
        },
    };

    const submit = fn => {
        ref.current.form.validateFields().then(values => {
            values.validTime && (values.validTime = values.validTime.valueOf());
            values.licenceValidityDate && (values.licenceValidityDate = values.licenceValidityDate.valueOf());
            values.id = lib.getParam("id");
            lib.request({
                url: "/ccs/bizDeclareForm/updateDetail",
                data: values,
                success(data) {
                    message.success("提交成功");
                    fn();
                    load();
                    // closeFn(true);
                },
            });
        });
    };

    useEffect(() => {
        if (detail) {
            detail.validTime && (detail.validTime = moment(detail.validTime));
            ref.current.mergeDetail(detail || {});
        }
    }, [detail]);

    return (
        <DTEditForm
            configs={configs}
            layout={{
                mode: "appoint",
                colNum: 4,
            }}
            ref={ref}
            totalMode="edit"
        />
    );
};
