import React, { useEffect, useRef, useState } from "react";
import { Space, Button, Input, Select, Table, Form, message, Modal } from "antd";
import { DTEditForm, DTEditFormRefs } from "@dt/components";

import { EditOutlined, SearchOutlined, RedoOutlined } from "@ant-design/icons";
import { DDYObject, lib } from "react-single-app";
import EditModal from "./edit-modal";
import { DeclareFormStatus } from "../configs";
let timeout: ReturnType<typeof setTimeout> | null;
export default ({ detail, showItem }) => {
    const [form] = Form.useForm();
    const ref = useRef<DTEditFormRefs>();
    const [dataSource, setDataSource] = useState([]);
    const dataCache = useRef([]);
    const [editBol, setEditBol] = useState(false);
    const [row, setRow] = useState<DDYObject>({});
    const [mode, setMode] = useState("add");
    const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
    const [selectedRows, setSelectedRows] = useState<DDYObject[]>([]);
    const onSelectChange = (newSelectedRowKeys: React.Key[], rows) => {
        setSelectedRowKeys(newSelectedRowKeys);
        setSelectedRows(rows);
        if (rows.length === 1) {
            showItem(rows[0]);
        } else {
            showItem();
        }
    };
    const rowSelection = {
        selectedRowKeys,
        onChange: onSelectChange,
        // onChange: (row, selectedRows) => {
        //     setSelectedRowKeys(row);
        //     if (selectedRows.length === 1) {
        //         setCurrent(selectedRows[0]);
        //         currentRef.current = { ...selectedRows[0] };
        //     } else {
        //         setCurrent({});
        //         currentRef.current = {};
        //     }
        // },
    };
    const fileldData = useRef<DDYObject>(null);
    const add = () => {
        if (fileldData.current) {
            setEditBol(true);
            setMode("add");
            fileldData.current.seqNo = dataSource.length + 1;
            console.log(form.getFieldValue("mtpckEndprdMarkCd"));
            //@ts-ignore
            setRow({ ...fileldData.current, mtpckEndprdMarkCd: ref.current.form.getFieldValue("mtpckEndprdMarkCd") });
        } else {
            message.error("请先选择料号");
        }
    };
    // const [queryParams, setQueryParams] = useState({
    //     mtpckEndprdMarkCd: "",
    //     productId: "",
    //     goodsName: "",
    //     goodsCode: "",
    // });

    const handleSearch = () => {
        // console.log("查询参数:", queryParams);
        const queryParams = form.getFieldsValue();
        // 这里可以发起查询请求
        // getData();
        const filteredData = dataCache.current.filter(item => {
            if (
                !queryParams?.mtpckEndprdMarkCd &&
                !queryParams?.productId &&
                !queryParams?.goodsName &&
                !queryParams?.goodsCode
            ) {
                return true;
            }
            console.log(queryParams.mtpckEndprdMarkCd, item?.mtpckEndprdMarkCd);
            return (
                (item?.mtpckEndprdMarkCd + "")?.includes(queryParams.mtpckEndprdMarkCd) ||
                (item?.productId + "")?.includes(queryParams.productId) ||
                (item?.goodsName + "")?.includes(queryParams.goodsName) ||
                (item?.goodsCode + "")?.includes(queryParams.goodsCode)
            );
        });
        setDataSource(filteredData);
    };

    const handleReset = () => {
        form.resetFields();
        console.log("重置查询条件");
        setDataSource([...dataCache.current]);
    };

    const columns = [
        {
            title: "申报序号",
            dataIndex: "seqNo",
            key: "seqNo",
        },
        {
            title: "料件成品标志",
            dataIndex: "mtpckEndprdMarkCdDesc",
            key: "mtpckEndprdMarkCdDesc",
        },
        {
            title: "料号",
            dataIndex: "productId",
            key: "productId",
        },
        {
            title: "底账商品序号",
            dataIndex: "goodsSeqNo",
            key: "goodsSeqNo",
        },
        {
            title: "商品编码",
            dataIndex: "goodsCode",
            key: "goodsCode",
        },
        {
            title: "商品名称",
            dataIndex: "goodsName",
            key: "goodsName",
        },
        {
            title: "数量",
            dataIndex: "qty",
            key: "qty",
        },
        {
            title: "申报单位",
            dataIndex: "declareUnit",
            key: "declareUnit",
        },
        {
            title: "国别（地区）",
            dataIndex: "country",
            key: "country",
        },
        {
            title: "单价",
            dataIndex: "price",
            key: "price",
        },
        {
            title: "总价",
            dataIndex: "totalPrice",
            key: "totalPrice",
        },
        {
            title: "操作",
            key: "action",
            render: (row, _, index) =>
                row.id &&
                ![
                    DeclareFormStatus.RECORDING,
                    DeclareFormStatus.FINISHING,
                    DeclareFormStatus.FINISHED,
                    DeclareFormStatus.DISCARD,
                ].includes(detail.status) && (
                    <Button
                        type="link"
                        icon={<EditOutlined />}
                        onClick={() => {
                            setEditBol(true);
                            setMode("edit");
                            //@ts-ignore
                            setRow({ ...row, index: index });
                        }}>
                        编辑
                    </Button>
                ),
        },
    ];

    const save = () => {
        dataCache.current.map(item => {
            // if(item.licenceValidityDate){
            //     item.licenceValidityDate = item.licenceValidityDate.valueOf();
            // }
        });
        lib.request({
            url: "/ccs/bizDeclareForm/saveItems",
            data: {
                bizDeclareFormId: lib.getParam("id"),
                itemDTOList: dataCache.current,
            },
            success(data) {
                message.success("保存成功");
                getData();
                // handleReset();
            },
        });
    };

    const getData = () => {
        lib.request({
            url: "/ccs/bizDeclareForm/listItemsById",
            data: { id: lib.getParam("id") },
            success(data) {
                console.log("data:", data);
                setDataSource(data);
                dataCache.current = [...data];
            },
        });
    };

    const del = () => {
        // /bizDeclareForm/batchDeleteItems
        if (selectedRowKeys.length === 0) {
            return message.error("请选择删除的数据");
        }
        const idList = selectedRows.map(item => item.id);
        Modal.confirm({
            title: "确认",
            content: "确定删除申报表表体吗？",
            onOk: () => {
                lib.request({
                    url: "/ccs/bizDeclareForm/batchDeleteItems",
                    data: {
                        bizDeclareFormId: lib.getParam("id"),
                        idList: idList,
                    },
                    success(data) {
                        message.success("删除成功");
                        getData();
                        // handleReset();
                    },
                });
            },
        });
    };

    const importData = () => {
        let arr = [];
        const params = { bizDeclareFormId: lib.getParam("id"), bookId: detail?.bookId };
        for (let i in params) {
            arr.push(i + "=" + params[i]);
        }
        const str = arr.join("&");
        let importExtendParamBase64 = window.btoa(str + "");
        lib.openPage(
            `/excel/import-data?page_title=申报表表体导入&code=IMPORT_BIZ_DECLARE_FORM_ITEM&importExtendParam=${encodeURIComponent(
                importExtendParamBase64,
            )}`,
            () => {
                getData();
            },
        );
    };

    useEffect(() => {
        getData();
    }, []);

    return (
        <div>
            <DTEditForm
                configs={[
                    {
                        type: "SELECT",
                        fProps: {
                            label: "申报表料件成品标志",
                            name: "mtpckEndprdMarkCd",
                            rules: [{ required: true, message: "申报表料件成品标志是必选的" }],
                        },
                        list: [
                            { id: "I", name: "料件/半成品" },
                            { id: "E", name: "成品/残次品" },
                        ],
                    },
                    {
                        type: "SELECT",
                        fProps: {
                            label: "料号/序号",
                            name: "queryInfo",
                            rules: [{ required: true, message: "料号/序号是必选的" }],
                        },
                        cProps: {
                            filterOption: false,
                            onSearch: e => {
                                const data = ref.current.form.getFieldsValue();
                                if (!e) {
                                    return message.error("请输入料号/序号查询数据");
                                }
                                // if(timeout)
                                if (timeout) {
                                    clearTimeout(timeout);
                                    timeout = null;
                                }
                                timeout = setTimeout(() => {
                                    lib.request({
                                        data: {
                                            mtpckEndprdMarkCd: data.mtpckEndprdMarkCd,
                                            queryInfo: e,
                                            bookId: detail.bookId,
                                        },
                                        needMask: true,
                                        url: "/ccs/bizDeclareForm/matchItem",
                                        success(res) {
                                            console.log("res:", res);
                                            res.map(item => {
                                                // {`${item.productId}(${item.skuId})`}-
                                                // {item.recordProductName}
                                                item.name = `${item.goodsSeqNo}-${item.productId}-${item.goodsName}`;
                                                item.query_data = `${item.goodsSeqNo}-${item.productId}`;
                                            });
                                            // res.
                                            ref.current.setConfigFormItem("queryInfo", {
                                                list: res || [],
                                            });
                                            clearTimeout(timeout);
                                            timeout = null;
                                        },
                                    });
                                }, 500);
                            },
                        },
                        list: [],
                        listItemValueKey: "query_data",
                        listItemLabelKey: "name",
                        colSpan: 16,
                        // selectShowMode: "together",
                        dataUrl: "/ccs/bizDeclareForm/matchItem",
                    },
                ]}
                layout={{
                    mode: "appoint",
                    colNum: 3,
                }}
                ref={ref}
                onChange={(name, value, arr) => {
                    if (name === "queryInfo") {
                        // queryInfo
                        fileldData.current = arr[0];
                    }
                    if (name === "mtpckEndprdMarkCd") {
                        ref.current.form.setFieldValue("queryInfo", "");
                        ref.current.setConfigFormItem("queryInfo", { list: [] });
                    }
                }}
            />
            <Space>
                <Button
                    type="primary"
                    onClick={() => {
                        if (
                            [
                                DeclareFormStatus.RECORDING,
                                DeclareFormStatus.FINISHING,
                                DeclareFormStatus.FINISHED,
                                DeclareFormStatus.DISCARD,
                            ].includes(detail.status)
                        ) {
                            message.error("申报表状态无法新增表体");
                            return;
                        }
                        ref.current.form.validateFields().then(res => {
                            if (fileldData.current) {
                                add();
                            } else {
                                message.error("请先选择料号/序号");
                            }
                        });
                    }}>
                    新增
                </Button>
                <Button
                    onClick={() => {
                        importData();
                    }}>
                    导入
                </Button>
                <Button
                    onClick={() => {
                        del();
                    }}>
                    批量删除
                </Button>
                <Button
                    onClick={() => {
                        save();
                    }}>
                    保存
                </Button>
            </Space>
            <Form form={form} layout="horizontal" style={{ marginTop: "20px" }}>
                <Space style={{ marginBottom: "16px" }}>
                    <Form.Item name="productId" label="料号">
                        <Input placeholder="料号" style={{ width: "150px" }} />
                    </Form.Item>
                    <Form.Item name="goodsCode" label="商品编码">
                        <Input placeholder="商品编码" style={{ width: "150px" }} />
                    </Form.Item>
                    <Form.Item name="goodsName" label="商品名称">
                        <Input placeholder="商品名称" style={{ width: "150px" }} />
                    </Form.Item>
                    <Form.Item name="mtpckEndprdMarkCd" label="料件成品标志">
                        <Select placeholder="料件成品标志" style={{ width: "150px" }}>
                            <Select.Option value="I">料件</Select.Option>
                            <Select.Option value="E">成品</Select.Option>
                        </Select>
                    </Form.Item>
                    <Form.Item>
                        <Button type="primary" icon={<SearchOutlined />} onClick={handleSearch}>
                            查询
                        </Button>
                    </Form.Item>
                    <Form.Item>
                        <Button icon={<RedoOutlined />} onClick={handleReset}>
                            重置
                        </Button>
                    </Form.Item>
                </Space>
            </Form>

            <Table
                dataSource={dataSource}
                columns={columns}
                pagination={{ pageSize: 10, showSizeChanger: false }}
                rowSelection={rowSelection}
                rowKey={(record, index) => {
                    return `${record.goodsSeqNo}-${record.productId}`;
                }}
                onRow={record => {
                    return {
                        onClick: event => {
                            showItem(record);
                        },
                    };
                }}
            />
            <EditModal
                row={row}
                mode={mode}
                open={editBol}
                typeBol={row ? row?.mtpckEndprdMarkCd === "I" : false}
                closeFn={load => {
                    setEditBol(false);
                }}
                updateBody={({ values, type, index }) => {
                    if (type === "add") {
                        console.log(values);
                        dataSource.unshift(values);
                        setDataSource([...dataSource]);
                        dataCache.current.unshift(values);
                    } else {
                        console.log(values);
                        dataSource[index] = values;
                        setDataSource([...dataSource]);
                        // const
                        const i = dataCache.current.findIndex(item => item.id === values.id);
                        dataCache.current[i] = values;
                    }
                }}
            />
        </div>
    );
};
