import React, { useEffect, useRef, useState } from "react";
import { DTEditForm, DTEditFormRefs, DTEditFormListConfigs, DTEditFormItemProps } from "@dt/components";
import { Modal, Space, message } from "antd";
import { lib } from "react-single-app";
import { Button } from "antd";
import { DetailBodyHeadConfigs } from "../configs";
import DetailBodyForm from "./detail-body-form";
import moment from "moment";

export default ({ detail }) => {
    const ref = useRef<DTEditFormRefs>();
    const [mode, setMode] = useState<"read" | "edit">("read");
    const configs: DTEditFormItemProps[] = DetailBodyHeadConfigs;
    useEffect(() => {
        ref.current.setConfigFormItem("goodsCode", { listItemLabelKey: "id" });
    }, []);
    return (
        <div style={{ minHeight: "600px" }}>
            <Space align="end" direction="vertical" style={{ width: "100%" }}>
                <Button
                    onClick={() => {
                        // /bizDeclareForm/item/exportExcel
                        lib.request({
                            url: "/ccs/bizDeclareForm/item/exportExcel",
                            needMask: true,
                            data: { id: lib.getParam("id") },
                            success: json => {
                                lib.openPage("/excel/download-center?page_title=下载中心");
                            },
                        });
                    }}>
                    导出
                </Button>
            </Space>
            <DTEditForm
                configs={configs}
                layout={{
                    mode: "appoint",
                    colNum: 4,
                }}
                ref={ref}
                totalMode={mode}
            />
            <div>
                <DetailBodyForm
                    detail={detail}
                    showItem={item => {
                        console.log("item:", item);
                        if (item) {
                            if (item.productId === ref.current.form.getFieldValue("productId")) {
                                ref.current.form.resetFields();
                            } else {
                                if (item.licenceValidityDate) {
                                    item.licenceValidityDate = moment(item.licenceValidityDate);
                                }
                                ref.current.form.setFieldsValue(item);
                            }
                        } else {
                            // ref.current.setDetail({})
                            ref.current.form.resetFields();
                        }
                    }}
                />
            </div>
        </div>
    );
};
