import React, { useEffect, useRef } from "react";
import { Modal } from "antd";
import { Space, Button, Input, Select, Table, Form } from "antd";
import { DTEditForm, DTEditFormRefs, DTEditFormConfigs, DTEditFormItemProps } from "@dt/components";

import { EditOutlined, SearchOutlined, RedoOutlined } from "@ant-design/icons";
// import { ModelUnitConsumptionConfigs } from "../configs";
import { DDYObject, lib } from "react-single-app";
import moment from "moment";
export default ({ open, closeFn, row, mode, updateBody }) => {
    const ref = useRef<DTEditFormRefs>({} as DTEditFormRefs);
    const dataCache = useRef<DDYObject>({});
    const ModelUnitConsumptionConfigs: DTEditFormItemProps[] = [
        {
            type: "SELECT",
            fProps: {
                label: "成品申报序号",
                name: "endprdId",
                rules: [
                    {
                        required: true,
                        message: "请选择成品申报序号",
                    },
                ],
            },
            list: [],
            dataUrl: "/ccs/bizDeclareForm/listEndprdSeqById",
            defaultParams: { id: lib.getParam("id") },
            listItemLabelKey: "goodsName",
            // selectShowMode: "together",
        },
        {
            type: "SELECT",
            fProps: {
                label: "料件申报序号",
                name: "mtpckId",
                rules: [
                    {
                        required: true,
                        message: "请选择料件申报序号",
                    },
                ],
            },
            list: [],
            dataUrl: "/ccs/bizDeclareForm/listMtpckSeqById",
            // dataUrl: "/ccs/bizDeclareForm/listMtpckSeqById",
            defaultParams: { id: lib.getParam("id") },
            listItemLabelKey: "goodsName",
            // selectShowMode: "together",
        },
        {
            type: "INPUT",
            fProps: {
                label: "净耗",
                name: "netConsumption",
                rules: [
                    {
                        required: true,
                        message: "请填写净耗",
                    },
                    {
                        pattern: /^[1-9]\d{0,2}$/,
                        message: "仅能输入大于0的整数，不超过3位",
                    },
                ],
            },
        },
        {
            type: "INPUT",
            fProps: {
                label: "损耗率",
                name: "consumptionRate",
                rules: [
                    {
                        required: true,
                        message: "请填写损耗率",
                    },
                    {
                        pattern: /^(0?\.\d{1,2}|0)$/,
                        message: "仅能输入百分比小数，小数点后2位",
                    },
                ],
            },
        },
    ];
    const configs: DTEditFormConfigs = ModelUnitConsumptionConfigs;

    const ok = () => {
        ref.current.form.validateFields().then(res => {
            console.log("res:", res);
            res.editMark = "3";
            res.editMarkDesc = "增加";
            res = { ...dataCache.current, ...res };
            updateBody({ values: res, type: mode, index: row?.index });
            dataCache.current = {};
            closeFn();
        });
    };

    useEffect(() => {
        if (open) {
            ref.current.mergeDetail({ ...row });
            dataCache.current = row || {};
            // if(mode !== "edit" )
            if (mode === "add") {
                ref.current.form.resetFields();
                ref.current.form.setFieldsValue({ consumptionRate: "0" });
            } else {
                ref.current.mergeDetail({ ...row });
                dataCache.current = row || {};
            }
        }
    }, [open, row]);

    return (
        <>
            <Modal
                title={mode === "edit" ? "编辑" : "新增"}
                open={open}
                // width={1300}
                onCancel={() => {
                    dataCache.current = {};
                    closeFn(false);
                }}
                onOk={() => {
                    ok();
                }}>
                <DTEditForm
                    ref={ref}
                    configs={configs}
                    layout={{
                        mode: "appoint",
                        colNum: 1,
                    }}
                    onChange={(name, value, selects) => {
                        console.log(name, value, selects[0]);
                        if (name === "endprdId") {
                            // ref.current.form.setFieldValue('endprdSeqNo', selects[0].seqNo)
                            // ref.current.form.setFieldValue('endprdGoodsName', selects[0].goodsName)
                            dataCache.current.endprdSeqNo = selects[0].seqNo;
                            dataCache.current.endprdGoodsName = selects[0].goodsName.split("-")[1];
                        }
                        if (name === "mtpckId") {
                            dataCache.current.mtpckSeqNo = selects[0].seqNo;
                            dataCache.current.mtpckGoodsName = selects[0].goodsName.split("-")[1];
                        }
                    }}
                />
            </Modal>
        </>
    );
};
