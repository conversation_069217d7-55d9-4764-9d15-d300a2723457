import { Tabs } from "antd";
import React, { useEffect, useState } from "react";
import DetailHead from "./components/detail-head";
import DetailBody from "./components/detail-body";
import DetailUnitConsumption from "./components/detail-unit-consumption";
import { DDYObject, lib } from "react-single-app";
import DetailLog from "./components/detail-log";
export default () => {
    const [detail, setDetail] = useState<DDYObject>({});
    const getDetail = () => {
        lib.request({
            url: "/ccs/bizDeclareForm/detail",
            data: {
                id: lib.getParam("id"),
            },
            success(data) {
                setDetail(data);
            },
        });
    };

    // const
    const coms = {
        0: <DetailHead detail={detail} load={getDetail} />,
        1: <DetailBody detail={detail} />,
        2: <DetailUnitConsumption detail={detail} />,
        3: <DetailLog />,
    };

    useEffect(() => {
        getDetail();
    }, []);
    return (
        <div style={{ padding: "24px" }}>
            <h2>{detail?.statusDesc}</h2>

            <Tabs
                defaultActiveKey="0"
                destroyInactiveTabPane={true}
                items={["申报表表头", "申报表表体", "单耗", "操作日志"].map((text, i) => {
                    const id = String(i + 1);
                    return {
                        key: id,
                        label: `${text}`,
                        children: coms[i],
                        // icon: <Icon />,
                    };
                })}
            />
        </div>
    );
};
