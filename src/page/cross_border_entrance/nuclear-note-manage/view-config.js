export const nuclearNoteManageBatchManualData = () => {
    return {
        baseInfo: {
            children: [
                {
                    label: "手动操作原因",
                    editEnable: true,
                    type: "single-select",
                    name: "reason",
                    hidden: true,
                    rules: [
                        {
                            required: true,
                            message: "请选择手动操作原因!",
                        },
                    ],
                },
                {
                    label: "核注单状态",
                    editEnable: false,
                    name: "status",
                    type: "radioSelect",
                    from: "/ccs/modify/status/list",
                    rules: [
                        {
                            required: false,
                            message: "请选择核注单状态!",
                        },
                    ],
                },
            ],
            label: "",
            name: "baseInfo",
            isGroup: true,
            className: "nuclear-note-manage batch-manual-processing-modal",
        },
    };
};
