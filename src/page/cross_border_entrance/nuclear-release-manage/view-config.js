export const nuclearReleaseManageBatchManualData = () => {
    return {
        baseInfo: {
            children: [
                {
                    label: "手动操作原因",
                    editEnable: true,
                    type: "single-select",
                    name: "reason",
                    hidden: true,
                    rules: [
                        {
                            required: true,
                            message: "请选择手动操作原因!",
                        },
                    ],
                },
                {
                    label: "核放单状态",
                    editEnable: false,
                    name: "status",
                    type: "radioSelect",
                    from: "/ccs/modify/status/list",
                    rules: [
                        {
                            required: true,
                            message: "请选择核放单状态!",
                        },
                    ],
                },
            ],
            label: "",
            name: "baseInfo",
            isGroup: true,
            className: "nuclear-note-manage batch-manual-processing-modal",
        },
    };
};

export class NuclearReleaseManageBatchManualData {}
