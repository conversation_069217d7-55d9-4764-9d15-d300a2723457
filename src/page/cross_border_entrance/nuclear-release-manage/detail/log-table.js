import React, { useEffect, useState } from "react";
import { Table, Button, Modal } from "antd";
import { lib } from "react-single-app";
import BaowenBtn from "../../customs-clearance-detail/components/baowen-btn";

export default () => {
    const [data, setData] = useState([]);
    const columns = [
        {
            title: "核放单状态",
            dataIndex: "statusDesc",
        },
        {
            title: "日志描述",
            dataIndex: "logInfo",
        },
        {
            title: "操作时间",
            dataIndex: "createTime",
        },
        {
            title: "操作人",
            dataIndex: "operator",
        },
        {
            title: "操作",
            // dataIndex: "requestMessage",
            render: (val, row, index) => {
                if (!row.callbackDetail) return null;
                return <BaowenBtn content={row.callbackDetail} />;
            },
        },
    ];
    const getData = () => {
        lib.request({
            url: "/ccs/checklist/listTrackLog",
            data: {
                checklistId: Number(lib.getParam("id")),
            },
            success(data) {
                setData(data);
            },
        });
    };
    useEffect(() => {
        getData();
    }, []);
    return <Table dataSource={data} columns={columns} />;
};
