import React, { Fragment, useState, useRef, useEffect } from "react";
import { ConfigFormCenter, lib, SearchList, getConfigDataUtils } from "react-single-app";
import { Button, Modal, Space, message } from "antd";
import axios from "axios";
import { bindsNuclearReleaseData } from "./view-config";
//核放单表体
export default class NuclearReleaseManageTable extends SearchList {
    constructor(props) {
        super(props);
        this.state.showBindNuclearNoteModal = false;
    }

    getConfig() {
        return axios.get(getConfigDataUtils.getDataUrlByDataId(372)).then(e => {
            let config = e.data.data;
            config.page.importDisabled = this.props.disableEdit;
            config.page.exportDisabled = this.props.disableEdit;
            return config;
        });
    }

    lineNo(row, index) {
        return index + 1;
    }

    renderLeftOperation() {
        let disabled = this.state.bindType !== 4 || this.props.disableEdit;
        return (
            <>
                <Button
                    type="primary"
                    onClick={() =>
                        this.setState({
                            showBindNuclearNoteModal: true,
                        })
                    }
                    disabled={disabled}>
                    新增
                </Button>
            </>
        );
    }

    myOperation(row) {
        return (
            <>
                <Button
                    disabled={this.props.disableEdit}
                    size={"small"}
                    type={"link"}
                    onClick={() => this.editHandle(row)}>
                    编辑
                </Button>
                <Button
                    disabled={this.props.disableEdit}
                    size={"small"}
                    type={"link"}
                    onClick={() => this.deleteHandle(row)}>
                    删除
                </Button>
            </>
        );
    }

    deleteHandle(row) {
        let modal = Modal.confirm({
            title: "提示",
            content: "确定删除吗？",
            onOk: () => {
                lib.request({
                    url: "/ccs/checklist/deleteChecklistItem",
                    data: { id: row.id },
                    needMask: true,
                    success: res => {
                        message.success("删除成功");
                        this.props.refresh();
                        modal.destroy();
                    },
                });
            },
            onCancel: () => modal.destroy(),
        });
    }

    editHandle(row) {
        this.setState({
            checklistItemId: row.id,
            showBindNuclearNoteModal: true,
        });
    }

    renderModal() {
        return (
            <Fragment>
                {this.state.showBindNuclearNoteModal && (
                    <BindNuclearNote
                        checklistItemId={this.state.checklistItemId}
                        detailData={this.props.detailData}
                        showBindNuclearNoteModal={this.state.showBindNuclearNoteModal}
                        dialogClose={success => {
                            this.setState(prevState => {
                                prevState.showBindNuclearNoteModal = false;
                                delete prevState.checklistItemId;
                                return prevState;
                            });
                            if (success) {
                                this.props.refresh();
                            }
                        }}
                    />
                )}
            </Fragment>
        );
    }

    beforeImport() {
        let { detailData } = this.props;
        let checklistId = detailData.checklist.id;
        let checklistSn = detailData.checklist.sn;
        let endorsementVOList = detailData.endorsementVOList;
        let enableImport = false;
        let endorsementId;
        if (endorsementVOList && endorsementVOList.length > 0) {
            if (detailData.checklist.bindType === 4) {
                enableImport = true;
                endorsementId = endorsementVOList[0].id;
            } else {
                message.error("绑定类型非一票多车不能导入!");
            }
        } else {
            message.error("未关联核注单不能导入!");
        }
        return {
            disableImport: !enableImport,
            importExtendParam: `checklistId=${checklistId}&checklistSn=${checklistSn}&endorsementId=${endorsementId}`,
        };
    }

    componentWillUpdate(nextProps, nextState) {
        let detailData = nextProps.detailData;
        if (detailData.checklist) {
            let bindType = detailData.checklist.bindType;
            let disabled = bindType !== 4;
            let { config } = nextState;
            if (config) {
                config.page.importDisabled = disabled;
                config.page.exportDisabled = disabled;
            }
            nextState.config = config;
        }
    }

    componentWillReceiveProps(nextProps) {
        let bindType = nextProps.detailData.checklist.bindType;
        this.setState({
            bindType: bindType,
            dataList: nextProps.detailData.checklistItemList,
            _loading: false,
        });
    }

    load(needMask) {}
}

/**
 * 新增核放单弹框
 * @param detailData
 * @param checklistItemId
 * @param showBindNuclearNoteModal
 * @param dialogClose
 * @constructor
 */
function BindNuclearNote({ detailData, checklistItemId, showBindNuclearNoteModal, dialogClose }) {
    const ref = useRef();
    const dataRef = useRef();

    useEffect(() => {
        if (checklistItemId) {
            lib.request({
                url: "/ccs/checklist/viewChecklistItem",
                data: { id: checklistItemId },
                needMask: true,
                success: data => {
                    dataRef.current = data;
                    // let totalWeight = calculationTotalWeight(data.declareUnitQfy, data.grossWeight, data.netWeight);
                    bindConfigData.baseInfo.children[3].extend = data.allDeclareUnitQfy;
                    ref.current.setMergeDetail(Object.assign(data, {}));
                },
            });
        }
    }, [checklistItemId]);
    const bindConfigData = bindsNuclearReleaseData({
        onClick: item => {
            switch (item.name) {
                case "serialNumber":
                    let endorsementId,
                        checklistId = detailData.checklist.id;
                    if (detailData.endorsementVOList && detailData.endorsementVOList.length > 0) {
                        endorsementId = detailData.endorsementVOList[0].id;
                    } else {
                        message.error("新增核放单表体失败，未绑定核注清单");
                        return;
                    }
                    let formFieldsValue = ref.current.getFormFiled("serialNumber");
                    lib.request({
                        url: "/ccs/checklist/checklistAssociateItem",
                        data: {
                            serialNumber: Number(formFieldsValue),
                            checklistId: checklistId,
                            endorsementId: endorsementId,
                        },
                        success: data => {
                            bindConfigData.baseInfo.children[3].extend = data.allDeclareUnitQfy;
                            dataRef.current = data;
                            ref.current.setMergeDetail(data);
                        },
                    });
                    break;
                default:
                    break;
            }
        },
    });

    const handleCancel = () => {
        dialogClose();
    };
    const handleOk = () => {
        ref.current.submitForm();
    };

    function calculationTotalWeight(qty, grossWeight, netWeight) {
        let totalGrossWeight, totalNetWeight;
        if (grossWeight) {
            totalGrossWeight = qty * grossWeight;
        }
        if (netWeight) {
            totalNetWeight = qty * netWeight;
        }
        return { totalGrossWeight, totalNetWeight };
    }

    const onSubmitSuccess = () => {
        dialogClose(true);
    };

    const onSinglesSelectChange = desc => {
        switch (desc.name) {
            case "declareUnitQfy":
                let { grossWeight, netWeight } = dataRef.current;
                let qty = Number(desc.value);
                let totalWeight = calculationTotalWeight(qty, grossWeight, netWeight);
                ref.current.setMergeDetail(Object.assign({ declareUnitQfy: qty }, totalWeight));
                break;
            default:
                break;
        }
    };
    const beforeSubmit = value => {
        let { checklist, endorsementVOList } = detailData;
        let { endorsementItemId, customsBookItemId, goodsRecordId, checklistItemId, allDeclareUnitQfy } =
            dataRef.current;
        let endorsementId;
        if (endorsementVOList && endorsementVOList.length > 0) {
            endorsementId = endorsementVOList[0].id;
        }
        const result = {
            checklistItemId: checklistItemId,
            checklistId: checklist.id,
            endorsementId: endorsementId,
            endorsementItemId: endorsementItemId,
            customsBookItemId: customsBookItemId,
            goodsRecordId: goodsRecordId,
            allDeclareUnitQfy: allDeclareUnitQfy,
            ...value,
        };
        for (let i in result) {
            if (result[i] === "") {
                delete result[i];
            }
        }
        return result;
    };
    return (
        <Modal
            destroyOnClose
            onCancel={handleCancel}
            onOk={handleOk}
            title={"新增核放单表体"}
            open={showBindNuclearNoteModal}>
            <ConfigFormCenter
                ref={ref}
                onSinglesSelectChange={onSinglesSelectChange}
                confData={bindConfigData}
                submitUrl={"/ccs/checklist/saveChecklistItem"}
                onSubmitSuccess={onSubmitSuccess}
                beforeSubmit={beforeSubmit}
            />
        </Modal>
    );
}
