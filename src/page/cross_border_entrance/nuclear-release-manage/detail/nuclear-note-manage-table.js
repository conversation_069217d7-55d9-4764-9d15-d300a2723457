import React from "react";

import { lib, SearchList, getConfigDataUtils } from "react-single-app";
import axios from "axios";

//核注表体
export default class NuclearNoteManageTable extends SearchList {
    constructor(props) {
        super(props);
        console.log("NuclearNoteManageTable", this.props.endorsementVOList);
    }

    getConfig() {
        return axios.get(getConfigDataUtils.getDataUrlByDataId(373)).then(e => e.data.data);
    }
    myOperation() {}
    componentWillReceiveProps(nextProps) {
        this.setState({
            dataList: nextProps.endorsementVOList,
            _loading: false,
        });
    }
    load(needMask) {}
}
