import React, { useState, useEffect, createRef } from "react";
// import './shop-good.less';
import { ConfigCenter, lib, SearchList, getConfigDataUtils, HOC, event } from "react-single-app";
import { Checkbox, Button, Modal, Input, Select, Form, Tabs, Table, message, InputNumber, Tooltip, Space } from "antd";
const TabPane = Tabs.TabPane;
const FormItem = Form.Item;
const Option = Select.Option;
import axios from "axios";
import BatchManualProcessingModal from "@/components/batch-manual-processing-modal";
import { nuclearReleaseManageBatchManualData } from "./view-config";

class EditModalCom extends React.PureComponent {
    constructor(props) {
        super(props);
        this.formRef = React.createRef();
        this.state = {};
    }

    render() {
        let {
            editModalVisible,
            editRow,
            onOk,
            onCancel,
            form,
            enterpriseList,
            checkList,
            onSelect,
            onDeselect,
            nuclearNoteModalVisible,
            selectRows,
            onCancel2,
            theItem,
            columns,
            selectedRowKeys,
            onChange,
        } = this.props;
        let formItemLayout = {
            labelCol: { span: 6 },
            wrapperCol: { span: 14 },
        };
        return (
            <React.Fragment>
                <Modal
                    zIndex={120}
                    title="编辑核放单"
                    open={editModalVisible}
                    destroyOnClose={true}
                    onOk={() => {
                        this.formRef.current
                            .validateFields()
                            .then(values => {
                                onOk(values);
                            })
                            .catch(err => {
                                console.log(err);
                            });
                    }}
                    onCancel={() => onCancel()}>
                    <Form initialValues={editRow} ref={this.formRef}>
                        <FormItem {...formItemLayout} label="企业内部编号">
                            {editRow && editRow.sn}
                        </FormItem>
                        <FormItem
                            {...formItemLayout}
                            label="车辆信息"
                            name="licensePlate"
                            rules={[{ required: true, message: "请选择车辆信息" }]}>
                            <Input />
                        </FormItem>
                        <FormItem {...formItemLayout} label="IC卡号(电子车牌)" name="vehicleIcNo">
                            <Input />
                        </FormItem>
                        <FormItem
                            {...formItemLayout}
                            label="清关企业"
                            name="declareCompanyID"
                            rules={[{ required: true, message: "请选择清关企业" }]}>
                            <Select>
                                {enterpriseList.map(item => {
                                    return (
                                        <Option value={item.id} key={item.id}>
                                            {item.name}
                                        </Option>
                                    );
                                })}
                            </Select>
                        </FormItem>
                        <FormItem
                            {...formItemLayout}
                            label="申请人"
                            name="applicant"
                            rules={[{ required: true, message: "请输入申请人" }]}>
                            <Input />
                        </FormItem>
                        {editRow && editRow.bindType !== "空车核放" && editRow.typeDesc !== "两步申报" && (
                            <FormItem
                                {...formItemLayout}
                                label="关联核注清单"
                                name="endorsementSn"
                                rules={[{ required: true, message: "请选择关联核注清单" }]}>
                                <Select
                                    mode="multiple"
                                    showSearch
                                    onSelect={e => onSelect(e)}
                                    onDeselect={e => onDeselect(e)}
                                    filterOption={(input, option) => {
                                        return option.props.children.toLowerCase().indexOf(input.toLowerCase()) >= 0;
                                    }}>
                                    {checkList.map((item, index) => {
                                        return (
                                            <Option value={item?.id} key={index}>
                                                {item?.sn}
                                            </Option>
                                        );
                                    })}
                                </Select>
                            </FormItem>
                        )}
                        {editRow && editRow.typeDesc === "两步申报" && (
                            <FormItem
                                {...formItemLayout}
                                label="报关单号"
                                name="declareOrderNo"
                                rules={[{ required: true, message: "请输入报关单号" }]}>
                                <Input maxLength={80} />
                            </FormItem>
                        )}
                    </Form>
                </Modal>
                <Modal
                    zIndex={121}
                    title="一票多车核注清单"
                    open={nuclearNoteModalVisible}
                    onOk={() => {
                        if (selectedRowKeys.length === 0) {
                            let list = this.formRef.current.getFieldValue("endorsementSn");
                            list.map((item, index) => {
                                if (item === theItem.id) {
                                    list.splice(index, 1);
                                }
                            });
                            this.formRef.current.setFieldsValue({ endorsementSn: list });
                        }
                        selectRows();
                    }}
                    destroyOnClose={true}
                    onCancel={() => {
                        let list = this.formRef.current.getFieldValue("endorsementSn");
                        list.map((item, index) => {
                            if (item === theItem.id) {
                                list.splice(index, 1);
                            }
                        });
                        this.formRef.current.setFieldsValue({ endorsementSn: list });
                        onCancel2();
                    }}>
                    <Table
                        dataSource={theItem && theItem.itemList}
                        columns={columns}
                        rowSelection={{
                            selectedRowKeys: selectedRowKeys,
                            onChange: onChange,
                        }}
                        rowKey="id"
                    />
                </Modal>
            </React.Fragment>
        );
    }
}

@HOC.mapAuthButtonsToState({})
class App extends SearchList {
    constructor(props) {
        super(props);
        this.addFormRef = createRef();
        this.state.modalTitle = "新增核放单";
        // 编辑/新建url
        this.state.upUrl = "/ccs/checklist/create";
        this.state.configList = [
            {
                type: "SELECT",
                labelName: "核放单类型",
                labelKey: "type",
                list: [],
                ccs: "/ccs/checklist/listType",
                required: true,
                onChange: e => {
                    //  一线入区:1；二线入区:2；空车入区:3；二线出区:4；空车出区:5；两步申报:6
                    this.state.configList.map(item => {
                        if (item.labelKey === "declareOrderNo") {
                            item.hide = e !== 6;
                        }
                    });
                    this.setState(this.state);
                },
            },
            {
                type: "SELECT",
                labelName: "绑定类型",
                labelKey: "bindType",
                list: [],
                ccs: "/ccs/checklist/bindType",
                required: true,
            },
            {
                type: "SELECT",
                labelName: "清关企业",
                labelKey: "declareCompanyID",
                list: [],
                ccs: "/ccs/company/listWithSBQY",
                required: true,
            },
            { type: "INPUT", labelName: "申请人", labelKey: "applicant", required: true },
            { type: "INPUT", labelName: "报关单号", labelKey: "declareOrderNo", required: true, hide: true },
            {
                type: "SELECT",
                labelName: "车辆信息",
                labelKey: "licensePlate",
                required: true,
                list: [],
                ccs: "/ccs/checklist/listVehicle",
                onSearch: e => {
                    let carsList = this.state.configList[5].list;
                    if (e) {
                        console.log(e);
                        carsList.map((item, index) => {
                            if (item.isAdd) {
                                carsList.splice(index, 1);
                            }
                        });
                        carsList.push({
                            id: +new Date() + "_" + e,
                            name: e,
                            isAdd: true,
                        });
                        this.state.configList[5].list = carsList;
                        this.setState(this.state);
                    }
                },
                onSelect: e => {
                    e = String(e);
                    if (e.indexOf("_") !== -1) {
                    } else {
                        lib.request({
                            url: "/ccs/checklist/getVehicleById",
                            data: {
                                id: Number(e),
                            },
                            needMask: true,
                            success: res => {
                                this.waitEnd();
                                this.addFormRef.current.setFields([
                                    // {
                                    //     name: "carWeight",
                                    //     value: res.vehicleWeight,
                                    // },
                                    {
                                        name: "licenseFrame",
                                        value: res.vehicleFrameNo,
                                    },
                                    // {
                                    //     name: "frameWeight",
                                    //     value: res.vehicleFrameWeight,
                                    // },
                                ]);
                            },
                        });
                    }
                },
            },
            {
                type: "INPUT",
                labelName: "车辆自重(KG)",
                labelKey: "carWeight",
                required: true,
                hidden: false,
                rules: [
                    {
                        required: true,
                        // message: "请输入货物总毛重!",
                    },
                    {
                        validator(_, value) {
                            if (!value) return Promise.resolve();
                            if (value <= 0) return Promise.reject("请输入大于零的数");
                            if (!/^(\d{1,14}(\.\d{0,5})?|100000000000000)$/.test(value)) {
                                return Promise.reject("最多5位小数，14位整数");
                            }
                            return Promise.resolve();
                        },
                    },
                ],
            },
            {
                type: "INPUT",
                labelName: "车架号",
                labelKey: "licenseFrame",
                required: true,
                hidden: false,
                maxLength: 10,
            },
            {
                type: "INPUT",
                labelName: "车架重(KG)",
                labelKey: "frameWeight",
                required: true,
                hidden: false,
                rules: [
                    {
                        required: true,
                        // message: "请输入货物总毛重!",
                    },
                    {
                        validator(_, value) {
                            if (!value) return Promise.resolve();
                            if (value <= 0) return Promise.reject("请输入大于零的数");
                            if (!/^(\d{1,14}(\.\d{0,5})?|100000000000000)$/.test(value)) {
                                return Promise.reject("最多5位小数，14位整数");
                            }
                            return Promise.resolve();
                        },
                    },
                ],
            },
        ];

        this.state.visible1 = false;
        this.state.visible2 = false;
        this.state.editModalVisible = false;
        this.state.nuclearNoteModalVisible = false;
        this.state.nuclearNoteModalVisible2 = false;
        this.state.value = "";
        this.state.enterpriseList = [];
        this.state.checkList = [];
        this.state.watchData = [];
        this.state.endorsementItemIdList = {};
        this.state.dist = {};
        this.state.watchRow = {};
        this.onSearchReset = this.onSearchReset.bind(this);
        this.tagSelectRef = React.createRef();
    }
    componentWillUnmount() {
        event.off("onSearchReset", this.onSearchReset);
    }
    onSearchReset() {
        this.setState({
            status: "",
        });
    }
    getConfig() {
        return axios.get(getConfigDataUtils.getDataUrlByDataId(219)).then(res => res.data.data);
    }
    load(_, toTop) {
        super.load(_, toTop);
        this.getStatusCount();
    }
    componentDidMount() {
        this.getStatusCount();
        // 获取清关企业列表
        lib.request({
            url: "/ccs/company/listWithSBQY",
            success: res => {
                if (res) {
                    this.setState({
                        enterpriseList: res,
                    });
                }
            },
        });
        lib.request({
            url: "/ccs/checklist/check-list-type-map-bussiness",
            success: res => {
                if (res) {
                    let result = res.result;
                    let maps = result
                            .slice(1, -1)
                            .split("]")
                            .filter(item => item && item.trim())
                            .map(item => {
                                if (item.startsWith(",")) {
                                    item = item.slice(1);
                                }
                                return (item += "]");
                            }),
                        dist = {};
                    maps.map(item => {
                        let items = item.split(":");
                        dist[items[0]] = items[1]
                            .slice(1, -1)
                            .split(",")
                            .map(item => item.slice(1, -1));
                    });
                    this.setState({
                        dist,
                    });
                }
            },
        });
        this.fetchCheckList();
        console.log("onSearchReset:");
        event.on("onSearchReset", this.onSearchReset);
    }

    // 关联单证号
    renderSns(row) {
        let arr = row.endorsementSns?.split("/") || [];
        return (
            <React.Fragment>
                {arr.map(item => (
                    <p style={{ lineHeight: "24px", marginBottom: 0 }} key={item}>
                        {item}
                    </p>
                ))}
            </React.Fragment>
        );
    }

    wait(time) {
        var div = document.getElementById("body-wait");
        if (!div) {
            div = document.createElement("div");
            div.className = "wait";
            div.id = "body-wait";
            div.innerHTML = `
            <div class='mask' style="display:flex;justify-content:center;align-items: center;background: rgba(256,256,256,0.5);opacity: 1;">
            <div class="ant-spin ant-spin-lg ant-spin-spinning center">
                <span class="ant-spin-dot ant-spin-dot-spin">
                    <i class="ant-spin-dot-item"></i>
                    <i class="ant-spin-dot-item"></i>
                    <i class="ant-spin-dot-item"></i>
                    <i class="ant-spin-dot-item"></i>
                </span>
                <div>正在处理中，请稍等</>
            </div>
        </div>
            `;
            document.getElementsByTagName("body")[0].append(div);
        }
        if (time) {
            setTimeout(lib.waitEnd, time);
        }
    }

    waitEnd() {
        if (document.getElementById("body-wait")) {
            document.getElementById("body-wait").remove();
        }
    }

    // 获取关联核注清单列表
    fetchCheckList() {
        this.wait();
        lib.request({
            url: "/ccs/endorsement/effective/book/auth/listForChecklist",
            needMask: true,
            success: res => {
                this.waitEnd();
                this.setState({
                    checkList: res,
                });
            },
        });
    }

    renderCustomsStatusDesc(row) {
        return <Tooltip title={row.informationDesc}>{row.customsStatusDesc}</Tooltip>;
    }

    onSelect(e) {
        let theItem = {},
            { checkList } = this.state;
        checkList.map(item => {
            if (e === item.id) {
                theItem = item;
            }
        });
        if (theItem.allowCheckItem) {
            if (this.state.editRow && this.state.editRow.id) {
                this.setState({
                    theItem,
                    nuclearNoteModalVisible2: true,
                    selectedRowKeys: [],
                });
            } else {
                this.setState({
                    theItem,
                    nuclearNoteModalVisible: true,
                    selectedRowKeys: [],
                });
            }
        } else {
            let { endorsementItemIdList } = this.state,
                list = [];
            theItem.itemList.map(item => {
                list.push(item.id);
            });
            endorsementItemIdList[theItem.id] = list;
            this.setState({
                endorsementItemIdList,
            });
        }
    }

    onDeselect(e) {
        let theItem;
        this.state.checkList.map(item => {
            if (e === item.id) {
                theItem = item;
            }
        });
        let { endorsementItemIdList } = this.state;
        if (endorsementItemIdList[theItem.id]) {
            delete endorsementItemIdList[theItem.id];
        }
        this.setState({
            endorsementItemIdList,
        });
    }

    getStatusCount() {
        lib.request({
            url: "/ccs/checklist/countPagingStatus",
            data: {
                ...this.state.search,
            },
            success: data => {
                const statusCount = {};
                data.map(item => {
                    statusCount[item.status] = item.count;
                });
                this.setState({
                    statusCount,
                    statusList: data,
                });
            },
        });
    }

    renderOperationTopView() {
        const statusCount = this.state.statusCount || {};
        const statusList = this.state.statusList || [];
        return (
            <Tabs
                className="customs-clearance-manage-tabs"
                defaultActiveKey={""}
                activeKey={this.state.status}
                onChange={key => {
                    this.changeImmutable({
                        status: key,
                    });
                    this.setState({
                        status: key,
                    });
                }}>
                <TabPane tab={`全部`} key={""} />
                {/* <TabPane tab={`已创建(${statusCount["CREATED"]})`} key={"CREATED"} />
                <TabPane tab={`申报中(${statusCount["CREATED"]})`} key={"CREATED"} />
                <TabPane tab={`暂存中(${statusCount["ERFECT"]})`} key={"ERFECT"} />
                <TabPane tab={`已暂存(${statusCount["CONFIRMING"]})`} key={"CONFIRMING"} />
                <TabPane tab={`申报失败(${statusCount["AUDITED"]})`} key={"AUDITED"} />
                <TabPane tab={`已审批(${statusCount["ENDORSEMENT"]})`} key={"ENDORSEMENT"} />
                <TabPane tab={`已完成(${statusCount["SERVERING"]})`} key={"SERVERING"} />
                <TabPane tab={`作废申报中(${statusCount["FAILURE"]})`} key={"FAILURE"} />
                <TabPane tab={`已作废(${statusCount["COMPLETE"]})`} key={"COMPLETE"} /> */}
                {statusList.map((item, index) => (
                    <TabPane tab={`${item.statusDesc}(${item.count})`} key={item.status} />
                ))}
            </Tabs>
        );
    }
    getConfigList() {
        // 获取configList 里面的下拉数据
        let configList = this.state.configList;
        configList.map(item => {
            if (item.ccs) {
                lib.request({
                    url: item.ccs,
                    success: res => {
                        let list = res || [];
                        if (item.labelKey === "endorsementSns") {
                            list.map(item => {
                                item.name = item.sn;
                                item.value = item.id;
                            });
                            item.list = [];
                            item.lists = list;
                            this.setState({
                                checkList: list,
                            });
                        } else {
                            item.list = list;
                            item.lists = list;
                        }
                        this.setState({ configList });
                    },
                });
            }
        });
        this.setState({ configList });
    }

    editFunc(row) {
        lib.openPage(`/nuclear-release-manage-detail?id=${row.id}&page_title=编辑核放单`);
    }

    invalidFunc(row) {
        let modal = Modal.confirm({
            cancelText: "取消",
            okText: "确定",
            title: "提示",
            content: "确认作废该条数据吗？",
            onOk: () => this.batchOperation(row),
            onCancel: () => modal.destroy(),
        });
    }

    finishFunc(e, row) {
        e.stopPropagation();
        const modal = Modal.confirm({
            cancelText: "取消",
            okText: "确定",
            title: "请输入核放单号，手动审核",
            content: (
                <Input
                    onChange={e => {
                        this.setState({
                            realNo: e.target.value,
                        });
                    }}
                />
            ),
            onOk: res => {
                if (this.state.realNo) {
                    lib.request({
                        url: "/ccs/checklist/finish",
                        data: {
                            id: row.id,
                            realNo: this.state.realNo,
                        },
                        method: "POST",
                        needMask: true,
                        success: res => {
                            if (res.errorMessage) {
                                message.warning(res.errorMessage);
                            } else {
                                message.success("审核成功");
                                this.load(true);
                                modal.destroy();
                                this.setState({
                                    realNo: "",
                                });
                            }
                        },
                    });
                } else {
                    message.warning("请输入核放单号");
                }
            },
            onCancel: () => modal.destroy(),
        });
    }

    myOperation(row) {
        return (
            <React.Fragment>
                <Space style={{ flexWrap: "wrap" }}>
                    {row.allowLoadItem && (
                        <a className="link" onClick={() => this.watchModal(row)}>
                            查看
                        </a>
                    )}
                    {row.allowEdit && (
                        <a className="link" onClick={() => this.editFunc(row)}>
                            编辑
                        </a>
                    )}
                    {row.allowPush && (
                        <a className="link" onClick={() => this.pushRow(row)}>
                            推送
                        </a>
                    )}
                    {row.allowDiscard && (
                        <a className="link" onClick={() => this.invalidFunc(row)}>
                            作废
                        </a>
                    )}
                    {row.allowFinish && (
                        <a className="link" onClick={e => this.finishFunc(e, row)}>
                            手动审核
                        </a>
                    )}
                    {row.allowTemporaryStorage && (
                        <a className="link" onClick={e => this.templateStore(row)}>
                            暂存
                        </a>
                    )}
                </Space>
            </React.Fragment>
        );
    }

    templateStore(row) {
        lib.request({
            url: "/ccs/checklist/pushTemporaryStorage",
            data: {
                id: row.id,
            },
            needMask: true,
            success: res => {
                message.success("暂存操作成功");
                this.load();
            },
        });
    }

    // 查看表体信息
    watchModal(row) {
        lib.openPage(`/nuclear-release-manage-detail?id=${row.id}&disableEdit=true&page_title=核放单详情`);
        // if (row.typeDesc === "两步申报") {
        //     this.setState({
        //         watchRow: row,
        //         watchModalVisible2: true
        //     })
        //     return;
        // }
        // lib.request({
        //     url: "/ccs/checklist/loadEndorsementItem",
        //     data: {
        //         id: row.id
        //     },
        //     needMask: true,
        //     success: res => {
        //         if (res) {
        //             this.setState({
        //                 watchData: res,
        //                 watchModalVisible: true
        //             })
        //         }
        //     }
        // })
    }

    // 推送核放
    pushRow(row) {
        lib.request({
            url: `/ccs/checklist/retryException`,
            data: {
                id: row.id,
            },
            needMask: true,
            success: res => {
                message.success("推送成功");
                this.load(true);
            },
        });
    }

    renderRightOperation() {
        return (
            <React.Fragment>
                <Button
                    type="primary"
                    onClick={() => {
                        this.setState({
                            visible: true,
                            modalTitle: "新增核放单",
                            theItem: {},
                            endorsementItemIdList: {},
                        });
                        // this.fetchConfigListCheckList()
                        this.getConfigList();
                        // this.fetchCheckList()
                    }}>
                    新增核放单
                </Button>
            </React.Fragment>
        );
    }

    renderLeftOperation() {
        return (
            <React.Fragment>
                <Button
                    onClick={() => {
                        let list = this.getCheckedRows();
                        if (list.length) {
                            const modal = Modal.confirm({
                                cancelText: "取消",
                                okText: "确定",
                                content: "确定手动过卡吗?",
                                onOk: res => {
                                    modal.destroy();
                                    this.handleCard();
                                },
                                onCancel: res => modal.destroy(),
                            });
                        } else {
                            message.warning("请选择数据");
                        }
                    }}>
                    手动过卡
                </Button>
                {/* <Button type="primary" onClick={() => this.push()}>推送</Button> */}
                {this.state.buttons.includes("batch-manual-processing") && (
                    <Button onClick={() => this.batchManual()}>批量手动操作</Button>
                )}
            </React.Fragment>
        );
    }

    getCheckedRows() {
        return this.state.selectedRows;
    }

    handleCard() {
        let list = this.getCheckedRows();
        if (list.length) {
            let ids = list.reduce((prev, curr) => [...prev, curr.id], []).join(",");
            lib.request({
                url: "/ccs/checklist/handler-check",
                data: {
                    ids,
                },
                needMask: true,
                success: res => {
                    if (res.errorMessage) {
                        message.error(res.errorMessage);
                        this.load(true);
                    } else {
                        message.success("操作成功");
                        this.load(true);
                    }
                },
                fail: err => {
                    this.load(true);
                },
            });
        } else {
            message.warning("请选择数据");
        }
    }

    // 批量推送核放
    push() {
        let list = this.getCheckedRows(),
            idList = [];
        if (list.length === 0) {
            message.warning("请至少选择一条数据");
            return;
        }
        list.map(item => {
            idList.push(item.id);
        });
        lib.request({
            url: "/ccs/checklist/push",
            data: {
                idList,
            },
            method: "POST",
            needMask: true,
            success: res => {
                if (res) {
                    message.success("推送核放成功");
                    this.load(true);
                }
            },
        });
    }

    batchOperation(row) {
        lib.request({
            url: "/ccs/checklist/discard",
            method: "POST",
            data: {
                id: row.id,
            },
            needMask: true,
            success: res => {
                if (res) {
                    message.success("作废成功");
                    this.load(true);
                }
            },
        });
    }

    handleOk() {
        // this.props.form.validateFields((err, values) => {
        this.addFormRef.current
            .validateFields()
            .then(values => {
                let list = [];
                for (let i in this.state.endorsementItemIdList) {
                    list = list.concat(this.state.endorsementItemIdList[i]);
                }
                values.endorsementItemIdList = list;
                //这里不知道为啥 delete 字段，bindType下拉选择无时该字段为0，
                // 如果不提交给后端，在详情会返回null，还必须bindType=0时提交的
                for (let i in values) {
                    if (!values[i] && i !== "bindType") {
                        delete values[i];
                    }
                }
                // values.carWeight && (values.carWeight = values.carWeight.toFixed(4));
                // values.frameWeight && (values.frameWeight = values.frameWeight.toFixed(4));
                // if (!values.frameWeight) {
                //     values.frameWeight = "0.0000";
                // }
                // if (!values.carWeight) {
                //     values.carWeight = "0.0000";
                // }
                values.licensePlate && (values.licensePlate = String(values.licensePlate));
                if (values.endorsementItemIdList?.length === 0) {
                    delete values.endorsementItemIdList;
                }
                if (values.licensePlate?.indexOf("_") !== -1) {
                    values.licensePlate = values.licensePlate.split("_")[1];
                } else {
                    this.state.configList[5].list.map(item => {
                        if (values.licensePlate === String(item.id)) {
                            values.licensePlate = item.name;
                        }
                    });
                }
                lib.request({
                    url: this.state.upUrl,
                    data: values,
                    method: "POST",
                    needMask: true,
                    success: res => {
                        this.setState({
                            visible: false,
                            endorsementItemIdList: {},
                            theItem: {},
                        });
                        message.success("新增成功");
                        this.load(true);
                    },
                });
            })
            .catch(err => {
                console.log(err);
            });
        // })
    }

    handleCancel() {
        this.setState({
            visible: false,
            endorsementItemIdList: {},
            theItem: {},
        });
    }

    // 一票多车核注清单弹窗 确认
    selectRows() {
        let { selectedRowKeys, endorsementItemIdList, theItem } = this.state;
        endorsementItemIdList[theItem.id] = selectedRowKeys;
        this.setState({
            endorsementItemIdList,
            nuclearNoteModalVisible: false,
            nuclearNoteModalVisible2: false,
            theItem: {},
        });
    }

    // 编辑弹窗 确认
    editSubmit(values) {
        let value = JSON.parse(JSON.stringify(values));
        value.declareCompanyID = Number(value.declareCompanyID);
        if (this.state.selectedRowKeys) {
            value.endorsementItemIdList = this.state.selectedRowKeys;
        }
        let endorsementItemIdList = [];
        for (let i in this.state.endorsementItemIdList) {
            endorsementItemIdList = endorsementItemIdList.concat(this.state.endorsementItemIdList[i]);
        }
        value.endorsementItemIdList = endorsementItemIdList;
        value.id = this.state.editRow.id;
        value.type = this.state.editRow.type;
        delete value.allowDiscard;
        delete value.allowEdit;
        delete value.allowLoadItem;
        delete value.allowPush;
        delete value.createAt;
        delete value.lastCustomsAt;
        delete value.endorsementSns;
        delete value.endorsementSn;
        lib.request({
            url: "/ccs/checklist/upset",
            method: "POST",
            data: value,
            needMask: true,
            success: res => {
                if (res) {
                    message.success("编辑成功");
                    this.load(true);
                    this.setState(
                        {
                            editModalVisible: false,
                            selectedRowKeys: [],
                            theItem: {},
                            editRow: {},
                            endorsementItemIdList: {},
                            checkList: [],
                        },
                        () => this.fetchCheckList(),
                    );
                }
            },
        });
    }

    batchManual() {
        let { selectedRows } = this.state;
        if (selectedRows.length === 0) {
            message.warning("请选择数据");
            return;
        }
        this.setState({
            batchManualProcessingModalVisible: true,
        });
    }

    renderStatus(row) {
        return <Tooltip title={row.informationDesc}>{row.customsStatusDesc}</Tooltip>;
    }

    okHandle() {
        let { selectedRowKeys, endorsementItemIdList, theItem } = this.state;
        endorsementItemIdList[theItem.id] = selectedRowKeys;
        // const { getFieldValue, setFieldsValue } = this.props.form;
        if (selectedRowKeys.length === 0) {
            let list = this.addFormRef.current.getFieldValue("endorsementSns");
            list.map((item, index) => {
                if (item === theItem.id) {
                    list.splice(index, 1);
                }
            });
            this.addFormRef.current.setFieldsValue({ endorsementSns: list });
        }
        this.setState({
            endorsementItemIdList,
            nuclearNoteModalVisible: false,
            theItem: {},
        });
    }

    cancelHandle() {
        let { theItem } = this.state;
        this.setState(
            {
                theItem: {},
                nuclearNoteModalVisible: false,
                selectedRowKeys: [],
            },
            () => {
                // const { getFieldValue, setFieldsValue } = this.props.form;
                let list = this.addFormRef.current.getFieldValue("endorsementSns");
                list.map((item, index) => {
                    if (item === theItem.id) {
                        list.splice(index, 1);
                    }
                });
                this.addFormRef.current.setFieldsValue({ endorsementSns: list });
            },
        );
    }

    renderModal() {
        let {
            modalData,
            importData,
            configList,
            enterpriseList,
            editRow,
            checkList,
            watchData,
            theItem,
            selectedRowKeys,
            editModalVisible,
            watchRow,
            watchModalVisible2,
        } = this.state;
        const { form } = this.props;
        modalData = modalData || {};
        importData = importData || {};
        const columns = [
            {
                title: "账册号",
                dataIndex: "accountBookNo",
            },
            {
                title: "序号",
                dataIndex: "goodsSeqNo",
            },
            {
                title: "料号",
                dataIndex: "productId",
            },
        ];
        let formItemLayout = {
            labelCol: { span: 6 },
            wrapperCol: { span: 14 },
        };
        return (
            <React.Fragment>
                {/* 一票多车核注清单弹窗 */}
                <Modal
                    zIndex={121}
                    title="一票多车核注清单"
                    open={this.state.nuclearNoteModalVisible}
                    onOk={() => this.okHandle()}
                    destroyOnClose={true}
                    onCancel={() => this.cancelHandle()}>
                    <Table
                        dataSource={theItem && theItem.itemList}
                        columns={columns}
                        rowSelection={{
                            selectedRowKeys: selectedRowKeys,
                            onChange: selectedRowKeys => {
                                if (selectedRowKeys.length == theItem.itemList.length && !theItem.allowCheckTable) {
                                    message.info("当前核注清单为第一次关联核放单，不能选择全部表体信息");
                                } else {
                                    this.setState({ selectedRowKeys });
                                }
                            },
                        }}
                        rowKey="id"
                    />
                </Modal>
                {/* 新增弹窗 */}
                <Modal
                    zIndex={120}
                    cancelText="取消"
                    okText="确定"
                    title={this.state.modalTitle}
                    open={this.state.visible}
                    onOk={() => this.handleOk()}
                    onCancel={() => this.handleCancel()}
                    destroyOnClose={true}>
                    <Form ref={this.addFormRef}>
                        {configList.map((item, index) => {
                            return <React.Fragment key={index}>{this.renderFormItem(item)}</React.Fragment>;
                        })}
                    </Form>
                </Modal>
                {/* 编辑弹窗 */}
                <EditModalCom
                    {...{
                        onOk: this.editSubmit.bind(this),
                        onCancel: () => {
                            this.setState(
                                {
                                    editModalVisible: false,
                                    checkList: [],
                                    theItem: {},
                                    endorsementItemIdList: {},
                                },
                                () => this.fetchCheckList(),
                            );
                        },
                        editModalVisible: editModalVisible,
                        editRow,
                        enterpriseList,
                        checkList,
                        onSelect: this.onSelect.bind(this),
                        onDeselect: this.onDeselect.bind(this),
                        nuclearNoteModalVisible: this.state.nuclearNoteModalVisible2,
                        selectRows: () => this.selectRows(),
                        onCancel2: () => {
                            this.setState({
                                theItem: {},
                                nuclearNoteModalVisible2: false,
                                selectedRowKeys: [],
                            });
                        },
                        theItem,
                        columns,
                        selectedRowKeys,
                        onChange: selectedRowKeys => {
                            if (selectedRowKeys.length == theItem.itemList.length && !theItem.allowCheckTable) {
                                message.info("当前核注清单为第一次关联核放单，不能选择全部表体信息");
                            } else {
                                this.setState({ selectedRowKeys });
                            }
                        },
                    }}
                />
                {/* 查看弹窗 */}
                <Modal
                    title="查看表体"
                    open={this.state.watchModalVisible}
                    onOk={() => this.setState({ watchModalVisible: false })}
                    onCancel={() => this.setState({ watchModalVisible: false })}
                    destroyOnClose={true}>
                    <Table dataSource={watchData} columns={columns} rowKe="id" pagination={false} />
                </Modal>
                {/* 查看两步申报弹窗 */}
                <Modal
                    title="查看"
                    open={watchModalVisible2}
                    onOk={() => this.setState({ watchModalVisible2: false })}
                    onCancel={() => this.setState({ watchModalVisible2: false })}
                    destroyOnClose={true}>
                    <FormItem {...formItemLayout} label="账册号">
                        {watchRow.sn}
                    </FormItem>
                    <FormItem {...formItemLayout} label="报关单号">
                        {watchRow.declareOrderNo}
                    </FormItem>
                </Modal>
                <BatchManualProcessingModal
                    ids={this.state.selectedRows.reduce((prev, curr) => [...prev, curr.id], [])}
                    visible={this.state.batchManualProcessingModalVisible}
                    close={success => {
                        if (success) {
                            this.load();
                        }
                        this.setState({ batchManualProcessingModalVisible: false });
                    }}
                    config={nuclearReleaseManageBatchManualData()}
                    submitUrl={"/ccs/checklist/manualUpdStatus"}
                    type={"Checklist"}
                />
            </React.Fragment>
        );
    }

    switchType(item) {
        switch (item.type) {
            case "INPUT":
                return (
                    <Input
                        placeholder={item.placeholder || `请输入${item.labelName}`}
                        maxLength={item.maxLength || 999}
                    />
                );
            case "INPUTNUMBER":
                return (
                    <InputNumber style={{ width: 170 }} placeholder={item.placeholder || `请输入${item.labelName}`} />
                );
            case "SELECT":
                if (typeof item.onChange === "function") {
                    return (
                        <Select
                            style={{ width: "100%" }}
                            onChange={item.onChange}
                            showSearch
                            optionFilterProp="children">
                            {item.list.length &&
                                item.list.map((ite, index) => {
                                    return (
                                        <Option value={ite.id} key={index}>
                                            {ite.name}
                                        </Option>
                                    );
                                })}
                        </Select>
                    );
                } else if (item.mode) {
                    return (
                        <Select
                            mode={item.mode}
                            onSelect={item.onSelect}
                            onDeselect={item.onDeselect}
                            style={{ width: "100%" }}
                            filterOption={(input, option) => {
                                return option.props.children.toLowerCase().indexOf(input.toLowerCase()) >= 0;
                            }}>
                            {item.list.length &&
                                item.list.map((ite, index) => {
                                    return (
                                        <Option value={ite.id} key={index}>
                                            {ite.name}
                                        </Option>
                                    );
                                })}
                        </Select>
                    );
                } else if (item.onSearch) {
                    return (
                        <Select
                            style={{ width: "100%" }}
                            onSearch={item.onSearch}
                            onSelect={item.onSelect}
                            showSearch
                            filterOption={(input, option) => {
                                return option.props.children.toLowerCase().indexOf(input.toLowerCase()) >= 0;
                            }}>
                            {item.list.length &&
                                item.list.map((ite, index) => {
                                    // return <Option value={ite.id} key={ite.value}>{ite.name}</Option>
                                    return (
                                        <Option value={ite.id} key={index}>
                                            {ite.name}
                                        </Option>
                                    );
                                })}
                        </Select>
                    );
                }
                {
                    return (
                        <Select style={{ width: "100%" }} showSearch optionFilterProp="children">
                            {item.list.length &&
                                item.list.map((ite, index) => {
                                    return (
                                        <Option value={ite.id} key={index}>
                                            {ite.name}
                                        </Option>
                                    );
                                })}
                        </Select>
                    );
                }
            case "CHECKBOX":
                return (
                    <Checkbox.Group onChange={e => this.checkOnChange(e)}>
                        {item.list.length &&
                            item.list.map((ite, index) => {
                                return (
                                    <Checkbox value={ite.id} key={index}>
                                        {ite.name}
                                    </Checkbox>
                                );
                            })}
                    </Checkbox.Group>
                );
            case "DATE":
                return <DatePicker showTime />;
            case "TEXTAREA":
                return <TextArea />;
            default:
                return null;
        }
    }

    renderFormItem(item) {
        let formItemLayout = {
            labelCol: { span: 6 },
            wrapperCol: { span: 14 },
        };
        return item.hide !== true ? (
            <FormItem
                {...formItemLayout}
                hidden={item.hidden || false}
                label={item.labelName}
                name={item.labelKey}
                rules={
                    item.rules
                        ? item.rules
                        : [
                              {
                                  message: `请${item.type === "SELECT" ? "选择" : "输入"}${item.labelName}`,
                                  required: item.required,
                              },
                          ]
                }>
                {this.switchType(item)}
            </FormItem>
        ) : null;
    }
}

export default App;
