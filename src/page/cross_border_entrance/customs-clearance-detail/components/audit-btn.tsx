import React, { useEffect, useState } from "react";
import { BussinessType } from "../enum";
import { Modal, Form, message, Button } from "antd";
import { DDYObject, lib } from "react-single-app";
// 审核按钮模块
function AuditBtn({ detail, reload }) {
    const id = lib.getParam("id");
    const [open, setOpen] = useState(false);
    let [form] = Form.useForm();
    const handleClose: (success?: boolean) => void = success => {
        // closeModal(success);
        setOpen(false);
    };
    useEffect(() => {
        form.setFieldsValue(detail);
    }, [detail]);

    function auditPass() {
        if (!detail?.inveBusinessType) {
            message.error("业务类型不能为空,请编辑保存后再审核！");
            return;
        } else {
            if (detail.inveBusinessType === "") {
                if (!detail.twoStepFlag) {
                    message.error("是否两步申报为空,请编辑保存后再审核！");
                    return;
                }
            }
        }
        lib.request({
            url: "/ccs/invenorder/audit",
            data: {
                id,
            },
            needMask: true,
            success: res => {
                console.log("res:", res);
                if (res && res.code === 2) {
                    Modal.warning({
                        title: "提示",
                        content: <div dangerouslySetInnerHTML={{ __html: res.errorMessage }}></div>,
                    });
                }
                if (res && res.code === -1) {
                    // message.error(res.message);
                    Modal.error({
                        title: "提示",
                        content: <div dangerouslySetInnerHTML={{ __html: res.errorMessage }}></div>,
                    });
                    return;
                }
                if (res && res.code === 1) {
                    message.success("审核通过");
                }
                handleClose(true);
                reload && reload();
            },
        });
    }

    const FormText: React.FC<{ value?: any }> = ({ value }) => {
        return <span>{value}</span>;
    };

    function handleCancel() {
        handleClose();
    }

    return (
        <>
            <Button
                type="primary"
                onClick={() => {
                    setOpen(true);
                }}>
                审核通过
            </Button>
            <Modal title="审核通过" open={open} onOk={auditPass} onCancel={() => handleCancel()} destroyOnClose>
                {BussinessType.BUSSINESS_ONELINE_IN === detail?.inveBusinessType && (
                    <p>一线入境需线下核实是否两步申报（是：区港联动；否：普通清单) </p>
                )}
                <Form form={form}>
                    <Form.Item label={"业务类型"} name={"inveBusinessTypeDesc"} required>
                        <FormText />
                    </Form.Item>
                    {BussinessType.BUSSINESS_ONELINE_IN === detail?.inveBusinessType && (
                        <Form.Item label={"是否两步申报"} name={"twoStepFlagDesc"} required>
                            <FormText />
                        </Form.Item>
                    )}
                </Form>
            </Modal>
        </>
    );
}

export default AuditBtn;
