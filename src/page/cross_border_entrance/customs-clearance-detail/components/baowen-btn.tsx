import React, { useState, useEffect } from "react";
import { Button, Modal, message } from "antd";
import copy from "copy-to-clipboard";
export default ({ content }) => {
    const [open, setOpen] = useState(false);
    return (
        <>
            <Button
                type="link"
                onClick={() => {
                    setOpen(true);
                }}>
                查看报文
            </Button>
            <Modal
                open={open}
                title="原始报文"
                onCancel={() => setOpen(false)}
                footer={[
                    <Button onClick={() => setOpen(false)}>取消</Button>,
                    <Button
                        onClick={() => {
                            copy(content);
                            message.success("复制成功");
                        }}>
                        复制报文
                    </Button>,
                ]}
                width={700}>
                <p>{content}</p>
            </Modal>
        </>
    );
};
