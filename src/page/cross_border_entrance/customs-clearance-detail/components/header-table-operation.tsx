import React, { useState, useEffect, Fragment } from "react";
import { Select, Modal } from "antd";
//@ts-ignore
import { ConfigFormCenter, DDYObject, event, lib, Uploader, UploadFile } from "react-single-app";
const Option = Select.Option;
import _ from "lodash";
import { detailBodyModalConfig, detailBodyModalConfig2, detailBodyModalConfig3 } from "../view-config";
import { BussinessType, CustomsClearanceStatus, TransitFlag } from "../enum";

export default function HeaderTableOperation({
    editRow,
    detail,
    countryList,
    currencyList,
    showHeaderTableOperation,
    closeModal,
    bodyEditType,
    isMaterials = false,
}) {
    const ref = React.useRef<any>();
    //记录下拉变化的desc
    const [selectChangeDesc, setSelectChangeDesc] = useState<DDYObject>({});
    const [bookItemId, setBookItemId] = useState();
    const oldOrNew = editRow.oldOrNew;
    const handleCancel = () => {
        setSelectChangeDesc({});
        closeModal();
    };

    const handleOk = () => {
        ref.current.getForm.validateFields().then(values => {
            setSelectChangeDesc({});
            closeModal(true, Object.assign(editRow, values, selectChangeDesc));
        });
    };
    const onConfigLoadSuccess = config => {
        config.baseInfo.children.map(item => {
            if (item.name === "dangerousFlag") {
                if (
                    [
                        BussinessType.BUSSINESS_REFUND_INAREA,
                        BussinessType.BUSSINESS_SECTION_OUT,
                        BussinessType.BUSSINESS_SECTION_IN,
                        BussinessType.BUSSINESS_SECTIONINNER_OUT,
                        BussinessType.BUSSINESS_SECTIONINNER_IN,
                        BussinessType.BUSSINESS_ONELINE_IN,
                    ].includes(detail.inveBusinessType)
                ) {
                    if (bodyEditType === "add") {
                        ref.current.getForm.setFieldsValue({ dangerousFlag: "0" });
                    }
                }
            }
            if (item.type === "single-select" && !item.fromParam && item.from) {
                if (item.name === "goodsSeqNo" && oldOrNew !== "new") {
                    ref.current.initSelect(item, {
                        productId: editRow.productId,
                        areaBookId: detail.areaBookId,
                    });
                } else if (item.name === "productId") {
                    ref.current.initSelect(item, {
                        originProductId: editRow.originProductId, //统一料号
                        areaBookId: detail.areaBookId,
                    });
                } else {
                    ref.current.initSelect(item);
                }
            }
        });
    };

    function changeSecondUnitRules(secondUnit) {
        let rule = [
            {
                required: Boolean(secondUnit),
                message: "请输入第二法定数量",
            },
            {
                pattern: RegExp("^[1-9]\\d*$|^[1-9]\\d*\\.\\d*|0\\.\\d*[1-9]\\d*$"),
                message: "请输入大于0的整数",
            },
        ];
        let config = ref.current.config;
        config.baseInfo.children.map(item => {
            if (item.name.toString() === "secondUnitQfy") {
                item.rules = rule;
                item.disabled = !Boolean(secondUnit);
            }
        });
    }

    function goodsSeqNoChange(desc, formFieldsValue) {
        setBookItemId(desc);
        lib.request({
            url: "/ccs/invenorder/getInventoryOrderItemByItemIdV2",
            data: {
                oldOrNew: "old",
                bookId: detail.areaBookId,
                bookItemId: desc,
                originProductId: editRow.originProductId ? editRow.originProductId : editRow.productId, //统一料号
            },
            needMask: true,
            success: res => {
                let declareUnitQfy = parseInt(formFieldsValue.declareUnitQfy);
                formFieldsValue.goodsName = res.goodsName;
                formFieldsValue.hsCode = res.hsCode;
                formFieldsValue.goodsModel = res.goodsModel;
                formFieldsValue.unitDesc = res.unitDesc;
                formFieldsValue.firstUnit = res.firstUnit;
                formFieldsValue.firstUnitDesc = res.firstUnitDesc;
                formFieldsValue.firstUnitQfy = res.firstUnitQfy;
                formFieldsValue.declarePrice = res.declarePrice;
                formFieldsValue.declareTotalPrice = (Number(res.declarePrice) * Number(declareUnitQfy)).toFixed(5);
                formFieldsValue.firstUnitQfys = parseFloat((res.firstUnitQfy * declareUnitQfy).toFixed(5));
                if (res.secondUnit) {
                    formFieldsValue.secondUnitDesc = res.secondUnitDesc;
                    formFieldsValue.secondUnit = res.secondUnit;
                    formFieldsValue.secondUnitQfy = res.secondUnitQfy;
                    formFieldsValue.secondUnitQfys = parseFloat(((res.secondUnitQfy || 0) * declareUnitQfy).toFixed(5));
                } else {
                    formFieldsValue.secondUnitDesc = null;
                    formFieldsValue.secondUnit = null;
                    formFieldsValue.secondUnitQfys = null;
                    formFieldsValue.secondUnitQfy = null;
                }
                changeSecondUnitRules(res.secondUnit);
                formFieldsValue.currency = res.currency;
                formFieldsValue.netweight = res.netweight;
                formFieldsValue.netweights = Number(res.netweight);
                formFieldsValue.originCountry = res.originCountry;
                formFieldsValue.goodsBar = res.goodsBar;
                ref.current.setMergeDetail(formFieldsValue);
                selectChangeDesc.unit = res.unit;
                selectChangeDesc.firstUnitDesc = res.secondUnitDesc;
                selectChangeDesc.secondUnitDesc = res.firstUnitDesc;
                selectChangeDesc.originCountryDesc = res.originCountryDesc;
                selectChangeDesc.currencyDesc = res.currencyDesc;
                setSelectChangeDesc(selectChangeDesc);
            },
        });
    }

    function oldOrNewChange(value, formFieldsValue) {
        let data: DDYObject = {
            oldOrNew: value,
            bookId: detail.areaBookId,
            originProductId: editRow.originProductId ? editRow.originProductId : editRow.productId, //统一料号
        };
        if (value === "old") {
            data.bookItemId = bookItemId;
        }

        let config = ref.current.config;
        config.baseInfo.children.map(item => {
            if (item.name.toString() === "goodsSeqNo") {
                item.disabled = value === "new";
                item.rules =
                    value === "new"
                        ? []
                        : [
                              {
                                  required: true,
                                  message: "请输入选择备案序号",
                              },
                          ];
            } else if (item.name.toString() === "goodsName") {
                item.disabled = value !== "new";
            }
        });
        ref.current.changeConfig(config);
        if (value === "new") {
            lib.request({
                url: "/ccs/invenorder/getInventoryOrderItemByItemIdV2",
                data: data,
                needMask: true,
                success: res => {
                    let declareUnitQfy = parseInt(formFieldsValue.declareUnitQfy);

                    formFieldsValue.goodsSeqNo = res.goodsSeqNo;
                    formFieldsValue.goodsName = res.goodsName;
                    formFieldsValue.hsCode = res.hsCode;
                    formFieldsValue.goodsModel = res.goodsModel;
                    formFieldsValue.unitDesc = res.unitDesc;
                    formFieldsValue.firstUnit = res.firstUnit;
                    formFieldsValue.firstUnitDesc = res.firstUnitDesc;
                    formFieldsValue.firstUnitQfy = res.firstUnitQfy;
                    formFieldsValue.declarePrice = res.declarePrice;
                    formFieldsValue.declareTotalPrice = (Number(res.declarePrice) * Number(declareUnitQfy)).toFixed(5);
                    formFieldsValue.firstUnitQfys = parseFloat((res.firstUnitQfy * declareUnitQfy).toFixed(5));
                    if (res.secondUnit) {
                        formFieldsValue.secondUnitDesc = res.secondUnitDesc;
                        formFieldsValue.secondUnit = res.secondUnit;
                        formFieldsValue.secondUnitQfy = res.secondUnitQfy;
                        formFieldsValue.secondUnitQfys = parseFloat(
                            ((res.secondUnitQfy || 0) * declareUnitQfy).toFixed(5),
                        );
                    } else {
                        formFieldsValue.secondUnitDesc = null;
                        formFieldsValue.secondUnit = null;
                        formFieldsValue.secondUnitQfys = null;
                        formFieldsValue.secondUnitQfy = null;
                    }
                    changeSecondUnitRules(res.secondUnit);
                    formFieldsValue.netweight = res.netweight;
                    formFieldsValue.netweights = Number(res.netweight);
                    formFieldsValue.originCountry = res.originCountry;
                    formFieldsValue.currency = res.currency;
                    formFieldsValue.goodsBar = res.goodsBar;
                    ref.current.setMergeDetail(formFieldsValue);
                    selectChangeDesc.unit = res.unit;
                    selectChangeDesc.firstUnitDesc = res.secondUnitDesc;
                    selectChangeDesc.secondUnitDesc = res.firstUnitDesc;
                    selectChangeDesc.originCountryDesc = res.originCountryDesc;
                    selectChangeDesc.currencyDesc = res.currencyDesc;
                    setSelectChangeDesc(selectChangeDesc);
                },
                fail: () => {
                    ref.current.setMergeDetail({});
                },
            });
        } else {
            //是->否  先请求序号下拉选中第一个
            ref.current.config.baseInfo.children.map(item => {
                if (item.type === "single-select" && !item.fromParam) {
                    if (item.name === "goodsSeqNo") {
                        const productId = ref.current?.getFormFiled("productId");
                        lib.request({
                            url: item.from,
                            data: {
                                productId: productId ? productId : editRow.originProductId,
                                areaBookId: detail.areaBookId,
                            },
                            success: res => {
                                ref.current.updateSelectList(item, res);
                                if (res && res.length > 0) {
                                    formFieldsValue.goodsSeqNo = res[0].id;
                                    ref.current.setMergeDetail(formFieldsValue);
                                    goodsSeqNoChange(res[0].customsBookId, formFieldsValue);
                                } else {
                                    formFieldsValue.goodsSeqNo = "";
                                    ref.current.setMergeDetail(formFieldsValue);
                                }
                            },
                        });
                    }
                }
            });
        }
    }

    const onSinglesSelectChange = desc => {
        let formFieldsValue = ref.current.getFormFieldsValue();
        switch (desc.name) {
            case "goodsSeqNo":
                goodsSeqNoChange(desc.value.data.customsBookId, formFieldsValue);
                break;
            case "oldOrNew":
                oldOrNewChange(desc.value.value, formFieldsValue);
                break;
            case "productId":
                ref.current.config.baseInfo.children.map(item => {
                    if (item.type === "single-select" && !item.fromParam) {
                        const oldOrNew = ref.current?.getFormFiled("oldOrNew");
                        if (item.name === "goodsSeqNo" && oldOrNew !== "new") {
                            lib.request({
                                url: item.from,
                                data: {
                                    productId: desc.value.value,
                                    areaBookId: detail.areaBookId,
                                },
                                success: res => {
                                    ref.current.updateSelectList(item, res);
                                    if (res && res.length > 0) {
                                        formFieldsValue.goodsSeqNo = res[0].id;
                                        ref.current.setMergeDetail(formFieldsValue);
                                        goodsSeqNoChange(res[0].customsBookId, formFieldsValue);
                                    } else {
                                        formFieldsValue.goodsSeqNo = "";
                                        ref.current.setMergeDetail(formFieldsValue);
                                    }
                                },
                            });
                        }
                    }
                });
                break;
            case "declarePrice":
                let declarePrice = desc.value || 0;
                if (String(declarePrice).endsWith(".")) return;
                formFieldsValue.declarePrice = _.round(declarePrice, 4);
                formFieldsValue.declareTotalPrice = _.round((formFieldsValue.declareUnitQfy || 0) * declarePrice, 4);
                ref.current.setMergeDetail(formFieldsValue);
                break;
            case "secondUnitQfy":
                let secondUnitQfy = desc.value || 0;
                if (String(secondUnitQfy).endsWith(".")) return;
                formFieldsValue.secondUnitQfy = _.round(secondUnitQfy, 5);
                if (formFieldsValue.declareUnitQfy) {
                    formFieldsValue.secondUnitQfys = parseFloat(
                        (formFieldsValue.secondUnitQfy * formFieldsValue.declareUnitQfy).toFixed(5),
                    );
                }
                ref.current.setMergeDetail(formFieldsValue);
                break;
            case "firstUnitQfy":
                let firstUnitQfy = desc.value || 0;
                if (String(firstUnitQfy).endsWith(".")) return;
                formFieldsValue.firstUnitQfy = _.round(firstUnitQfy, 5);
                if (formFieldsValue.declareUnitQfy) {
                    formFieldsValue.firstUnitQfys = parseFloat(
                        (formFieldsValue.firstUnitQfy * formFieldsValue.declareUnitQfy).toFixed(5),
                    );
                }
                ref.current.setMergeDetail(formFieldsValue);
                break;
            case "declareUnitQfy":
                let declareUnitQfy = desc.value || 0;
                formFieldsValue.declareUnitQfy = declareUnitQfy;
                formFieldsValue.declareTotalPrice = parseFloat(
                    (formFieldsValue.declarePrice * declareUnitQfy).toFixed(4),
                );
                // if (detail?.bussinessType !== BussinessType.SIMPLE_PROCESSING) {
                // formFieldsValue.netweight = parseFloat((formFieldsValue.netweights * declareUnitQfy).toFixed(2));
                formFieldsValue.firstUnitQfys = parseFloat((formFieldsValue.firstUnitQfy * declareUnitQfy).toFixed(5));
                // }
                if (formFieldsValue.secondUnit) {
                    formFieldsValue.secondUnitQfys = parseFloat(
                        (formFieldsValue.secondUnitQfy * declareUnitQfy).toFixed(5),
                    );
                } else {
                    formFieldsValue.secondUnitDesc = null;
                    formFieldsValue.secondUnit = null;
                    formFieldsValue.secondUnitQfys = null;
                    formFieldsValue.secondUnitQfy = null;
                }
                ref.current.setMergeDetail(formFieldsValue);
                break;
            case "currency":
                selectChangeDesc.currencyDesc = desc.value.data.name;
                setSelectChangeDesc(selectChangeDesc);
                break;
            case "originCountry":
                selectChangeDesc.originCountryDesc = desc.value.data.name;
                setSelectChangeDesc(selectChangeDesc);
                break;
            case "hsCode":
                hsCodeChange(desc.value.value);
                break;
            default:
                break;
        }
    };

    function hsCodeChange(id) {
        lib.request({
            url: "/ccs/customs/hsTaxDetailV2",
            data: {
                id,
            },
            needMask: true,
            success: res => {
                let { firstLegalUnit, secondLegalUnit } = res;

                //法二单位不为空，法二数量必填
                let rule = [
                    {
                        required: Boolean(secondLegalUnit),
                        message: "请输入第二法定数量",
                    },
                    {
                        pattern: RegExp("^[1-9]\\d*$|^[1-9]\\d*\\.\\d*|0\\.\\d*[1-9]\\d*$"),
                        message: "请输入大于0的整数",
                    },
                ];
                let selectList = ref.current.getSelectList({ name: "firstUnit" });
                let findFirstUnit = selectList.find(item => item.id === firstLegalUnit);
                let findSecondUnit = selectList.find(item => item.id === secondLegalUnit);
                let firstUnitDesc, secondUnitDesc;
                if (findFirstUnit) {
                    firstUnitDesc = findFirstUnit.name;
                }
                let config = ref.current.config;
                config.baseInfo.children.map(item => {
                    if (item.name.toString() === "secondUnitQfy") {
                        item.rules = rule;
                        item.disabled = !Boolean(secondLegalUnit);
                    }
                });
                ref.current.changeConfig(config);
                if (findSecondUnit) {
                    secondUnitDesc = findSecondUnit.name;
                }
                if (secondLegalUnit) {
                    ref.current.setMergeDetail({
                        firstUnit: firstLegalUnit,
                        secondUnit: secondLegalUnit,
                    });
                } else {
                    ref.current.setMergeDetail({
                        firstUnit: firstLegalUnit,
                        secondUnit: null,
                        secondUnitQfys: null,
                        secondUnitQfy: null,
                    });
                }
                selectChangeDesc.firstUnitDesc = firstUnitDesc;
                selectChangeDesc.secondUnitDesc = secondUnitDesc;
                setSelectChangeDesc(selectChangeDesc);
            },
        });
    }

    const onInitSelectSuccess = item => {
        if (item.name === "hsCode") {
            ref.current.setMergeDetail(editRow);
        }
    };

    function makeConfData() {
        let confData =
            detail.inveBusinessType === BussinessType.SIMPLE_PROCESSING
                ? isMaterials
                    ? detailBodyModalConfig3({
                          countryList: countryList,
                          currencyList: currencyList,
                      })
                    : detailBodyModalConfig2({ countryList: countryList, currencyList: currencyList })
                : detailBodyModalConfig({ countryList: countryList, currencyList: currencyList });
        confData.baseInfo.children.map(item => {
            switch (item.name) {
                case "goodsSeqNo":
                    if (editRow.oldOrNew === "new") {
                        item.disabled = true;
                    } else {
                        item.rules =
                            detail.inveBusinessType === BussinessType.SIMPLE_PROCESSING
                                ? []
                                : [
                                      {
                                          required: true,
                                          message: "请输入选择备案序号",
                                      },
                                  ];
                    }
                    break;
                case "declareUnitQfy":
                    if (detail.channel === 1) {
                        item.label = "最终申报数量";
                        //协同单
                        if (detail.collaborateFlag) {
                            // 来源为外部，且订单状态为STATUS_CONFIRMING 不可编辑
                            if (detail.status === CustomsClearanceStatus.STATUS_CONFIRMING) {
                                item.disabled = true;
                            } else {
                                item.disabled = [
                                    BussinessType.BUSSINESS_ONELINE_IN,
                                    BussinessType.BUSSINESS_SECTION_IN,
                                    BussinessType.BUSSINESS_SECTION_OUT,
                                    BussinessType.BUSSINESS_SECTIONINNER_IN,
                                    BussinessType.BUSSINESS_SECTIONINNER_OUT,
                                ].some(item => item === detail.inveBusinessType);
                            }
                        } else {
                            item.disabled = [
                                BussinessType.BUSSINESS_SECTION_OUT,
                                BussinessType.BUSSINESS_SECTIONINNER_OUT,
                            ].some(item => item !== detail.inveBusinessType);
                        }
                    } else {
                        // item.disabled =
                        //     detail.transitFlag === TransitFlag.TRANSIT_IN ||
                        //     detail.transitFlag === TransitFlag.TRANSIT_OUT;
                    }
                    break;
                case "goodsName":
                    item.disabled = editRow.oldOrNew !== "new";
                    if (detail.inveBusinessType === BussinessType.SIMPLE_PROCESSING) {
                        item.disabled = true;
                    }
                    break;
                case "secondUnitQfy":
                    if (!editRow.secondUnit) {
                        item.disabled = true;
                    } else {
                        item.disabled = false;
                        item.rules = [
                            {
                                required: true,
                                message: "请输入第二法定数量",
                            },
                            {
                                //@ts-ignore
                                pattern: RegExp("^[1-9]\\d*$|^[1-9]\\d*\\.\\d*|0\\.\\d*[1-9]\\d*$"),
                                message: "请输入大于0的整数",
                            },
                        ];
                    }
                    if ([BussinessType.FB_IN, BussinessType.FB_OUT].includes(detail.inveBusinessType)) {
                        item.rules = [
                            {
                                pattern: RegExp("^[1-9]\\d*$|^[1-9]\\d*\\.\\d*|0\\.\\d*[1-9]\\d*$"),
                                message: "请输入大于0的整数",
                            },
                        ];
                    }
                    break;
                case "dangerousFlag":
                    if (
                        [
                            BussinessType.BUSSINESS_DESTORY,
                            BussinessType.INVENTORY_PROFIT,
                            BussinessType.RANDOM_INSPECTION_DECLARATION,
                            BussinessType.SIMPLE_PROCESSING,
                        ].includes(detail.inveBusinessType)
                    ) {
                        item.disabled = true;
                    } else {
                        item.disabled = false;
                    }
                    break;
                case "destinationCountry":
                    if (
                        [BussinessType.ONLINE_REFUND, BussinessType.SIMPLE_PROCESSING].includes(detail.inveBusinessType)
                    ) {
                        item.disabled = false;
                    } else {
                        item.disabled = true;
                    }
                    break;
                case "netweights":
                case "netweight":
                    if ([BussinessType.FB_IN, BussinessType.FB_OUT].includes(detail.inveBusinessType)) {
                        item.rules = [
                            {
                                pattern: RegExp("^[1-9]\\d*$|^[1-9]\\d*\\.\\d*|0\\.\\d*[1-9]\\d*$"),
                                message: "请输入大于0的数",
                            },
                        ];
                    }
                    break;
                case "declarePrice":
                    if ([BussinessType.FB_IN, BussinessType.FB_OUT].includes(detail.inveBusinessType)) {
                        item.rules = [
                            {
                                pattern: RegExp("^[1-9]\\d*$|^[1-9]\\d*\\.\\d*|0\\.\\d*[1-9]\\d*$"),
                                message: "请输入大于0的数",
                            },
                        ];
                    }
                    break;
                case "firstUnitQfy":
                    if ([BussinessType.FB_IN, BussinessType.FB_OUT].includes(detail.inveBusinessType)) {
                        item.rules = [
                            {
                                pattern: RegExp("^[1-9]\\d*$|^[1-9]\\d*\\.\\d*|0\\.\\d*[1-9]\\d*$"),
                                message: "请输入大于0的整数",
                            },
                        ];
                    }
                    break;
                case "originCountry":
                    if ([BussinessType.FB_IN, BussinessType.FB_OUT].includes(detail.inveBusinessType)) {
                        item.rules = [];
                    }
                    break;
                default:
                    break;
            }
        });
        return confData;
    }

    return (
        <Modal
            destroyOnClose
            onCancel={handleCancel}
            onOk={handleOk}
            title={"编辑"}
            width={1150}
            open={showHeaderTableOperation}>
            <ConfigFormCenter
                ref={ref}
                onSinglesSelectChange={onSinglesSelectChange}
                confData={makeConfData()}
                onConfigLoadSuccess={onConfigLoadSuccess}
                onInitSelectSuccess={onInitSelectSuccess}
            />
        </Modal>
    );
}
