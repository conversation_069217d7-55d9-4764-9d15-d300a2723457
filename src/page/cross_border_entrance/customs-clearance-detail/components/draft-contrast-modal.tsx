import React, { useMemo, useState, useRef, useEffect } from "react";
import { Button, Modal, Table, Form, InputNumber, Input, Select, Radio, Space } from "antd";
import { DDYObject, lib } from "react-single-app";
import { SearchOutlined } from "@ant-design/icons";
import type { InputRef, TableColumnsType, TableColumnType } from "antd";
// import { Button, Input, Space, Table } from 'antd';
import type { FilterDropdownProps } from "antd/es/table/interface";
// import Highlighter from 'react-highlight-words';

let IDX = 36,
    HEX = "";
while (IDX--) HEX += IDX.toString(36);
export function uid(len) {
    let str = "",
        num = len || 11;
    while (num--) str += HEX[(Math.random() * 36) | 0];
    return str;
}

const redColor = "#fff1f0";
const bidui = (obj1, obj2, type, keys) => {
    const result = {};
    // keys = [
    //     ...keys,
    //     ...[
    //         "hsCode",
    //         "originCountryName",
    //         "firstUnitQfy",
    //         "secondUnitQfy",
    //         "declarePrice",
    //         "currency",
    //         "declareUnitQfy",
    //         "unitName",
    //     ],
    // ];
    const defaults = [
        "hsCode",
        "originCountryName",
        "firstUnitQfy",
        "secondUnitQfy",
        "declarePrice",
        "currency",
        "declareUnitQfy",
        "unitName",
    ];

    keys = defaults.filter(item => !keys.includes(item));

    // console.log(keys);
    keys.map(item => {
        if (item === "d&u") return null;
        if (obj1[item] !== obj2[item]) {
            if (type === "in" && obj1[item]) {
                result[item + "IsRed"] = true;
            }
            if (type === "out" && obj2[item]) {
                result[item + "IsRed"] = true;
            }
            // && obj1[item] !== null && obj2[item] !== null
        } else {
            result[item + "IsRed"] = false;
        }
    });
    return result;
};
const bidui2 = (obj, keys) => {
    const result = [];
    const defaults = [
        "originCountryName",
        "firstUnitQfy",
        "secondUnitQfy",
        "declarePrice",
        "currency",
        "declareUnitQfy",
        "unitName",
        //declareUnitQfy和unitName 合并成 d&u，方便做差异比较
        "d&u",
    ];
    keys = defaults.filter(item => !keys.includes(item));
    console.log(keys);
    const inCache = {};
    const outCache = {};
    obj.transmitInItem.map((item, index) => {
        item.combireCount = obj.transmitInItem.length;
        if (keys.includes("d&u")) {
            if (item.declareUnitQfy || item.unitName) {
                item["d&u"] = item.declareUnitQfy + "&" + item.unitName;
            }
        }

        keys.map(attr => {
            if (item[attr]) {
                let hasValue = false;
                if (inCache[attr]) {
                    for (let i = 0; i < inCache[attr].length; i++) {
                        if (inCache[attr][i].value === item[attr]) {
                            hasValue = true;
                            inCache[attr][i].count++;
                            inCache[attr][i].indexs.push(index);
                            break;
                        }
                    }
                } else {
                    inCache[attr] = [];
                }

                if (!hasValue) {
                    // 此时缓存中没有这个数据
                    let value = item[attr];
                    inCache[attr].push({ value: value, count: 1, indexs: [index] });
                    // inCache[attr].push({ value: item[attr], count: 1, indexs: [index] })
                }
            }
        });
    });
    obj.transmitOutItem.map((item, index) => {
        item.combireCount = obj.transmitInItem.length;
        // if (item.declareUnitQfy || item.unitName) {
        //     item["d&u"] = item.declareUnitQfy + "&" + item.unitName;
        // }
        if (keys.includes("d&u")) {
            if (item.declareUnitQfy || item.unitName) {
                item["d&u"] = item.declareUnitQfy + "&" + item.unitName;
            }
        }
        keys.map(attr => {
            if (item[attr]) {
                let hasValue = false;
                if (outCache[attr]) {
                    for (let i = 0; i < outCache[attr].length; i++) {
                        if (outCache[attr][i].value === item[attr]) {
                            hasValue = true;
                            outCache[attr][i].count++;
                            outCache[attr][i].indexs.push(index);
                            break;
                        }
                    }
                } else {
                    outCache[attr] = [];
                }

                if (!hasValue) {
                    // 此时缓存中没有这个数据
                    outCache[attr].push({ value: item[attr], count: 1, indexs: [index] });
                    // outCache[attr].push({ value: item[attr], count: 1, indexs: [index] })
                }
            }
        });
    });
    keys.map(attr => {
        const inValues = {};
        inCache[attr] &&
            inCache[attr].map(item => {
                inValues[item.value] = { count: item.count, indexs: item.indexs };
            });
        const outValues = {};
        outCache[attr] &&
            outCache[attr].map(item => {
                outValues[item.value] = { count: item.count, indexs: item.indexs };
            });

        // 消消乐大法
        for (let key in inValues) {
            if (outValues[key]) {
                const inCount = inValues[key]?.count || 0;
                const outCount = outValues[key]?.count || 0;
                if (outCount > inCount) {
                    outValues[key].count = outCount - inCount;
                    outValues[key].indexs.splice(0, outValues[key].count);
                    delete inValues[key];
                } else if (inCount === outCount) {
                    delete inValues[key];
                    delete outValues[key];
                } else {
                    inValues[key].count = inCount - outCount;
                    inValues[key].indexs.splice(0, inValues[key].count);
                    delete outValues[key];
                }
            }
        }

        // 消完之后对剩余的属性加标记
        for (let key in inValues) {
            inValues[key]?.indexs.map(index => {
                obj.transmitInItem[index][attr + "IsRed"] = true;
            });
        }
        // 消完之后对剩余的属性加标记
        for (let key in outValues) {
            outValues[key]?.indexs.map(index => {
                obj.transmitOutItem[index][attr + "IsRed"] = true;
            });
        }
    });
    obj.transmitOutItem.map(item => {
        item.combireCount = obj.transmitInItem.length + obj.transmitOutItem.length;
        result.push({
            ...item,
            fromSource: "转出",
            hsCode: obj.hsCode,
            dataIndex: result.length,
        });
    });
    obj.transmitInItem.map(item => {
        item.combireCount = obj.transmitInItem.length + obj.transmitOutItem.length;
        result.push({
            ...item,
            fromSource: "转入",
            hsCode: obj.hsCode,
            dataIndex: result.length,
        });
    });

    return result;
};
export default ({ reload, currency, detail }) => {
    const [data, setData] = useState([]);
    const [open, setOpen] = useState(false);
    const [searchText, setSearchText] = useState("");
    const [searchedColumn, setSearchedColumn] = useState("");
    const searchInput = useRef<InputRef>(null);

    const [groupValue, setGroupValue] = useState();

    const [typeList, setTypeList] = useState([]);
    const [typeVal, setTypeVal] = useState(detail?.draftCompareType);

    const contrastOrder = async (val: boolean | number = false, compareType = 1) => {
        setData([]);
        // console.log(keys);
        Promise.all([
            new Promise((resolve, reject) => {
                lib.request({
                    url: "/ccs/invenorder/getDraftCompareFilterList",
                    success(data) {
                        // setTypeList(data);
                        console.log("data:", data);
                        resolve(data);
                    },
                });
            }),
            new Promise((resolve, reject) => {
                lib.request({
                    url: "/ccs/invenorder/compareDraftItemListV2",
                    data: {
                        id: lib.getParam("id"),
                        compareType: compareType,
                    },
                    needMask: true,
                    success(response) {
                        resolve(response);
                    },
                });
            }),
        ]).then(([keys, response]) => {
            console.log("keys:", keys);
            let arr = [];
            (response as any[]).map((item, index) => {
                if (val && val === 1 && !item.exitsDiff) {
                    return;
                }
                if (val && val === 2 && !item.exitsFail) {
                    return;
                }
                //draftCompareType 1：hs对比 2：统一料号对比
                const bol = compareType === 2;
                if (bol) {
                    const inObj = bidui(item.transmitInItem[0], item.transmitOutItem[0], "in", keys);
                    const outObj = bidui(item.transmitInItem[0], item.transmitOutItem[0], "out", keys);
                    arr.push({
                        ...item,
                        ...item.transmitInItem[0],
                        fromSource: "转入",
                        ...inObj,
                    });
                    arr.push({
                        ...item,
                        ...item.transmitOutItem[0],
                        fromSource: "转出",
                        ...outObj,
                    });
                } else {
                    const results = bidui2(item, keys);
                    results.map((item, index) => {
                        if (index === 0) {
                            item.rowSpan = item.combireCount;
                        } else {
                            item.rowSpan = 0;
                        }
                    });

                    arr = [...arr, ...results];
                }
            });
            console.log("arr:", arr);
            setData(arr);
            setOpen(true);
        });
        // lib.request({
        //     url: "/ccs/invenorder/compareDraftItemListV2",
        //     data: {
        //         id: lib.getParam("id"),
        //         compareType: compareType,
        //     },
        //     needMask: true,
        //     success(response) {
        //         let arr = [];
        //         response.map((item, index) => {
        //             if (val && val === 1 && !item.exitsDiff) {
        //                 return;
        //             }
        //             if (val && val === 2 && !item.exitsFail) {
        //                 return;
        //             }
        //             //draftCompareType 1：hs对比 2：统一料号对比
        //             const bol = compareType === 2;
        //             if (bol) {
        //                 const inObj = bidui(item.transmitInItem[0], item.transmitOutItem[0], "in", keys);
        //                 const outObj = bidui(item.transmitInItem[0], item.transmitOutItem[0], "out", keys);
        //                 arr.push({
        //                     ...item,
        //                     ...item.transmitInItem[0],
        //                     fromSource: "转入",
        //                     ...inObj,
        //                 });
        //                 arr.push({
        //                     ...item,
        //                     ...item.transmitOutItem[0],
        //                     fromSource: "转出",
        //                     ...outObj,
        //                 });
        //             } else {
        //                 const results = bidui2(item, keys);
        //                 results.map((item, index) => {
        //                     if (index === 0) {
        //                         item.rowSpan = item.combireCount;
        //                     } else {
        //                         item.rowSpan = 0;
        //                     }
        //                 });

        //                 arr = [...arr, ...results];
        //             }
        //         });
        //         console.log("arr:", arr);
        //         setData(arr);
        //         setOpen(true);
        //     },
        // });
    };
    const handleSearch = (selectedKeys: string[], confirm: FilterDropdownProps["confirm"], dataIndex: string) => {
        confirm();
        // console.log(selectedKeys[0], dataIndex)
        setSearchText(selectedKeys[0]);
        setSearchedColumn(dataIndex);
    };

    const handleReset = (clearFilters: () => void) => {
        clearFilters();
        setSearchText("");
    };
    const getColumnSearchProps = (dataIndex: string): TableColumnType<string> => ({
        filterDropdown: ({ setSelectedKeys, selectedKeys, confirm, clearFilters, close }) => (
            <div style={{ padding: 8 }} onKeyDown={e => e.stopPropagation()}>
                <Input
                    ref={searchInput}
                    placeholder={`搜索商品名称`}
                    value={selectedKeys[0]}
                    onChange={e => setSelectedKeys(e.target.value ? [e.target.value] : [])}
                    onPressEnter={() => handleSearch(selectedKeys as string[], confirm, dataIndex)}
                    style={{ marginBottom: 8, display: "block" }}
                />
                <Space>
                    <Button
                        type="primary"
                        onClick={() => handleSearch(selectedKeys as string[], confirm, dataIndex)}
                        icon={<SearchOutlined />}
                        size="small"
                        style={{ width: 90 }}>
                        搜索
                    </Button>
                    <Button
                        onClick={() => {
                            // confirm({ closeDropdown: false });
                            clearFilters && handleReset(clearFilters);
                            setTimeout(() => {
                                // setSearchText("");
                                confirm({ closeDropdown: false });
                                setData([...data]);
                            }, 200);
                        }}
                        size="small"
                        style={{ width: 90 }}>
                        重置
                    </Button>
                    {/* <Button
                        type="link"
                        size="small"
                        onClick={() => {
                            confirm({ closeDropdown: false });
                            setSearchText((selectedKeys as string[])[0]);
                            setSearchedColumn(dataIndex);
                        }}>
                        过滤
                    </Button> */}
                    <Button
                        type="link"
                        size="small"
                        onClick={() => {
                            close();
                        }}>
                        关闭
                    </Button>
                </Space>
            </div>
        ),
        filterIcon: (filtered: boolean) => <SearchOutlined style={{ color: "#1677ff", fontSize: "20px" }} />,
        onFilter: (value, record, ...arg) => {
            // console.log(arg)
            // console.log("onFilter", value, record);
            return (
                record[dataIndex] &&
                record[dataIndex]
                    ?.toString()
                    ?.toLowerCase()
                    ?.includes((value as string).toLowerCase())
            );
        },

        onFilterDropdownOpenChange: visible => {
            if (visible) {
                setTimeout(() => searchInput.current?.select(), 100);
            }
        },
        render: text =>
            searchedColumn === dataIndex ? (
                <div>{text}</div>
            ) : (
                // <Highlighter
                //     highlightStyle={{ backgroundColor: '#ffc069', padding: 0 }}
                //     searchWords={[searchText]}
                //     autoEscape
                //     textToHighlight={text ? text.toString() : ''}
                // />
                text
            ),
    });

    const columns = useMemo(() => {
        let result = [
            {
                title: "统一料号",
                dataIndex: "productId",
                width: 150,
                onCell: (_, index) => {
                    if ((index + 1) % 2 === 0) {
                        return { rowSpan: 0 };
                    }
                    return { rowSpan: 2 };
                },
            },
            {
                title: "对比数据",
                dataIndex: "fromSource",
                width: 80,
            },
            {
                title: "HS编码",
                dataIndex: "hsCode",
                width: 120,
                onCell: (data, index) => {
                    return {
                        style: { backgroundColor: data.hsCodeIsRed ? redColor : null },
                    };
                },
            },
            {
                title: "原产国（地区）",
                dataIndex: "originCountryName",
                width: 80,
                onCell: (data, index) => {
                    return {
                        style: { backgroundColor: data.originCountryNameIsRed ? redColor : null },
                    };
                },
            },
            {
                title: "商品名称",
                dataIndex: "goodsName",
                width: 200,
                // (detail.draftCompareType === 1?)
                ...(typeVal === "1" ? getColumnSearchProps("goodsName") : {}),
            },
            {
                title: "申报单位",
                dataIndex: "unitName",
                width: 80,
                onCell: (data, index) => {
                    return {
                        style: {
                            backgroundColor: data["d&uIsRed"] || data.unitNameIsRed ? redColor : null,
                        },
                    };
                },
            },
            {
                title: "申报数量",
                dataIndex: "declareUnitQfy",
                onCell: (data, index) => {
                    return {
                        style: {
                            backgroundColor: data["d&uIsRed"] || data.declareUnitQfyIsRed ? redColor : null,
                        },
                    };
                },
                render: (value, row, index) => {
                    if (!!!row.id) return null;
                    return (
                        <InputNumber
                            value={row.declareUnitQfy}
                            disabled={row?.channel === 1}
                            onChange={val => {
                                row.declareUnitQfy = val;
                                row.declareTotalPrice = (row.declarePrice * 1000 * val) / 1000;
                                setData([...data]);
                            }}
                        />
                    );
                },
                width: 100,
            },
            {
                title: "法定数量（总）",
                dataIndex: "firstUnitQfy",
                width: 100,
                onCell: (data, index) => {
                    return {
                        style: { backgroundColor: data.firstUnitQfyIsRed ? redColor : null },
                    };
                },
                render: (value, row, index) => {
                    if (!!!row.id) return null;
                    return (
                        <InputNumber
                            value={row.firstUnitQfy}
                            onChange={val => {
                                row.firstUnitQfy = val;
                                setData([...data]);
                            }}
                        />
                    );
                },
            },
            {
                title: "法定计量单位",
                dataIndex: "firstUnitName",
                width: 70,
            },
            {
                title: "第二法定数量（总）",
                dataIndex: "secondUnitQfy",
                width: 100,
                onCell: (data, index) => {
                    return {
                        style: { backgroundColor: data.secondUnitQfyIsRed ? redColor : null },
                    };
                },
                render: (value, row, index) => {
                    if (!!!row.id) return null;
                    return (
                        <InputNumber
                            value={row.secondUnitQfy}
                            disabled={!row.secondUnitName}
                            onChange={val => {
                                row.secondUnitQfy = val;
                                setData([...data]);
                            }}
                        />
                    );
                },
            },
            {
                title: "法定第二计量单位",
                dataIndex: "secondUnitName",
                width: 70,
            },
            {
                title: "币制",
                dataIndex: "currency",
                width: 120,
                onCell: (data, index) => {
                    return {
                        style: { backgroundColor: data.currencyIsRed ? redColor : null },
                    };
                },
                render: (value, row, index) => {
                    if (!!!row.id) return null;
                    return (
                        <Select
                            value={row.currency}
                            style={{ width: "100%" }}
                            onChange={val => {
                                row.currency = val;
                                setData([...data]);
                            }}>
                            {currency.map(item => {
                                return (
                                    <Select.Option value={item.id}>
                                        {item.id}:{item.name}
                                    </Select.Option>
                                );
                            })}
                        </Select>
                    );
                },
            },
            {
                title: "申报单价",
                dataIndex: "declarePrice",
                width: 100,
                onCell: (data, index) => {
                    return {
                        style: { backgroundColor: data.declarePriceIsRed ? redColor : null },
                    };
                },
                render: (value, row, index) => {
                    if (!!!row.id) return null;
                    return (
                        <InputNumber
                            value={row.declarePrice}
                            onChange={val => {
                                row.declarePrice = val;
                                row.declareTotalPrice = (row.declareUnitQfy * 1000 * val) / 1000;
                                // data[row.dataIndex] = { ...row };
                                setData([...data]);
                            }}
                        />
                    );
                },
            },

            {
                title: "申报总价",
                dataIndex: "declareTotalPrice",
                width: 100,
            },
        ];
        //typeVal 1：hs对比 2：统一料号对比
        const bol = typeVal === "1";
        if (bol) {
            result[0].title = "HS编码";
            result[0].dataIndex = "hsCode";
            result[0].onCell = (row, index) => {
                return { rowSpan: row.rowSpan };
            };
            result.splice(2, 1);
        }
        return result;
    }, [data, typeVal]);

    const submit = () => {
        const itemList = data
            .filter(item => {
                return item.id;
            })
            .map(item => {
                return {
                    id: item.id,
                    declareUnitQfy: item.declareUnitQfy,
                    firstUnitQfy: item.firstUnitQfy,
                    secondUnitQfy: item.secondUnitQfy,
                    currency: item.currency,
                    declarePrice: item.declarePrice,
                };
            });
        lib.request({
            url: "/ccs/invenorder/updateItemListByDraftCompare",
            data: {
                id: lib.getParam("id"),
                itemList: itemList,
                compareType: Number(typeVal),
            },
            success(response) {
                setOpen(false);
                reload && reload();
            },
        });
    };

    const getTypeData = () => {
        lib.request({
            url: "/ccs/invenorder/listDraftCompareType",
            success(data) {
                setTypeList(data);
            },
        });
    };

    const getFilterKeys = () => {
        // /invenorder/getDraftCompareFilterList
        lib.request({
            url: "/ccs/invenorder/getDraftCompareFilterList",
            success(data) {
                // setTypeList(data);
                console.log("data:", data);
            },
        });
    };

    useEffect(() => {
        // getFilterKeys();
        getTypeData();
    }, []);

    return (
        <>
            <Button
                onClick={() => {
                    setTypeVal(String(detail?.draftCompareType));
                    contrastOrder(false, detail?.draftCompareType);
                }}>
                草单比对
            </Button>
            <Modal
                title="草单比对"
                open={open}
                width={1900}
                onCancel={() => {
                    setOpen(false);
                    setSearchText("");
                    setSearchedColumn("");
                }}
                destroyOnClose
                onOk={() => {
                    submit();
                }}>
                <>
                    <Radio.Group
                        defaultValue={0}
                        value={groupValue}
                        onChange={e => {
                            const val = e.target.value;
                            setGroupValue(val);
                            contrastOrder(val, Number(typeVal));
                        }}>
                        <Radio value={0}>全部</Radio>
                        <Radio value={1}>仅显示差异数据</Radio>
                        <Radio value={2}>仅显示比对失败数据</Radio>
                    </Radio.Group>
                    <br />
                    <Space style={{ margin: "20px 0" }}>
                        <span>对比方式</span>
                        <Select
                            value={typeVal}
                            onChange={e => {
                                console.log("e", e);
                                setTypeVal(String(e));
                                contrastOrder(groupValue, Number(e));
                            }}>
                            {typeList.map(item => (
                                <Select.Option key={item.id}>{item.name}</Select.Option>
                            ))}
                        </Select>
                    </Space>

                    <Table
                        rowKey={"id"}
                        key={"id"}
                        columns={columns}
                        dataSource={data}
                        pagination={false}
                        scroll={{ x: 1000, y: 1000 }}
                        onChange={(data, filters, _, extra) => {
                            console.log("extra:", extra);
                            if (extra.action === "filter") {
                                const dataCache: DDYObject = {};
                                if (detail.draftCompareType === 1) {
                                    extra.currentDataSource.forEach((item, index) => {
                                        if (dataCache[item.hsCode]) {
                                            dataCache[item.hsCode].count += 1;
                                        } else {
                                            dataCache[item.hsCode] = {
                                                count: 1,
                                                // 首位是否被占用
                                                hasFirst: false,
                                            };
                                        }
                                    });
                                    extra.currentDataSource.forEach((item, index) => {
                                        if (dataCache[item.hsCode] && !dataCache[item.hsCode].hasFirst) {
                                            item.rowSpan = dataCache[item.hsCode].count;
                                            dataCache[item.hsCode].hasFirst = true;
                                        } else {
                                            item.rowSpan = 0;
                                        }
                                    });
                                } else {
                                    extra.currentDataSource.forEach((item, index) => {
                                        delete item.rowSpan;
                                    });
                                }
                            }
                        }}
                    />
                </>
            </Modal>
        </>
    );
};
