import React, { useEffect, useRef, useState } from "react";
import { DTEditForm, DTEditFormRefs, DTEditFormConfigs } from "@dt/components";
import { DetailTabHeaderConfig } from "../view-config";
import moment from "moment";
import { BussinessType, CustomsClearanceStatus } from "../enum";
import { detailHeaderClearValue } from "../default-value";
import { DDYObject, lib } from "react-single-app";
import DocumentAttachment from "./document-attachment";

export default ({ detail, reload }) => {
    const ref = useRef<DTEditFormRefs>();
    const configs: DTEditFormConfigs = DetailTabHeaderConfig(ref, (values, fn) => {
        fn && fn();
        reload && reload();
    });

    function mapClearValue(inveBusinessType) {
        if (detailHeaderClearValue.hasOwnProperty(inveBusinessType)) {
            return detailHeaderClearValue[inveBusinessType];
        } else {
            return {};
        }
    }

    useEffect(() => {
        if (Object.keys(detail).length) {
            ref.current.setDetail(detail);
            let bol = [
                CustomsClearanceStatus.STATUS_CREATED,
                CustomsClearanceStatus.STATUS_PERFECT,
                CustomsClearanceStatus.STATUS_CONFIRMING,
                CustomsClearanceStatus.STATUS_AUDITED,
            ].some(item => item === detail?.status);
            ref.current.setConfigFormItem("inveBusinessType", {
                editable: bol,
                fProps: {
                    rules: bol ? [{ required: true, message: "请选择清关类型" }] : [],
                },
            });
            bol = [
                CustomsClearanceStatus.STATUS_CREATED,
                CustomsClearanceStatus.STATUS_PERFECT,
                CustomsClearanceStatus.STATUS_CONFIRMING,
                CustomsClearanceStatus.STATUS_AUDITED,
                CustomsClearanceStatus.STATUS_ENDORSEMENT,
                CustomsClearanceStatus.STATUS_START_STORAGED,
                CustomsClearanceStatus.STATUS_FAILURE,
            ].some(item => item === detail?.status);
            ref.current.setConfigFormItem("transportMode", {
                editable: bol,
                fProps: { rules: bol ? [{ required: true, message: "请选择运输方式" }] : [] },
            });
            // setTimeout(() => {
            if (detail.inveBusinessType === BussinessType.SIMPLE_PROCESSING) {
                ref.current.setConfigFormItem("inveBusinessType", { editable: false });
            }
            // }, 200)

            ref.current.setConfigFormItem("masterOrderSn", { isHide: !detail?.masterOrderSn });
            ref.current.setConfigFormItem("subOrderSn", { isHide: !detail?.subOrderSn });
            // declareFormNo
            detail?.inveBusinessType === BussinessType.SIMPLE_PROCESSING &&
                lib.request({
                    url: "/ccs/invenorder/listDeclareFormNoByBookId",
                    data: {
                        bookId: detail?.areaBookId,
                    },
                    success(data) {
                        ref.current.setConfigFormItem("declareFormNo", { list: data });
                    },
                });
        }
    }, [detail]);

    useEffect(() => {
        const configs = ref.current.configs;
        for (let i in configs) {
            configs[i].configs.map(item => {
                if (!item.fProps.labelCol) {
                    item.fProps = {
                        ...item.fProps,
                        labelCol: { span: 10 },
                        wrapperCol: { span: 12 },
                    };
                }
            });
        }
        ref.current.setConfig({ ...configs });
    }, []);
    return (
        <>
            <DTEditForm
                layout={{
                    mode: "appoint",
                    colNum: 3,
                }}
                configs={configs}
                ref={ref}
                beforeMergeForm={fieldValue => {
                    return {
                        ...fieldValue,
                        // createTime: fieldValue.createTime && moment(fieldValue.createTime),
                        actualArrivalDate: fieldValue.actualArrivalDate && moment(fieldValue.actualArrivalDate),
                    };
                }}
                onChange={(name, value, selects) => {
                    // 根据不同的inveBusinessType。设置对应的默认值
                    // const detail = ref.current.form.setFieldsValue
                    const newDetail = ref.current.form.getFieldsValue();
                    if (name === "inveBusinessType") {
                        let inveBusinessTypeDesc = selects[0].name;
                        let inveBusinessType = value;
                        let customsFlagDesc, inOrOutFlag, customsType;
                        // 报关标志默认值设置
                        if (
                            [
                                BussinessType.BUSSINESS_ONELINE_IN,
                                BussinessType.ONLINE_REFUND,
                                BussinessType.BONDED_TO_TRADE,
                                BussinessType.BONDED_ONELINE_IN,
                                BussinessType.SUBSEQUENT_TAX,
                            ].includes(inveBusinessType)
                        ) {
                            customsFlagDesc = "报关";
                        } else {
                            customsFlagDesc = "非报关";
                        }

                        // 特殊综合保税区逻辑
                        if (inveBusinessType === BussinessType.BUSSINESS_SECTION_IN && detail.transportMode === "S") {
                            customsFlagDesc = "报关";
                            customsType = 2;
                        }

                        // 出入区默认值设置
                        if (
                            [
                                BussinessType.BUSSINESS_ONELINE_IN,
                                BussinessType.BUSSINESS_REFUND_INAREA,
                                BussinessType.BUSSINESS_SECTION_IN,
                                BussinessType.BUSSINESS_SECTIONINNER_IN,
                                BussinessType.BONDED_ONELINE_IN,
                                BussinessType.INVENTORY_PROFIT,
                            ].some(item => inveBusinessType === item)
                        ) {
                            inOrOutFlag = "入区";
                        } else {
                            inOrOutFlag = "出区";
                        }

                        // 非一线退运，保税物流转大贸
                        let commonClear: DDYObject = {};
                        if (![BussinessType.BONDED_TO_TRADE, BussinessType.ONLINE_REFUND].includes(inveBusinessType)) {
                            commonClear = {
                                corrCusDeclareCompanyId: null,
                                corrCusDeclareCompanyUSCC: null,
                                corrCusDeclareCompanyCode: null,
                                rltCusInnerSFHRCompanyId: null,
                                rltCusInnerSFHRCompanyUSCC: null,
                                rltCusInnerSFHRCompanyCode: null,
                                rltCusXFDYCompanyId: null,
                                rltCusXFDYCompanyUSCC: null,
                                rltCusXFDYCompanyCode: null,
                                rltCusDeclareCompanyId: null,
                                rltCusDeclareCompanyUSCC: null,
                                rltCusDeclareCompanyCode: null,
                                customsTypeDesc: null,
                                customsType: null,
                            };
                        }

                        //置空
                        let clearValue = mapClearValue(inveBusinessType);
                        let newValue = {
                            // ...newDetail,
                            ...commonClear,
                            ...clearValue,
                            inveBusinessType: inveBusinessType,
                            customsFlagDesc: customsFlagDesc || clearValue.customsFlagDesc,
                            customsType: customsType || clearValue.customsType || commonClear.customsType,
                            inOrOutFlag: inOrOutFlag,
                        };
                        ref.current.form.setFieldsValue(newValue);
                    }

                    if (name === "twoStepFlag") {
                        ref.current.form.setFieldsValue({
                            // ...newDetail,
                            twoStepFlag: value,
                            declareWay: value,
                        });
                    }
                    if (name === "finalOwnerCode") {
                        // case "finalOwnerCode":
                        let finalOwnerName = selects[0].value;
                        ref.current.form.setFieldsValue({
                            // ...newDetail,
                            [name]: value,
                            finalOwnerName: finalOwnerName,
                        });
                    }
                    if (name === "rltCusInnerSFHRCompanyId") {
                        ref.current.form.setFieldsValue({
                            // ...newDetail,
                            rltCusInnerSFHRCompanyId: value,
                            rltCusInnerSFHRCompanyCode: selects[0]?.code,
                            rltCusInnerSFHRCompanyUSCC: selects[0]?.uniformSocialCreditCode,
                        });
                    }
                    if (name === "corrCusDeclareCompanyId") {
                        ref.current.form.setFieldsValue({
                            // ...newDetail,
                            corrCusDeclareCompanyId: value,
                            corrCusDeclareCompanyCode: selects[0]?.code,
                            corrCusDeclareCompanyUSCC: selects[0]?.uniformSocialCreditCode,
                        });
                    }
                    if (name === "rltCusXFDYCompanyId") {
                        ref.current.form.setFieldsValue({
                            // ...newDetail,
                            rltCusXFDYCompanyId: value,
                            rltCusXFDYCompanyCode: selects[0]?.code,
                            rltCusXFDYCompanyUSCC: selects[0]?.uniformSocialCreditCode,
                        });
                    }
                    if (name === "rltCusDeclareCompanyId") {
                        ref.current.form.setFieldsValue({
                            // ...newDetail,
                            rltCusDeclareCompanyId: value,
                            rltCusDeclareCompanyCode: selects[0]?.code,
                            rltCusDeclareCompanyUSCC: selects[0]?.uniformSocialCreditCode,
                        });
                    }

                    if (name === "transportMode") {
                        //特殊综合保税区逻辑
                        let objValue = {};
                        if (detail.inveBusinessType === BussinessType.BUSSINESS_SECTION_IN && value === "S") {
                            objValue = {
                                // ...newDetail,
                                customsFlagDesc: "报关",
                                customsType: 2,
                                transportMode: value,
                            };
                            ref.current.form.setFieldsValue(objValue);
                        }
                    }
                }}
                onConfigChange={(name, value) => {
                    const newDetail = ref.current.form.getFieldsValue();

                    if (name === "inveBusinessType") {
                        let bol =
                            [
                                BussinessType.ONLINE_REFUND,
                                BussinessType.BUSSINESS_ONELINE_IN,
                                BussinessType.BONDED_ONELINE_IN,
                                BussinessType.SUBSEQUENT_TAX,
                            ].includes(value) ||
                            (BussinessType.BUSSINESS_SECTION_IN === value && newDetail?.transportMode === "S");
                        ref.current.getConfigFormItem("corrCusDeclareCompanyId").editable = bol;
                        ref.current.getConfigFormItem("corrCusDeclareCompanyId").fProps.rules = bol
                            ? [
                                  {
                                      required: true,
                                      message: `请选择对应报关单申报单位名称`,
                                  },
                              ]
                            : [];

                        //保税物流转大贸 处理逻辑
                        bol = BussinessType.BONDED_TO_TRADE === value;
                        ref.current.getConfigFormItem("rltCusInnerSFHRCompanyId").editable = bol;
                        ref.current.getConfigFormItem("rltCusInnerSFHRCompanyId").fProps.rules = bol
                            ? [
                                  {
                                      required: true,
                                      message: `请选择关联报关单境内收发货人名称`,
                                  },
                              ]
                            : [];
                        ref.current.getConfigFormItem("rltCusXFDYCompanyId").editable = bol;
                        ref.current.getConfigFormItem("rltCusXFDYCompanyId").fProps.rules = bol
                            ? [
                                  {
                                      required: true,
                                      message: `请选择关联报关单消费使用单位名称`,
                                  },
                              ]
                            : [];
                        ref.current.getConfigFormItem("rltCusDeclareCompanyId").editable = bol;
                        ref.current.getConfigFormItem("rltCusDeclareCompanyId").fProps.rules = bol
                            ? [
                                  {
                                      required: true,
                                      message: `请选择关联报关单申报单位名称`,
                                  },
                              ]
                            : [];
                        // ref.current.setConfigFormItems(
                        //     ["rltCusInnerSFHRCompanyId", "rltCusXFDYCompanyId", "rltCusDeclareCompanyId"],
                        //     {
                        //         editable: bol,
                        //         fProps: {
                        //             rules:
                        //                 bol
                        //                     ? [
                        //                         {
                        //                             required: true,
                        //                             message: `请选择`,
                        //                         },
                        //                     ]
                        //                     : [],
                        //         },
                        //     },
                        // );

                        // 实际到港日期处理
                        // case "actualArrivalDate":
                        //     transfereeEnable = BussinessType.BUSSINESS_ONELINE_IN === detail?.inveBusinessType;
                        //     item.pattern = !transfereeEnable ? "readPretty" : "editable";
                        //     item.rules = transfereeEnable
                        //         ? [
                        //               {
                        //                   required: true,
                        //               },
                        //           ]
                        //         : [];
                        //     if (detail?.inveBusinessType === BussinessType.BONDED_ONELINE_IN) {
                        //         item.pattern = "readPretty";
                        //         item.rules = [];
                        //     }
                        bol = BussinessType.BUSSINESS_ONELINE_IN === value;
                        ref.current.getConfigFormItem("actualArrivalDate").editable = bol;
                        ref.current.getConfigFormItem("actualArrivalDate").fProps.rules = bol
                            ? [{ required: true, message: "请选择实际到达时间" }]
                            : [];

                        // 报关单号,清关方式
                        bol = BussinessType.BUSSINESS_ONELINE_IN === value && newDetail?.twoStepFlag;
                        ref.current.getConfigFormItem("customsEntryNo").editable = bol;
                        ref.current.getConfigFormItem("customsEntryNo").fProps.rules = bol
                            ? [
                                  { required: true },
                                  {
                                      max: 18,
                                      message: "长度不允许超过18位",
                                  },
                              ]
                            : [];

                        bol =
                            (BussinessType.BUSSINESS_ONELINE_IN === value && newDetail?.twoStepFlag) ||
                            ([BussinessType.BUSSINESS_SECTIONINNER_OUT, BussinessType.BUSSINESS_SECTION_OUT].includes(
                                value,
                            ) &&
                                detail?.channel === 1);
                        console.log("declareWaybol:", bol, value, detail?.channel);
                        ref.current.getConfigFormItem("declareWay").editable = bol;
                        ref.current.getConfigFormItem("declareWay").fProps.rules = bol ? [{ required: true }] : [];

                        //关联转出账册,关联核注清单编号,关联转入账册
                        bol = [BussinessType.BUSSINESS_SECTION_IN, BussinessType.BUSSINESS_SECTIONINNER_IN].some(
                            item => item === value,
                        );
                        ref.current.getConfigFormItem("outAccountBook").editable = bol;

                        //关联转入账册, 关联核注清单编号
                        bol = [
                            BussinessType.BUSSINESS_SECTION_OUT,
                            BussinessType.BUSSINESS_SECTIONINNER_OUT,
                            BussinessType.SIMPLE_PROCESSING,
                        ].some(item => item === value);
                        // ref.current.setConfigFormItems(["inAccountBook", "associatedEndorsementNo"], {
                        //     editable: bol,
                        // });
                        ref.current.getConfigFormItem("inAccountBook").editable = bol;
                        ref.current.getConfigFormItem("associatedEndorsementNo").editable = bol;

                        //转出方,转入方
                        bol = [
                            BussinessType.BUSSINESS_ONELINE_IN,
                            BussinessType.BUSSINESS_DESTORY,
                            BussinessType.BUSSINESS_REFUND_INAREA,
                            BussinessType.BUSSINESS_EMPTY,
                            BussinessType.BONDED_ONELINE_IN,
                            BussinessType.INVENTORY_PROFIT,
                            BussinessType.RANDOM_INSPECTION_DECLARATION,
                            BussinessType.ONLINE_REFUND,
                            BussinessType.SUBSEQUENT_TAX,
                        ].every(item => item !== value);
                        // ref.current.setConfigFormItems(["transferor", "transferee"], {
                        //     editable: bol,
                        //     fProps: { rules: bol ? [{ required: true }] : [] },
                        // });
                        ref.current.getConfigFormItem("transferor").editable = bol;
                        ref.current.getConfigFormItem("transferor").fProps.rules = bol ? [{ required: true }] : [];
                        ref.current.getConfigFormItem("transferee").editable = bol;
                        ref.current.getConfigFormItem("transferee").fProps.rules = bol ? [{ required: true }] : [];

                        // 类目,品名
                        bol = [
                            BussinessType.BUSSINESS_DESTORY,
                            BussinessType.BUSSINESS_REFUND_INAREA,
                            BussinessType.BUSSINESS_EMPTY,
                            BussinessType.INVENTORY_PROFIT,
                            BussinessType.RANDOM_INSPECTION_DECLARATION,
                            BussinessType.SUBSEQUENT_TAX,
                        ].every(item => item !== value);
                        // ref.current.setConfigFormItems(["category", "productName"], {
                        //     editable: bol,
                        //     fProps: { rules: bol ? [{ required: true }] : [] },
                        // });
                        ref.current.getConfigFormItem("category").editable = bol;
                        ref.current.getConfigFormItem("category").fProps.rules = bol ? [{ required: true }] : [];
                        ref.current.getConfigFormItem("productName").editable = bol;
                        ref.current.getConfigFormItem("productName").fProps.rules = bol ? [{ required: true }] : [];

                        //集装箱号,到货港口/机场
                        bol = [
                            BussinessType.BUSSINESS_ONELINE_IN,
                            BussinessType.BONDED_ONELINE_IN,
                            BussinessType.ONLINE_REFUND,
                        ].includes(value);
                        // ref.current.setConfigFormItems(["conNo", "arrivalPort"], {
                        //     editable: bol,
                        //     fProps: { rules: bol ? [{ required: true }] : [] },
                        // });
                        ref.current.getConfigFormItem("conNo").editable = bol;
                        ref.current.getConfigFormItem("conNo").fProps.rules = bol ? [{ required: true }] : [];
                        ref.current.getConfigFormItem("arrivalPort").editable = bol;
                        ref.current.getConfigFormItem("arrivalPort").fProps.rules = bol ? [{ required: true }] : [];

                        //提单号,货代公司
                        bol = [
                            BussinessType.BONDED_ONELINE_IN,
                            BussinessType.ONLINE_REFUND,
                            BussinessType.BUSSINESS_ONELINE_IN,
                        ].includes(value);
                        ref.current.getConfigFormItem("forwardingCompany").editable = bol;
                        ref.current.getConfigFormItem("forwardingCompany").fProps.rules = bol
                            ? [{ required: true }]
                            : [];
                        ref.current.getConfigFormItem("pickUpNo").editable = bol;
                        ref.current.getConfigFormItem("pickUpNo").fProps.rules = bol ? [{ required: true }] : [];

                        //报关企业
                        // 一线入境： 必填，显示字段，可编辑，下拉 其它： 显示字段，不赋值，不可编辑
                        // bol = BussinessType.BUSSINESS_ONELINE_IN === value;
                        // ref.current.setConfigFormItem("customsEntryCompany", {
                        //     editable: bol,
                        //     fProps: { rules: bol ? [{ required: true }] : [] },
                        // });
                        // ref.current.getConfigFormItem('customsEntryCompany').editable = bol;
                        // ref.current.getConfigFormItem('customsEntryCompany').fProps.rules = bol ? [{ required: true }] : [];

                        //报关单类型
                        // case "customsEntryType":
                        bol =
                            [
                                BussinessType.ONLINE_REFUND,
                                BussinessType.BONDED_TO_TRADE,
                                BussinessType.BUSSINESS_ONELINE_IN,
                                BussinessType.BONDED_ONELINE_IN,
                                BussinessType.SUBSEQUENT_TAX,
                            ].includes(value) ||
                            (BussinessType.BUSSINESS_SECTION_IN === value && newDetail?.transportMode === "S");
                        // ref.current.setConfigFormItem("customsEntryType", {
                        //     editable: bol,
                        //     fProps: { rules: bol ? [{ required: true, message: "请选择报关单类型" }] : [] },
                        // });
                        ref.current.getConfigFormItem("customsEntryType").editable = bol;
                        ref.current.getConfigFormItem("customsEntryType").fProps.rules = bol
                            ? [{ required: true, message: "请选择报关单类型" }]
                            : [];

                        // 是否生成报关单
                        bol =
                            [
                                BussinessType.BUSSINESS_ONELINE_IN,
                                BussinessType.ONLINE_REFUND,
                                BussinessType.BONDED_TO_TRADE,
                                BussinessType.BONDED_ONELINE_IN,
                                BussinessType.SUBSEQUENT_TAX,
                            ].includes(value) ||
                            (BussinessType.BUSSINESS_SECTION_IN === value && newDetail?.transportMode === "S");
                        // ref.current.setConfigFormItem("declarationFlag", {
                        //     editable: bol,
                        //     fProps: {
                        //         rules:
                        //             bol && BussinessType.SUBSEQUENT_TAX !== value
                        //                 ? [{ required: true, message: "请选择是否生成报关单" }]
                        //                 : [],
                        //     },
                        // });
                        ref.current.getConfigFormItem("declarationFlag").editable = bol;
                        ref.current.getConfigFormItem("declarationFlag").fProps.rules =
                            bol && BussinessType.SUBSEQUENT_TAX !== value
                                ? [{ required: true, message: "请选择是否生成报关单" }]
                                : [];

                        // 启运/运抵国
                        // 一线入境： 必填，显示字段，可编辑，下拉 其它： 显示字段，不赋值，不可编辑
                        bol = [
                            BussinessType.BUSSINESS_ONELINE_IN,
                            BussinessType.ONLINE_REFUND,
                            BussinessType.BONDED_ONELINE_IN,
                        ].includes(value);
                        // ref.current.setConfigFormItem("shipmentCountry", {
                        //     editable: bol,
                        //     fProps: { rules: bol ? [{ required: true }] : [] },
                        // });
                        ref.current.getConfigFormItem("shipmentCountry").editable = bol;
                        ref.current.getConfigFormItem("shipmentCountry").fProps.rules = bol ? [{ required: true }] : [];

                        //货主是否自备车辆,车牌号,车辆费用备注
                        ref.current.setConfigFormItems(["selfOwnedVehicle", "licensePlate", "vehicleCostRemark"], {
                            cProps: {
                                disabled: [BussinessType.BUSSINESS_DESTORY, BussinessType.BUSSINESS_REFUND_INAREA].some(
                                    item => item === value,
                                ),
                            },
                            editable: ![
                                BussinessType.BUSSINESS_DESTORY,
                                BussinessType.BUSSINESS_REFUND_INAREA,
                                BussinessType.BUSSINESS_EMPTY,
                            ].some(item => item === value),
                        });
                        // @ts-ignore
                        ref.current.getConfigFormItem("selfOwnedVehicle").cProps = {
                            disabled: [BussinessType.BUSSINESS_DESTORY, BussinessType.BUSSINESS_REFUND_INAREA].some(
                                item => item === value,
                            ),
                        };
                        ref.current.getConfigFormItem("selfOwnedVehicle").editable = ![
                            BussinessType.BUSSINESS_DESTORY,
                            BussinessType.BUSSINESS_REFUND_INAREA,
                            BussinessType.BUSSINESS_EMPTY,
                        ].some(item => item === value);
                        // @ts-ignore
                        ref.current.getConfigFormItem("licensePlate").cProps = {
                            disabled: [BussinessType.BUSSINESS_DESTORY, BussinessType.BUSSINESS_REFUND_INAREA].some(
                                item => item === value,
                            ),
                        };
                        ref.current.getConfigFormItem("licensePlate").editable = ![
                            BussinessType.BUSSINESS_DESTORY,
                            BussinessType.BUSSINESS_REFUND_INAREA,
                            BussinessType.BUSSINESS_EMPTY,
                        ].some(item => item === value);
                        // @ts-ignore
                        ref.current.getConfigFormItem("vehicleCostRemark").cProps = {
                            disabled: [BussinessType.BUSSINESS_DESTORY, BussinessType.BUSSINESS_REFUND_INAREA].some(
                                item => item === value,
                            ),
                        };
                        ref.current.getConfigFormItem("vehicleCostRemark").editable = ![
                            BussinessType.BUSSINESS_DESTORY,
                            BussinessType.BUSSINESS_REFUND_INAREA,
                            BussinessType.BUSSINESS_EMPTY,
                        ].some(item => item === value);

                        // 是否两步申报
                        bol =
                            BussinessType.BUSSINESS_ONELINE_IN === value &&
                            [
                                CustomsClearanceStatus.STATUS_CREATED,
                                CustomsClearanceStatus.STATUS_PERFECT,
                                CustomsClearanceStatus.STATUS_CONFIRMING,
                                CustomsClearanceStatus.STATUS_AUDITED,
                                CustomsClearanceStatus.STATUS_ENDORSEMENT,
                                CustomsClearanceStatus.STATUS_START_STORAGED,
                                CustomsClearanceStatus.STATUS_FAILURE,
                            ].some(item => detail?.status === item);
                        // ref.current.setConfigFormItem("twoStepFlag", {
                        //     editable: bol,
                        //     fProps: {
                        //         rules: bol ? [{ required: true }] : [],
                        //     },
                        // });
                        ref.current.getConfigFormItem("twoStepFlag").editable = bol;
                        ref.current.getConfigFormItem("twoStepFlag").fProps.rules = bol ? [{ required: true }] : [];

                        bol = BussinessType.SIMPLE_PROCESSING === value;
                        ref.current.getConfigFormItem("declareFormNo").editable = bol;

                        const configs = { ...ref.current.configs };
                        ref.current.setConfig({ ...configs });
                    }
                    if (name === "twoStepFlag") {
                        const bol =
                            !!(BussinessType.BUSSINESS_ONELINE_IN === newDetail?.inveBusinessType && value) ||
                            ([BussinessType.BUSSINESS_SECTIONINNER_OUT, BussinessType.BUSSINESS_SECTION_OUT].includes(
                                value,
                            ) &&
                                detail?.channel === 1);
                        const configItem = ref.current.getConfigFormItem("declareWay");
                        configItem.editable = bol;
                        configItem.fProps.rules = bol ? [{ required: true }] : [];

                        const bol1 = !!(BussinessType.BUSSINESS_ONELINE_IN === newDetail?.inveBusinessType && value);
                        ref.current.getConfigFormItem("customsEntryNo").editable = bol1;
                        ref.current.getConfigFormItem("customsEntryNo").fProps.rules = bol1
                            ? [
                                  { required: true },
                                  {
                                      max: 18,
                                      message: "长度不允许超过18位",
                                  },
                              ]
                            : [];
                        ref.current.setConfig({ ...ref.current.configs });
                    }
                    if (name === "transportMode") {
                        let bol =
                            [
                                BussinessType.ONLINE_REFUND,
                                BussinessType.BUSSINESS_ONELINE_IN,
                                BussinessType.BONDED_ONELINE_IN,
                                BussinessType.SUBSEQUENT_TAX,
                            ].includes(newDetail?.inveBusinessType) ||
                            (BussinessType.BUSSINESS_SECTION_IN === newDetail?.inveBusinessType && value === "S");
                        ref.current.getConfigFormItem("corrCusDeclareCompanyId").editable = bol;
                        ref.current.getConfigFormItem("corrCusDeclareCompanyId").fProps.rules = bol
                            ? [
                                  {
                                      required: true,
                                      message: `请选择对应报关单申报单位名称`,
                                  },
                              ]
                            : [];
                        // 是否生成报关单
                        bol =
                            [
                                BussinessType.BUSSINESS_ONELINE_IN,
                                BussinessType.ONLINE_REFUND,
                                BussinessType.BONDED_TO_TRADE,
                                BussinessType.BONDED_ONELINE_IN,
                                BussinessType.SUBSEQUENT_TAX,
                            ].includes(newDetail?.inveBusinessType) ||
                            (BussinessType.BUSSINESS_SECTION_IN === newDetail?.inveBusinessType && value === "S");
                        ref.current.getConfigFormItem("declarationFlag").editable = bol;
                        ref.current.getConfigFormItem("declarationFlag").fProps.rules =
                            bol && BussinessType.SUBSEQUENT_TAX !== value
                                ? [{ required: true, message: "请选择是否生成报关单" }]
                                : [];
                        bol =
                            [
                                BussinessType.ONLINE_REFUND,
                                BussinessType.BONDED_TO_TRADE,
                                BussinessType.BUSSINESS_ONELINE_IN,
                                BussinessType.BONDED_ONELINE_IN,
                                BussinessType.SUBSEQUENT_TAX,
                            ].includes(newDetail?.inveBusinessType) ||
                            (BussinessType.BUSSINESS_SECTION_IN === newDetail?.inveBusinessType && value === "S");
                        ref.current.getConfigFormItem("customsEntryType").editable = bol;
                        ref.current.getConfigFormItem("customsEntryType").fProps.rules = bol
                            ? [{ required: true, message: "请选择报关单类型" }]
                            : [];
                        ref.current.setConfig({ ...ref.current.configs });
                    }
                }}
            />
            <DocumentAttachment detail={detail} />
        </>
    );
};
