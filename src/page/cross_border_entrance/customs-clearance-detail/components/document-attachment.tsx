import React, { useEffect, useState, useRef } from "react";
//@ts-ignore
import { lib, Uploader } from "react-single-app";
import { Button, message, Table, Descriptions, Modal, Space, Alert } from "antd";
import { DTEditForm, DTEditFormRefs, DTEditFormConfigs } from "@dt/components";
/***
 * 单据附件  from customs-clearance-detail2.js
 * @constructor
 */
export default function DocumentAttachment({ detail }) {
    let id = lib.getParam("id");
    const [dataSource, setDataSource] = useState([]);
    const [disabled, setDisabled] = useState(true);
    const [open, setOpen] = useState(false);
    const [downloadOpen, setDownloadOpen] = useState(false);
    const uploaderRef = useRef<any>();
    const ref = useRef<DTEditFormRefs>({} as DTEditFormRefs);
    const downloadRef = useRef<DTEditFormRefs>({} as DTEditFormRefs);
    const [editRow, setEditRow] = useState(null);

    useEffect(() => {
        getDetail();
    }, []);

    function getDetail() {
        lib.request({
            url: "/ccs/invenorder/viewInventoryOrderAttach",
            data: {
                id: id,
            },
            needMask: true,
            success: res => {
                setDataSource(res);
            },
        });
    }
    function previewFunc(row) {
        let newWindow = window.open();
        let img = new Image();
        img.src = row.attachPath;
        newWindow.document.body.appendChild(img);
    }

    function getBlob(url) {
        return new Promise(resolve => {
            const xhr = new XMLHttpRequest();

            xhr.open("GET", url, true);
            xhr.responseType = "blob";
            xhr.onload = () => {
                if (xhr.status === 200) {
                    resolve(xhr.response);
                }
            };

            xhr.send();
        });
    }

    /**
     * 保存
     * @param  {Blob} blob
     * @param  {String} filename 想要保存的文件名称
     */
    function saveAs(blob, filename) {
        //@ts-ignore
        if (window.navigator.msSaveOrOpenBlob) {
            //@ts-ignore
            navigator.msSaveBlob(blob, filename);
        } else {
            const link = document.createElement("a");
            const body = document.querySelector("body");

            link.href = window.URL.createObjectURL(blob);
            link.download = filename;

            // fix Firefox
            link.style.display = "none";
            body.appendChild(link);

            link.click();
            body.removeChild(link);

            window.URL.revokeObjectURL(link.href);
        }
    }

    /**
     * 下载
     * @param  {String} url 目标文件地址
     * @param  {String} filename 想要保存的文件名称
     */
    function download(url, filename) {
        getBlob(url).then(blob => {
            saveAs(blob, filename);
        });
    }

    function downloadFunc(row) {
        setEditRow(row);
        setDownloadOpen(true);
        // console.log(row, "row");
        // return
        // lib.request({
        //     url: "/ccs/invenorder/trace-log-download",
        //     data: {
        //         id: row.id,
        //     },
        //     needMask: true,
        //     success: res => {
        //         if (res) {
        //             // window.open(row.attachPath);
        //             download(row.attachPath, row.attachName);
        //         }
        //     },
        // });
    }

    function deleteFunc(row) {
        Modal.confirm({
            cancelText: "取消",
            okText: "确定",
            title: "确认删除该附件吗？",
            onOk: () => {
                lib.request({
                    url: "/ccs/invenorder/delete-attach",
                    data: {
                        attachId: row.id,
                        orderId: id,
                    },
                    needMask: true,
                    success: res => {
                        if (res) {
                            message.success("删除成功");
                            getDetail();
                        }
                    },
                });
            },
        });
    }

    const columns = [
        {
            title: "文件名称",
            dataIndex: "attachName",
        },
        {
            title: "接收时间",
            dataIndex: "createTime",
        },
        {
            title: "文件类型",
            dataIndex: "attachTypeDesc",
        },
        {
            title: "来源",
            dataIndex: "sourceDesc",
        },
        {
            title: "操作",
            render: row => {
                return (
                    <React.Fragment>
                        <Space>
                            {row.attachName.indexOf("jpg") !== -1 || row.attachName.indexOf("png") !== -1 ? (
                                <a className="link" onClick={() => previewFunc(row)}>
                                    预览
                                </a>
                            ) : row.attachName.indexOf("pdf") !== -1 ? (
                                <a className="link" href={row.attachPath} target="_blank">
                                    预览
                                </a>
                            ) : (
                                <a
                                    className="link"
                                    href={`https://view.officeapps.live.com/op/view.aspx?src=${row.attachPath}`}
                                    // href={`${row.attachPath}`}
                                    target="_blank">
                                    预览
                                </a>
                            )}
                            <a className="link" onClick={() => downloadFunc(row)}>
                                下载
                            </a>
                            <a className="link" onClick={() => deleteFunc(row)}>
                                删除
                            </a>
                        </Space>
                    </React.Fragment>
                );
            },
        },
    ];

    function importFile({ src, name }) {
        const attachType = ref.current.form.getFieldValue("type");

        lib.request({
            url: "/ccs/invenorder/upload-attach",
            data: {
                url: src,
                id: lib.getParam("id"),
                fileName: name,
                attachType: attachType,
            },
            needMask: true,
            success: res => {
                if (res.code == 0) {
                    message.success("附件上传成功");
                    getDetail();
                    setOpen(false);
                    ref.current.form.resetFields();
                    setDisabled(true);
                    uploaderRef.current.querySelector(".react-single-app-uploader input").value = "";
                } else {
                    message.error("附件上传失败");
                }
            },
        });
    }

    return (
        <React.Fragment>
            <Descriptions title="单据附件" style={{ marginLeft: "40px" }}></Descriptions>

            <div style={{ float: "right", paddingBottom: 20 }}>
                <Button
                    type="primary"
                    onClick={() => {
                        setOpen(true);
                    }}>
                    上传单据附件
                </Button>
            </div>
            <div>
                <Table dataSource={dataSource} columns={columns} rowKey="id" pagination={false} />
            </div>
            <Modal
                title={"上传单据附件"}
                open={open}
                onCancel={() => {
                    setOpen(false);
                    ref.current.form.resetFields();
                    setDisabled(true);
                }}
                footer={[
                    <Button
                        onClick={() => {
                            setOpen(false);
                            ref.current.form.resetFields();
                            setDisabled(true);
                        }}>
                        取消
                    </Button>,
                    <Button disabled={disabled} ref={uploaderRef}>
                        确定
                        <Uploader
                            onChange={file => importFile(file)}
                            style={{
                                width: "100%",
                                height: "100%",
                                opacity: 0,
                                position: "absolute",
                                zIndex: 999,
                                top: 0,
                                left: 0,
                            }}
                        />
                    </Button>,
                ]}>
                <DTEditForm
                    configs={[
                        {
                            type: "SELECT",
                            fProps: {
                                label: "文件类型",
                                name: "type",
                                rules: [{ required: true, message: "请选择文件类型" }],
                            },
                            list: [],
                            dataUrl: "/ccs/invenorder/list-attach-type",
                        },
                    ]}
                    ref={ref}
                    layout={{
                        mode: "appoint",
                        colNum: 1,
                    }}
                    onChange={(name, value) => {
                        setDisabled(!(name === "type" && !!(value || value === 0)));
                    }}
                />
            </Modal>

            <Modal
                title="下载"
                open={downloadOpen}
                onCancel={() => {
                    setDownloadOpen(false);
                }}
                onOk={() => {
                    downloadRef.current.form.validateFields().then(values => {
                        console.log(values);
                        lib.request({
                            url: "/ccs/invenorder/download-attach",
                            data: {
                                id: editRow.id,
                                isUpdateCompanyInfo: values.isUpdateCompanyInfo,
                            },
                            needMask: true,
                            success: res => {
                                if (res) {
                                    // window.open(row.attachPath);
                                    download(res, editRow.attachName);
                                }
                            },
                        });
                    });
                }}>
                <>
                    <Alert
                        message="请确认清关企业，系统根据此企业填写文件的收(发)货人"
                        style={{
                            marginBottom: "20px",
                        }}></Alert>
                    <DTEditForm
                        configs={[
                            {
                                type: "SELECT",
                                fProps: {
                                    label: "清关企业",
                                    name: "inveCompanyId",
                                    // rules: [{ required: true, message: "请选择清关企业" }],
                                },
                                cProps: {
                                    disabled: true,
                                },
                                list: [],
                                dataUrl: "/ccs/company/listWithSBQYAll",
                            },
                            {
                                type: "SELECT",
                                fProps: {
                                    name: "isUpdateCompanyInfo",
                                    label: "是否更新收(发)货人",
                                    rules: [{ required: true, message: "请选择" }],
                                },
                                list: [
                                    {
                                        id: 1,
                                        name: "是",
                                    },
                                    {
                                        id: 0,
                                        name: "否",
                                    },
                                ],
                            },
                        ]}
                        detail={{
                            inveCompanyId: detail?.inveCompanyId + "",
                        }}
                        ref={downloadRef}
                        layout={{
                            mode: "appoint",
                            colNum: 1,
                        }}
                    />
                </>
            </Modal>
        </React.Fragment>
    );
}
