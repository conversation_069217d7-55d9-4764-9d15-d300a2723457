import React, { useMemo, useState, useRef, useEffect } from "react";
import { Button, Modal, Table, Form, InputNumber, Input, Select, Radio, Space } from "antd";
import { DDYObject, lib } from "react-single-app";
import { SearchOutlined } from "@ant-design/icons";
import type { InputRef, TableColumnsType, TableColumnType } from "antd";
// import { Button, Input, Space, Table } from 'antd';
import type { FilterDropdownProps } from "antd/es/table/interface";
// import Highlighter from 'react-highlight-words';

let IDX = 36,
    HEX = "";
while (IDX--) HEX += IDX.toString(36);
export function uid(len) {
    let str = "",
        num = len || 11;
    while (num--) str += HEX[(Math.random() * 36) | 0];
    return str;
}

const redColor = "#fff1f0";
const clearanceRowColor = "#f6ffed"; // 清关单表体行背景色
const invoiceRowColor = "#fff7e6"; // 箱单/发票/合同行背景色
const recordRowColor = "#f0f5ff"; // 商品备案/账册库存行背景色
// 三方数据对比函数 - 支持清关单表体、箱单/发票/合同、商品备案/账册库存
const bidui = (obj1, obj2, obj3, type) => {
    const result = {};
    const keys = [
        "originCountry",
        "declarePrice",
        "currency",
        "declareUnitQfy",
        "totalNetWeight",
        "totalGrossWeight",
        "goodsName",
        "hsCode",
        "goodsModel",
        "unitDesc",
        "firstUnitDesc",
        "secondUnitDesc",
    ];

    keys.map(item => {
        // 三方数据对比逻辑
        const val1 = obj1?.[item];
        const val2 = obj2?.[item];
        const val3 = obj3?.[item]; // 商品备案/账册库存数据

        // 检查是否有差异
        const hasDiff = val1 !== val2 || val1 !== val3 || val2 !== val3;

        if (hasDiff) {
            if (type === "clearance" && val1) {
                result[item + "IsRed"] = true;
            }
            if (type === "invoice" && val2) {
                result[item + "IsRed"] = true;
            }
            if (type === "record" && val3) {
                result[item + "IsRed"] = true;
            }
        } else {
            result[item + "IsRed"] = false;
        }
    });
    return result;
};
export default ({ reload, currency, detail, countryList }) => {
    const [data, setData] = useState([]);
    const [open, setOpen] = useState(false);
    const [searchText, setSearchText] = useState("");
    const [searchedColumn, setSearchedColumn] = useState("");
    const searchInput = useRef<InputRef>(null);

    const [groupValue, setGroupValue] = useState();

    const [typeList, setTypeList] = useState([]);
    const [typeVal, setTypeVal] = useState(detail?.draftCompareType);

    const contrastOrder = async (val: boolean | number = false, compareType = 1) => {
        setData([]);
        Promise.all([
            new Promise((resolve, reject) => {
                lib.request({
                    url: "/ccs/invenorder/getDraftCompareFilterList",
                    success(data) {
                        resolve(data);
                    },
                });
            }),
            new Promise((resolve, reject) => {
                lib.request({
                    url: "/ccs/invenorder/compareItemList",
                    data: {
                        id: lib.getParam("id"),
                        compareType: compareType,
                    },
                    needMask: true,
                    success(response) {
                        resolve(response);
                    },
                });
            }),
            // // 新增：获取商品备案/账册库存数据
            // new Promise((resolve, reject) => {
            //     lib.request({
            //         url: "/ccs/invenorder/getRecordItemList",
            //         data: {
            //             id: lib.getParam("id"),
            //         },
            //         success(response) {
            //             resolve(response);
            //         },
            //         fail() {
            //             resolve([]); // 如果获取失败，返回空数组
            //         },
            //     });
            // }),
        ]).then(([keys, response]) => {
            console.log("response:", response);
            // console.log("recordItems:", recordItems);
            let arr = [];
            (response as any[]).map(item => {
                if (!item.productId) return;

                // 查找对应的商品备案/账册库存数据
                const recordItem = (recordItems as any[]).find(record => record.productId === item.productId);

                // 检查是否有三方差异
                const hasThreeWayDiff =
                    recordItem &&
                    (item.item.goodsName !== recordItem.goodsName ||
                        item.item.declarePrice !== recordItem.declarePrice ||
                        item.item.currency !== recordItem.currency ||
                        item.compareItem.goodsName !== recordItem.goodsName ||
                        item.compareItem.declarePrice !== recordItem.declarePrice ||
                        item.compareItem.currency !== recordItem.currency);

                // 筛选逻辑
                if (val && val === 1 && !item.exitsDiff) {
                    return; // 仅显示差异数据
                }
                if (val && val === 2 && !item.exitsFail) {
                    return; // 仅显示比对失败数据
                }
                if (val && val === 3 && !hasThreeWayDiff) {
                    return; // 仅显示三方差异数据
                }

                // 三方数据对比
                const clearanceObj = bidui(item.item, item.compareItem, recordItem, "clearance");
                const invoiceObj = bidui(item.item, item.compareItem, recordItem, "invoice");
                const recordObj = bidui(item.item, item.recorditem, recordItem, "record");

                // 清关单表体数据
                arr.push({
                    ...item.item,
                    productId: item.productId,
                    fromSource: "清关单表体",
                    ...clearanceObj,
                    originalIndex: arr.length,
                });

                // 箱单/发票/合同数据
                arr.push({
                    ...item.compareItem,
                    fromSource: "箱单/发票/合同",
                    productId: item.productId,
                    ...invoiceObj,
                    disabled: true,
                    originalIndex: arr.length,
                });

                // 商品备案/账册库存数据
                // if (recordItem) {
                arr.push({
                    ...recordItem,
                    fromSource: "商品备案/账册库存",
                    productId: item.productId,
                    ...recordObj,
                    disabled: true,
                    originalIndex: arr.length,
                });
                // }
            });
            console.log("arr:", arr);
            setData(arr);
            setOpen(true);
        });
    };
    const handleSearch = (selectedKeys: string[], confirm: FilterDropdownProps["confirm"], dataIndex: string) => {
        confirm();
        setSearchText(selectedKeys[0]);
        setSearchedColumn(dataIndex);
    };

    const handleReset = (clearFilters: () => void) => {
        clearFilters();
        setSearchText("");
    };
    const getColumnSearchProps = (dataIndex: string): TableColumnType<string> => ({
        filterDropdown: ({ setSelectedKeys, selectedKeys, confirm, clearFilters, close }) => (
            <div style={{ padding: 8 }} onKeyDown={e => e.stopPropagation()}>
                <Input
                    ref={searchInput}
                    placeholder={`搜索商品名称`}
                    value={selectedKeys[0]}
                    onChange={e => setSelectedKeys(e.target.value ? [e.target.value] : [])}
                    onPressEnter={() => handleSearch(selectedKeys as string[], confirm, dataIndex)}
                    style={{ marginBottom: 8, display: "block" }}
                />
                <Space>
                    <Button
                        type="primary"
                        onClick={() => handleSearch(selectedKeys as string[], confirm, dataIndex)}
                        icon={<SearchOutlined />}
                        size="small"
                        style={{ width: 90 }}>
                        搜索
                    </Button>
                    <Button
                        onClick={() => {
                            // confirm({ closeDropdown: false });
                            clearFilters && handleReset(clearFilters);
                            setTimeout(() => {
                                // setSearchText("");
                                confirm({ closeDropdown: false });
                                setData([...data]);
                            }, 200);
                        }}
                        size="small"
                        style={{ width: 90 }}>
                        重置
                    </Button>
                    <Button
                        type="link"
                        size="small"
                        onClick={() => {
                            close();
                        }}>
                        关闭
                    </Button>
                </Space>
            </div>
        ),
        filterIcon: (filtered: boolean) => <SearchOutlined style={{ color: "#1677ff", fontSize: "20px" }} />,
        onFilter: (value, record, ...arg) => {
            return (
                record[dataIndex] &&
                record[dataIndex]
                    ?.toString()
                    ?.toLowerCase()
                    ?.includes((value as string).toLowerCase())
            );
        },

        onFilterDropdownOpenChange: visible => {
            if (visible) {
                setTimeout(() => searchInput.current?.select(), 100);
            }
        },
        render: text => (searchedColumn === dataIndex ? <div>{text}</div> : text),
    });

    const columns = [
        {
            title: "海关备案料号",
            dataIndex: "productId",
            key: "productId",
            width: 150,
            onCell: (_, index) => {
                // 支持三行数据：清关单表体、箱单/发票/合同、商品备案/账册库存
                const rowIndex = (index + 1) % 3;
                if (rowIndex === 2 || rowIndex === 0) {
                    return { rowSpan: 0 }; // 第2、3行不显示
                }
                return { rowSpan: 3 }; // 第1行跨3行
            },
        },
        {
            title: "对比数据",
            dataIndex: "fromSource",
            key: "fromSource",
            width: 80,
        },
        {
            title: "原产国（地区）",
            dataIndex: "originCountry",
            key: "originCountry",
            width: 200,
            onCell: (data, index) => {
                return {
                    style: { backgroundColor: data.originCountryIsRed ? redColor : null },
                };
            },
            render: (value, row, index) => {
                if (row.disabled) return (row.originCountry || "") + ":" + (row.originCountryName || "");
                return (
                    <Select
                        value={row.originCountry}
                        style={{ width: "100%" }}
                        disabled={row.disabled}
                        onChange={val => {
                            row.originCountry = val;
                            setData([...data]);
                        }}>
                        {countryList.map(item => {
                            return (
                                <Select.Option value={item.id}>
                                    {item.id}:{item.name}
                                </Select.Option>
                            );
                        })}
                    </Select>
                );
            },
        },
        {
            title: "商品名称",
            dataIndex: "goodsName",
            key: "goodsName",
            width: 200,
            // (detail.draftCompareType === 1?)
            ...getColumnSearchProps("goodsName"),
        },
        {
            title: "申报数量",
            dataIndex: "declareUnitQfy",
            key: "declareUnitQfy",
            onCell: (data, index) => {
                return {
                    style: {
                        backgroundColor: data.declareUnitQfyIsRed ? redColor : null,
                    },
                };
            },

            width: 100,
        },
        {
            title: "法定数量（总）",
            dataIndex: "firstUnitQfy",
            width: 100,
            onCell: (data, index) => {
                return {
                    style: { backgroundColor: data.firstUnitQfyIsRed ? redColor : null },
                };
            },
        },
        {
            title: "第二法定数量（总）",
            dataIndex: "secondUnitQfy",
            key: "secondUnitQfy",
            width: 100,
            onCell: (data, index) => {
                return {
                    style: { backgroundColor: data.secondUnitQfyIsRed ? redColor : null },
                };
            },
        },
        {
            title: "币制",
            dataIndex: "currency",
            key: "currency",
            width: 120,
            onCell: (data, index) => {
                return {
                    style: { backgroundColor: data.currencyIsRed ? redColor : null },
                };
            },
            render: (value, row, index) => {
                // console.log("row.disabled:", row.disabled, row.currencyName)
                if (row.disabled) return (row.currency || "") + ":" + (row.currencyName || "");
                return (
                    <Select
                        value={row.currency}
                        style={{ width: "100%" }}
                        onChange={val => {
                            row.currency = val;
                            setData([...data]);
                        }}>
                        {currency.map(item => {
                            return (
                                <Select.Option value={item.id}>
                                    {item.id}:{item.name}
                                </Select.Option>
                            );
                        })}
                    </Select>
                );
            },
        },
        {
            title: "申报单价",
            dataIndex: "declarePrice",
            key: "declarePrice",
            width: 100,
            onCell: (data, index) => {
                return {
                    style: { backgroundColor: data.declarePriceIsRed ? redColor : null },
                };
            },
            render: (value, row, index) => {
                if (row.disabled) return row.declarePrice;
                return (
                    <InputNumber
                        value={row.declarePrice}
                        onChange={val => {
                            row.declarePrice = val;
                            row.declareTotalPrice = (row.declareUnitQfy * 1000 * val) / 1000;
                            // data[row.dataIndex] = { ...row };
                            setData([...data]);
                        }}
                    />
                );
            },
        },

        {
            title: "申报总价",
            dataIndex: "declareTotalPrice",
            key: "declareTotalPrice",
            width: 100,
        },

        {
            title: "总净重",
            dataIndex: "totalNetWeight",
            key: "totalNetWeight",
            width: 100,
            onCell: (data, index) => {
                // console.log("data.totalNetWeightIsRed:", data)
                return {
                    style: { backgroundColor: data.totalNetWeightIsRed ? redColor : null },
                };
            },
            render: (value, row, index) => {
                // console.log("index:", row)
                if (row.disabled) return <>{row.totalNetWeight}</>;
                return (
                    <InputNumber
                        value={row.totalNetWeight}
                        onChange={val => {
                            console.log(val);
                            row.totalNetWeight = val;
                            setData([...data]);
                        }}
                    />
                );
            },
        },
        {
            title: "总毛重",
            dataIndex: "totalGrossWeight",
            key: "totalGrossWeight",
            width: 100,
            onCell: (data, index) => {
                return {
                    style: { backgroundColor: data.totalGrossWeightIsRed ? redColor : null },
                };
            },
            render: (value, row, index) => {
                if (row.disabled) return row.totalGrossWeight;
                return (
                    <InputNumber
                        value={row.totalGrossWeight}
                        onChange={val => {
                            row.totalGrossWeight = val;
                            console.log(data[index].totalGrossWeight);
                            setData([...data]);
                        }}
                    />
                );
            },
        },
    ];

    const submit = () => {
        console.log("data:", data);
        // 只提交清关单表体的数据（可编辑的数据）
        const itemList = data
            .filter(obj => obj.fromSource === "清关单表体")
            .map(obj => {
                return {
                    id: obj.id,
                    declareUnitQfy: obj.declareUnitQfy,
                    firstUnitQfy: obj.firstUnitQfy,
                    secondUnitQfy: obj.secondUnitQfy,
                    currency: obj.currency,
                    declarePrice: obj.declarePrice,
                    originCountry: obj.originCountry,
                    totalNetWeight: obj.totalNetWeight,
                    totalGrossWeight: obj.totalGrossWeight,
                    source: obj.source,
                    declareTotalPrice: obj.declareTotalPrice,
                };
            });
        console.log("itemList:", itemList);
        lib.request({
            url: "/ccs/invenorder/updateItemListByCompare",
            data: {
                id: lib.getParam("id"),
                itemList: itemList,
            },
            success(response) {
                setOpen(false);
                reload && reload();
            },
        });
    };

    const getTypeData = () => {
        lib.request({
            url: "/ccs/invenorder/listDraftCompareType",
            success(data) {
                setTypeList(data);
            },
        });
    };

    useEffect(() => {
        getTypeData();
    }, []);

    return (
        <>
            <Button
                onClick={() => {
                    setTypeVal(String(detail?.draftCompareType));
                    contrastOrder(false, detail?.draftCompareType);
                }}>
                表体比对
            </Button>
            <Modal
                title="三方表体比对 - 清关单表体 vs 箱单/发票/合同 vs 商品备案/账册库存"
                open={open}
                width={1900}
                onCancel={() => {
                    setOpen(false);
                    setSearchText("");
                    setSearchedColumn("");
                }}
                destroyOnClose
                onOk={() => {
                    submit();
                }}>
                <>
                    {/* 数据源图例 */}
                    <div
                        style={{
                            marginBottom: "16px",
                            padding: "12px",
                            backgroundColor: "#fafafa",
                            borderRadius: "6px",
                        }}>
                        <div style={{ fontWeight: "bold", marginBottom: "8px" }}>数据源说明：</div>
                        <div style={{ display: "flex", gap: "20px", flexWrap: "wrap" }}>
                            <div style={{ display: "flex", alignItems: "center", gap: "8px" }}>
                                <div
                                    style={{
                                        width: "16px",
                                        height: "16px",
                                        backgroundColor: "#f6ffed",
                                        border: "1px solid #d9f7be",
                                    }}></div>
                                <span>清关单表体（可编辑）</span>
                            </div>
                            <div style={{ display: "flex", alignItems: "center", gap: "8px" }}>
                                <div
                                    style={{
                                        width: "16px",
                                        height: "16px",
                                        backgroundColor: "#fff7e6",
                                        border: "1px solid #ffd591",
                                    }}></div>
                                <span>箱单/发票/合同（只读）</span>
                            </div>
                            <div style={{ display: "flex", alignItems: "center", gap: "8px" }}>
                                <div
                                    style={{
                                        width: "16px",
                                        height: "16px",
                                        backgroundColor: "#f0f5ff",
                                        border: "1px solid #adc6ff",
                                    }}></div>
                                <span>商品备案/账册库存（只读）</span>
                            </div>
                            <div style={{ display: "flex", alignItems: "center", gap: "8px" }}>
                                <div
                                    style={{
                                        width: "16px",
                                        height: "16px",
                                        backgroundColor: "#fff1f0",
                                        border: "1px solid #ffccc7",
                                    }}></div>
                                <span>数据差异标识</span>
                            </div>
                        </div>
                    </div>

                    <Radio.Group
                        defaultValue={0}
                        value={groupValue}
                        onChange={e => {
                            const val = e.target.value;
                            setGroupValue(val);
                            contrastOrder(val, Number(typeVal || detail?.draftCompareType));
                        }}>
                        <Radio value={0}>全部</Radio>
                        <Radio value={1}>仅显示差异数据</Radio>
                        <Radio value={2}>仅显示比对失败数据</Radio>
                        <Radio value={3}>仅显示三方差异数据</Radio>
                    </Radio.Group>
                    <div style={{ marginBottom: "10px" }}>
                        <span style={{ marginRight: "10px" }}>对比类型：</span>
                        <Select
                            value={typeVal}
                            style={{ width: 200 }}
                            onChange={val => {
                                setTypeVal(val);
                                contrastOrder(groupValue, Number(val));
                            }}>
                            {typeList.map(item => (
                                <Select.Option key={item.value} value={item.value}>
                                    {item.label}
                                </Select.Option>
                            ))}
                        </Select>
                    </div>
                    <div style={{ marginBottom: "20px" }} />

                    <Table
                        rowKey={row => `${row.productId}_${row.fromSource}_${row.id || uid(8)}`}
                        columns={columns}
                        dataSource={data}
                        pagination={false}
                        scroll={{ x: 1200, y: 1000 }}
                        size="small"
                        rowClassName={record => {
                            if (record.fromSource === "清关单表体") {
                                return "clearance-row";
                            } else if (record.fromSource === "箱单/发票/合同") {
                                return "invoice-row";
                            } else if (record.fromSource === "商品备案/账册库存") {
                                return "record-row";
                            }
                            return "";
                        }}
                    />
                    <style>{`
                        .clearance-row {
                            background-color: #f6ffed !important;
                        }
                        .invoice-row {
                            background-color: #fff7e6 !important;
                        }
                        .record-row {
                            background-color: #f0f5ff !important;
                        }
                        .clearance-row:hover,
                        .invoice-row:hover,
                        .record-row:hover {
                            background-color: #e6f7ff !important;
                        }
                    `}</style>
                </>
            </Modal>
        </>
    );
};
