import React, { useMemo, useState, useRef, useEffect } from "react";
import { Button, Modal, Table, Form, InputNumber, Input, Select, Radio, Space } from "antd";
import { DDYObject, lib } from "react-single-app";
import { SearchOutlined } from "@ant-design/icons";
import type { InputRef, TableColumnsType, TableColumnType } from "antd";
// import { Button, Input, Space, Table } from 'antd';
import type { FilterDropdownProps } from "antd/es/table/interface";
// import Highlighter from 'react-highlight-words';

let IDX = 36,
    HEX = "";
while (IDX--) HEX += IDX.toString(36);
export function uid(len) {
    let str = "",
        num = len || 11;
    while (num--) str += HEX[(Math.random() * 36) | 0];
    return str;
}

const redColor = "#fff1f0";
const bidui = (obj1, obj2, type) => {
    const result = {};
    const keys = [
        "originCountry",
        // "firstUnitQfy",
        // "secondUnitQfy",
        "declarePrice",
        "currency",
        "declareUnitQfy",
        // "unitName",
        "totalNetWeight",
        "totalGrossWeight",
    ];

    // keys = defaults.filter(item => !keys.includes(item));

    // console.log(keys);
    keys.map(item => {
        if (obj1[item] !== obj2[item]) {
            if (type === "in" && obj1[item]) {
                result[item + "IsRed"] = true;
            }
            if (type === "out" && obj2[item]) {
                result[item + "IsRed"] = true;
            }
            // && obj1[item] !== null && obj2[item] !== null
        } else {
            result[item + "IsRed"] = false;
        }
    });
    return result;
};
export default ({ reload, currency, detail, countryList }) => {
    const [data, setData] = useState([]);
    const [open, setOpen] = useState(false);
    const [searchText, setSearchText] = useState("");
    const [searchedColumn, setSearchedColumn] = useState("");
    const searchInput = useRef<InputRef>(null);

    const [groupValue, setGroupValue] = useState();

    const [typeList, setTypeList] = useState([]);
    const [typeVal, setTypeVal] = useState(detail?.draftCompareType);

    const contrastOrder = async (val: boolean | number = false, compareType = 1) => {
        setData([]);
        // console.log(keys);
        Promise.all([
            new Promise((resolve, reject) => {
                lib.request({
                    url: "/ccs/invenorder/getDraftCompareFilterList",
                    success(data) {
                        resolve(data);
                    },
                });
            }),
            new Promise((resolve, reject) => {
                lib.request({
                    url: "/ccs/invenorder/compareItemList",
                    data: {
                        id: lib.getParam("id"),
                        compareType: compareType,
                    },
                    needMask: true,
                    success(response) {
                        resolve(response);
                    },
                });
            }),
        ]).then(([keys, response]) => {
            console.log("response:", response);
            let arr = [];
            (response as any[]).map((item, index) => {
                if (!item.productId) return;
                if (val && val === 1 && !item.exitsDiff) {
                    return;
                }
                if (val && val === 2 && !item.exitsFail) {
                    return;
                }
                const inObj = bidui(item.item, item.compareItem, "in");
                const outObj = bidui(item.item, item.compareItem, "out");
                arr.push({
                    // ...item,
                    ...item.item,
                    productId: item.productId,
                    fromSource: "清关单表体",
                    ...inObj,
                });
                arr.push({
                    // ...item,
                    ...item.compareItem,
                    fromSource: "箱单/发票/合同",
                    productId: item.productId,
                    ...outObj,
                    disabled: true,
                });
            });
            console.log("arr:", arr);
            setData(arr);
            setOpen(true);
        });
    };
    const handleSearch = (selectedKeys: string[], confirm: FilterDropdownProps["confirm"], dataIndex: string) => {
        confirm();
        setSearchText(selectedKeys[0]);
        setSearchedColumn(dataIndex);
    };

    const handleReset = (clearFilters: () => void) => {
        clearFilters();
        setSearchText("");
    };
    const getColumnSearchProps = (dataIndex: string): TableColumnType<string> => ({
        filterDropdown: ({ setSelectedKeys, selectedKeys, confirm, clearFilters, close }) => (
            <div style={{ padding: 8 }} onKeyDown={e => e.stopPropagation()}>
                <Input
                    ref={searchInput}
                    placeholder={`搜索商品名称`}
                    value={selectedKeys[0]}
                    onChange={e => setSelectedKeys(e.target.value ? [e.target.value] : [])}
                    onPressEnter={() => handleSearch(selectedKeys as string[], confirm, dataIndex)}
                    style={{ marginBottom: 8, display: "block" }}
                />
                <Space>
                    <Button
                        type="primary"
                        onClick={() => handleSearch(selectedKeys as string[], confirm, dataIndex)}
                        icon={<SearchOutlined />}
                        size="small"
                        style={{ width: 90 }}>
                        搜索
                    </Button>
                    <Button
                        onClick={() => {
                            // confirm({ closeDropdown: false });
                            clearFilters && handleReset(clearFilters);
                            setTimeout(() => {
                                // setSearchText("");
                                confirm({ closeDropdown: false });
                                setData([...data]);
                            }, 200);
                        }}
                        size="small"
                        style={{ width: 90 }}>
                        重置
                    </Button>
                    <Button
                        type="link"
                        size="small"
                        onClick={() => {
                            close();
                        }}>
                        关闭
                    </Button>
                </Space>
            </div>
        ),
        filterIcon: (filtered: boolean) => <SearchOutlined style={{ color: "#1677ff", fontSize: "20px" }} />,
        onFilter: (value, record, ...arg) => {
            return (
                record[dataIndex] &&
                record[dataIndex]
                    ?.toString()
                    ?.toLowerCase()
                    ?.includes((value as string).toLowerCase())
            );
        },

        onFilterDropdownOpenChange: visible => {
            if (visible) {
                setTimeout(() => searchInput.current?.select(), 100);
            }
        },
        render: text => (searchedColumn === dataIndex ? <div>{text}</div> : text),
    });

    const columns = [
        {
            title: "海关备案料号",
            dataIndex: "productId",
            key: "productId",
            width: 150,
            onCell: (_, index) => {
                if ((index + 1) % 2 === 0) {
                    return { rowSpan: 0 };
                }
                return { rowSpan: 2 };
            },
        },
        {
            title: "对比数据",
            dataIndex: "fromSource",
            key: "fromSource",
            width: 80,
        },
        {
            title: "原产国（地区）",
            dataIndex: "originCountry",
            key: "originCountry",
            width: 200,
            onCell: (data, index) => {
                return {
                    style: { backgroundColor: data.originCountryIsRed ? redColor : null },
                };
            },
            render: (value, row, index) => {
                if (row.disabled) return (row.originCountry || "") + ":" + (row.originCountryName || "");
                return (
                    <Select
                        value={row.originCountry}
                        style={{ width: "100%" }}
                        disabled={row.disabled}
                        onChange={val => {
                            row.originCountry = val;
                            setData([...data]);
                        }}>
                        {countryList.map(item => {
                            return (
                                <Select.Option value={item.id}>
                                    {item.id}:{item.name}
                                </Select.Option>
                            );
                        })}
                    </Select>
                );
            },
        },
        {
            title: "商品名称",
            dataIndex: "goodsName",
            key: "goodsName",
            width: 200,
            // (detail.draftCompareType === 1?)
            ...getColumnSearchProps("goodsName"),
        },
        {
            title: "申报数量",
            dataIndex: "declareUnitQfy",
            key: "declareUnitQfy",
            onCell: (data, index) => {
                return {
                    style: {
                        backgroundColor: data.declareUnitQfyIsRed ? redColor : null,
                    },
                };
            },

            width: 100,
        },
        {
            title: "法定数量（总）",
            dataIndex: "firstUnitQfy",
            width: 100,
            onCell: (data, index) => {
                return {
                    style: { backgroundColor: data.firstUnitQfyIsRed ? redColor : null },
                };
            },
        },
        {
            title: "第二法定数量（总）",
            dataIndex: "secondUnitQfy",
            key: "secondUnitQfy",
            width: 100,
            onCell: (data, index) => {
                return {
                    style: { backgroundColor: data.secondUnitQfyIsRed ? redColor : null },
                };
            },
        },
        {
            title: "币制",
            dataIndex: "currency",
            key: "currency",
            width: 120,
            onCell: (data, index) => {
                return {
                    style: { backgroundColor: data.currencyIsRed ? redColor : null },
                };
            },
            render: (value, row, index) => {
                // console.log("row.disabled:", row.disabled, row.currencyName)
                if (row.disabled) return (row.currency || "") + ":" + (row.currencyName || "");
                return (
                    <Select
                        value={row.currency}
                        style={{ width: "100%" }}
                        onChange={val => {
                            row.currency = val;
                            setData([...data]);
                        }}>
                        {currency.map(item => {
                            return (
                                <Select.Option value={item.id}>
                                    {item.id}:{item.name}
                                </Select.Option>
                            );
                        })}
                    </Select>
                );
            },
        },
        {
            title: "申报单价",
            dataIndex: "declarePrice",
            key: "declarePrice",
            width: 100,
            onCell: (data, index) => {
                return {
                    style: { backgroundColor: data.declarePriceIsRed ? redColor : null },
                };
            },
            render: (value, row, index) => {
                if (row.disabled) return row.declarePrice;
                return (
                    <InputNumber
                        value={row.declarePrice}
                        onChange={val => {
                            row.declarePrice = val;
                            row.declareTotalPrice = (row.declareUnitQfy * 1000 * val) / 1000;
                            // data[row.dataIndex] = { ...row };
                            setData([...data]);
                        }}
                    />
                );
            },
        },

        {
            title: "申报总价",
            dataIndex: "declareTotalPrice",
            key: "declareTotalPrice",
            width: 100,
        },

        {
            title: "总净重",
            dataIndex: "totalNetWeight",
            key: "totalNetWeight",
            width: 100,
            onCell: (data, index) => {
                // console.log("data.totalNetWeightIsRed:", data)
                return {
                    style: { backgroundColor: data.totalNetWeightIsRed ? redColor : null },
                };
            },
            render: (value, row, index) => {
                // console.log("index:", row)
                if (row.disabled) return <>{row.totalNetWeight}</>;
                return (
                    <InputNumber
                        value={row.totalNetWeight}
                        onChange={val => {
                            console.log(val);
                            row.totalNetWeight = val;
                            setData([...data]);
                        }}
                    />
                );
            },
        },
        {
            title: "总毛重",
            dataIndex: "totalGrossWeight",
            key: "totalGrossWeight",
            width: 100,
            onCell: (data, index) => {
                return {
                    style: { backgroundColor: data.totalGrossWeightIsRed ? redColor : null },
                };
            },
            render: (value, row, index) => {
                if (row.disabled) return row.totalGrossWeight;
                return (
                    <InputNumber
                        value={row.totalGrossWeight}
                        onChange={val => {
                            row.totalGrossWeight = val;
                            console.log(data[index].totalGrossWeight);
                            setData([...data]);
                        }}
                    />
                );
            },
        },
    ];

    const submit = () => {
        console.log("data:", data);
        const itemList = data.map(obj => {
            return {
                id: obj.id,
                declareUnitQfy: obj.declareUnitQfy,
                firstUnitQfy: obj.firstUnitQfy,
                secondUnitQfy: obj.secondUnitQfy,
                currency: obj.currency,
                declarePrice: obj.declarePrice,
                originCountry: obj.originCountry,
                totalNetWeight: obj.totalNetWeight,
                totalGrossWeight: obj.totalGrossWeight,
                source: obj.source,
            };
        });
        console.log("itemList:", itemList);
        lib.request({
            url: "/ccs/invenorder/updateItemListByCompare",
            data: {
                id: lib.getParam("id"),
                itemList: itemList,
            },
            success(response) {
                setOpen(false);
                reload && reload();
            },
        });
    };

    const getTypeData = () => {
        lib.request({
            url: "/ccs/invenorder/listDraftCompareType",
            success(data) {
                setTypeList(data);
            },
        });
    };

    useEffect(() => {
        getTypeData();
    }, []);

    return (
        <>
            <Button
                onClick={() => {
                    setTypeVal(String(detail?.draftCompareType));
                    contrastOrder(false, detail?.draftCompareType);
                }}>
                表体比对
            </Button>
            <Modal
                title="表体比对"
                open={open}
                width={1900}
                onCancel={() => {
                    setOpen(false);
                    setSearchText("");
                    setSearchedColumn("");
                }}
                destroyOnClose
                onOk={() => {
                    submit();
                }}>
                <>
                    <Radio.Group
                        defaultValue={0}
                        value={groupValue}
                        onChange={e => {
                            const val = e.target.value;
                            setGroupValue(val);
                            contrastOrder(val, Number(val));
                        }}>
                        <Radio value={0}>全部</Radio>
                        <Radio value={1}>仅显示差异数据</Radio>
                        <Radio value={2}>仅显示比对失败数据</Radio>
                    </Radio.Group>
                    <div style={{ marginBottom: "20px" }} />

                    <Table
                        rowKey={row => row.productId + row.fromSource}
                        // key={"id"}
                        columns={columns}
                        dataSource={data}
                        pagination={false}
                        scroll={{ x: 1000, y: 1000 }}
                    />
                </>
            </Modal>
        </>
    );
};
