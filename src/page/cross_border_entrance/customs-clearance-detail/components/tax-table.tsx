import React, { useEffect, useState } from "react";
import { Table, Button } from "antd";
import { lib } from "react-single-app";

export default () => {
    const [data, setData] = useState([]);
    const columns = [
        {
            title: "核注企业单号",
            dataIndex: "endorsementSn",
        },
        {
            title: "预录入核注编号",
            dataIndex: "preOrderNo",
        },
        {
            title: "核注清单编号",
            dataIndex: "realEndorsementNo",
        },
        {
            title: "核注单状态",
            dataIndex: "endorsementStatusDesc",
        },
        {
            title: "创建时间",
            dataIndex: "createTime",
        },
        {
            title: "企业核放单号",
            dataIndex: "checklistSn",
        },
        {
            title: "是否核扣账册",
            dataIndex: "stockChangeEnable",
            // render: row => {
            //     return <>{row.stockChangeEnable ? "是" : "否"}</>;
            // },
        },
        {
            title: "操作",
            // dataIndex: '',
            render: row => {
                return (
                    <Button
                        type="link"
                        onClick={() => {
                            lib.openPage(`/ccs/nuclear-note-detail?page_title=核注单详情&id=${row.endorsementId}`);
                        }}>
                        查看
                    </Button>
                );
            },
        },
    ];
    const getData = () => {
        lib.request({
            url: "/ccs/invenorder/viewInventoryOrderEndorsement",
            data: {
                id: lib.getParam("id"),
            },
            success(data) {
                setData(data);
            },
        });
    };
    useEffect(() => {
        getData();
    }, []);
    return <Table dataSource={data} columns={columns} />;
};
