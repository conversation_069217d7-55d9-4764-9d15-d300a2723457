import React, { useState, useEffect, Fragment, useRef } from "react";
import {
    Button,
    Input,
    Select,
    Form,
    message,
    Tabs,
    Row,
    Col,
    Table,
    Descriptions,
    Modal,
    InputNumber,
    Space,
    Tooltip,
    Tag,
    Divider,
} from "antd";
//@ts-ignore
import { DDYObject, event, lib, Uploader, UploadFile } from "react-single-app";
import { QuestionCircleOutlined, ExclamationCircleFilled, LockOutlined, UnlockOutlined } from "@ant-design/icons";
import { create, all } from "mathjs";
const mathconfig = {
    number: "BigNumber",
    precision: 20,
};
//@ts-ignore
const math = create(all, mathconfig);
const TabPane = Tabs.TabPane;
const FormItem = Form.Item;
const Option = Select.Option;
import _ from "lodash";
import { BussinessType, CustomsClearanceStatus, TransitFlag } from "../enum";
import BodyDetailInfo from "./body-detail-info";

import NewModal from "@/components/NewModal";
import HeaderTableOperation from "./header-table-operation";
import { dangerousFlagKeysList, dangerousFlagKeys, orderTagList } from "../view-config";
import DraftContrastModal from "./draft-contrast-modal";
import UpdateSourceModal from "./update-source-modal";
const columns1 = [
    {
        title: "行号",
        dataIndex: "idx",
    },
    {
        title: "金二序号",
        dataIndex: "goodsSeqNo",
    },
    {
        title: "统一料号",
        dataIndex: "productId",
    },
    {
        title: "海关备案料号",
        dataIndex: "customsRecordProductId",
    },
    {
        title: "申报数量",
        dataIndex: "declareQty",
    },
];
const columns2 = [
    {
        title: "行号",
        dataIndex: "idx",
    },
    {
        title: "金二序号",
        dataIndex: "goodsSeqNo",
    },
    {
        title: "统一料号",
        dataIndex: "productId",
    },
    {
        title: "海关备案料号",
        dataIndex: "customsRecordProductId",
    },
    {
        title: "错误信息",
        dataIndex: "errorMsg",
    },
];
export default function FinishedProductBody({
    detail = {} as DDYObject,
    countries = [],
    currency = [],
    bodyList = [],
    buttons = [],
    fetchDetail,
    inveBusinessType,
}) {
    let [formRef] = Form.useForm();
    let [dataList, setDataList] = useState([]);
    let [isOld, setOldChange] = useState("new");
    let [isValues, setValues] = useState("");
    const [list, setList] = useState([]);
    let [listItems, setListItems] = useState([]);
    let [visible, setVisible] = useState(false);
    let [previewRow, setPreviewRow] = useState<DDYObject[]>([]);
    let [importResult, setImportResult] = useState<DDYObject>({});
    let [previewModalVisible, setPreviewModalVisible] = useState(false);
    let [showHeaderTableOperation, setShowHeaderTableOperation] = useState(false);
    let [editRow, setEditRow] = useState();
    let [editIndex, setIndex] = useState<number>();
    let [bodyEditType, setBodyEditType] = useState("edit");
    let id = lib.getParam("id");
    const [current, setCurrent] = useState<DDYObject>({});
    const [source, setSource] = useState({});
    const [sourceOpen, setSourceOpen] = useState(false);
    const currentRef = useRef<any>();
    const [selectedRowKeys, setSelectedRowKeys] = useState([]);
    const [babelBol, setBabelBol] = useState(false);
    const [inventoryOrderItemDTOList, setInventoryOrderItemDTOList] = useState<any[]>([]);
    // detail
    let [columns, setColumns] = useState([
        {
            title: "通关校验",
            dataIndex: "verifyResultList",
            width: 160,
            fixed: true,
            render: (text, record, index) => {
                return (
                    <Space wrap>
                        {record.verifyResultList &&
                            record.verifyResultList.map(item => {
                                return (
                                    <Tooltip title={item.note}>
                                        <Tag color="red">{item.desc}</Tag>
                                    </Tooltip>
                                );
                            })}
                    </Space>
                );
            },
        },
        {
            title: "行号",
            width: 60,
            render: (_, __, index) => index + 1,
        },

        {
            title: "报关单商品序号",
            width: 150,
            dataIndex: "declareCustomsGoodsSeqNo",
            key: "declareCustomsGoodsSeqNo",
        },
        {
            title: "是否新品",
            dataIndex: "oldOrNew",
            width: 100,
            render: param => (param === "new" ? "是" : "否"),
        },
        {
            title: "商品sku",
            dataIndex: "skuId",
            width: 160,
        },
        {
            title: "统一料号",
            dataIndex: "originProductId",
            width: 160,
        },
        {
            title: "海关备案料号",
            dataIndex: "productId",
            width: 160,
            render: (text, record, index) => {
                return (
                    <div>
                        {text}
                        {record.productIdTagDesc && <Tag color="red">{record.productIdTagDesc}</Tag>}
                    </div>
                );
            },
        },

        {
            title: "备案序号",
            dataIndex: "goodsSeqNo",
            width: 120,
            render: (text, record, index) => {
                return record.oldOrNew === "new" ? "" : text;
            },
        },
        {
            title: (
                <Tooltip title="记账核注清单的账册序号">
                    记账金二序号
                    <QuestionCircleOutlined style={{ marginLeft: 4 }} />
                </Tooltip>
            ),
            dataIndex: "customsCallBackSeqNo",
            width: 135,
        },
        {
            title: "商品名称",
            dataIndex: "goodsName",
            width: 300,
        },
        {
            title: "申报表序号",
            width: 160,
            dataIndex: "declareFormItemSeqNo",
            key: "declareCustomsGoodsSeqNo",
        },
        {
            title: "HS编码",
            dataIndex: "hsCode",
            width: 100,
        },
        {
            title: "规格型号",
            dataIndex: "goodsModel",
            width: 300,
        },
        {
            title: "申报计量单位",
            dataIndex: "unitDesc",
            width: 120,
        },
        {
            title: "申报数量",
            dataIndex: "declareUnitQfy",
            width: 120,
        },
        {
            title: "法定第一单位",
            dataIndex: "firstUnitDesc",
            width: 120,
        },
        {
            title: "法定数量（单）",
            dataIndex: "firstUnitQfy",
            width: 140,
        },
        {
            title: "法定数量（总）",
            dataIndex: "firstUnitQfys",
            width: 140,
        },
        {
            title: "法定第二单位",
            dataIndex: "secondUnitDesc",
            width: 120,
        },
        {
            title: "第二法定数量（单）",
            dataIndex: "secondUnitQfy",
            width: 160,
        },
        {
            title: "第二法定数量（总）",
            dataIndex: "secondUnitQfys",
            width: 160,
            render: item => {
                return item ? item : "";
            },
        },
        {
            title: "净重(kg)",
            dataIndex: "netweight",
            width: 100,
        },
        {
            title: "毛重(kg)",
            dataIndex: "grossWeight",
            width: 100,
        },
        {
            title: "申报单价",
            dataIndex: "declarePrice",
            width: 140,
        },
        {
            title: "申报总价",
            dataIndex: "declareTotalPrice",
            width: 120,
        },
        {
            title: "原产国(地区)",
            dataIndex: "originCountryDesc",
            width: 120,
        },
        {
            title: "最终目的国(地区)",
            dataIndex: "destinationCountryDesc",
            width: 150,
        },
        {
            title: "商品条码",
            dataIndex: "goodsBar",
            width: 150,
        },
        {
            title: (
                <Tooltip title="请同时更改币制和申报单价">
                    币制
                    <QuestionCircleOutlined style={{ marginLeft: 4 }} />
                </Tooltip>
            ),
            dataIndex: "currencyDesc",
            width: 120,
        },
        {
            title: "关联一线入境报关单号",
            dataIndex: "customsEntryNo",
            width: 150,
        },

        {
            title: "征免方式",
            dataIndex: "avoidTaxMethod",
            width: 120,
        },
        {
            title: "来源标识",
            dataIndex: "goodsSourceDesc",
            width: 120,
        },
        {
            title: "危化品标志",
            dataIndex: "dangerousFlag",
            key: "dangerousFlag",
            width: 120,
            render: (text, row, index) => {
                return dangerousFlagKeys[text] || "";
            },
        },
        {
            title: "操作",
            width: 250,
            fixed: "right",
            render: (text, row, index: number) => {
                return (
                    <Space wrap>
                        {[
                            CustomsClearanceStatus.STATUS_CREATED, //已创建
                            CustomsClearanceStatus.STATUS_PERFECT, //已完善
                            CustomsClearanceStatus.STATUS_CONFIRMING, //提交资料（待确认）
                            CustomsClearanceStatus.STATUS_AUDITED, //审核通过
                            CustomsClearanceStatus.STATUS_ENDORSEMENT, //生成核注单
                            CustomsClearanceStatus.STATUS_START_STORAGED, //已暂存
                            CustomsClearanceStatus.STATUS_FAILURE, //清关异常
                        ].some(item => item === detail.status) && (
                            <Fragment>
                                <a
                                    className={"link"}
                                    onClick={e => {
                                        e.nativeEvent.stopImmediatePropagation();
                                        e.stopPropagation();
                                        setShowHeaderTableOperation(true);
                                        setEditRow(row);
                                        setIndex(index);
                                        setBodyEditType("edit");
                                    }}>
                                    编辑
                                </a>

                                <a
                                    onClick={e => {
                                        e.nativeEvent.stopImmediatePropagation();
                                        e.stopPropagation();
                                        let modal = Modal.confirm({
                                            cancelText: "取消",
                                            okText: "确定",
                                            title: "提示",
                                            content: "确认删除吗？",
                                            onOk: () => {
                                                if (selectedRowKeys.includes(`${row.productId}${row.goodsSeqNo}`)) {
                                                    setSelectedRowKeys(selectedRowKeys => {
                                                        const i = selectedRowKeys.indexOf(
                                                            `${row.productId}${row.goodsSeqNo}`,
                                                        );
                                                        selectedRowKeys.splice(i, 1);
                                                        return selectedRowKeys.slice();
                                                    });
                                                    if (
                                                        currentRef.current &&
                                                        `${currentRef.current.productId}${currentRef.current.goodsSeqNo}` ===
                                                            `${row.productId}${row.goodsSeqNo}`
                                                    ) {
                                                        setCurrent({});
                                                        currentRef.current = {};
                                                    }
                                                }
                                                setListItems(listItems => {
                                                    listItems.splice(index, 1);
                                                    listItems.map((item, index) => {
                                                        item.currentIndex = index + 1;
                                                    });
                                                    return listItems.slice();
                                                });
                                                modal.destroy();
                                            },
                                            onCancel: () => modal.destroy(),
                                        });
                                    }}>
                                    删除
                                </a>
                            </Fragment>
                        )}
                    </Space>
                );
            },
        },
    ]);
    let previewColumns = [
        {
            title: "理货编号",
            dataIndex: "tallyOrderNo",
            width: 150,
        },
        {
            title: "出库单号",
            dataIndex: "outBoundNo",
            width: 180,
        },
        {
            title: "货品名称",
            dataIndex: "goodsName",
            width: 150,
        },
        {
            title: "SKU",
            dataIndex: "sku",
            width: 150,
        },
        {
            title: "料号",
            dataIndex: "productId",
            width: 100,
        },
        {
            title: "计划理货数量",
            dataIndex: "planTallyQty",
            width: 120,
        },
        {
            title: "实际理货数量",
            dataIndex: "actualTallyQty",
            width: 120,
        },
        {
            title: "备注",
            dataIndex: "remark",
            width: 120,
        },
    ];
    useEffect(() => {
        let listItems = bodyList.length ? bodyList : inventoryOrderItemDTOList;
        if (listItems.length) {
            const result = listItems.map((item, index) => {
                const neItem = { ...item };
                neItem.declareUnitQfy = parseInt(item.declareUnitQfy);
                neItem.index = `${item.productId}.${index}`;
                neItem.currentIndex = index + 1;
                if (item.isNew && !item.oldOrNew) {
                    neItem.oldOrNew = item.isNew;
                }

                // neItem.netweights = Number(item.netweight);
                // neItem.netweight = (item.netweight * item.declareUnitQfy).toFixed(2);
                neItem.firstUnitQfys = parseFloat((item.firstUnitQfy * item.declareUnitQfy).toFixed(5));
                if (neItem.secondUnitQfy) {
                    neItem.secondUnitQfys = parseFloat((item.secondUnitQfy * item.declareUnitQfy).toFixed(5));
                }
                return neItem;
            });
            setListItems([...result]);
        } else {
            setListItems([]);
        }
        setColumns(columns => {
            let index = columns.findIndex(item => item.dataIndex === "planDeclareQty");
            if (detail.channel === 1) {
                if (!~index) {
                    let declareUnitQfyIndex = columns.findIndex(item => item.dataIndex === "declareUnitQfy");
                    if (declareUnitQfyIndex !== -1) {
                        columns.splice(
                            declareUnitQfyIndex,
                            0,
                            {
                                title: "计划申报数量",
                                dataIndex: "planDeclareQty",
                                width: 160,
                            },
                            {
                                title: "实际理货数量",
                                dataIndex: "actualTallyQty",
                                width: 160,
                            },
                        );
                    }
                    let index = columns.findIndex(item => item.dataIndex === "declareUnitQfy");
                    columns[index].title = "最终申报数量";
                }
            }
            let customsCallBackSeqNoIndex = columns.findIndex(item => item.dataIndex === "customsCallBackSeqNo");
            if (customsCallBackSeqNoIndex !== -1 && detail?.transitFlag !== TransitFlag.TRANSIT) {
                columns.splice(customsCallBackSeqNoIndex, 1);
            }
            return columns.slice();
        });
    }, [detail, inventoryOrderItemDTOList, bodyList]);

    const getBodyData = () => {
        lib.request({
            url: "/ccs/invenorder/viewInventoryOrderItem",
            data: {
                id: id || lib.getParam("id"),
            },
            needMask: true,
            success: res => {
                setInventoryOrderItemDTOList(res);
            },
        });
    };

    useEffect(() => {
        getBodyData();
        getList();
    }, []);

    useEffect(() => {
        detail?.declareFormNo && getList();
    }, [detail?.declareFormNo]);
    const getList = () => {
        lib.request({
            url: "/ccs/bizDeclareForm/listEndPrdSeqByDeclareFormNo",
            data: {
                declareFormNo: detail?.declareFormNo,
                // id: 3,
            },
            success(data) {
                setList(data);
            },
        });
    };
    // 新增表体数据
    function finishHandle(values) {
        let theData = dataList.find(item => item.declareFormItemSeqNo === values.declareFormItemSeqNo);
        console.log("theData:", theData);
        if (theData && listItems.some(item => `${item.declareFormItemSeqNo}` === `${theData.declareFormItemSeqNo}`)) {
            message.warning("请勿重复添加");
            return;
        }
        console.log("values:", values);
        lib.request({
            url: "/ccs/invenorder/findEndPrdDetail",
            data: {
                bookId: String(detail.areaBookId),
                bizDeclareFormItemId: formRef.getFieldValue("declareFormItemSeqNo"),
                qty: formRef.getFieldValue("declareUnitQfy"),
            },
            needMask: true,
            success: res => {
                if (listItems.findIndex(item => item.declareFormItemSeqNo === res.declareFormItemSeqNo) > -1) {
                    return message.warning("成品已存在");
                }
                setEditRow(res);
                setBodyEditType("add");
                setShowHeaderTableOperation(true);
            },
        });
    }

    // 添加通关教研数据
    function getCheckData(items) {
        items.map(item => {
            item.isNew = item.oldOrNew;
            if (!item.secondUnitDesc) {
                delete item.secondUnitDesc;
            }
            if (!item.secondUnit) {
                delete item.secondUnit;
            }
            if (!item.secondUnitQfys) {
                delete item.secondUnitQfys;
            }
            if (!item.secondUnitQfy) {
                delete item.secondUnitQfy;
            }
        });
        return new Promise((resolve, reject) => {
            lib.request({
                url: "/ccs/invenorder/itemVerify",
                data: {
                    invenOrderId: id,
                    listOrderItems: items,
                },
                success: data => {
                    const result = items.map((item, index) => {
                        item.verifyResultList = data[index];
                        return { ...item };
                    });
                    resolve(result);
                },
            });
        });
    }

    // 保存数据
    function submitHandle() {
        if (listItems.length === 0) {
            message.warning("请选择数据");
            return;
        }
        let flag = false;
        listItems.map(item => {
            item.isNew = item.oldOrNew;
            item.netweight = item.netweight; //不知道为啥要这样赋值，之前人写的🤢
            if (!item.declareUnitQfy) {
                flag = true;
                message.warning(`${item.goodsName}的申报单位数量为0`);
            }
            if (!item.secondUnitDesc) {
                delete item.secondUnitDesc;
            }
            if (!item.secondUnit) {
                delete item.secondUnit;
            }
            if (!item.secondUnitQfys) {
                delete item.secondUnitQfys;
            }
            if (!item.secondUnitQfy) {
                delete item.secondUnitQfy;
            }
        });
        if (inveBusinessType != "REFUND_INAREA") {
            let repeatArry = [];
            const expressCode = listItems.reduce(
                (prev, curr) => [...prev, `${curr.productId}-${curr.goodsSeqNo || ""}`],
                [],
            );
            for (let i = 0; i < expressCode.length; i++) {
                for (let j = i + 1; j < expressCode.length; j++) {
                    if (expressCode[i] === expressCode[j]) {
                        repeatArry.push(expressCode[i]);
                    }
                }
            }
            if (repeatArry.length != 0) {
                let repeatMessage;
                repeatMessage = repeatArry.join(",");
                message.error(`sku-料号-金二序号:${repeatMessage} 存在重复`);
                return;
            }
        }

        if (flag) return;
        lib.request({
            url: "/ccs/invenorder/build-inventory-order-item",
            data: {
                invenOrderId: id,
                listOrderItems: listItems,
            },
            needMask: true,
            success: res => {
                if (res.errorMessage) {
                    message.warning(res.errorMessage);
                } else {
                    if (res && res.code === 2) {
                        message.warning(res.message);
                    }
                    if (res && res.code === -1) {
                        message.error(res.message);
                        return;
                    }
                    fetchDetail({ activeKey: "5", id: lib.getParam("id") });
                    message.success("编辑成功");
                    getBodyData();
                }
            },
        });
    }

    // 弹窗关闭
    function okHandle() {
        setVisible(false);
        setPreviewRow([]);
    }

    // 导入文件
    function importFile() {
        let arr = [];
        const params = { inventoryOrderId: lib.getParam("id"), bookId: detail?.bookId };
        for (let i in params) {
            arr.push(i + "=" + params[i]);
        }
        const str = arr.join("&");
        let importExtendParamBase64 = window.btoa(str + "");
        lib.openPage(
            `/excel/import-data?page_title=清关单成品表体导入&code=IMPORT_INV_ORDER_ITEM_ITEM_END_PRD&importExtendParam=${encodeURIComponent(
                importExtendParamBase64,
            )}`,
            () => {
                getBodyData();
            },
        );
    }

    //预览弹窗确认
    function modalOk() {
        if (importResult.successCount == 0) {
            message.warning("暂无有效数据");
            return;
        }
        lib.request({
            url: "/ccs/invenorder/importExcel",
            data: {
                id,
                recordList: importResult.successRecordList,
            },
            needMask: true,
            success: res => {
                message.success("导入成功");
                setPreviewModalVisible(false);
                setImportResult({});
                getCheckData(res).then((data: any[]) => {
                    setInventoryOrderItemDTOList(data);
                });
            },
        });
    }

    function modalCancel() {
        setImportResult({});
        setPreviewModalVisible(false);
    }

    function renderModal() {
        return (
            <Fragment>
                {/* 导入预览弹窗 */}
                <Modal
                    width="800px"
                    open={previewModalVisible}
                    title={`提交预览  (修改数量${importResult.totalCount})`}
                    onOk={modalOk}
                    okText="提交"
                    onCancel={modalCancel}>
                    <Tabs defaultActiveKey="0">
                        <TabPane tab={`校验成功(${importResult.successCount})`} key="0">
                            <Table
                                dataSource={importResult.successRecordList}
                                columns={columns1}
                                rowKey="productId"></Table>
                        </TabPane>
                        <TabPane tab={`校验失败(${importResult.failCount})`} key="1">
                            <Table
                                dataSource={importResult.failRecordList}
                                columns={columns2}
                                rowKey="productId"></Table>
                        </TabPane>
                    </Tabs>
                </Modal>
                {editRow && (
                    <HeaderTableOperation
                        editRow={editRow}
                        detail={detail}
                        countryList={countries}
                        currencyList={currency}
                        bodyEditType={bodyEditType}
                        showHeaderTableOperation={showHeaderTableOperation}
                        closeModal={(success, formFieldsValue) => {
                            setShowHeaderTableOperation(false);
                            if (success) {
                                const arr = [...listItems];
                                if (bodyEditType === "edit") {
                                    arr[editIndex] = { ...arr[editIndex], ...formFieldsValue };
                                    getCheckData(arr).then((data: any[]) => {
                                        setListItems([...data]);
                                    });
                                } else {
                                    getCheckData([formFieldsValue]).then((data: any[]) => {
                                        setListItems([...arr, ...data]);
                                    });
                                }

                                if (
                                    current &&
                                    `${formFieldsValue.productId}${formFieldsValue.goodsSeqNo}` ===
                                        `${current.productId}${current.goodsSeqNo}`
                                ) {
                                    setCurrent({ ...formFieldsValue });
                                }
                            }
                        }}
                    />
                )}
                <NewModal
                    visible={babelBol}
                    configList={[
                        {
                            labelKey: "dangerousFlag",
                            labelName: "危化品标志",
                            type: "SELECT",
                            list: dangerousFlagKeysList,
                            required: true,
                        },
                    ]}
                    title={"批量设置危化品标志"}
                    onOk={data => {
                        if (
                            [
                                BussinessType.BUSSINESS_DESTORY,
                                BussinessType.INVENTORY_PROFIT,
                                BussinessType.RANDOM_INSPECTION_DECLARATION,
                            ].includes(inveBusinessType)
                        ) {
                            message.error("业务类型：销毁、盘盈、抽检申报，不允许设置危化品标志为是/否");
                            return;
                        }
                        listItems.map(item => {
                            if (selectedRowKeys.includes(`${item.productId}${item.goodsSeqNo}`)) {
                                item.dangerousFlag = data.dangerousFlag;
                                item = { ...item };
                            }
                        });

                        // const arr = JSON.parse(JSON.stringify(listItems))
                        const arr = lib.clone(listItems) as any[];
                        setBabelBol(false);
                        setListItems(arr);

                        const result = arr.filter(
                            item =>
                                `${item.productId}${item.goodsSeqNo}` === `${current.productId}${current.goodsSeqNo}`,
                        );
                        if (result.length > 0) {
                            setCurrent({ ...result[0] });
                        }
                    }}
                    onCancel={() => {
                        setBabelBol(false);
                    }}
                />
                <NewModal
                    visible={sourceOpen}
                    configList={[
                        {
                            labelKey: "skuId",
                            labelName: "SKU",
                            type: "TEXT",
                            disabled: true,
                        },
                        {
                            labelKey: "originProductId",
                            labelName: "统一料号",
                            type: "TEXT",
                            disabled: true,
                        },
                        {
                            labelKey: "productId",
                            labelName: "海关备案料号",
                            type: "TEXT",
                            disabled: true,
                        },
                        {
                            labelKey: "customsEntryNo",
                            labelName: "报关单号",
                            type: "TEXT",
                            disabled: true,
                        },

                        {
                            labelKey: "customsEntryTime",
                            labelName: "报关日期",
                            type: "TEXT",
                            disabled: true,
                        },
                        {
                            labelKey: "originCountry",
                            labelName: "原产国",
                            type: "TEXT",
                            disabled: true,
                        },
                        {
                            labelKey: "shipmentCountry",
                            labelName: "启运国",
                            type: "TEXT",
                            disabled: true,
                        },
                        {
                            labelKey: "fromLocation",
                            labelName: "起运港(始发机场)",
                            type: "TEXT",
                            disabled: true,
                        },
                        {
                            labelKey: "transportMode",
                            labelName: "运输方式",
                            type: "TEXT",
                            disabled: true,
                        },
                        {
                            labelKey: "entryPort",
                            labelName: "进境口岸",
                            type: "TEXT",
                            disabled: true,
                        },
                    ]}
                    title={"查看溯源"}
                    cancelText={() => null}
                    okText={"关闭"}
                    editRow={source}
                    onCancel={() => {
                        setSourceOpen(false);
                    }}
                    onOk={() => {
                        setSourceOpen(false);
                    }}
                />
            </Fragment>
        );
    }

    /**
     * 导出表体
     */
    function exportBody() {
        lib.request({
            url: "/ccs/invenorder/exportItemExcel",
            needMask: true,
            data: { id: id },
            success: json => {
                lib.openPage("/excel/download-center?page_title=下载中心");
            },
        });
    }
    const descriptionsExtra = () => {
        return (
            <Space>
                {[1, 2, 3].includes(detail?.channel) && detail?.inOrOutFlag === "out" && (
                    <Button
                        type="primary"
                        onClick={() => {
                            // /invenorder/releaseLockStockById
                            releaseLockStock();
                        }}>
                        释放全部表体锁定库存
                    </Button>
                )}
                {detail.areaBookType === 4 && (
                    <UpdateSourceModal
                        id={lib.getParam("id")}
                        selectedRowKeys={selectedRowKeys}
                        onSuccess={({ goodsSource, goodsSourceDesc }) => {
                            const arr = listItems.map(item => {
                                if (selectedRowKeys.includes(`${item.productId}${item.goodsSeqNo}`)) {
                                    item.goodsSource = goodsSource;
                                    item.goodsSourceDesc = goodsSourceDesc;
                                }
                                return item;
                            });
                            // setListItems([...listItems]);
                            console.log("arr:", editRow);
                            if (current && current.goodsSourceDesc) {
                                // setEditRow()
                                current.goodsSourceDesc = goodsSourceDesc;
                                setCurrent({ ...current });
                            }
                            setListItems(lib.clone(arr));
                        }}
                    />
                )}
                {buttons.includes("caodan-comparison") && (
                    <DraftContrastModal
                        currency={currency}
                        reload={() => {
                            fetchDetail({ activeKey: "5" });
                            getBodyData();
                        }}
                        detail={detail}
                    />
                )}
                <Button onClick={() => exportBody()}>导出</Button>
                {[
                    BussinessType.BUSSINESS_ONELINE_IN,
                    BussinessType.BUSSINESS_SECTIONINNER_IN,
                    BussinessType.BUSSINESS_SECTION_IN,
                ].includes(detail?.inveBusinessType) && (
                    <Button
                        onClick={() => {
                            const importExtendParamBase64 = window.btoa(`inventoryOrderId=${lib.getParam("id")}`);
                            let url = `/excel/import-data?page_title=导入溯源&code=${encodeURIComponent(
                                "IMPORT_INVENTORY_ITEM_LOGISTICS_INFO",
                            )}&importExtendParam=${encodeURIComponent(importExtendParamBase64)}`;
                            lib.openPage(url, () => {
                                fetchDetail({ activeKey: "5", id: lib.getParam("id") });
                            });
                        }}>
                        导入溯源
                    </Button>
                )}
            </Space>
        );
    };

    function checkEndangeredUploadFile() {
        let notUpload = false;
        for (let i = 0; i < listItems.length; i++) {
            let listItem = listItems[i];
            if (listItem.endangered && !listItem.speciesCertificateAttachmentName) {
                notUpload = true;
                break;
            }
        }
        return notUpload;
    }

    function createNuclearNote() {
        let content = "是否生成核注单?";
        if (checkEndangeredUploadFile()) {
            content = "存在表体涉濒危未上传物种证明，是否核注?";
        }
        Modal.confirm({
            title: "生成核注单",
            content: <span style={{ color: "rgba(0, 0, 0, 0.65)" }}>{content}</span>,
            okText: "确认",
            onOk() {
                return new Promise((resolve, reject) => {
                    lib.request({
                        url: "/ccs/invenorder/generateEndorsement",
                        data: { id: id },
                        success: res => {
                            resolve(res);
                            refreshNotification();
                            fetchDetail({ activeKey: "5", id: lib.getParam("id") });
                            getBodyData();
                            message.success("生成核注单成功");
                        },
                        fail: e => {
                            reject(e);
                        },
                    });
                });
            },
            cancelText: "取消",
        });
    }

    /**
     * 刷新通知
     */
    function refreshNotification() {
        let refresh_event = lib.getParam("refresh_event") as string;
        if (refresh_event) {
            event.emit(refresh_event, true);
        }
    }

    let showSaveBtn = [
        CustomsClearanceStatus.STATUS_CREATED, //已创建
        CustomsClearanceStatus.STATUS_PERFECT, //已完善
        CustomsClearanceStatus.STATUS_CONFIRMING, //提交资料（待确认）
        CustomsClearanceStatus.STATUS_AUDITED, //审核通过
        CustomsClearanceStatus.STATUS_ENDORSEMENT, //生成核注单
        CustomsClearanceStatus.STATUS_START_STORAGED, //已暂存
        CustomsClearanceStatus.STATUS_FAILURE, //清关异常
    ].some(item => item === detail.status);

    const releaseLockStock = () => {
        Modal.confirm({
            title: "确定释放全部表体锁定库存吗",
            onOk: () => {
                lib.request({
                    url: "/ccs/invenorder/releaseLockStockById",
                    data: {
                        id: lib.getParam("id"),
                    },
                    success(data) {
                        message.success("释放成功");
                        fetchDetail({ activeKey: "5", id: lib.getParam("id") });
                        getBodyData();
                    },
                });
            },
        });
    };
    return (
        <React.Fragment>
            {renderModal()}
            <Space>
                <Descriptions
                    title={
                        <div>
                            成品表体
                            {[1, 2, 3].includes(detail?.channel) && detail?.inOrOutFlag === "out" ? (
                                <Tag
                                    icon={detail?.lockStockFlag === 1 ? <LockOutlined /> : <UnlockOutlined />}
                                    bordered={true}
                                    style={{ borderRadius: "10px", marginLeft: "10px" }}
                                    color="blue">
                                    {detail?.lockStockFlag === 1 ? "清关单表体已锁定库存" : "清关单表体未锁定库存"}
                                </Tag>
                            ) : null}
                        </div>
                    }
                    extra={descriptionsExtra()}
                />
            </Space>

            <BodyDetailInfo detail={current} />
            <Form form={formRef} onFinish={finishHandle} labelCol={{ span: 12 }} wrapperCol={{ span: 12 }}>
                <Row>
                    <Col span={8}>
                        <FormItem
                            label="成品申报序号/品名"
                            name="declareFormItemSeqNo"
                            rules={[{ required: true, message: "请选择成品申报序号/品名" }]}>
                            <Select
                                showSearch
                                filterOption={(input, option) => {
                                    return option.props.children.toLowerCase().indexOf(input.toLowerCase()) >= 0;
                                }}>
                                {list.map(item => {
                                    return (
                                        <Option value={item.id} key={item.id}>
                                            {item.goodsName}
                                        </Option>
                                    );
                                })}
                            </Select>
                        </FormItem>
                    </Col>
                    <Col span={8} offset={1}>
                        <Form.Item
                            label="数量"
                            name="declareUnitQfy"
                            rules={[
                                { required: true, message: "请填写数量" },
                                { pattern: /^[1-9]\d{0,7}$/, message: "数量仅能输入整数且不超过8位" },
                            ]}>
                            <Input />
                        </Form.Item>
                    </Col>
                    <Col span={6} offset={1}>
                        {(!detail.orderTagList ||
                            (Array.isArray(detail.orderTagList) &&
                                !detail.orderTagList.includes(orderTagList.CWLABEL))) && (
                            <Space>
                                <Button type="primary" htmlType="submit">
                                    新增
                                </Button>
                                <Button
                                    onClick={() => {
                                        importFile();
                                    }}>
                                    导入
                                </Button>
                            </Space>
                        )}
                    </Col>
                </Row>
            </Form>
            <Table
                dataSource={listItems}
                //@ts-ignore
                // columns={columns.filter(item => {
                //     if (item.dataIndex === "goodsSourceDesc") {
                //         return detail.areaBookType === 4;
                //     }
                //     return true;
                // })}
                columns={columns}
                scroll={{ y: 500 }}
                rowKey={record => `${record.productId}${record.goodsSeqNo}`}
                rowSelection={{
                    type: "checkbox",
                    selectedRowKeys: selectedRowKeys,
                    preserveSelectedRowKeys: false,
                    onChange: (row, selectedRows) => {
                        setSelectedRowKeys(row);
                        if (selectedRows.length === 1) {
                            setCurrent(selectedRows[0]);
                            currentRef.current = { ...selectedRows[0] };
                        } else {
                            setCurrent({});
                            currentRef.current = {};
                        }
                    },
                }}
                onRow={record => {
                    return {
                        onClick: event => {
                            if (selectedRowKeys.includes(`${record.productId}${record.goodsSeqNo}`)) {
                                const numIndex = selectedRowKeys.indexOf(`${record.productId}${record.goodsSeqNo}`);
                                selectedRowKeys.splice(numIndex, 1);
                            } else {
                                selectedRowKeys.push(`${record.productId}${record.goodsSeqNo}`);
                            }
                            if (selectedRowKeys.length === 1) {
                                const result = listItems.filter(
                                    item => `${item.productId}${record.goodsSeqNo}` === selectedRowKeys[0],
                                );
                                setCurrent(result[0]);
                                currentRef.current = { ...result[0] };
                            } else {
                                setCurrent({});
                                currentRef.current = {};
                            }
                            setSelectedRowKeys([...selectedRowKeys]);
                        }, // 点击行
                    };
                }}
                pagination={false}
                summary={pageData => {
                    let totaldeclareUnitQfy = 0;
                    let totaldeclareTotalPrice = 0;
                    let totalfirstUnitQfys = 0;
                    let totalsecondUnitQfys = 0;
                    let totalgrossWeight = 0;
                    let totalnetweight = 0;
                    pageData.forEach(
                        ({
                            declareUnitQfy,
                            declareTotalPrice,
                            firstUnitQfys,
                            secondUnitQfys,
                            netweight,
                            grossWeight,
                        }) => {
                            // totaldeclareUnitQfy += (declareUnitQfy || 0) * 100000;
                            totaldeclareUnitQfy = math.add(totaldeclareUnitQfy, declareUnitQfy || 0);
                            // totaldeclareTotalPrice += (declareTotalPrice || 0) * 100000;
                            totaldeclareTotalPrice = math.add(totaldeclareTotalPrice, declareTotalPrice || 0);
                            // totalfirstUnitQfys += (firstUnitQfys || 0) * 100000;
                            totalfirstUnitQfys = math.add(totalfirstUnitQfys, firstUnitQfys || 0);
                            // totalsecondUnitQfys += (secondUnitQfys || 0) * 100000;
                            totalsecondUnitQfys = math.add(totalsecondUnitQfys, secondUnitQfys || 0);
                            totalgrossWeight = math.add(totalgrossWeight, (grossWeight || 0) * declareUnitQfy);
                            totalnetweight = math.add(totalnetweight, (netweight || 0) * declareUnitQfy);
                        },
                    );
                    return (
                        <Table.Summary fixed>
                            <Table.Summary.Row>
                                <Table.Summary.Cell index={0} colSpan={10}>
                                    汇总：申报总价={totaldeclareTotalPrice.toFixed(6)} 申报数量=
                                    {totaldeclareUnitQfy.toFixed(6)} 法定数量=
                                    {totalfirstUnitQfys.toFixed(6)} 第二法定数量={totalsecondUnitQfys.toFixed(6)}
                                    毛重={totalgrossWeight.toFixed(6)}
                                    净重={totalnetweight.toFixed(6)}
                                </Table.Summary.Cell>
                            </Table.Summary.Row>
                        </Table.Summary>
                    );
                }}
            />
            <div className="btn-wrap">
                <Space>
                    {/* {detail?.orderTagList?.includes(2048) && (
                        <Button
                            onClick={() => {
                                Modal.confirm({
                                    title: "草单确认",
                                    content: (
                                        <>
                                            <span>确定草单确认吗？</span>
                                            <p>草单确认成功后，系统开始全自动调拨申报</p>
                                        </>
                                    ),
                                    onOk: () => {
                                        lib.request({
                                            url: "/ccs/invenorder/confirmDraftV2",
                                            data: {
                                                id: lib.getParam("id"),
                                                compareType: detail?.draftCompareType,
                                            },
                                            success(data) {
                                                message.success("确认成功");
                                                fetchDetail({ activeKey: "1", id: lib.getParam("id") });
                                                getBodyData();
                                            },
                                        });
                                    },
                                });
                            }}>
                            草单确认
                        </Button>
                    )} */}
                    {(detail.status === CustomsClearanceStatus.STATUS_AUDITED ||
                        detail.status === CustomsClearanceStatus.STATUS_PERFECT) &&
                        detail.inveBusinessType !== BussinessType.FB_IN &&
                        detail.inveBusinessType !== BussinessType.FB_OUT &&
                        listItems.length === listItems.filter(item => !!item.originProductId).length && (
                            <Button onClick={() => createNuclearNote()}>生成核注单</Button>
                        )}

                    {showSaveBtn && (
                        <Button type="primary" onClick={submitHandle}>
                            保存
                        </Button>
                    )}
                </Space>
            </div>
            {showSaveBtn && <Divider />}
            {/* 预览文件弹窗 */}
            <Modal open={visible} title="预览" width="1000px" onOk={okHandle} onCancel={okHandle}>
                <Table
                    dataSource={previewRow}
                    pagination={false}
                    columns={previewColumns}
                    scroll={{ x: "max-content" }}
                    rowKey="id"
                />
            </Modal>
        </React.Fragment>
    );
}
