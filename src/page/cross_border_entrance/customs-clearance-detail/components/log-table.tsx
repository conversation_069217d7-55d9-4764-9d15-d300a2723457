import React, { useEffect, useState } from "react";
import { Table, Button, Modal } from "antd";
import { lib } from "react-single-app";
import BaowenBtn from "./baowen-btn";

export default () => {
    const [data, setData] = useState([]);
    const columns = [
        {
            title: "清关单状态",
            dataIndex: "inventoryStatus",
        },
        {
            title: "日志描述",
            dataIndex: "logDesc",
        },
        {
            title: "操作时间",
            dataIndex: "operateTime",
        },
        {
            title: "操作人",
            dataIndex: "operator",
        },
        {
            title: "请求报文",
            dataIndex: "requestMessage",
            render: (val, row, index) => {
                if (!row.requestMessage) return null;
                return (
                    // <Button
                    //     type="link"
                    //     onClick={() => {
                    //         Modal.confirm({
                    //             title: "查看报文",
                    //             // content: "报文字段呢?",
                    //             content: row.requestMessage,
                    //             style: { width: "600px" },
                    //             footer:[
                    //                 <Button onClick={() => this.handleCancel()}>取消</Button>,
                    //                 <Button onClick={() => this.handleCopy(requestData)}>复制报文</Button>,
                    //             ]
                    //         });
                    //     }}>
                    //     查看报文
                    // </Button>
                    <BaowenBtn content={row.requestMessage} />
                );
            },
        },
        {
            title: "返回报文",
            dataIndex: "returnMessage",
            render: (val, row, index) => {
                if (!row.returnMessage) return null;
                return <BaowenBtn content={row.returnMessage} />;
            },
        },
    ];
    const getData = () => {
        lib.request({
            url: "/ccs/invenorder/getInventoryOrderInfoTrackLogs",
            data: {
                inventoryOrderInfoId: lib.getParam("id"),
            },
            success(data) {
                setData(data);
            },
        });
    };
    useEffect(() => {
        getData();
    }, []);
    return <Table dataSource={data} columns={columns} />;
};
