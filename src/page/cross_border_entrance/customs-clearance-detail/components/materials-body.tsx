import React, { useState, useEffect, Fragment, useRef } from "react";
import {
    Button,
    Input,
    Select,
    Form,
    message,
    Tabs,
    Row,
    Col,
    Table,
    Descriptions,
    Modal,
    InputNumber,
    Space,
    Tooltip,
    Tag,
    Divider,
} from "antd";
//@ts-ignore
import { DDYObject, event, lib, Uploader, UploadFile } from "react-single-app";
import { QuestionCircleOutlined, ExclamationCircleFilled, LockOutlined, UnlockOutlined } from "@ant-design/icons";
import { create, all } from "mathjs";
const mathconfig = {
    number: "BigNumber",
    precision: 20,
};
//@ts-ignore
const math = create(all, mathconfig);
const TabPane = Tabs.TabPane;
const FormItem = Form.Item;
const Option = Select.Option;
import _ from "lodash";
import { BussinessType, CustomsClearanceStatus, TransitFlag } from "../enum";
import BodyDetailInfo from "./body-detail-info";

import NewModal from "@/components/NewModal";
import HeaderTableOperation from "./header-table-operation";
import { dangerousFlagKeysList, dangerousFlagKeys, orderTagList } from "../view-config";
import DraftContrastModal from "./draft-contrast-modal";
export default function MaterialsBody({
    detail = {} as DDYObject,
    countries = [],
    currency = [],
    bodyList = [],
    buttons = [],
    fetchDetail,
    inveBusinessType,
}) {
    let [listItems, setListItems] = useState([]);
    let [showHeaderTableOperation, setShowHeaderTableOperation] = useState(false);
    let [editRow, setEditRow] = useState();
    let [editIndex, setIndex] = useState<number>();
    let [bodyEditType, setBodyEditType] = useState("edit");
    let id = lib.getParam("id");
    const [current, setCurrent] = useState<DDYObject>({});
    const currentRef = useRef<any>();
    const [selectedRowKeys, setSelectedRowKeys] = useState([]);
    const [inventoryOrderItemDTOList, setInventoryOrderItemDTOList] = useState<any[]>([]);
    // detail
    let [columns, setColumns] = useState([
        {
            title: "通关校验",
            dataIndex: "verifyResultList",
            width: 160,
            fixed: true,
            render: (text, record, index) => {
                return (
                    <Space wrap>
                        {record.verifyResultList &&
                            record.verifyResultList.map(item => {
                                return (
                                    <Tooltip title={item.note}>
                                        <Tag color="red">{item.desc}</Tag>
                                    </Tooltip>
                                );
                            })}
                    </Space>
                );
            },
        },
        {
            title: "行号",
            width: 60,
            render: (_, __, index) => index + 1,
        },
        {
            title: "申报表序号",
            width: 150,
            dataIndex: "declareFormItemSeqNo",
            key: "declareFormItemSeqNo",
        },
        // {
        //     title: "是否新品",
        //     dataIndex: "oldOrNew",
        //     width: 100,
        //     render: param => (param === "new" ? "是" : "否"),
        // },
        {
            title: "商品sku",
            dataIndex: "skuId",
            width: 160,
        },
        {
            title: "统一料号",
            dataIndex: "originProductId",
            width: 160,
        },
        {
            title: "海关备案料号",
            dataIndex: "productId",
            width: 160,
            render: (text, record, index) => {
                return (
                    <div>
                        {text}
                        {record.productIdTagDesc && <Tag color="red">{record.productIdTagDesc}</Tag>}
                    </div>
                );
            },
        },

        {
            title: "备案序号",
            dataIndex: "goodsSeqNo",
            width: 120,
            render: (text, record, index) => {
                return record.oldOrNew === "new" ? "" : text;
            },
        },
        // {
        //     title: (
        //         <Tooltip title="记账核注清单的账册序号">
        //             记账金二序号
        //             <QuestionCircleOutlined style={{ marginLeft: 4 }} />
        //         </Tooltip>
        //     ),
        //     dataIndex: "customsCallBackSeqNo",
        //     width: 135,
        // },
        {
            title: "商品名称",
            dataIndex: "goodsName",
            width: 300,
        },
        {
            title: "HS编码",
            dataIndex: "hsCode",
            width: 100,
        },
        {
            title: "规格型号",
            dataIndex: "goodsModel",
            width: 300,
        },
        {
            title: "申报计量单位",
            dataIndex: "unitDesc",
            width: 120,
        },
        {
            title: "申报数量",
            dataIndex: "declareUnitQfy",
            width: 120,
        },
        {
            title: "法定第一单位",
            dataIndex: "firstUnitDesc",
            width: 120,
        },
        {
            title: "法定数量（单）",
            dataIndex: "firstUnitQfy",
            width: 140,
        },
        {
            title: "法定数量（总）",
            dataIndex: "firstUnitQfys",
            width: 140,
        },
        {
            title: "法定第二单位",
            dataIndex: "secondUnitDesc",
            width: 120,
        },
        {
            title: "第二法定数量（单）",
            dataIndex: "secondUnitQfy",
            width: 160,
        },
        {
            title: "第二法定数量（总）",
            dataIndex: "secondUnitQfys",
            width: 160,
            render: item => {
                return item ? item : "";
            },
        },
        {
            title: "总毛重(kg)",
            dataIndex: "totalGrossWeight",
            width: 100,
        },
        {
            title: "总净重(kg)",
            dataIndex: "totalNetWeight",
            width: 100,
        },

        {
            title: "申报单价",
            dataIndex: "declarePrice",
            width: 140,
        },
        {
            title: "申报总价",
            dataIndex: "declareTotalPrice",
            width: 120,
        },
        {
            title: "原产国(地区)",
            dataIndex: "originCountryDesc",
            width: 120,
        },
        {
            title: "最终目的国(地区)",
            dataIndex: "destinationCountryDesc",
            width: 150,
        },
        {
            title: "商品条码",
            dataIndex: "goodsBar",
            width: 150,
        },
        {
            title: (
                <Tooltip title="请同时更改币制和申报单价">
                    币制
                    <QuestionCircleOutlined style={{ marginLeft: 4 }} />
                </Tooltip>
            ),
            dataIndex: "currencyDesc",
            width: 120,
        },
        {
            title: "关联一线入境报关单号",
            dataIndex: "customsEntryNo",
            width: 150,
        },

        {
            title: "征免方式",
            dataIndex: "avoidTaxMethod",
            width: 120,
        },
        {
            title: "来源标识",
            dataIndex: "goodsSourceDesc",
            width: 120,
        },
        {
            title: "危化品标志",
            dataIndex: "dangerousFlag",
            key: "dangerousFlag",
            width: 120,
            render: (text, row, index) => {
                return dangerousFlagKeys[text] || "";
            },
        },

        {
            title: "操作",
            width: 150,
            fixed: "right",
            render: (text, row, index: number) => {
                return (
                    <Space wrap>
                        {[
                            CustomsClearanceStatus.STATUS_CREATED, //已创建
                            CustomsClearanceStatus.STATUS_PERFECT, //已完善
                            CustomsClearanceStatus.STATUS_CONFIRMING, //提交资料（待确认）
                            CustomsClearanceStatus.STATUS_AUDITED, //审核通过
                            CustomsClearanceStatus.STATUS_ENDORSEMENT, //生成核注单
                            CustomsClearanceStatus.STATUS_START_STORAGED, //已暂存
                            CustomsClearanceStatus.STATUS_FAILURE, //清关异常
                        ].some(item => item === detail.status) && (
                            <Fragment>
                                <a
                                    className={"link"}
                                    onClick={e => {
                                        e.nativeEvent.stopImmediatePropagation();
                                        e.stopPropagation();
                                        setShowHeaderTableOperation(true);
                                        setEditRow(row);
                                        setIndex(index);
                                        setBodyEditType("edit");
                                    }}>
                                    编辑
                                </a>
                            </Fragment>
                        )}
                    </Space>
                );
            },
        },
    ]);

    const getBodyData = () => {
        lib.request({
            url: "/ccs/invenorder/viewInventoryOrderItemGoodsList",
            data: {
                inventoryOrderId: id || lib.getParam("id"),
            },
            needMask: true,
            success: res => {
                setInventoryOrderItemDTOList(res);
            },
        });
    };

    useEffect(() => {
        getBodyData();
    }, []);
    useEffect(() => {
        let listItems = bodyList.length ? bodyList : inventoryOrderItemDTOList;
        if (listItems.length) {
            const result = listItems.map((item, index) => {
                const neItem = { ...item };
                neItem.declareUnitQfy = parseInt(item.declareUnitQfy);
                neItem.index = `${item.productId}.${index}`;
                neItem.currentIndex = index + 1;
                if (item.isNew && !item.oldOrNew) {
                    neItem.oldOrNew = item.isNew;
                }

                // neItem.netweights = Number(item.netweight);
                // neItem.netweight = (item.netweight * item.declareUnitQfy).toFixed(2);
                neItem.firstUnitQfys = parseFloat((item.firstUnitQfy * item.declareUnitQfy).toFixed(5));
                if (neItem.secondUnitQfy) {
                    neItem.secondUnitQfys = parseFloat((item.secondUnitQfy * item.declareUnitQfy).toFixed(5));
                }
                return neItem;
            });
            setListItems([...result]);
        } else {
            setListItems([]);
        }
        // setColumns(columns => {
        //     let index = columns.findIndex(item => item.dataIndex === "planDeclareQty");
        //     if (detail.channel === 1) {
        //         if (!~index) {
        //             let declareUnitQfyIndex = columns.findIndex(item => item.dataIndex === "declareUnitQfy");
        //             if (declareUnitQfyIndex !== -1) {
        //                 columns.splice(
        //                     declareUnitQfyIndex,
        //                     0,
        //                     {
        //                         title: "计划申报数量",
        //                         dataIndex: "planDeclareQty",
        //                         width: 160,
        //                     },
        //                     {
        //                         title: "实际理货数量",
        //                         dataIndex: "actualTallyQty",
        //                         width: 160,
        //                     },
        //                 );
        //             }
        //             let index = columns.findIndex(item => item.dataIndex === "declareUnitQfy");
        //             columns[index].title = "最终申报数量";
        //         }
        //     }
        //     let customsCallBackSeqNoIndex = columns.findIndex(item => item.dataIndex === "customsCallBackSeqNo");
        //     if (customsCallBackSeqNoIndex !== -1 && detail?.transitFlag !== TransitFlag.TRANSIT) {
        //         columns.splice(customsCallBackSeqNoIndex, 1);
        //     }
        //     return columns.slice();
        // });
    }, [detail, inventoryOrderItemDTOList, bodyList]);

    // 添加通关教研数据
    function getCheckData(items) {
        items.map(item => {
            item.isNew = item.oldOrNew;
            if (!item.secondUnitDesc) {
                delete item.secondUnitDesc;
            }
            if (!item.secondUnit) {
                delete item.secondUnit;
            }
            if (!item.secondUnitQfys) {
                delete item.secondUnitQfys;
            }
            if (!item.secondUnitQfy) {
                delete item.secondUnitQfy;
            }
        });
        return new Promise((resolve, reject) => {
            lib.request({
                url: "/ccs/invenorder/itemVerify",
                data: {
                    invenOrderId: id,
                    listOrderItems: items,
                },
                success: data => {
                    const result = items.map((item, index) => {
                        item.verifyResultList = data[index];
                        return { ...item };
                    });
                    resolve(result);
                },
            });
        });
    }

    function renderModal() {
        return (
            <Fragment>
                {editRow && (
                    <HeaderTableOperation
                        editRow={editRow}
                        detail={detail}
                        countryList={countries}
                        currencyList={currency}
                        bodyEditType={bodyEditType}
                        showHeaderTableOperation={showHeaderTableOperation}
                        isMaterials={true}
                        closeModal={(success, formFieldsValue) => {
                            setShowHeaderTableOperation(false);
                            if (success) {
                                formFieldsValue.id = editRow?.id;
                                if (bodyEditType === "edit") {
                                    edit(formFieldsValue);
                                }
                                if (
                                    current &&
                                    `${formFieldsValue.productId}${formFieldsValue.goodsSeqNo}` ===
                                        `${current.productId}${current.goodsSeqNo}`
                                ) {
                                    setCurrent({ ...formFieldsValue });
                                }
                            }
                        }}
                    />
                )}
            </Fragment>
        );
    }

    const edit = values => {
        lib.request({
            url: "/ccs/invenorder/editInventoryOrderItemGoods",
            data: {
                inventoryOrderId: lib.getParam("id"),
                id: values.id,
                firstUnitQfy: values.firstUnitQfy,
                secondUnitQfy: values.secondUnitQfy,
                declarePrice: values.declarePrice,
                grossWeight: values.grossWeight,
                netweight: values.netweight,
                totalGrossWeight: values.totalGrossWeight,
                totalNetWeight: values.totalNetWeight,
            },
            success(data) {
                message.success("修改成功");
            },
        });
    };

    /**
     * 导出表体
     */
    function exportBody() {
        lib.request({
            url: "/ccs/invenorder/exportItemGoodsExcel",
            needMask: true,
            data: { id: id },
            success: json => {
                lib.openPage("/excel/download-center?page_title=下载中心");
            },
        });
    }
    const releaseLockStock = () => {
        lib.request({
            url: "/ccs/invenorder/releaseLockStockById",
            data: {
                id: lib.getParam("id"),
            },
            success(data) {
                message.success("释放成功");
            },
        });
    };
    const descriptionsExtra = () => {
        return (
            <Space>
                <Button onClick={() => exportBody()}>导出</Button>
            </Space>
        );
    };

    return (
        <React.Fragment>
            {renderModal()}
            <Descriptions title={<div>料件表体</div>} extra={descriptionsExtra()} />
            <BodyDetailInfo detail={current} />
            <Table
                dataSource={listItems}
                //@ts-ignore
                // columns={columns.filter(item => {
                //     if (item.dataIndex === "goodsSourceDesc") {
                //         return detail.areaBookType === 4;
                //     }
                //     return true;
                // })}
                columns={columns}
                scroll={{ y: 500 }}
                rowKey={record => `${record.productId}${record.goodsSeqNo}`}
                onRow={record => {
                    return {
                        onClick: event => {
                            if (selectedRowKeys.includes(`${record.productId}${record.goodsSeqNo}`)) {
                                const numIndex = selectedRowKeys.indexOf(`${record.productId}${record.goodsSeqNo}`);
                                selectedRowKeys.splice(numIndex, 1);
                            } else {
                                selectedRowKeys.push(`${record.productId}${record.goodsSeqNo}`);
                            }
                            if (selectedRowKeys.length === 1) {
                                const result = listItems.filter(
                                    item => `${item.productId}${record.goodsSeqNo}` === selectedRowKeys[0],
                                );
                                setCurrent(result[0]);
                                currentRef.current = { ...result[0] };
                            } else {
                                setCurrent({});
                                currentRef.current = {};
                            }
                            setSelectedRowKeys([...selectedRowKeys]);
                        }, // 点击行
                    };
                }}
                pagination={false}
                summary={pageData => {
                    let totaldeclareUnitQfy = 0;
                    let totaldeclareTotalPrice = 0;
                    let totalfirstUnitQfys = 0;
                    let totalsecondUnitQfys = 0;
                    pageData.forEach(({ declareUnitQfy, declareTotalPrice, firstUnitQfys, secondUnitQfys }) => {
                        // totaldeclareUnitQfy += (declareUnitQfy || 0) * 100000;
                        totaldeclareUnitQfy = math.add(totaldeclareUnitQfy, declareUnitQfy || 0);
                        // totaldeclareTotalPrice += (declareTotalPrice || 0) * 100000;
                        totaldeclareTotalPrice = math.add(totaldeclareTotalPrice, declareTotalPrice || 0);
                        // totalfirstUnitQfys += (firstUnitQfys || 0) * 100000;
                        totalfirstUnitQfys = math.add(totalfirstUnitQfys, firstUnitQfys || 0);
                        // totalsecondUnitQfys += (secondUnitQfys || 0) * 100000;
                        totalsecondUnitQfys = math.add(totalsecondUnitQfys, secondUnitQfys || 0);
                    });
                    return (
                        <Table.Summary fixed>
                            <Table.Summary.Row>
                                <Table.Summary.Cell index={0} colSpan={10}>
                                    汇总：申报总价={totaldeclareTotalPrice.toFixed(6)} 申报数量=
                                    {totaldeclareUnitQfy.toFixed(6)} 法定数量=
                                    {totalfirstUnitQfys.toFixed(6)} 第二法定数量={totalsecondUnitQfys.toFixed(6)}
                                </Table.Summary.Cell>
                            </Table.Summary.Row>
                        </Table.Summary>
                    );
                }}
            />
        </React.Fragment>
    );
}
