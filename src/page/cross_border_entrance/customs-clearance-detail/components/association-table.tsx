import React, { useState, useEffect } from "react";
import { Button, Input, Select, Form, message, Table, Descriptions, Modal, Space, Tabs } from "antd";
import { DDYObject, lib } from "react-single-app";
import { BussinessType, CustomsClearanceStatus } from "../enum";

const FormItem = Form.Item;
const { TextArea } = Input;
const Option = Select.Option;

// 关联单证号
export default function DocumentNumber({ detail = {} as DDYObject, setDetail, fetchDetail }) {
    let [visible, setVisible] = useState(false);
    let [editVisible, setEditVisible] = useState(false);
    let [editRow, setEditRow] = useState<DDYObject>({});
    let [showBtn, setShowBtn] = useState(false);
    let [disabled, setDisabled] = useState(false);
    const [inventoryOrderItemDTOList, setInventoryOrderItemDTOList] = useState<any[]>([]);
    let type = lib.getParam("type");
    let [formRef] = Form.useForm();
    let [editForm] = Form.useForm();
    const [previewResult, setPreviewResult] = useState<DDYObject>({});
    const columns = [
        {
            title: "运单号",
            render: (_, row) => {
                if (row.relTypeDesc === "运单") {
                    return row.relNo;
                }
            },
        },
        {
            title: "报关单号",
            render: (_, row) => {
                if (row.relTypeDesc === "报关单") {
                    return row.relNo;
                }
            },
        },
        {
            title: "操作",
            render: (item, row, index) => {
                return (
                    showBtn && (
                        <React.Fragment>
                            <Space>
                                <span className="link" onClick={() => editHandle(row)}>
                                    编辑
                                </span>
                                <span className="link" onClick={() => deleteHandle(row)}>
                                    删除
                                </span>
                            </Space>
                        </React.Fragment>
                    )
                );
            },
        },
    ];

    const columns1 = [
        {
            title: "运单号",
            dataIndex: "relNo",
            key: "relNo",
        },
    ];
    const columns2 = [
        {
            title: "运单号",
            dataIndex: "relNo",
            key: "relNo",
        },
        {
            title: "错误信息",
            dataIndex: "errorMsg",
            key: "errorMsg",
        },
    ];
    let [tableVisible, setTableVisible] = useState(false);

    useEffect(() => {
        setShowBtn(
            detail.inveBusinessType === BussinessType.BUSSINESS_REFUND_INAREA &&
                detail.channel !== 1 &&
                [
                    CustomsClearanceStatus.STATUS_CREATED,
                    CustomsClearanceStatus.STATUS_PERFECT,
                    CustomsClearanceStatus.STATUS_ENDORSEMENT,
                    CustomsClearanceStatus.STATUS_START_STORAGING,
                    CustomsClearanceStatus.STATUS_START_STORAGED,
                    CustomsClearanceStatus.STATUS_FAILURE,
                ].some(item => item === detail.status),
        );

        if (detail.inveBusinessTypeDesc === "退货入区" || detail.inveBusinessTypeDesc === "一线入境") {
            setDisabled(true);
        }
    }, [detail]);
    let [statusList, setStatusList] = useState([]);
    useEffect(() => {
        lib.request({
            url: "/ccs/invenorder/list-relation-status",
            needMask: true,
            success: res => {
                if (res) {
                    setStatusList(res);
                    if (detail.inveBusinessTypeDesc === "一线入境") {
                        formRef.setFieldsValue({ relType: res[1].id });
                    } else {
                        formRef.setFieldsValue({ relType: res[0].id });
                    }
                }
            },
        });
        getBodyData();
    }, []);

    function editHandle(row) {
        setEditVisible(true);
        setEditRow(row);
        editForm.setFieldsValue(row);
    }

    function deleteHandle(row) {
        const modal = Modal.confirm({
            cancelText: "取消",
            okText: "确定",
            title: "提示",
            content: "确认删除该条数据吗？",
            onOk: res => {
                lib.request({
                    url: "/ccs/invenorder/delete-inventory-order-relation",
                    data: {
                        id: row.id,
                    },
                    needMask: true,
                    success: res => {
                        if (res) {
                            message.success("删除成功");
                            setVisible(false);
                            fetchDetail({ activeKey: "3", id: lib.getParam("id") });
                            // setDetail(detail => {
                            //     return {
                            //         ...detail,
                            //         inventoryOrderItemDTOList: res.result,
                            //     };
                            // });
                            setDetail(res.result);
                            modal.destroy();
                        }
                    },
                });
            },
            onCancel: res => {
                modal.destroy();
            },
        });
    }

    function editModalOk() {
        editForm.submit();
    }
    const onFinish = values => {
        lib.request({
            url: "/ccs/invenorder/update-inventory-order-relation",
            data: {
                id: editRow.id,
                relNo: values.relNo,
            },
            needMask: true,
            success: res => {
                if (res.errorMessage) {
                    message.error(res.errorMessage);
                } else {
                    message.success("编辑成功");
                    setEditRow({});
                    setEditVisible(false);
                    // setDetail(detail => {
                    //     return {
                    //         ...detail,
                    //         inventoryOrderItemDTOList: res.result,
                    //     };
                    // });
                    setDetail(res.result);
                    fetchDetail({ activeKey: "3", id: lib.getParam("id") });
                }
            },
        });
    };

    function editModalCancel() {
        setEditRow({});
        setEditVisible(false);
    }

    function modalOk() {
        formRef.validateFields().then(values => {
            let data = JSON.parse(JSON.stringify(values));
            data.relNos = values.relNos.split("\n");
            data.invenOrderId = Number(lib.getParam("id"));
            getPreviewData(data);
        });
    }

    function modalCancel() {
        setVisible(false);
    }

    const getPreviewData = data => {
        lib.request({
            url: "/ccs/invenorder/pre-build-inventory-order-relation",
            data: {
                invenOrderId: data.invenOrderId,
                relNos: data.relNos,
                relType: data.relType,
            },
            needMask: true,
            success: res => {
                console.log("res:", res);
                setPreviewResult(res);
                setTableVisible(true);
            },
        });
    };

    const getBodyData = () => {
        lib.request({
            url: "/ccs/invenorder/viewInventoryOrderRelation",
            data: {
                id: lib.getParam("id"),
            },
            needMask: true,
            success: res => {
                setInventoryOrderItemDTOList(res);
            },
        });
    };

    return (
        <React.Fragment>
            <Descriptions title="关联单证号"></Descriptions>
            {showBtn && (
                <div style={{ padding: 20, boxSizing: "border-box" }}>
                    <div style={{ float: "right", paddingBottom: 20 }}>
                        <Button
                            type="primary"
                            onClick={() => {
                                setVisible(true);
                                if (detail.inveBusinessTypeDesc === "一线入境") {
                                    formRef.setFieldsValue({ relType: statusList[1].id });
                                } else {
                                    formRef.setFieldsValue({ relType: statusList[0].id });
                                }
                            }}>
                            录入单证号
                        </Button>
                    </div>
                </div>
            )}

            <div>
                <Table dataSource={inventoryOrderItemDTOList} columns={columns} rowKey="id" pagination={false}></Table>
            </div>
            <Modal
                title="编辑单证号"
                open={editVisible}
                onOk={editModalOk}
                onCancel={editModalCancel}
                destroyOnClose={true}>
                <Form form={editForm} preserve={false} onFinish={onFinish}>
                    <FormItem label="单证号" name="relNo" rules={[{ required: true }]}>
                        <Input />
                    </FormItem>
                </Form>
            </Modal>
            <Modal title="录入单证号" open={visible} onCancel={modalCancel} onOk={modalOk}>
                <Form form={formRef} preserve={false}>
                    <FormItem label="单证类型" name="relType">
                        <Select style={{ width: 150 }} disabled={disabled}>
                            {statusList.map((item, index) => {
                                return (
                                    <Option key={index} value={item.id}>
                                        {item.name}
                                    </Option>
                                );
                            })}
                        </Select>
                    </FormItem>
                    <FormItem label="单证号" name="relNos" rules={[{ required: true, message: "请输入单证号" }]}>
                        <TextArea rows={20} />
                    </FormItem>
                </Form>
            </Modal>
            <Modal
                title="预览"
                open={tableVisible}
                onCancel={() => {
                    setTableVisible(false);
                    setPreviewResult({});
                }}
                onOk={() => {
                    formRef.validateFields().then(values => {
                        let data = JSON.parse(JSON.stringify(values));
                        data.relNos = values.relNos.split("\n");
                        data.invenOrderId = Number(lib.getParam("id"));
                        lib.request({
                            url: "/ccs/invenorder/build-inventory-order-relation",
                            data,
                            needMask: true,
                            success: res => {
                                if (!res.errorMessage) {
                                    message.success("导入成功");
                                    setVisible(false);
                                    setDetail(res.result);
                                    fetchDetail({ activeKey: "3", id: lib.getParam("id") });
                                    setTableVisible(false);
                                    setPreviewResult({});
                                    formRef.resetFields();
                                } else {
                                    setVisible(false);
                                    setTableVisible(false);
                                    setPreviewResult({});
                                    formRef.resetFields();
                                    message.error(res.errorMessage);
                                }
                            },
                        });
                    });
                }}
                destroyOnClose>
                <Tabs defaultActiveKey="0">
                    <Tabs.TabPane tab={`校验成功(${previewResult.successCount})`} key="0">
                        <Table dataSource={previewResult.successList || []} columns={columns1} rowKey="relNo"></Table>
                    </Tabs.TabPane>
                    <Tabs.TabPane tab={`校验失败(${previewResult.errorCount})`} key="1">
                        <Table dataSource={previewResult.errorList || []} columns={columns2} rowKey="relNo"></Table>
                    </Tabs.TabPane>
                </Tabs>
            </Modal>
        </React.Fragment>
    );
}
