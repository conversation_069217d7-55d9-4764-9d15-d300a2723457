import React, { useState, useEffect, useRef } from "react";
import { Form } from "antd";
import { derailBodyInfoConfig } from "../view-config";
//@ts-ignore
import { lib, ConfigFormCenter, event } from "react-single-app";
export default ({ detail }) => {
    const ref = useRef<any>();
    useEffect(() => {
        if (detail && Object.keys(detail).length != 0) {
            ref.current.setMergeDetail(detail);
        } else {
            ref.current.getForm.resetFields();
        }
    }, [detail]);
    return (
        <ConfigFormCenter
            ref={ref}
            formProps={{ layout: "horizontal", formItemLayout: { labelCol: { span: 8 }, wrapperCol: { span: 14 } } }}
            confData={derailBodyInfoConfig}
            // disableEdit={true}
        />
    );
};
