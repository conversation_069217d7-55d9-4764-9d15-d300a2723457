import React, { useEffect, useRef, useState } from "react";
import { DTEditForm, DTEditFormRefs, DTEditFormConfigs } from "@dt/components";
import { DetailHeaderConfig } from "../view-config";
import { message } from "antd";
import moment from "moment";
import { lib } from "react-single-app";
import NewModal from "@/components/NewModal";
export default ({ detail, reload, buttons }) => {
    const ref = useRef<DTEditFormRefs>();
    const [open, setOpen] = useState(false);
    // const [configs, setConfigs] = useState<DTEditFormConfigs>(DetailHeaderConfig);
    const configs = DetailHeaderConfig(
        ref,
        (values, fn) => {
            if (values) {
                lib.request({
                    url: "/ccs/invenorder/detail/updHead",
                    data: { ...values, id: lib.getParam("id") },
                    success: () => {
                        fn && fn();
                        reload && reload();
                    },
                });
            } else {
                reload && reload();
            }
        },
        () => {
            setOpen(true);
        },
        buttons,
    );
    useEffect(() => {
        if (Object.keys(detail).length) {
            ref.current.setDetail(detail);
            ref.current.setConfig({ ...ref.current.configs });
        }
    }, [detail]);

    useEffect(() => {
        setTimeout(() => {
            const configs = DetailHeaderConfig(
                ref,
                (values, fn) => {
                    if (values) {
                        lib.request({
                            url: "/ccs/invenorder/detail/updHead",
                            data: { ...values, id: lib.getParam("id") },
                            success: () => {
                                fn && fn();
                                reload && reload();
                            },
                        });
                    } else {
                        reload && reload();
                    }
                },
                () => {
                    setOpen(true);
                },
                buttons,
            );
            //@ts-ignore
            configs.header.configs.map(item => {
                if (!item.fProps.labelCol) {
                    item.fProps = {
                        ...item.fProps,
                        labelCol: { span: 10 },
                        wrapperCol: { span: 12 },
                    };
                }
            });
            ref.current.setConfig({ ...configs });
        }, 500);
    }, [buttons]);

    return (
        <>
            <DTEditForm
                layout={{
                    mode: "appoint",
                    colNum: 3,
                }}
                configs={configs}
                ref={ref}
                beforeMergeForm={fieldValue => {
                    return {
                        ...fieldValue,
                        // createTime: fieldValue.createTime ? moment(fieldValue.createTime) : null,
                    };
                }}
                onConfigChange={(name, value) => {
                    if (name === "transitFlagDesc") {
                        console.log(name, value);
                        const bol = value === "是";
                        ref.current.setConfigFormItem("finalEntityWarehouseCode", {
                            fProps: { rules: bol ? [{ required: true }] : [] },
                            editable: bol,
                        });
                        ref.current.setConfigFormItem("finalOwnerCode", {
                            fProps: { rules: bol ? [{ required: true }] : [] },
                            editable: bol,
                        });
                        // 清关企业似乎是详情携带过来的
                        // ref.current.setConfigFormItem('finalInveCompanyName', {
                        //     fProps: { rules: value === 1 ? [{ required: true }] : [] },
                        //     editable: value === 1
                        // })
                        ref.current.setConfigFormItem("finalBookId", {
                            fProps: { rules: bol ? [{ required: true }] : [] },
                            editable: bol,
                        });
                    }
                }}
                onChange={(name, value) => {
                    if (name === "finalBookId") {
                        ref.current.form.setFieldsValue({ finalOwnerCode: null });
                    }
                }}
            />
            <NewModal
                visible={open}
                configList={[
                    {
                        labelKey: "associatedOrderSn",
                        labelName: "请输入关联清关单号",
                        type: "INPUT",
                        labelCol: { span: 8 },
                        required: true,
                        // disabled: true,
                    },
                ]}
                title={"关联清关单号"}
                okText={"确认"}
                cancelText={"取消"}
                // editRow={source}
                onCancel={() => {
                    setOpen(false);
                }}
                onOk={values => {
                    console.log("values:", values);
                    values.inveOrderSn = detail.inveCustomsSn;
                    lib.request({
                        url: "/ccs/invenorder/associateTransfer",
                        data: values,
                        needMask: true,
                        success: res => {
                            message.success("关联成功");
                            setOpen(false);
                            reload && reload();
                        },
                    });
                }}
            />
        </>
    );
};
