import React, { useState, useRef } from "react";
import { Modal, Button, message } from "antd";
import { DTEditForm, DTEditFormRefs, DTEditFormConfigs } from "@dt/components";
import { lib } from "react-single-app";

export default ({ id, onSuccess }) => {
    const [visible, setVisible] = useState(false);
    const ref = useRef<DTEditFormRefs>({} as DTEditFormRefs);
    const goodsSourceDesc = useRef();
    // 表单配置
    const configs: DTEditFormConfigs = [
        {
            type: "SELECT",
            fProps: {
                label: "来源标识",
                name: "goodsSource",
                rules: [{ required: true, message: "请选择来源标识" }],
                // labelCol: { span: 24 },
            },
            list: [],
            dataUrl: "/ccs/goodsSource/list",
            cProps: {
                placeholder: "请选择来源标识",
                style: { width: "100%" },
            },
        },
    ];

    // 提交表单
    const handleSubmit = () => {
        ref.current.form.validateFields().then(values => {
            values.goodsSourceDesc = goodsSourceDesc.current;
            onSuccess(values);
            ref.current.form.resetFields();
            setVisible(false);
        });
    };

    return (
        <>
            <Button onClick={() => setVisible(true)}>修改来源标识</Button>

            <Modal
                title="设置来源标识"
                open={visible}
                onCancel={() => setVisible(false)}
                onOk={handleSubmit}
                maskClosable={false}
                destroyOnClose={true}
                cancelText="取消"
                okText="确定"
                width={500}
                closeIcon={<span className="modal-close-icon">×</span>}
                className="source-flag-modal">
                <DTEditForm
                    ref={ref}
                    configs={configs}
                    layout={{
                        mode: "appoint",
                        colNum: 1,
                    }}
                    onChange={(name, value, selects) => {
                        if (name === "goodsSource") {
                            goodsSourceDesc.current = selects[0].name;
                        }
                    }}
                />
            </Modal>
        </>
    );
};
