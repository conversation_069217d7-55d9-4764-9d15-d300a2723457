import React, { useState, useEffect, useMemo, useCallback } from "react";
import { Tabs, Steps } from "antd";
//@ts-ignore
import { lib, hooks } from "react-single-app";
import DetailBody from "./components/detail-body";
import DetailHeader from "./components/detail-header";
import Headers from "./components/header";
import TaxTable from "./components/tax-table";
import LogTable from "./components/log-table";
import AssociationTable from "./components/association-table";

import "./index.less";
import FinishedProductBody from "./components/finished-product-body";
import MaterialsBody from "./components/materials-body";
import { BussinessType } from "./enum";
const { useGetAuthButtons } = hooks;
const TabPane = Tabs.TabPane;
export default () => {
    let id = lib.getParam("id");
    const [buttons] = useGetAuthButtons({ systemCode: "CCS_ADMIN", pagePath: "/customs-clearance-manage" });
    const [activeKey, setActiveKey] = useState("0");
    const [detail, setDetail] = useState({});
    const [countries, setCountries] = useState([]);
    const [currency, setCurrency] = useState();
    const [inveBusinessType, setInveBusinessType] = useState();
    const [flowState, setFlowState] = useState();

    const [bodyList, setBodyList] = useState([]);
    function fetchDetail({ activeKey = "0", id }) {
        lib.request({
            url: "/ccs/invenorder/getFlowState",
            data: {
                id: id || lib.getParam("id"),
            },
            needMask: true,
            success: res => {
                setFlowState(res);
            },
        });
        lib.request({
            url: "/ccs/invenorder/detail",
            data: {
                id: id || lib.getParam("id"),
            },
            needMask: true,
            success: res => {
                if (!res.actualArrivalDate) delete res.actualArrivalDate;
                setInveBusinessType(res.inveBusinessType);
                if (activeKey == "2") {
                    setDetail(detail => {
                        return {
                            ...detail,
                            // inventoryOrderRelationDTOList: res.inventoryOrderRelationDTOList,
                        };
                    });
                } else {
                    setDetail(res);
                }

                // setActiveKey(activeKey);
            },
        });
    }

    // 获取详情
    useEffect(() => {
        id && fetchDetail({ activeKey: "0", id });
    }, [id]);
    // 获取下拉列表
    useEffect(() => {
        [
            ["/ccs/customs/listCountry", setCountries],
            ["/ccs/customs/listCurrency", setCurrency],
            //@ts-ignore
        ].map(([url, success]) => lib.request({ url, success }));
    }, []);

    // console.log("buttons:", buttons);

    const loadFn = useMemo(() => {
        console.log("relaod2222 ------> activeKey:", activeKey);
        return () => {
            console.log("relaod ------> activeKey:", activeKey);
            fetchDetail({ activeKey: activeKey || "0", id: lib.getParam("id") });
        };
    }, [activeKey]);

    return (
        <div className="customs-clearance-detail2">
            <Headers detail={detail} buttons={buttons} reload={loadFn} />
            <Tabs
                activeKey={activeKey}
                destroyInactiveTabPane
                onChange={e => {
                    console.log("e:", e);
                    setActiveKey(e);
                    fetchDetail({ activeKey: e, id });
                }}>
                <TabPane tab="清关单表头" key="0">
                    <DetailHeader
                        detail={detail}
                        reload={() => {
                            fetchDetail({ activeKey, id: lib.getParam("id") });
                        }}
                    />
                </TabPane>
                {detail?.inveBusinessType !== BussinessType.SIMPLE_PROCESSING ? (
                    <TabPane tab="清关单表体" key="1">
                        <DetailBody
                            detail={{ ...detail }}
                            bodyList={bodyList}
                            countries={countries}
                            currency={currency}
                            fetchDetail={fetchDetail}
                            inveBusinessType={inveBusinessType}
                            buttons={buttons}
                        />
                    </TabPane>
                ) : (
                    <>
                        <TabPane tab="成品表体" key="5">
                            <FinishedProductBody
                                detail={{ ...detail }}
                                bodyList={bodyList}
                                countries={countries}
                                currency={currency}
                                fetchDetail={fetchDetail}
                                inveBusinessType={inveBusinessType}
                                buttons={buttons}
                            />
                        </TabPane>
                        <TabPane tab="料件表体" key="6">
                            <MaterialsBody
                                detail={{ ...detail }}
                                bodyList={bodyList}
                                countries={countries}
                                currency={currency}
                                fetchDetail={fetchDetail}
                                inveBusinessType={inveBusinessType}
                                buttons={buttons}
                            />
                        </TabPane>
                    </>
                )}

                <TabPane tab="保税核注单" key="2">
                    <TaxTable />
                </TabPane>
                <TabPane tab="关联单证号" key="3">
                    <AssociationTable
                        detail={{ ...detail }}
                        // countries={countries}

                        setDetail={data => {
                            // setDetail({
                            //     ...detail,
                            //     inventoryOrderRelationDTOList: data
                            // })
                            setBodyList(data);
                        }}
                        // currency={currency}
                        fetchDetail={fetchDetail}
                        // inveBusinessType={inveBusinessType}
                    />
                </TabPane>
                <TabPane tab="操作日志" key="4">
                    <LogTable />
                </TabPane>
            </Tabs>
        </div>
    );
};
