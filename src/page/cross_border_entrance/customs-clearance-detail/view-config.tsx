import { DTEditForm, DTEditFormRefs, DTEditFormConfigs } from "@dt/components";
import React from "react";
import { Tag, Button, message, Space } from "antd";
import { textToRGB } from "@/common/utils";
import { CopyOutlined } from "@ant-design/icons";
import { lib } from "react-single-app";
import AuditBtn from "./components/audit-btn";

export const orderTagList = {
    CWLABEL: 1024, // cw标记
};

import copy from "copy-to-clipboard";
import { CustomsClearanceStatus, tagToColor } from "./enum";
export const dangerousFlagKeys = {
    0: "否",
    1: "是",
};

export const dangerousFlagKeysList = Object.keys(dangerousFlagKeys).map(item => {
    return { name: dangerousFlagKeys[item], value: item };
});

export const derailBodyInfoConfig = {
    baseInfo: {
        children: [
            {
                label: "行号",
                editEnable: true,
                name: "currentIndex",
                type: "text",
            },
            {
                label: "是否新品",
                editEnable: false,
                name: "oldOrNew",
                type: "renderContainer",
                render: (item, disabled, value, name, formId) => {
                    if (value === "new") return "是";
                    if (value === "old") return "否";
                    return null;
                },
                // disabled: true,
                // type: "single-select",
                // list: [
                //     { name: "是", id: "new" },
                //     { name: "否", id: "old" },
                // ],
            },
            {
                label: "商品SKU",
                editEnable: false,
                name: "skuId",
                type: "text",
            },
            {
                label: "统一料号",
                editEnable: false,
                name: "originProductId",
                type: "text",
            },
            {
                label: "海关备案料号",
                editEnable: true,
                type: "text",
                name: "productId",
            },
            {
                label: "备案序号",
                editEnable: true,
                type: "text",
                name: "goodsSeqNo",
            },
            {
                label: "商品名称",
                editEnable: true,
                type: "text",
                name: "goodsName",
            },
            {
                label: "商品编码",
                editEnable: true,
                type: "text",
                name: "hsCode",
            },
            {
                label: "规格型号",
                editEnable: false,
                name: "goodsModel",
                type: "text",
            },

            {
                label: "申报计量单位",
                editEnable: false,
                name: "unitDesc",
                type: "text",
            },
            {
                label: "最终申报数量",
                editEnable: false,
                name: "declareUnitQfy",
                type: "text",
            },

            {
                label: "币制",
                editEnable: false,
                name: "currencyDesc",
                type: "text",
            },

            {
                label: "法定计量单位",
                editEnable: false,
                name: "firstUnitDesc",
                type: "text",
            },
            {
                label: "法定数量(单)",
                editEnable: false,
                name: "firstUnitQfy",
                type: "text",
            },
            {
                label: "法定数量(总)",
                editEnable: false,
                name: "firstUnitQfys",
                type: "text",
            },
            {
                label: "法定第二计量单位",
                editEnable: true,
                name: "secondUnitDesc",
                type: "text",
            },
            {
                label: "第二法定数量(单)",
                editEnable: false,
                name: "secondUnitQfy",
                type: "text",
            },
            {
                label: "第二法定数量(总)",
                editEnable: false,
                name: "secondUnitQfys",
                type: "text",
            },
            {
                label: "申报单价",
                editEnable: false,
                name: "declarePrice",
                type: "text",
            },
            {
                label: "申报总价",
                editEnable: false,
                name: "declareTotalPrice",
                type: "text",
            },
            {
                label: "原产国(地区)",
                editEnable: false,
                name: "originCountryDesc",
                type: "text",
            },
            {
                label: "毛重(kg)",
                editEnable: false,
                name: "grossWeight",
                type: "text",
            },
            {
                label: "净重(kg)",
                editEnable: false,
                name: "netweight",
                type: "text",
            },
            {
                label: "商品条码",
                name: "goodsBar",
                editEnable: true,
                type: "text",
            },
            {
                label: "征免方式",
                name: "avoidTaxMethod",
                editEnable: true,
                type: "text",
            },
            {
                label: "最终目的国(地区)",
                name: "destinationCountryDesc",
                editEnable: true,
                type: "text",
            },
            {
                label: "危化品标志",
                name: "dangerousFlag",
                editEnable: true,
                type: "renderContainer",
                render: (text, record, value, name, formId) => {
                    if (value == 0) {
                        return "否";
                    } else if (value == 1) {
                        return "是";
                    } else {
                        return null;
                    }
                },
            },
            {
                label: "来源标识",
                name: "goodsSourceDesc",
                editEnable: true,
                type: "text",
            },
        ],
        label: "",
        name: "baseInfo",
        isGroup: true,
        // className: "baseInfo",
    },
};

export function detailBodyModalConfig(prop) {
    return {
        baseInfo: {
            children: [
                {
                    label: "是否新品",
                    editEnable: true,
                    name: "oldOrNew",

                    labelCol: { span: 10 },
                    type: "single-select",
                    list: [
                        { name: "是", id: "new" },
                        { name: "否", id: "old" },
                    ],
                },
                {
                    label: "商品料号",
                    editEnable: false,

                    labelCol: { span: 10 },
                    name: "productId",
                    type: "single-select",
                    from: "/ccs/invenorder/listProductId",
                },
                {
                    label: "备案序号",
                    editEnable: true,

                    labelCol: { span: 10 },
                    type: "single-select",
                    name: "goodsSeqNo",
                    from: "/ccs/invenorder/listSeqNoByProductIdV2",
                },
                {
                    label: "商品sku",
                    editEnable: false,
                    name: "skuId",

                    labelCol: { span: 10 },
                    type: "text",
                },
                {
                    label: "商品名称",

                    labelCol: { span: 10 },
                    editEnable: false,
                    name: "goodsName",
                    type: "textInput",
                },
                {
                    label: "商品编码",

                    labelCol: { span: 10 },
                    wrapperCol: { span: 14 },
                    editEnable: true,
                    name: "hsCode",
                    type: "single-select",
                    from: "/ccs/customs/listHsV2",
                    customConfig: { together: true, showSearch: true },
                },
                {
                    label: "规格型号",

                    labelCol: { span: 10 },
                    editEnable: false,
                    name: "goodsModel",
                    type: "textInput",
                    rules: [
                        {
                            required: true,
                            message: "请输入规格型号!",
                        },
                    ],
                },
                {
                    label: "申报计量单位",

                    labelCol: { span: 10 },
                    name: "unitDesc",
                    editEnable: true,
                    type: "text",
                },
                {
                    label: "申报数量",

                    labelCol: { span: 10 },
                    name: "declareUnitQfy",
                    editEnable: true,
                    type: "number",
                    rules: [
                        {
                            required: true,
                        },
                        {
                            pattern: RegExp("^[1-9]\\d*$"),
                            message: "请输入大于0的整数",
                        },
                    ],
                },
                {
                    label: "法定计量单位",

                    labelCol: { span: 10 },
                    name: "firstUnit",
                    disabled: true,
                    from: "/ccs/customs/listUom",
                    type: "single-select",
                },
                {
                    label: "法定数量（单）",

                    labelCol: { span: 10 },
                    name: "firstUnitQfy",
                    editEnable: true,
                    type: "number",
                    rules: [
                        {
                            required: true,
                        },
                        {
                            pattern: RegExp("^[1-9]\\d*$|^[1-9]\\d*\\.\\d*|0\\.\\d*[1-9]\\d*$"),
                            message: "请输入大于0的整数",
                        },
                    ],
                },
                {
                    label: "法定数量（总）",

                    labelCol: { span: 10 },
                    name: "firstUnitQfys",
                    editEnable: true,
                    type: "text",
                },
                {
                    label: "第二计量单位",

                    labelCol: { span: 10 },
                    name: "secondUnit",
                    from: "/ccs/customs/listUom",
                    disabled: true,
                    type: "single-select",
                },
                {
                    label: "法定第二数量(单)",

                    labelCol: { span: 10 },
                    name: "secondUnitQfy",
                    editEnable: true,
                    type: "number",
                },
                {
                    label: "法定第二数量(总)",

                    labelCol: { span: 10 },
                    name: "secondUnitQfys",
                    editEnable: true,
                    type: "text",
                },
                {
                    label: "申报单价",

                    labelCol: { span: 10 },
                    name: "declarePrice",
                    editEnable: true,
                    type: "number",
                    rules: [
                        {
                            required: true,
                        },
                        {
                            pattern: RegExp("^[1-9]\\d*$|^[1-9]\\d*\\.\\d*|0\\.\\d*[1-9]\\d*$"),
                            message: "请输入大于0的数",
                        },
                    ],
                },
                {
                    label: "申报总价",

                    labelCol: { span: 10 },
                    name: "declareTotalPrice",
                    editEnable: true,
                    type: "text",
                },
                {
                    label: "原产国(地区)",

                    labelCol: { span: 10 },
                    name: "originCountry",
                    editEnable: true,
                    type: "single-select",
                    list: prop.countryList,
                    customConfig: { together: true, showSearch: true },
                    rules: [
                        {
                            required: true,
                            message: "请选择原产国",
                        },
                    ],
                },
                {
                    label: "币制",

                    labelCol: { span: 10 },
                    name: "currency",
                    editEnable: true,
                    type: "single-select",
                    list: prop.currencyList,
                    customConfig: { together: true, showSearch: true },
                    rules: [
                        {
                            required: true,
                            message: "请选择币制",
                        },
                    ],
                },
                {
                    label: "净重（kg）",

                    labelCol: { span: 10 },
                    name: "netweight",
                    editEnable: true,
                    type: "number",
                    rules: [
                        {
                            required: true,
                        },
                        {
                            pattern: RegExp("^[1-9]\\d*$|^[1-9]\\d*\\.\\d*|0\\.\\d*[1-9]\\d*$"),
                            message: "请输入大于0的数",
                        },
                    ],
                },
                {
                    label: "毛重（kg）",

                    labelCol: { span: 10 },
                    name: "grossWeight",
                    editEnable: true,
                    type: "number",
                    rules: [
                        {
                            pattern: RegExp("^[1-9]\\d*$|^[1-9]\\d*\\.\\d*|0\\.\\d*[1-9]\\d*$"),
                            message: "请输入大于0的数",
                        },
                    ],
                },
                {
                    label: "最终目的国(地区)",

                    labelCol: { span: 10 },
                    name: "destinationCountry",
                    editEnable: true,
                    disabled: true,
                    type: "single-select",
                    customConfig: { showSearch: true },
                    list: prop.countryList,
                },
                {
                    label: "商品条码",

                    labelCol: { span: 10 },
                    name: "goodsBar",
                    editEnable: true,
                    type: "text",
                },
                {
                    label: "征免方式",

                    labelCol: { span: 10 },
                    name: "avoidTaxMethod",
                    editEnable: false,
                    type: "text",
                },
                {
                    label: "危化品标志",
                    labelCol: { span: 10 },
                    name: "dangerousFlag",
                    type: "single-select",
                    editEnable: false,
                    list: dangerousFlagKeysList,
                },
                {
                    label: "来源标识",
                    labelCol: { span: 10 },
                    name: "goodsSource",
                    editEnable: true,
                    type: "single-select",
                    list: [],
                    from: "/ccs/goodsSource/list",
                    customConfig: { together: true, showSearch: true },
                    rules: [
                        {
                            required: true,
                            message: "请选来源标识",
                        },
                    ],
                },
            ],
            label: "",
            name: "baseInfo",
            isGroup: true,
            className: "detail-body-modal",
        },
    };
}

export function detailBodyModalConfig2(prop) {
    return {
        baseInfo: {
            children: [
                {
                    label: "是否新品",
                    editEnable: true,
                    name: "oldOrNew",
                    labelCol: { span: 10 },
                    type: "single-select",
                    list: [
                        { name: "是", id: "new" },
                        { name: "否", id: "old" },
                    ],
                    disabled: true,
                },
                {
                    label: "商品料号",
                    editEnable: false,

                    labelCol: { span: 10 },
                    name: "productId",
                    type: "single-select",
                    from: "/ccs/invenorder/listProductId",
                    disabled: true,
                },
                {
                    label: "备案序号",
                    editEnable: true,

                    labelCol: { span: 10 },
                    type: "single-select",
                    name: "goodsSeqNo",
                    from: "/ccs/invenorder/listSeqNoByProductIdV2",
                    disabled: true,
                },
                {
                    label: "申报表序号",
                    editEnable: true,

                    labelCol: { span: 10 },
                    type: "textInput",
                    name: "declareFormItemSeqNo",
                    disabled: true,
                },
                {
                    label: "商品名称",

                    labelCol: { span: 10 },
                    editEnable: false,
                    name: "goodsName",
                    type: "textInput",
                    disabled: true,
                },
                {
                    label: "商品编码",

                    labelCol: { span: 10 },
                    wrapperCol: { span: 14 },
                    editEnable: true,
                    name: "hsCode",
                    type: "single-select",
                    from: "/ccs/customs/listHsV2",
                    customConfig: { together: true, showSearch: true },
                },
                {
                    label: "规格型号",

                    labelCol: { span: 10 },
                    editEnable: false,
                    name: "goodsModel",
                    type: "textInput",
                    rules: [
                        {
                            required: true,
                            message: "请输入规格型号!",
                        },
                    ],
                },
                {
                    label: "申报计量单位",
                    disabled: true,
                    labelCol: { span: 10 },
                    name: "unitDesc",
                    editEnable: true,
                    type: "text",
                },
                {
                    label: "申报数量",

                    labelCol: { span: 10 },
                    name: "declareUnitQfy",
                    editEnable: true,
                    type: "number",
                    rules: [
                        {
                            required: true,
                        },
                        {
                            pattern: RegExp("^[1-9]\\d*$"),
                            message: "请输入大于0的整数",
                        },
                    ],
                },
                {
                    label: "法定计量单位",
                    labelCol: { span: 10 },
                    name: "firstUnit",
                    disabled: true,
                    from: "/ccs/customs/listUom",
                    type: "single-select",
                },
                {
                    label: "法定数量（单）",

                    labelCol: { span: 10 },
                    name: "firstUnitQfy",
                    editEnable: true,
                    type: "number",
                    rules: [
                        {
                            required: true,
                        },
                        {
                            pattern: RegExp("^[1-9]\\d*$|^[1-9]\\d*\\.\\d*|0\\.\\d*[1-9]\\d*$"),
                            message: "请输入大于0的整数",
                        },
                    ],
                },
                {
                    label: "法定数量（总）",

                    labelCol: { span: 10 },
                    name: "firstUnitQfys",
                    editEnable: true,
                    type: "text",
                    disabled: true,
                },
                {
                    label: "第二计量单位",

                    labelCol: { span: 10 },
                    name: "secondUnit",
                    from: "/ccs/customs/listUom",
                    disabled: true,
                    type: "single-select",
                },
                {
                    label: "法定第二数量(单)",

                    labelCol: { span: 10 },
                    name: "secondUnitQfy",
                    editEnable: true,
                    type: "number",
                },
                {
                    label: "法定第二数量(总)",

                    labelCol: { span: 10 },
                    name: "secondUnitQfys",
                    editEnable: true,
                    type: "text",
                    disabled: true,
                },
                {
                    label: "申报单价",

                    labelCol: { span: 10 },
                    name: "declarePrice",
                    editEnable: true,
                    type: "number",
                    rules: [
                        {
                            required: true,
                        },
                        {
                            pattern: RegExp("^[1-9]\\d*$|^[1-9]\\d*\\.\\d*|0\\.\\d*[1-9]\\d*$"),
                            message: "请输入大于0的数",
                        },
                    ],
                },
                {
                    label: "申报总价",

                    labelCol: { span: 10 },
                    name: "declareTotalPrice",
                    editEnable: true,
                    type: "text",
                    disabled: true,
                },
                {
                    label: "原产国(地区)",

                    labelCol: { span: 10 },
                    name: "originCountry",
                    editEnable: true,
                    type: "single-select",
                    list: prop.countryList,
                    customConfig: { together: true, showSearch: true },
                    rules: [
                        {
                            required: true,
                            message: "请选择原产国",
                        },
                    ],
                },
                {
                    label: "币制",

                    labelCol: { span: 10 },
                    name: "currency",
                    editEnable: true,
                    type: "single-select",
                    list: prop.currencyList,
                    customConfig: { together: true, showSearch: true },
                    rules: [
                        {
                            required: true,
                            message: "请选择币制",
                        },
                    ],
                },
                {
                    label: "净重（kg）",

                    labelCol: { span: 10 },
                    name: "netweight",
                    editEnable: true,
                    type: "number",
                    rules: [
                        {
                            required: true,
                        },
                        {
                            pattern: RegExp("^[1-9]\\d*$|^[1-9]\\d*\\.\\d*|0\\.\\d*[1-9]\\d*$"),
                            message: "请输入大于0的数",
                        },
                    ],
                },
                {
                    label: "毛重（kg）",

                    labelCol: { span: 10 },
                    name: "grossWeight",
                    editEnable: true,
                    type: "number",
                    rules: [
                        {
                            pattern: RegExp("^[1-9]\\d*$|^[1-9]\\d*\\.\\d*|0\\.\\d*[1-9]\\d*$"),
                            message: "请输入大于0的数",
                        },
                    ],
                },
                {
                    label: "最终目的国(地区)",

                    labelCol: { span: 10 },
                    name: "destinationCountry",
                    editEnable: true,
                    disabled: true,
                    type: "single-select",
                    customConfig: { showSearch: true },
                    list: prop.countryList,
                },
                {
                    label: "商品条码",

                    labelCol: { span: 10 },
                    name: "goodsBar",
                    editEnable: true,
                    type: "text",
                    disabled: true,
                },
                {
                    label: "征免方式",

                    labelCol: { span: 10 },
                    name: "avoidTaxMethodDesc",
                    editEnable: false,
                    type: "text",
                    disabled: true,
                },
                {
                    label: "危化品标志",
                    labelCol: { span: 10 },
                    name: "dangerousFlag",
                    type: "single-select",
                    editEnable: false,
                    list: dangerousFlagKeysList,
                    disabled: true,
                },
                {
                    label: "来源标识",
                    labelCol: { span: 10 },
                    name: "goodsSource",
                    editEnable: true,
                    type: "single-select",
                    list: [],
                    from: "/ccs/goodsSource/list",
                    customConfig: { together: true, showSearch: true },
                    rules: [
                        {
                            required: true,
                            message: "请选择来源标识",
                        },
                    ],
                },
            ],
            label: "",
            name: "baseInfo",
            isGroup: true,
            className: "detail-body-modal",
        },
    };
}

// 料件表体
export function detailBodyModalConfig3(prop) {
    return {
        baseInfo: {
            children: [
                {
                    label: "是否新品",
                    editEnable: true,
                    name: "oldOrNew",
                    labelCol: { span: 10 },
                    type: "single-select",
                    list: [
                        { name: "是", id: "new" },
                        { name: "否", id: "old" },
                    ],
                    disabled: true,
                },
                {
                    label: "商品料号",
                    editEnable: false,

                    labelCol: { span: 10 },
                    name: "productId",
                    type: "single-select",
                    from: "/ccs/invenorder/listProductId",
                    disabled: true,
                },
                {
                    label: "备案序号",
                    editEnable: true,

                    labelCol: { span: 10 },
                    type: "single-select",
                    name: "goodsSeqNo",
                    from: "/ccs/invenorder/listSeqNoByProductIdV2",
                    disabled: true,
                },
                {
                    label: "申报表序号",
                    editEnable: true,

                    labelCol: { span: 10 },
                    type: "textInput",
                    name: "declareFormItemSeqNo",
                    disabled: true,
                },
                {
                    label: "商品名称",

                    labelCol: { span: 10 },
                    editEnable: false,
                    name: "goodsName",
                    type: "textInput",
                    disabled: true,
                },
                {
                    label: "商品编码",

                    labelCol: { span: 10 },
                    wrapperCol: { span: 14 },
                    editEnable: true,
                    disabled: true,
                    name: "hsCode",
                    type: "single-select",
                    from: "/ccs/customs/listHsV2",
                    customConfig: { together: true, showSearch: true },
                },
                {
                    label: "规格型号",

                    labelCol: { span: 10 },
                    editEnable: false,
                    name: "goodsModel",
                    disabled: true,
                    type: "textInput",
                    rules: [
                        {
                            required: true,
                            message: "请输入规格型号!",
                        },
                    ],
                },
                {
                    label: "申报计量单位",
                    disabled: true,
                    labelCol: { span: 10 },
                    name: "unitDesc",
                    editEnable: true,
                    type: "text",
                },
                {
                    label: "申报数量",

                    labelCol: { span: 10 },
                    name: "declareUnitQfy",
                    editEnable: true,
                    disabled: true,
                    type: "number",
                    rules: [
                        {
                            required: true,
                        },
                        {
                            pattern: RegExp("^[1-9]\\d*$"),
                            message: "请输入大于0的整数",
                        },
                    ],
                },
                {
                    label: "法定计量单位",
                    labelCol: { span: 10 },
                    name: "firstUnit",
                    disabled: true,
                    from: "/ccs/customs/listUom",
                    type: "single-select",
                },
                {
                    label: "法定数量（单）",

                    labelCol: { span: 10 },
                    name: "firstUnitQfy",
                    editEnable: true,
                    type: "number",
                    rules: [
                        {
                            required: true,
                        },
                        {
                            pattern: RegExp("^[1-9]\\d*$|^[1-9]\\d*\\.\\d*|0\\.\\d*[1-9]\\d*$"),
                            message: "请输入大于0的整数",
                        },
                    ],
                },
                {
                    label: "法定数量（总）",

                    labelCol: { span: 10 },
                    name: "firstUnitQfys",
                    editEnable: true,
                    type: "text",
                    disabled: true,
                },
                {
                    label: "第二计量单位",

                    labelCol: { span: 10 },
                    name: "secondUnit",
                    from: "/ccs/customs/listUom",
                    disabled: true,
                    type: "single-select",
                },
                {
                    label: "法定第二数量(单)",

                    labelCol: { span: 10 },
                    name: "secondUnitQfy",
                    editEnable: true,
                    type: "number",
                },
                {
                    label: "法定第二数量(总)",

                    labelCol: { span: 10 },
                    name: "secondUnitQfys",
                    editEnable: true,
                    type: "text",
                    disabled: true,
                },
                {
                    label: "申报单价",

                    labelCol: { span: 10 },
                    name: "declarePrice",
                    editEnable: true,
                    type: "number",
                    rules: [
                        {
                            required: true,
                        },
                        {
                            pattern: RegExp("^[1-9]\\d*$|^[1-9]\\d*\\.\\d*|0\\.\\d*[1-9]\\d*$"),
                            message: "请输入大于0的数",
                        },
                    ],
                },
                {
                    label: "申报总价",

                    labelCol: { span: 10 },
                    name: "declareTotalPrice",
                    editEnable: true,
                    type: "text",
                    disabled: true,
                },
                {
                    label: "原产国(地区)",

                    labelCol: { span: 10 },
                    name: "originCountry",
                    editEnable: true,
                    type: "single-select",
                    disabled: true,
                    list: prop.countryList,
                    customConfig: { together: true, showSearch: true },
                    rules: [
                        {
                            required: true,
                            message: "请选择原产国",
                        },
                    ],
                },
                {
                    label: "币制",

                    labelCol: { span: 10 },
                    name: "currency",
                    editEnable: true,
                    type: "single-select",
                    disabled: true,
                    list: prop.currencyList,
                    customConfig: { together: true, showSearch: true },
                    rules: [
                        {
                            required: true,
                            message: "请选择币制",
                        },
                    ],
                },
                {
                    label: "净重（kg）",

                    labelCol: { span: 10 },
                    name: "netweight",
                    editEnable: true,
                    type: "number",
                    rules: [
                        {
                            required: true,
                        },
                        {
                            pattern: RegExp("^[1-9]\\d*$|^[1-9]\\d*\\.\\d*|0\\.\\d*[1-9]\\d*$"),
                            message: "请输入大于0的数",
                        },
                    ],
                },
                {
                    label: "毛重（kg）",

                    labelCol: { span: 10 },
                    name: "grossWeight",
                    editEnable: true,
                    type: "number",
                    rules: [
                        {
                            pattern: RegExp("^[1-9]\\d*$|^[1-9]\\d*\\.\\d*|0\\.\\d*[1-9]\\d*$"),
                            message: "请输入大于0的数",
                        },
                    ],
                },
                {
                    label: "最终目的国(地区)",

                    labelCol: { span: 10 },
                    name: "destinationCountry",
                    editEnable: true,
                    disabled: true,
                    type: "single-select",
                    customConfig: { showSearch: true },
                    list: prop.countryList,
                },
                {
                    label: "商品条码",

                    labelCol: { span: 10 },
                    name: "goodsBar",
                    editEnable: true,
                    type: "text",
                    disabled: true,
                },
                {
                    label: "征免方式",

                    labelCol: { span: 10 },
                    name: "avoidTaxMethodDesc",
                    editEnable: false,
                    type: "text",
                    disabled: true,
                },
                {
                    label: "危化品标志",
                    labelCol: { span: 10 },
                    name: "dangerousFlag",
                    type: "single-select",
                    editEnable: false,
                    list: dangerousFlagKeysList,
                    disabled: true,
                },
                {
                    label: "来源标识",
                    labelCol: { span: 10 },
                    name: "goodsSource",
                    editEnable: true,
                    type: "single-select",
                    list: [],
                    from: "/ccs/goodsSource/list",
                    customConfig: { together: true, showSearch: true },
                    rules: [
                        {
                            required: true,
                            message: "请选择来源标识",
                        },
                    ],
                },
            ],
            label: "",
            name: "baseInfo",
            isGroup: true,
            className: "detail-body-modal",
        },
    };
}

export const DetailHeaderConfig: (
    ref: React.RefObject<DTEditFormRefs>,
    editFn: (values, fn) => void,
    associateFn: () => void,
    buttons: string[],
) => DTEditFormConfigs = (ref, editFn, associateFn, buttons) => {
    return {
        header: {
            title: "清关单号: ",
            moduleMode: "read",
            renderHeadRight: ({ configItem, detail, names }) => {
                return (
                    <Space style={{ display: "flex", justifyContent: "space-between" }}>
                        <Space>
                            <h3 style={{ marginBottom: 0 }}>{detail?.inveCustomsSn}</h3>
                            <CopyOutlined
                                style={{
                                    fontSize: "16px",
                                    color: "#1890ff",
                                    marginLeft: 8,
                                }}
                                onClick={() => {
                                    if (detail?.inveCustomsSn) {
                                        copy(detail?.inveCustomsSn);
                                        message.success("复制成功");
                                    } else {
                                        message.warning("没有可复制内容");
                                    }
                                }}
                            />
                            {detail?.orderTagDescList?.map(item => {
                                return (
                                    <Tag style={{ borderRadius: "4px", padding: "4px" }} color={tagToColor[item]}>
                                        {item}
                                    </Tag>
                                );
                            })}
                        </Space>

                        <Space>
                            {[
                                CustomsClearanceStatus.STATUS_CREATED,
                                CustomsClearanceStatus.STATUS_PERFECT,
                                CustomsClearanceStatus.STATUS_CONFIRMING,
                                CustomsClearanceStatus.STATUS_AUDITED,
                                CustomsClearanceStatus.STATUS_FAILURE,
                                CustomsClearanceStatus.STATUS_ENDORSEMENT,
                                CustomsClearanceStatus.STATUS_START_STORAGING,
                                CustomsClearanceStatus.STATUS_START_STORAGED,
                            ].some(item => item === detail?.status) && (
                                <>
                                    {" "}
                                    {configItem.moduleMode === "edit" ? (
                                        <Button
                                            onClick={() => {
                                                ref.current.form.validateFields(names).then(res => {
                                                    res.createTime = res.createTime.valueOf();
                                                    editFn(res, () => {
                                                        ref.current.setConfigModuleItem("header", {
                                                            moduleMode: "read",
                                                        });
                                                    });
                                                });
                                            }}>
                                            保存
                                        </Button>
                                    ) : (
                                        <>
                                            <Button
                                                onClick={() => {
                                                    ref.current.setConfigModuleItem("header", { moduleMode: "edit" });
                                                }}>
                                                编辑
                                            </Button>
                                            {CustomsClearanceStatus.STATUS_CONFIRMING === detail?.status &&
                                                buttons.includes("audit-access") && (
                                                    <AuditBtn detail={detail} reload={editFn} />
                                                )}
                                        </>
                                    )}
                                </>
                            )}
                        </Space>
                    </Space>
                );
            },
            configs: [
                {
                    type: "TEXT",
                    fProps: {
                        label: "入库/出库单号",
                        name: "inOutOrderNo",
                    },
                    isCopy: true,
                },
                {
                    type: "TEXT",
                    fProps: {
                        label: "外部单号",
                        name: "upstreamNo",
                    },
                    isCopy: true,
                },
                {
                    type: "TEXT",
                    fProps: {
                        label: "进出标志",
                        name: "inOrOutFlagDesc",
                    },
                },
                {
                    type: "TEXT",
                    fProps: {
                        label: "关联入库出库单号",
                        name: "associatedInOutOrderNo",
                    },
                },
                {
                    type: "CUSTOM",
                    fProps: {
                        label: "关联清关单号",
                        name: "associatedInveCustomsSn",
                    },
                    render: ({ value }) => {
                        if (!value)
                            return (
                                <Button
                                    size="small"
                                    onClick={() => {
                                        associateFn();
                                    }}>
                                    绑定关联清关单号
                                </Button>
                            );
                        return <div>{value}</div>;
                    },
                    renderRead: ({ value }) => {
                        if (!value && buttons.includes("bind-order-no"))
                            return (
                                <Button
                                    size="small"
                                    onClick={() => {
                                        associateFn();
                                    }}>
                                    绑定关联清关单号
                                </Button>
                            );
                        return <div>{value}</div>;
                    },
                },
                {
                    type: "TEXT",
                    fProps: {
                        label: "子主清关单号",
                        name: "masterOrSubOrderSn",
                    },
                },
                {
                    type: "TEXT",
                    fProps: {
                        label: "中转清关单号",
                        name: "associatedTransitOrderSn",
                    },
                },

                {
                    type: "TEXT",
                    fProps: {
                        label: "关联调入清关单号",
                        name: "associatedInOrderSn",
                    },
                },
                {
                    type: "TEXT",
                    fProps: {
                        label: "关联调出清关单号",
                        name: "associatedOutOrderSn",
                    },
                },
                // {
                //     type: "SELECT",
                //     fProps: {
                //         label: "是否为中转账册",
                //         name: "transitFlag",
                //     },
                //     list: [
                //         { id: 1, name: "是" },
                //         { id: 0, name: "否" },
                //     ],
                //     // editable: true,
                // },
                {
                    type: "INPUT",
                    fProps: {
                        label: "是否为中转账册",
                        name: "transitFlagDesc",
                    },
                },
                {
                    type: "SELECT",
                    fProps: {
                        label: "终点仓",
                        name: "finalEntityWarehouseCode",
                    },
                    list: [],
                    editable: false,
                    dataUrl: "/ccs/entityWarehouse/listEntityByCustomsBook",
                    relys: [
                        {
                            from: "finalBookId",
                            reqkey: "customsBookId",
                        },
                    ],
                },
                {
                    type: "SELECT",
                    fProps: {
                        label: "终点仓货主",
                        name: "finalOwnerCode",
                    },
                    list: [],
                    editable: false,
                    dataUrl: "/ares-admin/owner/listV2",
                    listItemLabelKey: "ownerName",
                    listItemValueKey: "ownerCode",
                    relys: [{ from: "finalEntityWarehouseCode", reqkey: "entityWarehouseCode" }],
                    defaultParams: {
                        openStatus: 1,
                    },
                },
                {
                    type: "INPUT",
                    fProps: {
                        label: "清关企业(终点)",
                        name: "finalInveCompanyName",
                    },
                    // editable: false,
                    // list: [],
                    // dataUrl: "/ccs/company/listInfoWithBGQY",
                },
                {
                    type: "SELECT",
                    fProps: {
                        label: "账册编号(终点)",
                        name: "finalBookId",
                    },
                    list: [],
                    editable: false,
                    dataUrl: "/ccs/customsBook/effective/listBookNoByAccBookAuth",
                },
                {
                    type: "TEXT",
                    fProps: {
                        label: "分区结转单号",
                        name: "carryOverNo",
                    },
                },
                {
                    type: "TEXT",
                    fProps: {
                        label: "创建时间",
                        name: "createTime",
                    },
                },
                {
                    type: "TEXT",
                    fProps: {
                        label: "清关状态",
                        name: "statusDesc",
                    },
                },
                {
                    type: "INPUT",
                    fProps: {
                        label: "备注",
                        name: "remark",
                        // labelCol: { span: 5 },
                        // wrapperCol: { span: 16 },
                    },
                    editable: true,
                    // colSpan: 16,
                },
            ],
        },
    };
};

export const DetailTabHeaderConfig: (
    ref: React.RefObject<DTEditFormRefs>,
    editFn: (values, fn) => void,
) => DTEditFormConfigs = (ref, editFn) => {
    return {
        baseInfo: {
            title: (
                <>
                    <h4 style={{ fontWeight: 800, fontSize: "18px" }}>基本信息</h4>
                    <h5 style={{ fontWeight: 800 }}>调出仓库</h5>
                </>
            ),
            configs: [
                {
                    type: "TEXT",
                    fProps: {
                        label: "仓库类型",
                        name: ["outsetEntityWarehouseInfo", "entityWarehouseType"],
                    },
                },
                {
                    type: "TEXT",
                    fProps: {
                        label: "起点仓",
                        name: ["outsetEntityWarehouseInfo", "entityWarehouseName"],
                    },
                },
                {
                    type: "TEXT",
                    fProps: {
                        label: "起点仓货主",
                        name: ["outsetEntityWarehouseInfo", "ownerName"],
                    },
                },
                {
                    type: "TEXT",
                    fProps: {
                        label: "清关企业",
                        name: ["outsetEntityWarehouseInfo", "inveCompanyName"],
                    },
                },
                {
                    type: "TEXT",
                    fProps: {
                        label: "区内账册编号",
                        name: ["outsetEntityWarehouseInfo", "areaBookNo"],
                    },
                },
                {
                    type: "TEXT",
                    fProps: {
                        label: "主管关区",
                        name: ["outsetEntityWarehouseInfo", "masterCustoms"],
                    },
                },
            ],
        },
        baseInfo1: {
            title: (
                <>
                    <h5 style={{ fontWeight: 800 }}>调入仓库</h5>
                </>
            ),
            configs: [
                {
                    type: "TEXT",
                    fProps: {
                        label: "仓库类型",
                        name: ["destinationEntityWarehouseInfo", "entityWarehouseType"],
                    },
                },
                {
                    type: "TEXT",
                    fProps: {
                        label: "目的仓",
                        name: ["destinationEntityWarehouseInfo", "entityWarehouseName"],
                    },
                },
                {
                    type: "TEXT",
                    fProps: {
                        label: "目的仓货主",
                        name: ["destinationEntityWarehouseInfo", "ownerName"],
                    },
                },
                {
                    type: "TEXT",
                    fProps: {
                        label: "清关企业",
                        name: ["destinationEntityWarehouseInfo", "inveCompanyName"],
                    },
                },
                {
                    type: "TEXT",
                    fProps: {
                        label: "区内账册编号",
                        name: ["destinationEntityWarehouseInfo", "areaBookNo"],
                    },
                },
                {
                    type: "TEXT",
                    fProps: {
                        label: "主管关区",
                        name: ["destinationEntityWarehouseInfo", "masterCustoms"],
                    },
                },
            ],
        },
        transportInfo: {
            title: "运输信息",
            moduleMode: "read",
            renderHeadRight: ({ configItem, names, detail }) => {
                return (
                    <>
                        {[
                            CustomsClearanceStatus.STATUS_CREATED,
                            CustomsClearanceStatus.STATUS_PERFECT,
                            CustomsClearanceStatus.STATUS_CONFIRMING,
                            CustomsClearanceStatus.STATUS_AUDITED,
                            CustomsClearanceStatus.STATUS_FAILURE,
                            CustomsClearanceStatus.STATUS_ENDORSEMENT,
                            CustomsClearanceStatus.STATUS_START_STORAGING,
                            CustomsClearanceStatus.STATUS_START_STORAGED,
                        ].some(item => item === detail?.status) && (
                            <>
                                {configItem.moduleMode === "edit" ? (
                                    <Button
                                        onClick={() => {
                                            ref.current.form.validateFields(names).then(res => {
                                                lib.request({
                                                    url: "/ccs/invenorder/detail/updTransport",
                                                    data: { ...res, id: lib.getParam("id") },
                                                    success: () => {
                                                        editFn(res, () => {
                                                            ref.current.setConfigModuleItem("transportInfo", {
                                                                moduleMode: "read",
                                                            });
                                                        });
                                                    },
                                                });
                                            });
                                        }}>
                                        保存
                                    </Button>
                                ) : (
                                    <Button
                                        onClick={() => {
                                            ref.current.setConfigModuleItem("transportInfo", { moduleMode: "edit" });
                                        }}>
                                        编辑
                                    </Button>
                                )}
                            </>
                        )}
                    </>
                );
            },
            configs: [
                {
                    type: "INPUT",
                    fProps: {
                        label: "车牌号",
                        name: "licensePlate",
                    },
                    editable: true,
                },
                {
                    type: "SELECT",
                    fProps: {
                        label: "货主是否自备车辆",
                        name: "selfOwnedVehicle",
                    },
                    list: [
                        { id: 1, name: "是" },
                        { id: 0, name: "否" },
                    ],
                    editable: true,
                },
                {
                    type: "INPUT",
                    fProps: {
                        label: "车辆费用备注",
                        name: "vehicleCostRemark",
                    },
                    editable: true,
                },
                {
                    type: "TEXT",
                    fProps: {
                        label: "托数",
                        name: "palletsNums",
                    },
                },
                {
                    type: "TEXT",
                    fProps: {
                        label: "预计到港日期",
                        name: "expectedToPortTime",
                    },
                },
                {
                    type: "TEXT",
                    fProps: {
                        label: "预计出区日期",
                        name: "expectedOutAreaTime",
                    },
                },
            ],
        },
        clearanceInfo: {
            title: "通关信息",
            moduleMode: "read",

            renderHeadRight: ({ configItem, names, detail }) => {
                return (
                    <>
                        {[
                            CustomsClearanceStatus.STATUS_CREATED,
                            CustomsClearanceStatus.STATUS_PERFECT,
                            CustomsClearanceStatus.STATUS_CONFIRMING,
                            CustomsClearanceStatus.STATUS_AUDITED,
                            CustomsClearanceStatus.STATUS_FAILURE,
                            CustomsClearanceStatus.STATUS_ENDORSEMENT,
                            CustomsClearanceStatus.STATUS_START_STORAGING,
                            CustomsClearanceStatus.STATUS_START_STORAGED,
                        ].some(item => item === detail?.status) && (
                            <>
                                {configItem.moduleMode === "edit" ? (
                                    <Button
                                        onClick={() => {
                                            ref.current.form.validateFields(names).then(res => {
                                                res.actualArrivalDate &&
                                                    (res.actualArrivalDate = res.actualArrivalDate.valueOf());
                                                lib.request({
                                                    url: "/ccs/invenorder/detail/updCustoms",
                                                    data: { ...res, id: lib.getParam("id") },
                                                    success: () => {
                                                        editFn(res, () => {
                                                            ref.current.setConfigModuleItem("clearanceInfo", {
                                                                moduleMode: "read",
                                                            });
                                                        });
                                                    },
                                                });
                                            });
                                        }}>
                                        保存
                                    </Button>
                                ) : (
                                    <Button
                                        onClick={() => {
                                            ref.current.setConfigModuleItem("clearanceInfo", { moduleMode: "edit" });
                                        }}>
                                        编辑
                                    </Button>
                                )}
                            </>
                        )}
                    </>
                );
            },
            configs: [
                {
                    type: "SELECT",
                    fProps: {
                        label: "业务类型",
                        name: "inveBusinessType",
                        rules: [
                            {
                                required: true,
                                message: "请选择业务类型",
                            },
                        ],
                    },
                    list: [],
                    dataUrl: "/ccs/invenorder/listBusinessTypeById",
                    defaultParams: {
                        inventoryOrderId: lib.getParam("id"),
                    },
                    editable: true,
                },
                {
                    type: "SELECT",
                    fProps: {
                        label: "运输方式",
                        name: "transportMode",
                    },
                    cProps: {},
                    selectShowMode: "together",
                    list: [],
                    dataUrl: "/ccs/dictionary/listTransportMode",
                    editable: true,
                },
                {
                    type: "SELECT",
                    fProps: {
                        label: "启运/运抵国",
                        name: "shipmentCountry",
                    },
                    selectShowMode: "together",
                    list: [],
                    dataUrl: "/ccs/customs/listCountry",
                    editable: true,
                },
                {
                    type: "INPUT",
                    fProps: {
                        label: "进出境关别",
                        name: "entryExitCustoms",
                        rules: [
                            {
                                validator: (_, value) => {
                                    if (value && !RegExp("^[0-9]{1,4}$").test(value)) {
                                        return Promise.reject(new Error("请输入大于0的整数,且长度不允许超过4位"));
                                    }
                                    return Promise.resolve();
                                },
                            },
                            {
                                required: true,
                            },
                        ],
                    },
                    editable: true,
                },
                {
                    type: "SELECT",
                    list: [],
                    // dataUrl: "/ccs/invenorder/listDeclareFormNoByBookId",
                    fProps: {
                        label: "申报表编号",
                        name: "declareFormNo",
                    },
                    // defaultParams:{bookId:}
                },
                {
                    type: "SELECT",
                    fProps: {
                        label: "是否两步申报",
                        name: "twoStepFlag",
                    },
                    list: [
                        { id: 1, name: "是" },
                        { id: 0, name: "否" },
                    ],
                },
                {
                    type: "SELECT",
                    fProps: {
                        label: "清关方式",
                        name: "declareWay",
                    },
                    list: [
                        { id: 0, name: "先报后理" },
                        { id: 1, name: "先理后报" },
                    ],
                    editable: true,
                },
                {
                    type: "INPUT",
                    fProps: {
                        label: "报关单号",
                        name: "customsEntryNo",
                        rules: [
                            {
                                max: 18,
                                message: "长度不允许超过18位",
                            },
                            {
                                required: true,
                            },
                        ],
                    },
                    editable: true,
                },
                {
                    type: "TEXT",
                    fProps: {
                        label: "报关标志",
                        name: "customsFlagDesc",
                    },
                },
                {
                    type: "SELECT",
                    fProps: {
                        label: "是否生成报关单",
                        name: "declarationFlag",
                        rules: [
                            {
                                required: true,
                            },
                        ],
                    },
                    list: [
                        { id: 1, name: "是" },
                        { id: 0, name: "否" },
                    ],
                },
                {
                    type: "SELECT",
                    fProps: {
                        label: "报关单类型",
                        name: "customsEntryType",
                        rules: [
                            {
                                required: true,
                                message: "请选择报关单类型!",
                            },
                        ],
                    },
                    dataUrl: "/ccs/dictionary/listCustomsDecType",
                    list: [],
                },
                {
                    type: "SELECT",
                    fProps: {
                        label: "报关类型",
                        name: "customsType",
                        labelCol: { span: 5 },
                    },
                    colSpan: 16,
                    list: [
                        {
                            name: "关联报关",
                            id: 1,
                        },
                        {
                            name: "对应报关",
                            id: 2,
                        },
                    ],
                    editable: false,
                },
                {
                    type: "SELECT",
                    fProps: {
                        label: "对应报关单申报单位名称",
                        name: "corrCusDeclareCompanyId",
                    },
                    list: [],
                    editable: true,
                    dataUrl: "/ccs/company/listInfoWithBGQY",
                },
                {
                    type: "TEXT",
                    fProps: {
                        label: "对应报关单申报单位USCC",
                        name: "corrCusDeclareCompanyUSCC",
                    },
                },
                {
                    type: "TEXT",
                    fProps: {
                        label: "对应报关单申报单位编码",
                        name: "corrCusDeclareCompanyCode",
                    },
                },
                {
                    type: "INPUT",
                    fProps: {
                        label: "关联转入账册",
                        name: "inAccountBook",
                        rules: [
                            {
                                validator: (_, value) => {
                                    if (!value) return Promise.resolve();
                                    if (
                                        value &&
                                        !RegExp("^[a-zA-Z]{1}[0-9]{4}[a-zA-Z]{1}[a-zA-Z0-9]{6}$").test(value)
                                    ) {
                                        return Promise.reject(new Error("请输入合法的账册编号"));
                                    }
                                    return Promise.resolve();
                                },
                            },
                        ],
                    },
                    editable: true,
                },
                {
                    type: "INPUT",
                    fProps: {
                        label: "关联转出账册",
                        name: "outAccountBook",
                        rules: [
                            {
                                validator: (_, value) => {
                                    if (!value) return Promise.resolve();
                                    if (
                                        value &&
                                        !RegExp("^[a-zA-Z]{1}[0-9]{4}[a-zA-Z]{1}[a-zA-Z0-9]{6}$").test(value)
                                    ) {
                                        return Promise.reject(new Error("请输入合法的账册编号"));
                                    }
                                    return Promise.resolve();
                                },
                            },
                        ],
                    },
                    editable: true,
                },
                {
                    type: "INPUT",
                    fProps: {
                        label: "关联核注清单编号",
                        name: "associatedEndorsementNo",
                    },
                    editable: true,
                },
                {
                    type: "SELECT",
                    fProps: {
                        label: "关联报关单境内收发货人名称",
                        name: "rltCusInnerSFHRCompanyId",
                    },
                    list: [],
                    editable: true,
                    dataUrl: "/ccs/company/listInfoWithSFHR",
                },
                {
                    type: "TEXT",
                    fProps: {
                        label: "关联报关单境内收发货人USCC",
                        name: "rltCusInnerSFHRCompanyUSCC",
                    },
                },
                {
                    type: "TEXT",
                    fProps: {
                        label: "关联报关单境内收发货人编码",
                        name: "rltCusInnerSFHRCompanyCode",
                    },
                },
                {
                    type: "SELECT",
                    fProps: {
                        label: "关联报关单消费使用单位名称",
                        name: "rltCusXFDYCompanyId",
                    },
                    dataUrl: "/ccs/company/listInfoWithXFDW",
                    list: [],
                    editable: true,
                },
                {
                    type: "TEXT",
                    fProps: {
                        label: "关联报关单消费使用单位USCC",
                        name: "rltCusXFDYCompanyUSCC",
                    },
                },
                {
                    type: "TEXT",
                    fProps: {
                        label: "关联报关单消费使用单位编码",
                        name: "rltCusXFDYCompanyCode",
                    },
                },
                {
                    type: "SELECT",
                    fProps: {
                        label: "关联报关单申报单位名称",
                        name: "rltCusDeclareCompanyId",
                    },
                    list: [],
                    dataUrl: "/ccs/company/listInfoWithBGQY",
                    editable: true,
                },
                {
                    type: "TEXT",
                    fProps: {
                        label: "关联报关单申报单位USCC",
                        name: "rltCusDeclareCompanyUSCC",
                    },
                },
                {
                    type: "TEXT",
                    fProps: {
                        label: "关联报关单申报单位编码",
                        name: "rltCusDeclareCompanyCode",
                    },
                },
                {
                    type: "INPUT",
                    fProps: {
                        label: "核注单备注",
                        name: "endorsementRemark",
                    },
                    editable: true,
                },
                //     ],
                // },
                // otherInfo: {
                //     title: "其它信息",
                //     renderHeadRight: ({ configItem, names, detail }) => {
                //         return (
                //             <>
                //                 {" "}
                //                 {[
                //                     CustomsClearanceStatus.STATUS_CREATED,
                //                     CustomsClearanceStatus.STATUS_PERFECT,
                //                     CustomsClearanceStatus.STATUS_CONFIRMING,
                //                     CustomsClearanceStatus.STATUS_AUDITED,
                //                     CustomsClearanceStatus.STATUS_FAILURE,
                //                     CustomsClearanceStatus.STATUS_ENDORSEMENT,
                //                     CustomsClearanceStatus.STATUS_START_STORAGING,
                //                     CustomsClearanceStatus.STATUS_START_STORAGED,
                //                 ].some(item => item === detail?.status) && (
                //                     <>
                //                         {configItem.moduleMode === "edit" ? (
                //                             <Button
                //                                 onClick={() => {
                //                                     ref.current.form.validateFields(names).then(res => {
                //                                         res.actualArrivalDate &&
                //                                             (res.actualArrivalDate = res.actualArrivalDate.valueOf());
                //                                         lib.request({
                //                                             url: "/ccs/invenorder/detail/updCustoms",
                //                                             data: { ...res, id: lib.getParam("id") },
                //                                             success: () => {
                //                                                 editFn(res, () => {
                //                                                     ref.current.setConfigModuleItem("otherInfo", {
                //                                                         moduleMode: "read",
                //                                                     });
                //                                                 });
                //                                             },
                //                                         });
                //                                     });
                //                                 }}>
                //                                 保存
                //                             </Button>
                //                         ) : (
                //                             <Button
                //                                 onClick={() => {
                //                                     ref.current.setConfigModuleItem("otherInfo", { moduleMode: "edit" });
                //                                 }}>
                //                                 编辑
                //                             </Button>
                //                         )}
                //                     </>
                //                 )}
                //             </>
                //         );
                //     },
                //     configs: [
                {
                    type: "INPUT",
                    fProps: {
                        label: "提单号",
                        name: "pickUpNo",
                    },
                    // editable: true,
                },
                {
                    type: "SELECT",
                    fProps: {
                        label: "货代公司",
                        name: "forwardingCompany",
                    },
                    // editable: true,
                    list: [],
                    dataUrl: "/ccs/dictionary/listFreightForwardingCompany",
                },
                {
                    type: "INPUT",
                    fProps: {
                        label: "集装箱号",
                        name: "conNo",
                    },
                    editable: true,
                },
                {
                    type: "INPUT",
                    fProps: {
                        label: "进境口岸",
                        name: "entryPort",
                    },
                    editable: true,
                },
                {
                    type: "INPUT",
                    fProps: {
                        label: "起运港/始发机场",
                        name: "fromLocation",
                    },
                    editable: true,
                },
                {
                    type: "SELECT",
                    fProps: {
                        label: "到货港口/机场",
                        name: "arrivalPort",
                    },
                    editable: true,
                    list: [],
                    dataUrl: "/ccs/dictionary/listArrivePort",
                },
                {
                    type: "INPUT",
                    fProps: {
                        label: "品名",
                        name: "productName",
                    },
                    editable: true,
                },
                {
                    type: "SELECT",
                    fProps: {
                        label: "类目",
                        name: "category",
                    },
                    editable: true,
                    list: [],
                    dataUrl: "/ccs/invenorder/listCategory",
                },
                {
                    type: "INPUT",
                    fProps: {
                        label: "转出方",
                        name: "transferor",
                    },
                    editable: true,
                },
                {
                    type: "INPUT",
                    fProps: {
                        label: "转入方",
                        name: "transferee",
                    },
                    editable: true,
                },
                {
                    type: "DATE",
                    fProps: {
                        label: "实际到港日期",
                        name: "actualArrivalDate",
                    },
                    editable: true,
                },
            ],
            //     moduleMode: "read",
            // },
        },
    };
};
