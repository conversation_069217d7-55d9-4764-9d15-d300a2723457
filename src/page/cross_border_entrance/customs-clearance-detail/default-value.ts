export const detailHeaderClearValue = {
    ONELINE_IN: {
        inAccountBook: null,
        associatedEndorsementNo: null,
        outAccountBook: null,
        transferor: null,
        transferee: null,

        rltCusInnerSFHRCompanyId: null,
        rltCusInnerSFHRCompanyUSCC: null,
        rltCusInnerSFHRCompanyCode: null,
        rltCusXFDYCompanyId: null,
        rltCusXFDYCompanyUSCC: null,
        rltCusXFDYCompanyCode: null,
        rltCusDeclareCompanyId: null,
        rltCusDeclareCompanyUSCC: null,
        rltCusDeclareCompanyCode: null,
        // transferor: null,
        // transferee: null,
        customsTypeDesc: "对应报关",
        customsType: 2,
    },
    SECTIONINNER_IN: {
        customsEntryNo: null,
        shipmentCountry: null,
        declarationFlag: null,
        customsEntryType: null,
        customsEntryCompany: null,
        inAccountBook: null,
        associatedEndorsementNo: null,
        pickUpNo: null,
        forwardingCompany: null,
        conNo: null,
        arrivalPort: null,
        twoStepFlag: null,
        actualArrivalDate: null,
    },
    SECTIONINNER_OUT: {
        customsEntryNo: null,
        shipmentCountry: null,
        declarationFlag: null,
        customsEntryType: null,
        customsEntryCompany: null,
        outAccountBook: null,
        pickUpNo: null,
        forwardingCompany: null,
        conNo: null,
        arrivalPort: null,
        twoStepFlag: null,
        actualArrivalDate: null,
    },
    SECTION_IN: {
        customsEntryNo: null,
        shipmentCountry: null,
        declarationFlag: null,
        customsEntryType: null,
        customsEntryCompany: null,
        inAccountBook: null,
        associatedEndorsementNo: null,
        pickUpNo: null,
        forwardingCompany: null,
        conNo: null,
        arrivalPort: null,
        twoStepFlag: null,
        actualArrivalDate: null,
    },
    SECTION_OUT: {
        customsEntryNo: null,
        shipmentCountry: null,
        declarationFlag: null,
        customsEntryType: null,
        customsEntryCompany: null,
        outAccountBook: null,
        pickUpNo: null,
        forwardingCompany: null,
        conNo: null,
        arrivalPort: null,
        twoStepFlag: null,
        actualArrivalDate: null,
    },
    //退货
    REFUND_INAREA: {
        selfOwnedVehicle: null,
        licensePlate: null,
        vehicleCostRemark: null,
        declareWay: null,
        customsEntryNo: null,
        inAccountBook: null,
        associatedEndorsementNo: null,
        outAccountBook: null,
        pickUpNo: null,
        forwardingCompany: null,
        conNo: null,
        arrivalPort: null,
        twoStepFlag: null,
        actualArrivalDate: null,
    },
    //销毁
    DESTORY: {
        selfOwnedVehicle: null,
        licensePlate: null,
        vehicleCostRemark: null,
        declareWay: null,
        customsEntryNo: null,
        shipmentCountry: null,
        declarationFlag: null,
        customsEntryType: null,
        customsEntryCompany: null,
        inAccountBook: null,
        associatedEndorsementNo: null,
        pickUpNo: null,
        forwardingCompany: null,
        conNo: null,
        arrivalPort: null,
        productName: null,
        category: null,
        transferor: null,
        transferee: null,
        outAccountBook: null,
        twoStepFlag: null,
        actualArrivalDate: null,
    },
    ONLINE_REFUND: {
        rltCusInnerSFHRCompanyId: null,
        rltCusInnerSFHRCompanyUSCC: null,
        rltCusInnerSFHRCompanyCode: null,
        rltCusXFDYCompanyId: null,
        rltCusXFDYCompanyUSCC: null,
        rltCusXFDYCompanyCode: null,
        rltCusDeclareCompanyId: null,
        rltCusDeclareCompanyUSCC: null,
        rltCusDeclareCompanyCode: null,
        transferor: null,
        transferee: null,
        customsTypeDesc: "对应报关",
        customsType: 2,
    },
    BONDED_TO_TRADE: {
        corrCusDeclareCompanyId: null,
        corrCusDeclareCompanyUSCC: null,
        corrCusDeclareCompanyCode: null,
        pickUpNo: null,
        conNo: null,
        arrivalPort: null,
        forwardingCompany: null,
        customsTypeDesc: "关联报关",
        customsType: 1,
    },
    SUBSEQUENT_TAX: {
        declarationFlag: null,
        customsEntryType: null,
        pickUpNo: null,
        conNo: null,
        arrivalPort: null,
        productName: null,
        category: null,
        transferor: null,
        transferee: null,
        forwardingCompany: null,
        // customsTypeDesc: null,
        // customsType: null,
        customsTypeDesc: "对应报关",
        customsType: 2,
    },
    BONDED_ONELINE_IN: {
        inAccountBook: null,
        associatedEndorsementNo: null,
        outAccountBook: null,
        transferor: null,
        transferee: null,

        rltCusInnerSFHRCompanyId: null,
        rltCusInnerSFHRCompanyUSCC: null,
        rltCusInnerSFHRCompanyCode: null,
        rltCusXFDYCompanyId: null,
        rltCusXFDYCompanyUSCC: null,
        rltCusXFDYCompanyCode: null,
        rltCusDeclareCompanyId: null,
        rltCusDeclareCompanyUSCC: null,
        rltCusDeclareCompanyCode: null,
        // transferor: null,
        // transferee: null,
        customsTypeDesc: "对应报关",
        customsType: 2,
    },
    INVENTORY_PROFIT: {
        selfOwnedVehicle: null,
        licensePlate: null,
        vehicleCostRemark: null,
        declareWay: null,
        customsEntryNo: null,
        shipmentCountry: null,
        declarationFlag: null,
        customsEntryType: null,
        customsEntryCompany: null,
        inAccountBook: null,
        associatedEndorsementNo: null,
        pickUpNo: null,
        forwardingCompany: null,
        conNo: null,
        arrivalPort: null,
        productName: null,
        category: null,
        transferor: null,
        transferee: null,
        outAccountBook: null,
        twoStepFlag: null,
        actualArrivalDate: null,
    },
    RANDOM_INSPECTION_DECLARATION: {
        selfOwnedVehicle: null,
        licensePlate: null,
        vehicleCostRemark: null,
        declareWay: null,
        customsEntryNo: null,
        shipmentCountry: null,
        declarationFlag: null,
        customsEntryType: null,
        customsEntryCompany: null,
        inAccountBook: null,
        associatedEndorsementNo: null,
        pickUpNo: null,
        forwardingCompany: null,
        conNo: null,
        arrivalPort: null,
        productName: null,
        category: null,
        transferor: null,
        transferee: null,
        outAccountBook: null,
        twoStepFlag: null,
        actualArrivalDate: null,
    },
};
