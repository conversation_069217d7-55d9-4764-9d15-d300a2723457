import React, { Fragment } from "react";
import { lib, SearchList, getConfigDataUtils } from "react-single-app";
import axios from "axios";
import { Button, Space, message, Modal } from "antd";
import { MessageModal } from "../../components/message-modal";
import moment from "moment";

/**
 * 海关回执
 */
export default class CustomsCallback extends SearchList {
    constructor(props) {
        super(props);
    }

    getConfig() {
        return axios.get(getConfigDataUtils.getDataUrlByDataId(558)).then(res => res.data.data);
    }
    editHandle(row) {}
    componentDidMount() {
        let toTime = moment().format("YYYY-MM-DD"),
            fromTime = moment().subtract(1, "months").format("YYYY-MM-DD");
        this.changeImmutable({
            createTimeFrom: moment(fromTime + " 00:00:00.000").format("x"),
            createTimeTo: moment(toTime + " 23:59:59.999").format("x"),
        });
    }
    retry(id) {
        lib.request({
            url: "/ccs/callbackLog/retryById",
            data: { id },
            success: res => {
                message.success("执行成功");
                this.load();
            },
        });
    }

    batchRetryByTime(id) {
        let searchCondition = this.getSearchCondition();
        let that = this;
        Modal.confirm({
            cancelText: "取消",
            okText: "确定",
            title: "提示",
            content: "确定根据查询条件执行消息吗？",
            onOk() {
                return new Promise((resolve, reject) => {
                    lib.request({
                        url: "/ccs/callbackLog/retryBatchByCondition",
                        data: searchCondition,
                        success: res => {
                            resolve(res);
                            message.success("批量执行成功");
                            that.load();
                        },
                        fail: e => {
                            reject(e);
                        },
                    });
                });
            },
        });
    }
    batchRetry() {
        let ids = this.state.selectedIdList;
        if (ids.length < 1) {
            message.warning("请选择至少一条数据");
        }
        lib.request({
            url: "/ccs/callbackLog/retryBatchByIds",
            data: { ids },
            success: res => {
                message.success("批量执行成功");
                this.load();
            },
        });
    }
    requestHeadFunc(row) {
        return (
            <Button
                onClick={() => {
                    this.setState({
                        messageModalVisible: true,
                        messageContent: row.requestHead,
                    });
                }}>
                请求头
            </Button>
        );
    }

    requestParamsFunc(row) {
        return (
            <Button
                onClick={() => {
                    this.setState({
                        messageModalVisible: true,
                        messageContent: row.requestParams,
                    });
                }}>
                请求参数
            </Button>
        );
    }
    receiptContentFunc(row) {
        return (
            <Button
                onClick={() => {
                    this.setState({
                        messageModalVisible: true,
                        messageContent: row.content,
                    });
                }}>
                回执内容
            </Button>
        );
    }
    myOperation(row) {
        return (
            <Space>
                <span className="link" onClick={() => this.retry(row.id)}>
                    执行
                </span>
            </Space>
        );
    }

    renderLeftOperation() {
        return (
            <Space>
                <Button type={"primary"} onClick={() => this.batchRetry()}>
                    批量执行
                </Button>
                <Button onClick={() => this.batchRetryByTime()}>执行查询条件</Button>
            </Space>
        );
    }

    renderModal() {
        return (
            <Fragment>
                <MessageModal
                    visible={this.state.messageModalVisible}
                    setVisible={() => {
                        this.setState({ messageModalVisible: false });
                    }}
                    messageContent={this.state.messageContent}
                />
            </Fragment>
        );
    }
}
