import React, { useState, useEffect } from "react";
import "./shop-good.less";
import { Checkbox, Button, Modal, Drawer, Input, Select, Form, Icon, message, Tabs, Table, Tooltip, Space } from "antd";
import NewModal from "../components/NewModal";
import TextArea from "antd/lib/input/TextArea";
import { ConfigCenter, lib } from "react-single-app";
const FormItem = Form.Item;
const { TabPane } = Tabs;

class App extends ConfigCenter {
    constructor(props) {
        super(props);
        this.state.modalTitle = "新增核注清单";
        // 编辑/新建url
        this.state.upUrl = "/ccs/endorsement/addMailNo";
        this.state.mailNo = "";
        this.state.editRow = {};
        this.state.modalPagination = { current: 1, pageSize: 100 };
        this.state.eliminateData = {};
        this.state.configList = [
            {
                type: "SELECT",
                labelName: "清单业务",
                labelKey: "bussinessType",
                list: [],
                ccs: "/ccs/endorsement/list-bussiness-types",
                onChange: (e, form) => {
                    if (e === "SECONDE_OUT") {
                        // this.state.editRow.bussinessType2 = "1"
                        form.setFieldsValue({ bussinessType2: "1" });
                        this.state.configList[2].hide = false;
                        this.state.configList[3].hide = true;
                        this.state.configList[4].hide = true;
                    } else {
                        // this.state.editRow.bussinessType2 = "2"
                        form.setFieldsValue({ bussinessType2: "2" });
                        this.state.configList[2].hide = true;
                        this.state.configList[3].hide = false;
                        this.state.configList[4].hide = false;
                        this.state.configList[3].list = this.state.configList[3].lists.filter(
                            item => item.id && item.id.indexOf(e) !== -1,
                        );
                    }
                    this.setState(this.state);
                },
                required: true,
            },
            {
                type: "SELECT",
                labelName: "单据类型",
                labelKey: "bussinessType2",
                list: [
                    { name: "申报出库单", value: "1" },
                    { name: "清关单", value: "2" },
                ],
                disabled: true,
            },
            {
                type: "SELECT",
                labelName: "单据编号",
                labelKey: "exportOrderId",
                list: [],
                ccs: "/ccs/exportOrder/listForEndorsement",
                hide: false,
                required: true,
            },
            {
                type: "SELECT",
                labelName: "单据编号",
                labelKey: "inventoryOrderId",
                list: [],
                ccs: "/ccs/invenorder/list-select-invertory-order",
                hide: true,
                required: true,
            },
            { type: "SWITCH", labelName: "是否一票多车", labelKey: "checklistsFlag" },
        ];
    }
    componentDidMount() {
        this.getConfigList();
    }

    getConfigList() {
        let { configList } = this.state;
        configList.map(item => {
            if (item.ccs) {
                lib.request({
                    url: item.ccs,
                    needMask: false,
                    success: res => {
                        item.lists = res;
                        item.list = res;
                        this.setState({
                            configList,
                        });
                    },
                });
            }
        });
        this.setState({
            configList,
        });
    }

    // renderStatus(row) {
    //     return <Tooltip title={row.informationDesc}>
    //         {row.customsStatusDesc}
    //     </Tooltip>
    // }

    renderCustomsStatusDesc(row) {
        return <Tooltip title={row.informationDesc}>{row.customsStatusDesc}</Tooltip>;
    }

    fetchDetailAndHandle(row, types) {
        lib.request({
            url: "/ccs/endorsement/load-detail-info",
            data: {
                endorsementId: row.id,
            },
            needMask: true,
            success: res => {
                let endorsementDetailWarp = res;
                let defaultValue = "",
                    type = "",
                    defaultStr = "";
                if (endorsementDetailWarp) {
                    // 一线入境展示报关单
                    if (endorsementDetailWarp.bussinessType === "ONELINE_IN") {
                        if (endorsementDetailWarp.listInventoryOrderRelationDTO) {
                            endorsementDetailWarp.listInventoryOrderRelationDTO.map(item => {
                                defaultValue += `${item.relNo}\n`;
                            });
                        }
                        type = "1";
                        // 二线出区展示运单号
                    } else if (endorsementDetailWarp.bussinessType === "SECONDE_OUT") {
                        type = "2";
                        endorsementDetailWarp.itemList.map(item => {
                            defaultValue += `${item.mailNo}\n`;
                        });
                        // 退货入区展示关联单证号
                    } else if (endorsementDetailWarp.bussinessType === "REFUND_INAREA") {
                        type = "3";
                        if (endorsementDetailWarp.listInventoryOrderRelationDTO) {
                            endorsementDetailWarp.listInventoryOrderRelationDTO.map(item => {
                                defaultValue += `${item.relNo}\n`;
                            });
                        }
                    }
                    // endorsementDetailWarp.bodyItemInfos.map(item => {
                    //     let str = `${item.accountSeqNo} ${item.productId}`
                    //     if (defaultStr.indexOf(str) === -1) {
                    //         defaultStr += str + "\n"
                    //     }
                    // })
                }
                row.defaultValue = defaultValue;
                row.type = type;
                row.bodyItemInfos = res.bodyItemInfos.map((item, index) => {
                    item.index = index + 1;
                    return item;
                });
                row.bodyList = res.bodyItemInfos;
                // row.defaultStr = defaultStr;
                this.setState({
                    editRow: row,
                    detailVisible: true,
                    type: types,
                    mailNo: defaultValue,
                    exception: "all",
                });
            },
        });
    }
    export(row) {
        let { pagination, searchConditions } = this.state;
        lib.request({
            url: "/ccs/endorsement/singleExport",
            needMask: true,
            data: { id: row.id },
            success: json => {
                Modal.confirm({
                    okText: "去下载中心",
                    cancelText: "取消",
                    icon: null,
                    content: "新建下载任务成功",
                    onOk() {
                        lib.openPage("/download-center?page_title=下载中心");
                    },
                });
            },
        });
    }

    myOperation(row) {
        return (
            <Space>
                {row.allowFinish && (
                    <a className="link" onClick={e => this.finishFunc(e, row)}>
                        手动审核
                    </a>
                )}
                {/* {row.allowExport && <a className="link" onClick={(e) => {lib.download('/ccs/endorsement/singleExport' , {id: row.id})}}>导出</a>} */}
                {row.allowExport && (
                    <a
                        className="link"
                        onClick={e => {
                            this.export(row);
                        }}>
                        导出
                    </a>
                )}
                {row.allowDiscard && (
                    <a className="link" onClick={() => this.invalidRow(row)}>
                        作废
                    </a>
                )}
                {row.allowEdit && (
                    <a className="link" onClick={() => this.fetchDetailAndHandle(row, "edit")}>
                        编辑
                    </a>
                )}
                {row.allowView && (
                    <a className="link" onClick={() => this.fetchDetailAndHandle(row, "watch")}>
                        查看
                    </a>
                )}
                {/* {(row.statusDesc === '已创建' || row.statusDesc === '异常' || (row.statusDesc === '申报中' && row.realEndorsementOrderNo === '')) && <a className="link" onClick={() => this.staging(row)}>暂存</a>} */}
                {row.allowStorage && (
                    <span className="link" onClick={() => this.staging(row)}>
                        暂存
                    </span>
                )}
                {row.allowEliminateException && (
                    <span className="link" onClick={() => this.eliminate(row)}>
                        剔除异常
                    </span>
                )}
            </Space>
        );
    }
    // 剔除异常
    eliminate(row) {
        lib.request({
            url: "/ccs/endorsement/eliminateException",
            data: {
                id: row.id,
            },
            needMask: true,
            success: res => {
                message.success("剔除异常成功");
                let exceptionItem = [];
                Object.entries(res.exceptionItem).map(([key, value]) => {
                    exceptionItem.push({ key, value });
                });
                this.setState({
                    eliminateModalVisible: true,
                    eliminateData: {
                        exceptionMailNos: res.exceptionMailNos || [],
                        exceptionItem,
                    },
                });
                this.load(true);
            },
        });
    }

    staging(row) {
        lib.request({
            url: "/ccs/endorsement/temporaryStorage",
            data: {
                ids: String(row.id),
            },
            needMask: true,
            success: res => {
                this.load(true);
                message.success("暂存推送成功");
            },
        });
    }

    invalidRow(row) {
        const modal = Modal.confirm({
            cancelText: "取消",
            okText: "确定",
            title: "提示",
            content: "确定作废该条数据吗？",
            onOk: () => {
                lib.request({
                    url: "/ccs/endorsement/discard",
                    method: "POST",
                    data: {
                        id: row.id,
                    },
                    needMask: true,
                    success: res => {
                        if (res) {
                            message.success("作废成功");
                            this.load(true);
                            modal.destroy();
                        }
                    },
                });
            },
        });
    }

    finishFunc(e, row) {
        const modal = Modal.confirm({
            cancelText: "取消",
            okText: "确定",
            title: "请输入核注单号，手动审核",
            content: (
                <Input
                    onChange={e => {
                        this.setState({
                            realNo: e.target.value,
                        });
                    }}
                />
            ),
            onOk: res => {
                if (this.state.realNo) {
                    lib.request({
                        url: "/ccs/endorsement/finish",
                        data: {
                            id: row.id,
                            realNo: this.state.realNo,
                        },
                        method: "POST",
                        needMask: true,
                        success: res => {
                            if (res.errorMessage) {
                                message.error(res.errorMessage);
                            } else {
                                message.success("审核通过");
                                this.load(true);
                                modal.destroy();
                                this.setState({
                                    realNo: "",
                                });
                            }
                        },
                    });
                } else {
                    message.warning("请输入核注清单编号");
                }
            },
        });
    }

    renderLeftOperation() {
        return (
            <Space>
                {/* <Button onClick={() => this.export()}>批量导出</Button> */}
                {/* <Button type="primary" onClick={() => this.push()}>推送核注</Button> */}
                <Button onClick={() => this.handlePush()}>手动核扣</Button>
            </Space>
        );
    }
    getCheckedRows() {
        return this.state.dataList.filter(item => item.checked);
    }

    handlePush() {
        let ids = "",
            list = this.state.dataList.filter(item => item.checked);
        if (list.length) {
            let contentStr = "";
            list.map(item => {
                ids += item.id + ",";
                contentStr += `核注清单${item.realEndorsementOrderNo}
`;
            });
            let modal = Modal.confirm({
                cancelText: "取消",
                okText: "确定",
                title: "请确认完成核扣",
                content: <pre>{contentStr}</pre>,
                onOk: () => {
                    lib.request({
                        url: "/ccs/endorsement/handler-check",
                        data: { ids },
                        needMask: true,
                        success: res => {
                            if (res.errorMessage) {
                                message.error(res.errorMessage);
                            } else {
                                message.success("手动核注成功");
                                modal.destroy();
                                this.load(true);
                            }
                        },
                    });
                },
                onCancel: () => {
                    modal.destroy();
                },
            });
        }
    }

    renderRightOperation() {
        return (
            <React.Fragment>
                <Button
                    type="primary"
                    onClick={() => {
                        this.setState({
                            visible: true,
                            modalTitle: "新增核注清单",
                        });
                        this.getConfigList();
                    }}>
                    新增核注清单
                </Button>
            </React.Fragment>
        );
    }

    push() {
        let rows = this.state.dataList.filter(item => item.checked),
            list = [];
        if (rows.length === 0) {
            message.warning("请选择至少一条数据");
            return;
        }
        rows.map(item => list.push(item.id));
        lib.request({
            url: "/ccs/endorsement/push",
            method: "POST",
            data: {
                idList: list,
            },
            needMask: true,
            success: res => {
                if (!res.errorMessage) {
                    message.success("推送核注成功");
                    this.load(true);
                } else {
                    message.error(res.errorMessage);
                }
            },
        });
    }

    handleOk(values, modalForm) {
        for (let i in values) {
            if (!values[i]) {
                delete values[i];
            }
        }
        if (!values.checklistsFlag) {
            values.checklistsFlag = false;
        }
        if (values.inventoryOrderId) {
            let len = values.inventoryOrderId.indexOf("[");
            values.inventoryOrderId = values.inventoryOrderId.slice(
                0,
                len === -1 ? values.inventoryOrderId.length : len,
            );
        }
        lib.request({
            url: "/ccs/endorsement/createByChecklist",
            data: values,
            method: "POST",
            needMask: true,
            success: res => {
                if (res.errorMessage) {
                    message.error(res.errorMessage);
                } else {
                    this.setState({
                        visible: false,
                    });
                    this.load(true);
                    modalForm && modalForm.resetFields();
                }
            },
        });
    }

    handleCancel() {
        this.setState({
            visible: false,
        });
    }

    tabSubmit(values) {
        this.handleOk(values);
    }

    detailCancel() {
        this.setState({
            editRow: {},
            detailVisible: false,
        });
    }

    detailOk() {
        let recordList = [],
            url = "",
            { mailNo, editRow, type } = this.state;
        if (type === "watch") {
            this.setState({
                detailVisible: false,
            });
            return;
        }
        if (editRow.type === "2") {
            url = "/ccs/endorsement/preEditMailNo";
            if (mailNo) {
                mailNo
                    .split("\n")
                    .filter(item => item && item.trim())
                    .map(item => {
                        recordList.push({
                            mailNo: item,
                        });
                    });
            }
        } else if (editRow.type === "3") {
            url = "/ccs/endorsement/preRefundEditMailNo";
            if (mailNo) {
                mailNo
                    .split("\n")
                    .filter(item => item && item.trim())
                    .map(item => {
                        recordList.push({
                            relNo: item,
                        });
                    });
            }
        }
        lib.request({
            url,
            method: "POST",
            data: {
                id: editRow.id,
                recordList,
            },
            needMask: true,
            success: res => {
                let result = res;
                result.addFailList.map((item, index) => {
                    item.index = index;
                });
                result.addSuccessList.map((item, index) => {
                    item.index = index;
                });
                result.deleteFailList.map((item, index) => {
                    item.index = index;
                });
                result.deleteSuccessList.map((item, index) => {
                    item.index = index;
                });
                this.setState({
                    result,
                    previewVisible: true,
                });
            },
        });
    }

    submitHandler() {
        let { result, editRow } = this.state;
        if (
            (result.addSuccessList === null || result.addSuccessList.length === 0) &&
            (result.deleteSuccessList === null || result.deleteSuccessList.length === 0)
        ) {
            message.warning("暂无有效数据");
            return;
        }
        let url;
        if (editRow.type === "2") {
            url = "/ccs/endorsement/submitEditMailNo";
        } else {
            url = "/ccs/endorsement/submitRefundEditMailNo";
        }
        lib.request({
            url,
            method: "POST",
            data: {
                id: editRow.id,
                addList: result.addSuccessList,
                deleteList: result.deleteSuccessList,
            },
            needMask: true,
            success: res => {
                if (!res.errorMessage) {
                    message.success("修改运单号成功");
                    this.setState({
                        mailNo: "",
                        result: {},
                        previewVisible: false,
                        detailVisible: false,
                    });
                    this.load(true);
                } else {
                    message.error(res.errorMessage);
                }
            },
        });
    }

    tableChange(pagination) {
        this.setState({
            modalPagination: {
                current: pagination.current,
                pageSize: pagination.pageSize,
            },
        });
    }

    renderModal() {
        const {
            previewVisible,
            result,
            detailVisible,
            configList,
            visible,
            editRow,
            type,
            mailNo,
            modalPagination,
            exception,
            eliminateData,
            eliminateModalVisible,
        } = this.state;
        let props = {
            title: this.state.modalTitle,
            onOk: this.handleOk.bind(this),
            onCancel: this.handleCancel.bind(this),
            configList,
            visible,
            form: this.props.form,
            editRow,
            formItemLayout: {
                labelCol: { span: 8 },
                wrapperCol: { span: 14 },
            },
            modalStyle: { width: "800px" },
        };
        const columns1 = [
            {
                title: "序号",
                dataIndex: "index",
                render: (text, row, index) => {
                    return 1 + index;
                },
            },
            {
                title: "运单号",
                render: (text, row, index) => {
                    return row.mailNo || row.relNo;
                },
            },
        ];
        const columns2 = [
            {
                title: "序号",
                dataIndex: "index",
                render: (text, row, index) => {
                    return index + 1;
                },
            },
            {
                title: "运单号",
                render: (text, row, index) => {
                    return row.mailNo || row.relNo;
                },
            },
            {
                title: "错误信息",
                dataIndex: "errorMsg",
            },
        ];
        const bodyItemColumns = [
            {
                title: "行号",
                dataIndex: "index",
            },
            {
                title: "商品序号",
                dataIndex: "accountSeqNo",
            },
            {
                title: "料号",
                dataIndex: "productId",
            },
            {
                title: "申报数量",
                dataIndex: "declareQty",
            },
        ];
        const eliminateColumns = [
            {
                title: "商品序号",
                dataIndex: "key",
            },
            {
                title: "料号",
                dataIndex: "value",
            },
        ];
        const eliminateMailColumns = [
            {
                title: "运单号",
                render: (text, record) => record,
            },
        ];
        return (
            (<React.Fragment>
                {/* 剔除异常结果展示弹窗 */}
                <Modal
                    title="剔除异常"
                    open={eliminateModalVisible}
                    onOk={() => this.setState({ eliminateModalVisible: false, eliminateData: {} })}
                    onCancel={() => this.setState({ eliminateModalVisible: false, eliminateData: {} })}>
                    <Tabs defaultActiveKey="0">
                        <TabPane tab="表体" key="0">
                            <Table dataSource={eliminateData?.exceptionItem} columns={eliminateColumns} />
                        </TabPane>
                        <TabPane tab="运单号" key="1">
                            <Table dataSource={eliminateData?.exceptionMailNos} columns={eliminateMailColumns} />
                        </TabPane>
                    </Tabs>
                </Modal>
                <NewModal {...props} />
                <Modal
                    cancelText="取消"
                    okText="确定"
                    open={detailVisible}
                    width={660}
                    onOk={() => this.detailOk()}
                    onCancel={() => this.detailCancel()}>
                    <Tabs defaultActiveKey="0">
                        <TabPane tab="表体" key="0">
                            {/* 请输入商品账册序号+料号 */}
                            {/* <TextArea value={editRow.defaultStr} disabled rows={20} /> */}
                            <FormItem label="是否异常" wrapperCol={{ span: 8 }}>
                                <Select
                                    value={exception}
                                    onChange={e => {
                                        if (e === "all") {
                                            editRow.bodyList = editRow.bodyItemInfos;
                                        } else {
                                            editRow.bodyList = editRow.bodyItemInfos.filter(
                                                item => item.exception === e,
                                            );
                                        }
                                        this.setState({ editRow, exception: e });
                                    }}>
                                    <Option value={"all"}>全部</Option>
                                    <Option value={true}>异常</Option>
                                    <Option value={false}>正常</Option>
                                </Select>
                            </FormItem>
                            <Table
                                dataSource={editRow.bodyList}
                                columns={bodyItemColumns}
                                rowKey="index"
                                pagination={modalPagination}
                                scroll={{ y: "520px" }}
                                onChange={this.tableChange.bind(this)}
                            />
                        </TabPane>
                        {editRow.type === "1" && (
                            <TabPane tab="单据" key="1">
                                <TextArea disabled value={mailNo} rows={20} />
                            </TabPane>
                        )}
                        {editRow.type === "2" && (
                            <TabPane tab="单据" key="1">
                                请输入运单号
                                <TextArea
                                    disabled={type === "watch"}
                                    value={mailNo}
                                    rows={20}
                                    onChange={e => {
                                        this.setState({ mailNo: e.target.value });
                                    }}
                                />
                            </TabPane>
                        )}
                        {editRow.type === "3" && (
                            <TabPane tab="单据" key="1">
                                请输入运单号
                                <TextArea
                                    disabled={type === "watch"}
                                    defaultValue={mailNo}
                                    rows={20}
                                    onChange={e => {
                                        this.setState({ mailNo: e.target.value });
                                    }}
                                />
                            </TabPane>
                        )}
                    </Tabs>
                </Modal>
                {previewVisible && (
                    <Modal
                        width="800px"
                        open={previewVisible}
                        title={`提交预览  (修改数量${result && result.totalCount})`}
                        onOk={() => this.submitHandler()}
                        onCancel={() => {
                            this.setState({
                                previewVisible: false,
                                // mailNo: "",
                                // detailVisible: false
                            });
                        }}>
                        <Tabs defaultActiveKey="0">
                            <TabPane tab={`新增成功(${result.addSuccessList && result.addSuccessList.length})`} key="0">
                                <Table dataSource={result.addSuccessList} columns={columns1} rowKey="index"></Table>
                            </TabPane>
                            <TabPane tab={`新增失败(${result.addFailList && result.addFailList.length})`} key="1">
                                <Table dataSource={result.addFailList} columns={columns2} rowKey="index"></Table>
                            </TabPane>
                            <TabPane
                                tab={`删除成功(${result.deleteSuccessList && result.deleteSuccessList.length})`}
                                key="2">
                                <Table dataSource={result.deleteSuccessList} columns={columns1} rowKey="index"></Table>
                            </TabPane>
                            <TabPane tab={`删除失败(${result.deleteFailList && result.deleteFailList.length})`} key="3">
                                <Table dataSource={result.deleteFailList} columns={columns2} rowKey="index"></Table>
                            </TabPane>
                        </Tabs>
                    </Modal>
                )}
            </React.Fragment>)
        );
    }
}

export default App;
