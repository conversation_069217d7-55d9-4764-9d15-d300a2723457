import React, { Fragment } from "react";
import { Button, Input, message, Space } from "antd";
import { SearchList, HOC, lib, getConfigDataUtils } from "react-single-app";
import BatchManualProcessingModal from "../components/batch-manual-processing-modal";
import moment from "moment";
import axios from "axios";

@HOC.mapAuthButtonsToState({})
class App extends SearchList {
    constructor(props) {
        super(props);
    }

    getConfig() {
        return axios.get(getConfigDataUtils.getDataUrlByDataId(514)).then(e => e.data.data);
    }

    componentDidMount() {
        let toTime = moment().format("YYYY-MM-DD"),
            fromTime = moment().subtract(1, "months").format("YYYY-MM-DD");
        this.changeImmutable({
            createTimeFrom: moment(fromTime + " 00:00:00.000").format("x"),
            createTimeTo: moment(toTime + " 23:59:59.999").format("x"),
        });
    }

    getCheckedRows() {
        let { selectedRows } = this.state;
        return selectedRows;
    }

    batchManual() {
        let selectedRows = this.getCheckedRows();
        if (selectedRows.length === 0) {
            message.warning("请选择数据");
            return;
        }
        this.setState({
            batchManualProcessingModalVisible: true,
        });
    }

    renderLeftOperation() {
        return (
            <React.Fragment>
                <Space>
                    <Button type="primary" onClick={() => this.pushAgain("DECLARE_PAYMENT")}>
                        支付重推
                    </Button>
                    {this.state.buttons && this.state.buttons.includes("batch-manual-processing") && (
                        <Button onClick={() => this.batchManual()}>批量手动操作</Button>
                    )}
                </Space>
            </React.Fragment>
        );
    }

    pushAgain(action) {
        let list = this.getCheckedRows();
        if (list.length !== 0) {
            let ids = [];
            list.map(item => {
                ids += `${item.orderId},`;
            });
            lib.request({
                url: "/ccs/order/reDeclare",
                data: {
                    action,
                    ids: ids.slice(0, -1),
                },
                method: "POST",
                needMask: true,
                success: res => {
                    if (res.errorMessage) {
                        message.error(res.errorMessage);
                    } else {
                        message.success("重推成功");
                        this.load();
                    }
                },
            });
        } else {
            message.warning("请选择数据");
        }
    }

    renderModal() {
        return (
            <Fragment>
                <BatchManualProcessingModal
                    ids={this.getCheckedRows().reduce((prev, curr) => [...prev, curr.id], [])}
                    visible={this.state.batchManualProcessingModalVisible}
                    close={success => {
                        if (success) {
                            this.load();
                        }
                        this.setState({ batchManualProcessingModalVisible: false });
                    }}
                    config={paymentDeclarationBatchManualData()}
                    submitUrl={"/ccs/payment/manualUpdStatus"}
                    type={"Payment"}
                />
            </Fragment>
        );
    }
}

export default App;

const paymentDeclarationBatchManualData = () => {
    return {
        baseInfo: {
            children: [
                {
                    label: "手动操作原因",
                    editEnable: true,
                    type: "single-select",
                    name: "reason",
                    hidden: true,
                    rules: [
                        {
                            required: true,
                            message: "请选择手动操作原因!",
                        },
                    ],
                },
                {
                    label: "支付申报单状态",
                    editEnable: false,
                    name: "status",
                    type: "radioSelect",
                    from: "/ccs/modify/status/list",
                    rules: [
                        {
                            required: false,
                            message: "请选择支付申报单状态!",
                        },
                    ],
                },
            ],
            label: "",
            name: "baseInfo",
            isGroup: true,
            className: "nuclear-note-manage batch-manual-processing-modal",
        },
    };
};
