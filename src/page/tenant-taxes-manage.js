import React from "react";
import { ConfigCenter, lib } from "react-single-app";
import "./shop-good.less";
import { Modal, Form, Space } from "antd";

class App extends ConfigCenter {
    constructor(props) {
        super(props);
    }
    componentDidMount() {
        // lib.checkIsLogin()
    }

    handleEnable(id, enable) {
        if (!enable) {
            Modal.confirm({
                cancelText: "取消",
                okText: "确定",
                title: "确定禁用吗？",
                onOk: () => {
                    lib.request({
                        url: "/ccs/taxesTenant/updateEnable",
                        method: "POST",
                        data: {
                            id,
                            enable,
                        },
                        contentType: "application/x-www-form-urlencoded",
                        needMask: true,
                        success: res => {
                            if (res) {
                                this.load(true);
                            }
                        },
                    });
                },
            });
        } else {
            lib.request({
                url: "/ccs/taxesTenant/updateEnable",
                method: "POST",
                data: {
                    id,
                    enable,
                },
                contentType: "application/x-www-form-urlencoded",
                needMask: true,
                success: res => {
                    if (res) {
                        this.load(true);
                    }
                },
            });
        }
    }
    getDetail(row) {
        return row.name;
    }
    myOperation(row) {
        return (
            <Space>
                <a
                    onClick={() =>
                        lib.openPage(
                            `/tenant-taxes-recharge?pageTitle=${
                                "充值记录-" + row.name
                            }&config_id=****************&name=${row.name}&tenantAccountId=${row.id}`,
                        )
                    }>
                    充值记录
                </a>
                <a
                    onClick={() => {
                        lib.openPage(
                            `/tenant-taxes-bill?pageTitle=${
                                "电子税单-" + row.name
                            }&config_id=****************&tenantAccountId=${row.id}`,
                            () => {
                                this.load(true);
                            },
                        );
                    }}>
                    电子税单
                </a>
            </Space>
        );
    }
}
export default App;
