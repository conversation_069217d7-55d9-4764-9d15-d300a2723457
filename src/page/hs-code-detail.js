import React, { useState, useEffect } from "react";
// import "antd/dist/antd.css";
import { lib } from "react-single-app";
import "./shop-good.less";
import { Button, Input, Select, Form } from "antd";
function App() {
    const [type, setType] = useState(""); //获取页面类型
    const [datas, setDatas] = useState(""); //获取数据
    const [listConsumptionFlag, SetlistConsumptionFlag] = useState(null); //从价从量下拉
    const [listUom, SetListUom] = useState(null); //单位下拉
    const [value, setValue] = useState(null); //消费税类型
    const [floatVal, setFloatVal] = useState(null); // 浮动类型
    const [first, setFirst] = useState(null); //第一计量单位
    const [second, setSecond] = useState(null); //第二计量单位
    const [floatList, setFloatList] = useState([]);
    const [form1] = Form.useForm();
    const [form2] = Form.useForm();
    useEffect(() => {
        setType(lib.getParam("type"));
        lib.request({
            url: "/ccs/hsCode/detail", //详情页列表
            data: {
                id: lib.getParam("id"),
            },
            needMask: true,
            success: res => {
                res.consumptionTax = res.consumptionTax || 0;
                setDatas(res);
                setValue(res.consumptionFlag || 10);
                setFirst(res.firstLegalUnit);
                setSecond(res.secondLegalUnit);
                setFloatVal(res.floatType);
                setTimeout(() => {
                    onReset();
                }, 0);
                // console.log(res);
            },
        });
        lib.request({
            url: "/ccs/hsCode/listConsumptionFlag", //从量从价
            needMask: true,
            success: res => {
                SetlistConsumptionFlag(res);
            },
        });
        lib.request({
            url: "/ccs/customs/listUom", //计量单位
            needMask: true,
            success: res => {
                SetListUom(res);
            },
        });
        lib.request({
            url: "/ccs/hsCode/listFloatFlag",
            needMask: true,
            success: res => {
                setFloatList(res);
            },
        });
    }, []);
    function onReset() {
        form1.resetFields();
    }
    function getValue(value, type) {
        // if(floatType){}
        if (type === "floatType") {
            for (let i in floatList) {
                if (floatList[i].id == value) {
                    return floatList[i].name;
                }
            }
        }
        if (type === "consumptionFlag") {
            for (let i in listConsumptionFlag) {
                if (listConsumptionFlag[i].id == value) {
                    return listConsumptionFlag[i].name;
                }
            }
        }
        if (type === "uom") {
            for (let i in listUom) {
                if (listUom[i].id == value) {
                    return listUom[i].name;
                }
            }
        }
    }
    function watch(e) {
        const children = [];
        for (let i = 0; i < e.length; i++) {
            children.push(
                <Form.Item
                    className="hs-code-detail"
                    key={i}
                    label={e[i].title}
                    name={e[i].key}
                    style={{ float: "left", width: "100%", wordBreak: "break-all" }}>
                    <div>{e[i].value}</div>
                </Form.Item>,
            );
        }
        return children;
    }
    function firstOnchange(e) {
        //第一计量单位
        setFirst(e);
    }
    function secondOnchange(e) {
        //第二计量单位
        setSecond(e);
    }
    function onChanges(e) {
        //从价从量
        setValue(e);
        setFloatVal(null);
        form1?.setFieldsValue({
            consumptionTax: 0,
            pricePerUnit: null,
            uomName: null,
            // consumptionNumTax: 0,
            specificPricePerUnit: 0,
            specificUomName: null,
            floatType: null,
        });
        form2?.setFieldsValue({
            consumptionTax: 0,
            pricePerUnit: null,
            uomName: null,
            // consumptionNumTax: 0,
            specificPricePerUnit: 0,
            specificUomName: null,
            floatType: null,
        });
    }
    function onFloatChange(e) {
        console.log(e);
        if (e === 0 && value === 10) {
            form1?.setFieldsValue({ consumptionTax: 0 });
            form2?.setFieldsValue({ consumptionTax: 0 });
        }
        setFloatVal(e);
    }
    function handleSubmit(e) {
        //提交数据验证表单
        if (lib.getParam("id")) {
            e.id = lib.getParam("id");
        }
        if (!e.consumptionTax) {
            e.consumptionTax = 0;
        }
        e.pricePerUnit && (e.pricePerUnit = Number(String(e.pricePerUnit).trim()));
        e.specificPricePerUnit && (e.specificPricePerUnit = Number(String(e.specificPricePerUnit).trim()));
        lib.request({
            url: "/ccs/hsCode/upset",
            data: e,
            method: "POST",
            needMask: true,
            success: res => {
                lib.closePage();
            },
        });
    }
    function EditAdd(e) {
        //渲染input和select
        const children = [];
        for (let i = 0; i < e.length; i++) {
            if (e[i].type === "Input") {
                children.push(
                    <Form.Item
                        key={i}
                        label={e[i].title}
                        name={e[i].key}
                        rules={[
                            {
                                required: e[i].required,
                                pattern: e[i].pattern,
                            },
                        ]}
                        style={{ float: "left", width: "100%", wordBreak: "break-all" }}>
                        <Input
                            style={{ width: 350 }}
                            maxLength={e[i].maxLength}
                            minLength={e[i].minLength}
                            disabled={e[i].disabled}></Input>
                    </Form.Item>,
                );
            } else if (e[i].type === "Select") {
                // const list = e[i].key !== '' ? listConsumptionFlag : floatList;
                let list = [];
                let fn = null;
                if (e[i].key === "floatType") {
                    list = floatList;
                    fn = onFloatChange;
                }
                if (e[i].key === "consumptionFlag") {
                    list = listConsumptionFlag;
                    fn = onChanges;
                }
                children.push(
                    <React.Fragment>
                        <Form.Item
                            key={i}
                            label={e[i].title}
                            name={e[i].key}
                            rules={
                                value !== 10 && e[i].key === "floatType"
                                    ? []
                                    : [
                                          {
                                              required: e[i].required,
                                          },
                                      ]
                            }
                            style={{ float: "left", width: "100%", wordBreak: "break-all" }}>
                            <Select
                                onChange={fn}
                                style={{ width: 350 }}
                                disabled={value !== 10 && e[i].key === "floatType"}>
                                {list &&
                                    list.map((item, index) => {
                                        return (
                                            <Select.Option key={index} value={Number(item.id)}>
                                                {item.name}
                                            </Select.Option>
                                        );
                                    })}
                            </Select>
                        </Form.Item>
                    </React.Fragment>,
                );
            }
        }
        return children;
    }
    function getLegalUnit() {
        //渲染计量单位Select
        return (
            <React.Fragment>
                <Form.Item label="计量单位" className="required-icon">
                    <Input.Group compact>
                        <Form.Item
                            name={"firstLegalUnit"}
                            noStyle
                            rules={[{ required: true, message: "法定第一计量单位不能为空" }]}>
                            <Select
                                onChange={firstOnchange}
                                value={first}
                                optionFilterProp="children"
                                allowClear
                                showSearch
                                style={{
                                    float: "left",
                                    width: 175,
                                }}>
                                {listUom &&
                                    listUom.map((item, index) => {
                                        return (
                                            <Select.Option key={index} value={item.id}>
                                                {item.name}
                                            </Select.Option>
                                        );
                                    })}
                            </Select>
                        </Form.Item>
                        <Form.Item name={"secondLegalUnit"} noStyle>
                            <Select
                                onChange={secondOnchange}
                                value={second}
                                placeholder="请选择"
                                optionFilterProp="children"
                                allowClear
                                showSearch
                                style={{ width: 175 }}>
                                {listUom &&
                                    listUom.map((item, index) => {
                                        return (
                                            <Select.Option key={index} value={item.id}>
                                                {item.name}
                                            </Select.Option>
                                        );
                                    })}
                            </Select>
                        </Form.Item>
                    </Input.Group>
                </Form.Item>
            </React.Fragment>
        );
    }
    function vat() {
        const disabled = value === 5;
        // console.log("vat:", disabled, floatVal);
        return (
            <Form.Item
                label="从价定率"
                name="consumptionTax"
                key={123}
                rules={[{ required: value === 15 }]}
                style={{
                    float: "left",
                    width: "100%",
                    wordBreak: "break-all",
                }}>
                <span>为</span>
                <Form.Item
                    noStyle
                    name="consumptionTax"
                    rules={
                        value === 15
                            ? [
                                  {
                                      pattern: /^[1-9]\d*$/,
                                  },
                              ]
                            : [
                                  {
                                      pattern: /^([0]|[1-9][0-9]*)$/,
                                  },
                              ]
                    }>
                    <Input disabled={disabled} style={{ width: 100 }} maxLength={3}></Input>
                </Form.Item>
                <span>%</span>
            </Form.Item>
        );
    }
    function showHideList() {
        return (
            <Form.Item
                label="从价定率"
                className="required-icon"
                style={{ float: "left", width: "100%", wordBreak: "break-all" }}>
                <Input.Group compact>
                    <span className="translatey">完税价格≥</span>

                    <Form.Item
                        name="pricePerUnit"
                        noStyle
                        rules={[
                            {
                                required: true,
                                message: "价格界限不能为空",
                            },
                            {
                                pattern: /^(?!0(\.0+)?$)(\d+(\.\d+)?|\.\d+)$/,
                                message: "请输入大于0的数字",
                            },
                        ]}>
                        <Input style={{ width: 60 }} />
                    </Form.Item>
                    <Form.Item name="uomName" noStyle rules={[{ required: true, message: "从量计量单位不能为空" }]}>
                        <Input style={{ width: 60 }}></Input>
                    </Form.Item>
                    <span className="translatey">的，税率为</span>
                    <Form.Item
                        name="consumptionTax"
                        noStyle
                        rules={[
                            {
                                required: true,
                                message: "税率不能为空不能为0",
                            },
                            {
                                pattern: /^([0]|[1-9][0-9]*)$/,
                                message: "请输入整数",
                            },
                        ]}>
                        <Input style={{ width: 60 }} maxLength={3} />
                    </Form.Item>
                    <span className="translatey">%</span>
                </Input.Group>
            </Form.Item>
        );
    }

    function showLiang() {
        const required = value === 5 || value === 15;
        return (
            <Form.Item
                label="从量定额"
                className={required ? "required-icon" : ""}
                style={{ float: "left", width: "100%", wordBreak: "break-all" }}>
                <Input.Group compact>
                    <Form.Item
                        name="specificPricePerUnit"
                        noStyle
                        rules={
                            required
                                ? [
                                      {
                                          required: true,
                                          message: "单价不能为空",
                                      },
                                      {
                                          pattern: /^(?!0(\.0+)?$)(\d+(\.\d+)?|\.\d+)$/,
                                          message: "请输入大于0的数字",
                                      },
                                  ]
                                : [{ pattern: /^(0\.\d+|\d+(\.\d+)?)$/, message: "请输入非负数" }]
                        }>
                        <Input style={{ width: 120 }} placeholder="请输入单价" />
                    </Form.Item>
                    <Form.Item
                        name="specificUomName"
                        noStyle
                        rules={required ? [{ required: true, message: "规格不能为空" }] : []}>
                        <Input style={{ width: 120 }} placeholder="请输入规格"></Input>
                    </Form.Item>
                    <span className="translatey">例如：0.912 元/升</span>
                </Input.Group>
            </Form.Item>
        );
    }

    // function
    const formItemLayout = {
        labelCol: {
            xs: { span: 24 },
            sm: { span: 6 },
        },
    };
    const itemWatch = [
        { title: "HS编码", key: "hsCode", value: datas.hsCode },
        { title: "商品名称", key: "hsName", value: datas ? datas.hsName : "" },
        { title: "编码状态", key: "statusDesc", value: datas ? datas.statusDesc : "" },
        { title: "更新时间", key: "updateTime", value: datas ? datas.updateTime : "" },
    ];
    const item1Watch = [
        {
            title: "计量单位",
            key: "firstLegalUnit",
            type: "Input",
            value: datas.secondLegalUnit
                ? getValue(datas.firstLegalUnit, "uom") + "/" + getValue(datas.secondLegalUnit, "uom")
                : getValue(datas.firstLegalUnit, "uom"),
        },
        {
            title: "出口税率",
            key: "exportTaxRate",
            value: datas ? datas.exportTaxRate + "%" : "",
        },
        {
            title: "出口退税税率",
            key: "exportDrawbackTaxRate",
            value: datas ? datas.exportDrawbackTaxRate + "%" : "",
        },
        {
            title: "出口暂定税率",
            key: "exportTentativeTaxRate",
            value: datas ? datas.exportTentativeTaxRate + "%" : "",
        },
        { title: "增值税率", key: "vat", value: datas ? datas.vat + "%" : "" },
        {
            title: "进口优惠税率",
            key: "importDiscountTaxRate",
            value: datas ? datas.importDiscountTaxRate + "%" : "",
        },
        {
            title: "进口暂定税率",
            key: "importTentativeTaxRate",
            value: datas ? datas.importTentativeTaxRate + "%" : "",
        },
        {
            title: "进口普通税率",
            key: "importGeneralTaxRate",
            value: datas ? datas.importGeneralTaxRate + "%" : "",
        },
        // {
        //     title: "消费税率",
        //     key: datas && datas.consumptionFlag == 5 ? "consumptionNumTax" : "consumptionTax",
        //     value: datas
        //         ? getValue(datas.consumptionFlag) == "从量"
        //             ? `完税价格≥${datas.pricePerUnit}${datas.uomName || ""}的，税率为${datas.consumptionNumTax}%`
        //             : datas.consumptionTax + "%"
        //         : "",
        // },
        // {
        //     title: "从量/从价",
        //     key: "consumptionFlag",
        //     value: datas ? getValue(datas.consumptionFlag) : "",
        //     type: "Select",
        // },
        {
            title: "消费税类型",
            key: "consumptionFlag",
            value: datas ? getValue(datas.consumptionFlag, "consumptionFlag") : "",
            type: "Select",
            // required: true,
        },
        {
            title: "浮动类型",
            key: "floatType",
            value: datas ? getValue(datas.floatType, "floatType") : "",
            type: "Select",
            // required: true,
        },
        {
            title: "从价定率",
            key: "firstLegalUnit",
            type: "Input",
            // value === 10 && floatVal !== 0
            value: datas
                ? datas.consumptionFlag === 10 && datas.floatType !== 0
                    ? `完税价格≥${datas.pricePerUnit} ${datas.uomName}的，税率为${datas.consumptionTax}%`
                    : `${datas.consumptionTax}%`
                : null,
        },
        {
            title: "从量定额",
            key: "firstLegalUnit",
            type: "Input",
            value: datas ? `${datas.specificPricePerUnit} ${datas.specificUomName}` : null,
        },
    ];
    const getflag = [
        {
            title: "消费税类型",
            key: "consumptionFlag",
            value: datas ? getValue(datas.consumptionFlag, "consumptionFlag") : "",
            type: "Select",
            required: true,
        },
        {
            title: "浮动类型",
            key: "floatType",
            value: datas ? getValue(datas.floatType, "floatType") : "",
            type: "Select",
            required: true,
        },
    ];
    const edit1 = [
        {
            title: "HS编码",
            key: "hsCode",
            type: "Input",
            required: true,
            disabled: lib.getParam("type") == "edit" ? true : false,
            maxLength: lib.getParam("type") == "add" ? 10 : null,
            minLength: 10,
            pattern: /^([0-9][0-9]*)$/,
        },
        { title: "商品名称", key: "hsName", type: "Input", required: true, maxLength: 255 },
    ];
    const edit2 = [
        {
            title: "出口税率",
            key: "exportTaxRate",
            type: "Input",
            pattern: /^([0]|[1-9][0-9]*)$/,
            maxLength: 3,
        },
        {
            title: "出口退税税率",
            key: "exportDrawbackTaxRate",
            type: "Input",
            pattern: /^([0]|[1-9][0-9]*)$/,
            maxLength: 3,
        },
        {
            title: "出口暂定税率",
            key: "exportTentativeTaxRate",
            type: "Input",
            pattern: /^([0]|[1-9][0-9]*)$/,
            maxLength: 3,
        },
        {
            title: "增值税率",
            key: "vat",
            required: true,
            type: "Input",
            pattern: /^([0]|[1-9][0-9]*)$/,
            maxLength: 3,
        },
        {
            title: "进口优惠税率",
            key: "importDiscountTaxRate",
            type: "Input",
            pattern: /^([0]|[1-9][0-9]*)$/,
            maxLength: 3,
        },
        {
            title: "进口暂定税率",
            key: "importTentativeTaxRate",
            type: "Input",
            pattern: /^([0]|[1-9][0-9]*)$/,
            maxLength: 3,
        },
        {
            title: "进口普通税率",
            key: "importGeneralTaxRate",
            type: "Input",
            pattern: /^([0]|[1-9][0-9]*)$/,
            maxLength: 3,
        },
    ];
    const validateMessages = {
        pattern: {
            mismatch: `请输入正整数`,
        },
    };
    return (
        <div
            style={{
                paddingLeft: 10 + "%",
                paddingRight: 10 + "%",
                //     border: "none",
                //     boxShadow: "0px 1px 4px 0px rgba(0, 21, 41, 0.12)",
                //     borderRadius: "8px",
                background: "#fff",
                //     margin: 20,
                //     clear: "both",
                overflow: "auto",
                // height: "90vh",
                paddingBottom: "40px",
            }}>
            {type === "watch" && (
                <div>
                    <Form {...formItemLayout} form={form1}>
                        <h4 style={{ clear: "both", fontWeight: 900, marginLeft: "15%" }}>基本信息</h4>
                        {watch(itemWatch)}
                        <h4 style={{ clear: "both", fontWeight: 900, marginLeft: "15%" }}>税率信息</h4>
                        {watch(item1Watch)}
                    </Form>
                </div>
            )}
            {type === "edit" && (
                <div>
                    <Form
                        {...formItemLayout}
                        validateMessages={validateMessages}
                        form={form1}
                        onFinish={handleSubmit}
                        initialValues={datas}>
                        <div style={{ float: "left", width: "100%", height: "72px" }}>
                            <Button
                                type="primary"
                                onClick={() => {
                                    lib.closePage();
                                }}
                                style={{ float: "right", margin: "20px" }}>
                                取消
                            </Button>
                            <Button type="primary" htmlType="submit" style={{ float: "right", margin: "20px" }}>
                                保存
                            </Button>
                        </div>
                        <h4 style={{ clear: "both", fontWeight: 900, marginLeft: "15%" }}>基本信息</h4>
                        {EditAdd(edit1)}
                        <h4 style={{ clear: "both", fontWeight: 900, marginLeft: "15%" }}>税率信息</h4>
                        {getLegalUnit()}
                        {EditAdd(edit2)}
                        {EditAdd(getflag)}
                        {value === 10 && floatVal === 0 ? vat() : null}
                        {value === 5 || value === 15 || !value ? vat() : null}
                        {value === 10 && floatVal !== 0 ? showHideList() : null}
                        {showLiang()}
                    </Form>
                </div>
            )}
            {type === "add" && (
                <div>
                    <Form
                        {...formItemLayout}
                        form={form2}
                        validateMessages={validateMessages}
                        onFinish={handleSubmit}
                        initialValues={{
                            exportTaxRate: 0,
                            exportDrawbackTaxRate: 0,
                            exportTentativeTaxRate: 0,
                            importDiscountTaxRate: 0,
                            importTentativeTaxRate: 0,
                            importGeneralTaxRate: 0,
                            consumptionFlag: 10,
                        }}>
                        <div style={{ float: "left", width: "100%", height: "72px" }}>
                            <Button
                                type="primary"
                                onClick={() => {
                                    lib.closePage();
                                }}
                                style={{ float: "right", margin: "20px" }}>
                                取消
                            </Button>
                            <Button type="primary" htmlType="submit" style={{ float: "right", margin: "20px" }}>
                                保存
                            </Button>
                        </div>
                        <h4 style={{ clear: "both", fontWeight: 900, marginLeft: "15%" }}>基本信息</h4>
                        {EditAdd(edit1)}
                        <h4 style={{ clear: "both", fontWeight: 900, marginLeft: "15%" }}>税率信息</h4>
                        {getLegalUnit()}
                        {EditAdd(edit2)}
                        {EditAdd(getflag)}
                        {value === 10 && floatVal === 0 ? vat() : null}
                        {value === 5 || value === 15 || !value ? vat() : null}
                        {value === 10 && floatVal !== 0 ? showHideList() : null}
                        {showLiang()}
                    </Form>
                </div>
            )}
        </div>
    );
}
export default App;
