import React from "react";
import { But<PERSON>, Modal, message, Space, Image } from "antd";
import { SearchList, lib, getConfigDataUtils, HOC } from "react-single-app";
import NewModal from "../components/NewModal";
import "./shop-good.less";
import axios from "axios";

@HOC.mapAuthButtonsToState({ pagePath: "/tax-account-manage", buttonCodeArr: ["cancelRecharge"] })
class App extends SearchList {
    constructor(props) {
        super(props);
        this.state.editRow = {};
        this.state.upUrl = "/ccs/taxesCompany/recharge";

        this.state.configList = [
            { type: "TEXT", labelName: "业务单号", labelKey: "sn", hidden: true },
            { type: "TEXT", labelName: "税金账户名称", labelKey: "accountName" },
            { type: "TEXT", labelName: "担保企业名称", labelKey: "companyName" },
            { type: "TEXT", labelName: "担保总额度", labelKey: "rechargeTotal" },
            { type: "TEXT", labelName: "担保可用额度", labelKey: "available" },
            {
                type: "INPUTNUMBER",
                labelName: "充值金额",
                labelKey: "rechargeAmount",
                required: true,
                min: 1,
                disabled: false,
            },
            { type: "TEXTAREA", labelName: "备注", labelKey: "remark" },
            {
                type: "FILE",
                labelName: "上传凭证",
                labelKey: "voucher",
                required: false,
                allowTypes: ["jpg", "jpeg", "png", "pdf"],
                extra: "支持jpg、png格式，最多不超出2M",
            },
        ];
    }

    getConfig() {
        return axios.get(getConfigDataUtils.getDataUrlByDataId(568)).then(res => res.data.data);
    }
    form = React.createRef();

    componentDidMount() {
        this.setState({
            name: lib.getParam("name"),
            accountId: lib.getParam("accountId"),
        });
    }

    // 获取configList 里面的下拉数据
    getSelectList() {
        let configList = this.state.configList;
        configList.map(item => {
            if (item.ccs) {
                lib.request({
                    url: item.ccs,
                    method: "GET",
                    needMask: false,
                    success: res => {
                        let list = res.dataList || [],
                            newList = [];
                        list.map(item => {
                            newList.push({
                                name: item.name,
                                value: item.id,
                            });
                        });
                        item.list = newList;
                        this.setState({ configList });
                    },
                });
            }
        });
        this.setState({ configList });
    }
    voucherFunc(row) {
        let src = row.voucher;
        if (src) {
            let type = src.split(".").reverse()[0];
            if (["jpg", "jpeg", "png", "git", "svg", "webp", "bmp"].indexOf(type) > -1) {
                return <Image src={src} width={50} height={50} />;
            } else {
                let name = src.match(/([^/*.]+)\.\w+$/)[0];
                return (
                    <a href={src} target={"_blank"}>
                        {name}
                    </a>
                );
            }
        } else {
            return "待上传";
        }
    }
    myOperation(row) {
        return (
            <React.Fragment>
                <Space>
                    <a
                        className="link"
                        onClick={() => {
                            this.getDetail("detail", row.id);
                        }}>
                        查看
                    </a>
                    {!row.voucher && (
                        <a
                            className="link"
                            onClick={() => {
                                this.getDetail("upload", row.id);
                            }}>
                            上传凭证
                        </a>
                    )}
                    {this.state.buttonAuth?.cancelRecharge && (
                        <a
                            className="link"
                            onClick={() => {
                                this.cancelRecharge(row.id, this.load);
                            }}>
                            撤销
                        </a>
                    )}
                </Space>
            </React.Fragment>
        );
    }

    getDetail(showType, id) {
        let url, data, modalTitle;
        if (showType === "recharge") {
            url = "/ccs/taxesCompany/preRecharge";
            modalTitle = "税金充值";
            data = {
                accountId: lib.getParam("accountId"),
            };
        } else {
            url = "/ccs/taxesCompany/rechargeById";
            modalTitle = "充值详情";
            data = {
                id: id,
            };
        }
        let configList = this.state.configList;
        configList.map(item => {
            item.disabled = showType === "check" || showType === "detail";
            if (item.labelKey === "sn") item.hidden = showType === "recharge";
        });
        lib.request({
            url: url,
            method: "POST",
            data: data,
            needMask: true,
            success: res => {
                if (res) {
                    this.setState({
                        editRow: res,
                        modalTitle: modalTitle,
                        rechargeVisible: true,
                        showType: showType,
                        configList: configList,
                    });
                }
            },
        });
    }

    renderRightOperation() {
        return (
            <React.Fragment>
                <Button
                    type="primary"
                    onClick={() => {
                        this.getDetail("recharge");
                    }}>
                    充值
                </Button>
            </React.Fragment>
        );
    }

    handleOk(values) {
        values.rechargeAmount = Number(parseFloat(values.rechargeAmount).toFixed(2));
        values.accountId = lib.getParam("accountId");
        lib.request({
            url: this.state.upUrl,
            data: values,
            method: "POST",
            needMask: true,
            success: res => {
                if (res) {
                    this.setState({
                        rechargeVisible: false,
                    });
                    this.load(true);
                }
            },
            fail: e => {
                this.setState({
                    rechargeVisible: false,
                });
            },
        });
    }

    handleUpload(values) {
        lib.request({
            url: "/ccs/taxesCompany/uploadVoucher",
            data: { id: this.state.editRow.id, voucher: values.voucher },
            method: "POST",
            needMask: true,
            success: res => {
                this.setState({
                    rechargeVisible: false,
                    editRow: {},
                });
                this.load(true);
            },
        });
    }

    handleCancel() {
        this.setState({
            rechargeVisible: false,
            editRow: {},
        });
    }

    renderModal() {
        const { editRow, rechargeVisible, showType, name, accountId, configList } = this.state;
        editRow.name = name;
        editRow.accountId = accountId;
        let file = editRow.voucher;
        if (file) {
            editRow.voucher = { src: file, name: file.match(/([^/*.]+)\.\w+$/)[0] };
        }
        let onOk,
            onCancel,
            okText = "确定",
            cancelText = "取消",
            keyboard = true;
        if (showType === "recharge") {
            onOk = this.handleOk.bind(this);
            onCancel = this.handleCancel.bind(this);
        } else if (showType === "upload") {
            onOk = this.handleUpload.bind(this);
            onCancel = this.handleCancel.bind(this);
        } else if (showType === "detail") {
            onOk = this.handleCancel.bind(this);
            onCancel = this.handleCancel.bind(this);
        }

        let props = {
            title: this.state.modalTitle,
            onOk: onOk,
            onCancel: onCancel,
            configList: this.state.configList,
            visible: rechargeVisible,
            cancelText: cancelText,
            okText: okText,
            formIn: this.form,
            formItemLayout: {
                labelCol: { span: 8 },
                wrapperCol: { span: 14 },
            },
            modalStyle: { width: "800px" },
            editRow,
            keyboard: keyboard,
        };
        return (
            <React.Fragment>
                <NewModal {...props} />
            </React.Fragment>
        );
    }

    cancelRecharge(id, load) {
        Modal.confirm({
            cancelText: "取消",
            okText: "确定",
            title: "撤销",
            content: "确定撤销充值记录?",
            onOk() {
                return new Promise((resolve, reject) => {
                    lib.request({
                        url: "/ccs/taxesCompany/cancelRecharge",
                        needMask: true,
                        data: { id },
                        success: res => {
                            message.success("撤销成功");
                            load();
                            resolve(res);
                        },
                        fail: e => reject(e),
                    });
                });
            },
        });
    }
}

export default App;
