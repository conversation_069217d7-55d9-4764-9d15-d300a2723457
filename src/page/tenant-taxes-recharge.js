import React, { useState, useEffect } from "react";
import "./shop-good.less";
import { ConfigCenter, lib } from "react-single-app";
import { Button, Modal, message, Space } from "antd";
import NewModal from "../components/NewModal";
class App extends ConfigCenter {
    constructor(props) {
        super(props);
        this.state.editRow = {};
        this.state.upUrl = "/ccs/taxesTenant/recharge";
        this.state.configList = [
            { type: "TEXT", labelName: "税金账户名称", labelKey: lib.getParam("name") },
            {
                type: "SELECT",
                labelName: "充值类型",
                labelKey: "type",
                list: [
                    { name: "税金充值", value: "1" },
                    { name: "罚款缴纳", value: "2" },
                ],
                ccs: "",
                required: true,
            },
            { type: "INPUTNUMBER", labelName: "充值金额", labelKey: "rechargeAmount", required: true, min: 1 },
            { type: "FILE", labelName: "上传凭证", labelKey: "file", required: true },
        ];
    }
    componentDidMount() {
        this.setState({
            name: lib.getParam("name"),
            id: lib.getParam("tenantAccountId"),
        });
    }
    myOperation(row) {
        return (
            <React.Fragment>
                <Space>
                    {row.auditStatusName === "待审核" && (
                        <a
                            className="link"
                            onClick={() => {
                                this.setState({
                                    editRow: row,
                                    auditVisible: true,
                                });
                            }}>
                            审核
                        </a>
                    )}
                    {/* {(row.voucher.indexOf('jpg') !== -1 ||
                    row.voucher.indexOf('png') !== -1 ||
                    row.voucher.indexOf('pdf') !== -1) ?
                    <a className="link" onClick={() => {
                        let newWindow = window.open()
                        newWindow.document.write(`<img src="${row.voucher}"/>`)
                    }}>充值凭证</a> : <a className="link" href={`https://view.officeapps.live.com/op/view.aspx?src=${row.voucher}`} target="_blank">充值凭证</a>
                } */}
                    {row.voucher.indexOf("jpg") !== -1 || row.voucher.indexOf("png") !== -1 ? (
                        <a
                            className="link"
                            onClick={() => {
                                let newWindow = window.open();
                                newWindow.document.write(`<img src="${row.voucher}"/>`);
                            }}>
                            充值凭证
                        </a>
                    ) : row.voucher.indexOf("pdf") !== -1 ? (
                        <a
                            className="link"
                            onClick={() => {
                                let newWindow = window.open();
                                newWindow.document.write(
                                    `<embed type="application/pdf" style="overflow: auto; position: absolute; top: 0; right: 0; bottom: 0; left: 0; width: 100%; height: 100%;" src="${row.voucher}"/>`,
                                );
                            }}>
                            充值凭证
                        </a>
                    ) : (
                        <a
                            className="link"
                            href={`https://view.officeapps.live.com/op/view.aspx?src=${row.voucher}`}
                            target="_blank">
                            充值凭证
                        </a>
                    )}
                </Space>
            </React.Fragment>
        );
    }

    renderRightOperation() {
        return (
            <React.Fragment>
                <Button
                    type="primary"
                    onClick={() => {
                        this.setState({
                            rechargeVisible: true,
                        });
                    }}>
                    充值
                </Button>
            </React.Fragment>
        );
    }

    handleOk(values, modalForm, state) {
        values.rechargeAmount = Number(parseFloat(values.rechargeAmount).toFixed(2));
        values.accountId = lib.getParam("tenantAccountId");
        lib.request({
            url: this.state.upUrl,
            data: values,
            method: "POST",
            needMask: true,
            success: res => {
                if (res) {
                    this.setState({
                        rechargeVisible: false,
                    });
                    this.load(true);
                    modalForm && modalForm.resetFields();
                }
            },
        });
    }

    handleCancel() {
        this.setState({
            rechargeVisible: false,
        });
    }

    auditOk() {
        const { editRow } = this.state;
        lib.request({
            url: "/ccs/taxesTenant/rechargeAudit",
            method: "POST",
            data: {
                id: editRow.id,
                status: 2,
            },
            contentType: "application/x-www-form-urlencoded",
            needMask: true,
            success: res => {
                if (res) {
                    this.setState({
                        auditVisible: false,
                    });
                    message.success("审核成功");
                    this.load(true);
                }
            },
        });
    }

    auditCancel() {
        this.setState({
            auditVisible: false,
            editRow: {},
        });
    }

    renderModal() {
        const { editRow, rechargeVisible, auditVisible, name } = this.state;
        editRow.name = name;
        let props = {
            title: "税金充值",
            onOk: this.handleOk.bind(this),
            onCancel: this.handleCancel.bind(this),
            configList: this.state.configList,
            visible: this.state.rechargeVisible,
            form: this.props.form,
            formItemLayout: {
                labelCol: { span: 8 },
                wrapperCol: { span: 14 },
            },
            modalStyle: { width: "800px" },
            editRow,
        };
        return (
            (<React.Fragment>
                <NewModal {...props} />
                <Modal
                    title="充值审核"
                    open={auditVisible}
                    onOk={() => this.auditOk()}
                    onCancel={() => this.auditCancel()}>
                    确认完成本次充值，充值类型：{editRow.type === "1" ? "税金充值" : "罚款缴纳"}
                    <br />
                    充值金额：{editRow.rechargeAmount}
                </Modal>
            </React.Fragment>)
        );
    }
}
export default App;
