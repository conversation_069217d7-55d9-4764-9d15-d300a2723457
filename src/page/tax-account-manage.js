import React from "react";

import { Check<PERSON>, <PERSON><PERSON>, Modal, Drawer, Input, Select, Form, message, Space } from "antd";
import { ConfigCenter, lib } from "react-single-app";
import NewModal from "../components/NewModal";
import "./tax-account-manage.less";
import moment from "moment";

class App extends ConfigCenter {
    constructor(props) {
        super(props);
        this.state.modalTitle = "新增税金账户";
        // 编辑/新建url
        this.state.upUrl = "/ccs/taxesCompany/upset";
        // 获取详情url
        this.state.detailUrl = "/ccs/customsBookItem/detail";

        this.state.configList = [
            { type: "INPUT", labelName: "税金账户名称", labelKey: "name", required: true },
            {
                type: "SELECT",
                labelName: "担保企业",
                labelKey: "companyId",
                list: [],
                ccs: "/ccs/company/listWithDBQY",
                required: true,
            },
            {
                type: "SELECT",
                labelName: "口岸",
                labelKey: "customCodesList",
                list: [],
                ccs: "/ccs/customs/listDistrict",
                mode: "multiple",
                required: true,
            },
            { type: "DATERANGE", labelName: "担保时间", labelKey: "applyTime", required: true },
        ];
        this.state.editConfigList = [
            {
                type: "TEXT",
                labelName: "担保总额度目前为",
                labelKey: "currentRechargeTotal",
                required: true,
                disabled: true,
            },
            { type: "INPUTNUMBER", labelName: "担保总额度调整为", labelKey: "rechargeTotal", required: true },
            { type: "DATERANGE", labelName: "担保时间", labelKey: "applyTime", required: true },
            { type: "INPUTNUMBER", labelName: "预警金额：", labelKey: "warningAmount", required: true },
        ];
    }

    componentDidMount() {
        // lib.checkIsLogin()
    }

    // 获取configList 里面的下拉数据
    getSelectList() {
        let configList = this.state.configList;
        configList.map(item => {
            if (item.ccs) {
                lib.request({
                    url: item.ccs,
                    needMask: false,
                    success: res => {
                        item.list = res || [];
                        this.setState({ configList });
                    },
                });
            }
        });
        this.setState({ configList });
    }

    myOperation(row) {
        return (
            <Space wrap>
                <span
                    className={"link"}
                    onClick={() => {
                        this.setState({
                            assureSettingVisible: true,
                            modalTitle: "担保设置",
                            editRow: row,
                        });
                    }}>
                    担保设置
                </span>
                {
                    <a
                        onClick={() =>
                            lib.openPage(
                                `/tax-account-recharge?pageTitle=保证金记录-${row.name}&config_id=****************&accountId=${row.id}&name=${row.name}`,
                                () => {
                                    this.load(true);
                                },
                            )
                        }>
                        保证金记录
                    </a>
                }
                {
                    <a
                        onClick={() =>
                            lib.openPage(
                                `/tax-account-payment-letter-recharge?pageTitle=缴款书记录-${row.name}&config_id=****************&accountId=${row.id}&name=${row.name}`,
                                () => {
                                    this.load(true);
                                },
                            )
                        }>
                        缴款书记录
                    </a>
                }

                <a
                    onClick={() => {
                        lib.openPage(
                            `/base?pageTitle=${"电子税单-" + row.name}&config_id=****************&companyId=${
                                row.companyId
                            }&title=${row.companyName}`,
                            () => {
                                this.load(true);
                            },
                        );
                    }}>
                    电子税单
                </a>
            </Space>
        );
    }

    getDetail(row) {
        return row.companyName;
    }

    renderCustomCodesList(row) {
        return <span>{row.customCodesList.join(",")}</span>;
    }

    renderRightOperation() {
        return (
            <React.Fragment>
                <Button
                    type="primary"
                    onClick={() => {
                        this.setState({
                            visible: true,
                            modalTitle: "新增税金账户",
                        });
                        this.getSelectList();
                    }}>
                    新增税金账户
                </Button>
            </React.Fragment>
        );
    }

    // batchOperation(enable) {
    //     let rows = this.getCheckedRows(), list = [];
    //     if (rows.length === 0) return;
    //     rows.map(item => list.push(item.id))
    //     lib.request({
    //         url: '/ccs/customsBookItem/batchEnable',
    //         method: 'POST',
    //         data: {
    //             ids: list,
    //             enable
    //         },
    //         success: res => {
    //             console.log(res)
    //         }
    //     })
    // }
    // renderRowExpand(row) {
    //     if (row.id % 2 == 0) {
    //         return <table>
    //             <tr><th>ID</th></tr>
    //             <tr><td>{row.id}</td></tr>
    //         </table>
    //     }
    // }
    handleOk(values, modalForm) {
        for (let i in values) {
            if (!values[i]) {
                delete values[i];
            }
        }
        values.beginTime = +new Date(values.applyTime[0]);
        values.endTime = +new Date(values.applyTime[1]);
        if (values.endTime <= new Date()) {
            message.warning("担保结束时间必须大于当前时间");
            return;
        }
        if (values.beginTime >= values.endTime) {
            message.warning("担保结束时间必须大于开始时间");
            return;
        }
        delete values.applyTime;
        lib.request({
            url: this.state.upUrl,
            data: values,
            method: "POST",
            needMask: true,
            success: res => {
                if (res) {
                    this.setState({
                        visible: false,
                    });
                    message.success("新增税金账户成功");
                    this.load(true);
                    modalForm && modalForm.resetFields();
                }
            },
        });
    }

    handleCancel() {
        this.setState({
            visible: false,
        });
    }

    assureSettingHandleOk(values) {
        for (let i in values) {
            if (!values[i]) {
                delete values[i];
            }
        }
        values.rechargeTotal = Number(parseFloat(values.rechargeTotal).toFixed(2));
        values.warningAmount = Number(parseFloat(values.warningAmount).toFixed(2));
        values.beginTime = +new Date(values.applyTime[0]);
        values.endTime = +new Date(values.applyTime[1]);
        if (values.beginTime >= values.endTime) {
            message.warning("结束时间必须大于开始时间");
            return;
        }
        delete values.applyTime;
        let { id } = this.state.editRow;
        values.id = id;
        lib.request({
            url: "/ccs/taxesCompany/assureSetting",
            data: values,
            method: "POST",
            needMask: true,
            success: res => {
                this.setState({
                    assureSettingVisible: false,
                    editRow: {},
                });
                message.success("担保设置成功");
                this.load(true);
            },
        });
    }

    assureSettingHandleCancel() {
        this.setState({
            assureSettingVisible: false,
            editRow: {},
        });
    }

    renderModal() {
        let props = {
            title: this.state.modalTitle,
            onOk: this.handleOk.bind(this),
            onCancel: this.handleCancel.bind(this),
            configList: this.state.configList,
            visible: this.state.visible,
            form: this.props.form,
            formItemLayout: {
                labelCol: { span: 8 },
                wrapperCol: { span: 14 },
            },
            modalStyle: { width: "800px" },
            editRow: {},
        };
        let applyTime = [moment(this.state.editRow?.beginTime), moment(this.state.editRow?.endTime)];
        let assureSettingRow = {
            currentRechargeTotal: this.state.editRow?.rechargeTotal,
            rechargeTotal: this.state.editRow?.rechargeTotal,
            warningAmount: this.state.editRow?.warningAmount,
            applyTime: applyTime,
        };
        let assureSettingConfig = {
            title: this.state.modalTitle,
            onOk: this.assureSettingHandleOk.bind(this),
            onCancel: this.assureSettingHandleCancel.bind(this),
            configList: this.state.editConfigList,
            visible: this.state.assureSettingVisible,
            form: this.props.form,
            formItemLayout: {
                labelCol: { span: 6 },
                wrapperCol: { span: 18 },
            },
            modalStyle: { width: "576px" },
            editRow: assureSettingRow,
        };
        return (
            <React.Fragment>
                <NewModal {...props} />
                <NewModal {...assureSettingConfig} />
            </React.Fragment>
        );
    }
}

export default App;
