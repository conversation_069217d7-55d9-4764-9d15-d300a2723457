import React from "react";
import "./shop-good.less";
import NewModal from "../components/NewModal";
import { ConfigCenter, lib } from "react-single-app";
import { Button, Space } from "antd";

class App extends ConfigCenter {
    constructor(props) {
        super(props);
        this.state.upUrl = "/ccs/pay/payMerchantAccount/upset";
        this.state.configList = [
            {
                type: "INPUT",
                labelName: "商户名称",
                labelKey: "name",
                required: true,
                message: "请输入商户名称",
                maxLength: 50,
            },
            // { type: 'INPUT', labelName: '收款企业', labelKey: 'companyName', required: true, message: '请输入区内企业', maxLength: 32 },
            { type: "TEXTAREA", labelName: "备注", labelKey: "note", maxLength: 100 },
        ];
    }

    componentDidMount() {
        // lib.checkIsLogin()
    }

    renderRightOperation() {
        return (
            <React.Fragment>
                <Button
                    type="primary"
                    onClick={() =>
                        this.setState({
                            visible: true,
                            modalTitle: "新增商户",
                        })
                    }>
                    新增商户
                </Button>
            </React.Fragment>
        );
    }

    handleOk(values, modalForm) {
        if (this.state.editRow) {
            values.id = this.state.editRow.id;
        }
        lib.request({
            url: this.state.upUrl,
            data: values,
            method: "POST",
            needMask: true,
            success: res => {
                if (res) {
                    this.setState({
                        visible: false,
                        editRow: null,
                    });
                    this.load(true);
                    // modalForm.resetFields()
                }
            },
        });
    }

    handleCancel() {
        this.setState({
            visible: false,
            editRow: null,
        });
    }

    renderModal() {
        let { visible, modalTitle, editRow } = this.state;
        const props = {
            visible,
            title: modalTitle,
            editRow,
            form: this.props.form,
            onOk: this.handleOk.bind(this),
            onCancel: this.handleCancel.bind(this),
            configList: this.state.configList,
        };

        return (
            <React.Fragment>
                <NewModal {...props} />
            </React.Fragment>
        );
    }

    myOperation(row) {
        return (
            <React.Fragment>
                <Space>
                    {
                        <a
                            onClick={() => {
                                lib.openPage(
                                    `/collection-channels-manage?pageTitle=收款渠道&merchantId=${row.id}&config_id=1602553707744658&title=收款渠道`,
                                    () => {
                                        this.load(true);
                                    },
                                );
                            }}>
                            收款渠道
                        </a>
                    }
                    {/* {this.getLink(row, "'url=/customs-declaration-certificate&param=merchantId:' + row.id + ',config_id:159893883060565&pageTitle=报关资质&title=报关资质'")} */}
                    {
                        <a
                            onClick={() => {
                                lib.openPage(
                                    `/customs-declaration-certificate?pageTitle=报关资质&merchantId=${row.id}&config_id=1604373374083757&title=报关资质`,
                                    () => {
                                        this.load(true);
                                    },
                                );
                            }}>
                            报关资质
                        </a>
                    }
                    <span
                        className="link"
                        onClick={() =>
                            this.setState({
                                editRow: row,
                                visible: true,
                                modalTitle: "编辑商户",
                            })
                        }>
                        编辑
                    </span>
                </Space>
            </React.Fragment>
        );
    }
}
export default App;
