import React, { useEffect, useRef } from "react";
import { Modal, message } from "antd";
import { DTEditForm, DTEditFormListConfigs, DTEditFormRefs } from "@dt/components";
import { lib } from "react-single-app";
export default ({ open, closeFn, selects }) => {
    const ref = useRef<DTEditFormRefs>();
    const handleOk = () => {
        // closeFn()
        ref.current.form.validateFields().then(res => {
            lib.request({
                url: "/ccs/checklistAuth/manualUpdStatus",
                data: {
                    ids: selects.join(","),
                    status: res.status,
                },
                success(data) {
                    message.success("修改成功");
                    closeFn(true);
                },
            });
        });
    };

    const handleCancel = () => {
        closeFn();
    };
    const configs: DTEditFormListConfigs = [
        {
            type: "RADIO",
            fProps: {
                label: "任务状态",
                name: "status",
                rules: [{ required: true, message: "请选择任务状态" }],
            },
            list: [],
            dataUrl: "/ccs/checklistAuth/listStatus",
        },
    ];

    useEffect(() => {
        if (open) {
            ref.current.resetConfig();
        }
    }, [open]);

    return (
        <>
            <Modal
                title="修改授权状态"
                open={open}
                onOk={handleOk}
                onCancel={handleCancel}
                // footer={null}
            >
                <DTEditForm
                    ref={ref}
                    configs={configs}
                    layout={{
                        mode: "appoint",
                        colNum: 1,
                    }}
                />
            </Modal>
        </>
    );
};
