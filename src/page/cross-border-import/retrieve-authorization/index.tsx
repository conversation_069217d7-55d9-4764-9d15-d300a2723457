import React, { useEffect, useRef, useState } from "react";
import { SearchList } from "@dt/components";
//@ts-ignore
import { HOC, getConfigDataUtils, lib, hooks } from "react-single-app";
import axios from "axios";
import { Button, Space, Tabs, Modal, message } from "antd";
import AddModal from "./add-modal";
import LogModal from "./log-modal";
import UpdateStatusModal from "./update-status-modal";

export default () => {
    const [addOpen, setAddOpen] = React.useState(false);
    const [logOpen, setLogOpen] = useState(false);
    const [statusOpen, setStatusOpen] = useState(false);
    const [row, setRow] = useState(null);
    const [tabsData, settabsData] = useState([]);
    const [buttons] = hooks.useGetAuthButtons({
        systemCode: "CCS_ADMIN",
        pagePath: "/retrieve-authorization",
    });
    const selected = useRef([]);
    const ref = useRef<any>();
    const getConfig = async () => {
        let url = getConfigDataUtils.getDataUrlByDataId(853);
        const res = await axios.get(url);
        return res.data.data;
    };

    const getTabsData = () => {
        const params = ref.current.getSearchData();
        console.log(params);
        lib.request({
            url: "/ccs/checklistAuth/countPagingStatus",
            data: params,
            success(data) {
                console.log(data);
                settabsData(
                    data.map(item => {
                        return {
                            label: item.statusDesc === "" ? "全部" : item.statusDesc + "(" + item.count + ")",
                            key: item.status,
                        };
                    }),
                );
            },
        });
    };

    const pushAuth = () => {
        const selects = selected.current;
        if (selects.length === 0) {
            return message.warning("请勾选数据");
        }
        Modal.confirm({
            title: "确认",
            content: "确认推送授权吗",
            onOk: () => {
                lib.request({
                    url: "/ccs/checklistAuth/push",
                    data: {
                        ids: selects.join(","),
                    },
                    success(data) {
                        message.success("操作成功");
                        ref.current.load();
                    },
                });
            },
        });
    };

    const cancelAuth = () => {
        const selects = selected.current;
        if (selects.length === 0) {
            return message.warning("请勾选数据");
        }
        Modal.confirm({
            title: "确认",
            content: "确认取消授权吗",
            onOk: () => {
                lib.request({
                    url: "/ccs/checklistAuth/cancel",
                    data: {
                        ids: selects.join(","),
                    },
                    success(data) {
                        message.success("操作成功");
                        ref.current.load();
                    },
                });
            },
        });
    };

    const finishAuth = () => {
        const selects = selected.current;
        if (selects.length === 0) {
            return message.warning("请勾选数据");
        }
        Modal.confirm({
            title: "确认",
            content: "确认推送授权吗",
            onOk: () => {
                lib.request({
                    url: "/ccs/checklistAuth/manualFinish",
                    data: {
                        ids: selects.join(","),
                    },
                    success(data) {
                        message.success("操作成功");
                        ref.current.load();
                    },
                });
            },
        });
    };

    const delAuth = row => {
        Modal.confirm({
            title: "确认",
            content: "确认删除吗",
            onOk: () => {
                lib.request({
                    url: "/ccs/checklistAuth/delete",
                    data: {
                        id: row.id,
                    },
                    success(data) {
                        message.success("操作成功");
                        ref.current.load();
                    },
                });
            },
        });
    };

    const showReceipt = row => {
        Modal.confirm({
            title: "海关回执",
            content: row.customsReceipt,
            onOk() {
                console.log("OK");
            },
            onCancel() {
                console.log("Cancel");
            },
        });
    };

    useEffect(() => {
        // getTabsData();
        console.log(ref.current);
        setTimeout(() => {
            getTabsData();
        }, 500);
    }, []);

    return (
        <SearchList
            ref={ref}
            getConfig={getConfig}
            searchConditionConfig={{
                size: "middle",
            }}
            renderOperationTopView={() => (
                <>
                    <Tabs
                        items={tabsData}
                        onChange={e => {
                            ref.current?.changeImmutable({ status: e });
                        }}
                    />
                </>
            )}
            renderLeftOperation={() => {
                return (
                    <Space>
                        <Button
                            onClick={() => {
                                pushAuth();
                            }}>
                            推送授权
                        </Button>
                        <Button
                            onClick={() => {
                                cancelAuth();
                            }}>
                            取消授权
                        </Button>
                        <Button onClick={finishAuth}>手动授权完成</Button>
                        {buttons.includes("update-status") && (
                            <Button
                                onClick={() => {
                                    if (selected.current.length === 0) return message.error("请选择修改的数据");
                                    setStatusOpen(true);
                                }}>
                                修改任务状态
                            </Button>
                        )}
                    </Space>
                );
            }}
            renderRightOperation={() => {
                return (
                    <div>
                        <Button
                            type="primary"
                            onClick={() => {
                                setAddOpen(true);
                            }}>
                            新增
                        </Button>
                    </div>
                );
            }}
            tableCustomFun={{
                customsReceiptFn: record => {
                    if (!record.customsReceipt) return null;
                    return (
                        <a
                            onClick={() => {
                                showReceipt(record);
                            }}>
                            查看
                        </a>
                    );
                },
                endorsementRealOrderNosFn: record => {
                    return (
                        <>
                            {record.endorsementRealOrderNoList?.map(item => (
                                <p style={{ lineHeight: "24px", marginBottom: 0 }} key={item}>
                                    {item}
                                </p>
                            ))}
                        </>
                    );
                },
                operationFn: record => {
                    return (
                        <Space>
                            <Button
                                type="link"
                                onClick={() => {
                                    setLogOpen(true);
                                    setRow(record);
                                }}>
                                日志
                            </Button>
                            {["CREATED", "AUTHORIZED_FAIL"].includes(record.status) && (
                                <Button type="link" onClick={() => delAuth(record)}>
                                    删除
                                </Button>
                            )}
                        </Space>
                    );
                },
            }}
            onTableSelected={(selects, selectRows) => {
                console.log(selects, selectRows);
                selected.current = selects;
            }}
            renderModal={() => {
                return (
                    <>
                        <AddModal
                            open={addOpen}
                            closeFn={load => {
                                setAddOpen(false);

                                if (load) {
                                    ref.current.load();
                                }
                            }}
                        />
                        <LogModal
                            open={logOpen}
                            closeFn={() => {
                                setLogOpen(false);
                                setRow(null);
                            }}
                            row={row}
                        />
                        <UpdateStatusModal
                            open={statusOpen}
                            closeFn={load => {
                                setStatusOpen(false);
                                if (load) {
                                    ref.current.resetSelected();
                                    ref.current.load();
                                }
                            }}
                            selects={selected.current}
                        />
                    </>
                );
            }}
        />
    );
};
