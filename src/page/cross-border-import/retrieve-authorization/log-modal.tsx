import React, { useEffect, useState } from "react";
import { Modal, Table } from "antd";
import { lib } from "react-single-app";

export default ({ open, closeFn, row }) => {
    const [dataSource, setDataSource] = useState([]);
    const getData = () => {
        lib.request({
            url: "/ccs/checklistAuth/viewLog",
            data: {
                id: row.id,
            },
            success(data) {
                setDataSource(data);
            },
        });
    };

    useEffect(() => {
        if (open) {
            getData();
        }
    }, [open]);

    return (
        <Modal
            width={800}
            open={open}
            onOk={() => {
                closeFn();
            }}
            onCancel={() => {
                closeFn();
            }}>
            <Table
                columns={[
                    {
                        title: "任务状态",
                        dataIndex: "statusDesc",
                    },
                    {
                        title: "日志描述",
                        dataIndex: "logDesc",
                    },
                    {
                        title: "发生时间",
                        dataIndex: "createTime",
                    },
                    {
                        title: "操作人",
                        dataIndex: "operator",
                    },
                    {
                        title: "操作",
                        render(value, record, index) {
                            if (!record.logDetail) return null;
                            return (
                                <a
                                    onClick={() => {
                                        Modal.confirm({
                                            title: "报文",
                                            content: record.logDetail,
                                        });
                                    }}>
                                    查看报文
                                </a>
                            );
                        },
                    },
                ]}
                dataSource={dataSource}></Table>
        </Modal>
    );
};
