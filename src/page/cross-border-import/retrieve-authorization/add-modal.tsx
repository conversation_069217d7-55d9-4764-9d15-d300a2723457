import React, { useEffect, useRef } from "react";
import { Modal, message } from "antd";
import { DTEditForm, DTEditFormListConfigs, DTEditFormRefs } from "@dt/components";
import { lib } from "react-single-app";
export default ({ open, closeFn }) => {
    const ref = useRef<DTEditFormRefs>();
    const handleOk = () => {
        // closeFn()
        ref.current.form.validateFields().then(res => {
            lib.request({
                url: "/ccs/checklistAuth/create",
                data: {
                    ...res,
                    authCompanyId: +res.authCompanyId,
                    beAuthCompanyId: +res.beAuthCompanyId,
                    endorsementRealOrderNoList: res.endorsementRealOrderNoList,
                },
                needMask: true,
                success(data) {
                    message.success("创建成功");
                    closeFn(true);
                },
            });
        });
    };

    const handleCancel = () => {
        closeFn();
    };
    const configs: DTEditFormListConfigs = [
        {
            type: "SELECT",
            fProps: {
                label: "授权企业",
                name: "authCompanyId",
                rules: [{ required: true, message: "请选择授权企业" }],
            },
            list: [],
            dataUrl: "/ccs/company/listWithSBQYAll",
        },
        {
            type: "SELECT",
            fProps: {
                label: "业务类型",
                name: "businessType",
                rules: [{ required: true, message: "请选择业务类型" }],
            },
            list: [],
            dataUrl: "/ccs/endorsement/list-bussiness-types",
        },
        {
            type: "SELECT",
            fProps: {
                label: "核注清单编号",
                name: "endorsementRealOrderNoList",
                rules: [{ required: true, message: "请选择核注清单编号" }],
            },
            cProps: {
                mode: "multiple",
            },
            list: [],
            dataUrl: "/ccs/endorsement/effective/book/auth/listForChecklistByTypeAndCompany",
            relys: [
                {
                    from: "businessType",
                    reqkey: "businessType",
                },
                {
                    from: "authCompanyId",
                    reqkey: "companyId",
                },
            ],
            resDataHandleFn(response) {
                console.log("response:", response);
                return response.map((item: any) => {
                    return {
                        id: item.realOrderNo,
                        name: item.realOrderNo,
                    };
                });
            },
        },
        {
            type: "SELECT",
            fProps: {
                label: "被授权企业",
                name: "beAuthCompanyId",
                rules: [{ required: true, message: "请选择被授权企业" }],
            },
            list: [],
            dataUrl: "/ccs/company/listWithSBQYAll",
        },
    ];

    useEffect(() => {
        if (open) {
            ref.current.resetConfig();
            ref.current.form.resetFields();
        }
    }, [open]);

    return (
        <>
            <Modal title="新增授权任务" open={open} onOk={handleOk} onCancel={handleCancel}>
                <DTEditForm
                    ref={ref}
                    configs={configs}
                    layout={{
                        mode: "appoint",
                        colNum: 1,
                    }}
                />
            </Modal>
        </>
    );
};
