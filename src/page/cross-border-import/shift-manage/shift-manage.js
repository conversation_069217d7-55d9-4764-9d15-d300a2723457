import React, { useState, useEffect, useRef } from "react";
import { lib, SearchList, ConfigFormCenter, getConfigDataUtils } from "react-single-app";
import axios from "axios";
import { Space, Button, message, Modal, Tabs, Table, Typography, ConfigProvider, Empty } from "antd";
import "./index.less";
import { syncShiftData, createStoreOutOrderData, columns } from "./view-config";
const { TabPane } = Tabs;

export default class ShiftManage extends SearchList {
    constructor(props) {
        super(props);
        this.state.search = {};
        this.state.status = "0"; //0-待生成 2-部分出库 1-全部出库
        this.state.visible = false;
        this.state.waitExportCount = 0;
        this.state.partExportCount = 0;
    }

    componentDidMount() {
        this.changeImmutable({ status: this.state.status });
    }

    getConfig() {
        // let url = this.state.status === "2" ? getConfigDataUtils.getDataUrlByDataId(389) : getConfigDataUtils.getDataUrlByDataId(388)
        let url = getConfigDataUtils.getDataUrlByDataId(389);
        return axios.get(url).then(res => res.data.data);
    }

    syncShift() {
        this.setState({
            showSyncShiftOderModal: true,
        });
    }

    batchCreateStoreOutOrder() {
        let list = this.state.selectedRows;
        if (list?.length === 0) {
            message.warning("请选择数据");
        } else {
            const handoverSn = list.reduce((prev, curr) => [...prev, curr.handoverSn], []).join(",");
            const expressCode = list.reduce((prev, curr) => [...prev, curr.wmsExpressCode], []);
            let set = new Set(expressCode);
            if (set.size > 1) {
                message.error("无法生成出库单：非同一快递公司");
            } else {
                this.setState({
                    modalData: {
                        handoverSn: handoverSn,
                        expressCode: list[0].wmsExpressCode,
                        express_name: list[0].wmsExpressName,
                    },
                    showCreateStoreOutOrderModal: true,
                });
            }
        }
    }

    load(_, toTop) {
        let { pagination, search, config, sorter, status } = this.state;
        var data = {};
        this.setState({ _loading: true });
        Object.assign(data, pagination, search, sorter, { status: status });
        lib.request({
            url: config.page.api,
            data: data,
            success: data => {
                this.setState({
                    pagination: data.page,
                    dataList: data.dataList || [],
                    _loading: false,
                });
                toTop && (document.querySelector(".table-panel .ant-table-body").scrollTop = 0);
                setTimeout(this.resetSize, 100);
            },
            fail: () => {
                this.setState({
                    _loading: false,
                });
            },
        });
        const newData = { ...data };
        delete newData.status;
        lib.request({
            url: "/ccs/handoverOrder/getHandoverStatusCount",
            data: newData,
            success: data => {
                this.setState({
                    waitExportCount: data.waitExportCount,
                    partExportCount: data.partExportCount,
                });
            },
            fail: () => {},
        });
    }

    createStoreOutOrder(row) {
        this.setState({
            modalData: {
                handoverSn: row.handoverSn,
                expressCode: row.wmsExpressCode,
                express_name: row.wmsExpressName,
            },
            showCreateStoreOutOrderModal: true,
        });
    }

    renderLeftOperation() {
        return (
            <Space>
                {["0", "2", "1"].includes(this.state.status) && (
                    <Button type={"primary"} onClick={() => this.syncShift()}>
                        同步交接单
                    </Button>
                )}
                {["0", "2"].includes(this.state.status) && (
                    <Button type={"primary"} onClick={() => this.batchCreateStoreOutOrder()}>
                        批量生成出库单
                    </Button>
                )}
            </Space>
        );
    }

    renderRightOperation() {
        return (
            <>
                <Button
                    type="primary"
                    onClick={() => {
                        this.packageDataExport();
                    }}>
                    包裹数据导出
                </Button>
            </>
        );
    }

    packageDataExport() {
        lib.request({
            url: "/ccs/handoverOrder/packageExcelExport",
            data: this.state.search,
            needMask: true,
            success: res => {
                lib.openPage("/download-center?page_title=下载中心");
            },
            fail: () => {},
        });
    }

    showUnCountFn(row) {
        return (
            <Button
                type="link"
                onClick={() => {
                    this.setState({ visible: true, handoverSn: row.handoverSn, associateOutboundStatus: 0 });
                }}>
                {row.notAssociateCount}
            </Button>
        );
    }
    showCountFn(row) {
        return (
            <Button
                type="link"
                onClick={() => {
                    this.setState({ visible: true, handoverSn: row.handoverSn, associateOutboundStatus: 1 });
                }}>
                {row.associateCount}
            </Button>
        );
    }

    renderModal() {
        let { showSyncShiftOderModal, showCreateStoreOutOrderModal } = this.state;
        return (
            <React.Fragment>
                <SyncShiftOrderModal
                    showSyncShiftOderModal={showSyncShiftOderModal}
                    closeModal={success => {
                        this.setState({
                            showSyncShiftOderModal: false,
                        });
                        if (success) {
                            this.load();
                        }
                    }}
                />
                <CreateStoreOutOrderModal
                    showCreateStoreOutOrderModal={showCreateStoreOutOrderModal}
                    modalData={this.state.modalData}
                    closeModal={success => {
                        if (success) {
                            this.load();
                        }
                        this.setState({
                            modalData: null,
                            showCreateStoreOutOrderModal: false,
                        });
                    }}
                />
                <UnConnextModal
                    visible={this.state.visible}
                    handoverSn={this.state.handoverSn}
                    status={this.state.status}
                    associateOutboundStatus={this.state.associateOutboundStatus}
                    closeModal={success => {
                        if (success) {
                            this.load();
                        }
                        this.setState({
                            modalData: null,
                            visible: false,
                        });
                    }}
                />
            </React.Fragment>
        );
    }

    renderOperationTopView() {
        return (
            <Tabs
                defaultActiveKey={"0"}
                onChange={key => {
                    this.setState(
                        {
                            status: key,
                        },
                        () => {
                            this.loadConfig();
                        },
                    );
                }}>
                <TabPane tab={`待生成出库(${this.state.waitExportCount})`} key={"0"} />
                <TabPane tab={`部分出库(${this.state.partExportCount})`} key={"2"} />
                <TabPane tab={"全部出库"} key={"1"} />
            </Tabs>
        );
    }

    setConfigSuccess() {
        this.changeImmutable({ status: this.state.status });
    }

    reloadData(row) {
        lib.request({
            url: "/ccs/handoverOrder/refresh",
            data: { id: row.id },
            success: () => {
                message.success("刷新成功");
                this.load();
            },
            fail: () => {},
        });
    }

    myOperation(row) {
        return (
            <Space>
                <span
                    className="link"
                    onClick={() => lib.openPage(`/shift-detail?page_title=交接单详情&handoverSn=${row.handoverSn}`)}>
                    查看
                </span>
                {["0", "2"].includes(this.state.status) && (
                    <span className="link" onClick={() => this.createStoreOutOrder(row)}>
                        生成出库单
                    </span>
                )}
                <span className="link" onClick={() => this.reloadData(row)}>
                    刷新出库
                </span>
            </Space>
        );
    }
}

//同步交接单modal
function SyncShiftOrderModal({ showSyncShiftOderModal, closeModal }) {
    const ref = useRef();
    const [previewData, setPreviewData] = useState([]);
    const [showDetail, setShowDetail] = useState(false);
    const [storeHouseName, setStoreHouseName] = useState();
    const handleCancel = () => {
        close();
    };
    const close = success => {
        setShowDetail(false);
        setPreviewData([]);
        closeModal(success);
    };
    const handleOk = () => {
        //确认明细关闭弹框
        if (showDetail) {
            close(true);
        } else {
            ref.current.submitForm();
        }
    };
    const onConfigLoadSuccess = config => {
        config.baseInfo.children.map(item => {
            if (item.type === "single-select" && !item.fromParam) {
                ref.current.initSelect(item);
            }
        });
    };
    const beforeSubmit = values => {
        let timeArr = values.getTime.map(it => it.valueOf());
        delete values.getTime;
        values.staTime = timeArr[0];
        values.endTime = timeArr[1];
        return values;
    };
    const onSubmitSuccess = data => {
        setShowDetail(true);
        setPreviewData(data);
    };
    const onSinglesSelectChange = desc => {
        switch (desc.name) {
            case "storeHouseSn":
                setStoreHouseName(desc.value.children);
                break;
            default:
                break;
        }
    };
    const columns = [
        {
            title: "交接单号",
            dataIndex: "handoverSn",
        },
        {
            title: "快递名称",
            dataIndex: "wmsExpressName",
        },
    ];
    return (
        <Modal
            destroyOnClose
            cancelButtonProps={showDetail && { style: { display: "none" } }}
            onCancel={handleCancel}
            onOk={handleOk}
            title={"同步交接单"}
            width={showDetail ? 600 : 546}
            open={showSyncShiftOderModal}>
            {!showDetail && (
                <ConfigFormCenter
                    ref={ref}
                    onSinglesSelectChange={onSinglesSelectChange}
                    confData={syncShiftData}
                    onConfigLoadSuccess={onConfigLoadSuccess}
                    submitUrl={"/ccs/handoverOrder/synHandoverOrder"}
                    onSubmitSuccess={onSubmitSuccess}
                    beforeSubmit={beforeSubmit}
                />
            )}
            {showDetail && (
                <>
                    <ConfigProvider
                        renderEmpty={() => {
                            return <Empty description={"当前无交接单"} image={Empty.PRESENTED_IMAGE_SIMPLE} />;
                        }}>
                        <Typography.Title level={5}>{storeHouseName}</Typography.Title>
                        <Table dataSource={previewData} columns={columns} scroll={{ y: 400 }} rowKey={"id"} />
                    </ConfigProvider>
                </>
            )}
        </Modal>
    );
}

//生成出库单modal
function CreateStoreOutOrderModal({ modalData, showCreateStoreOutOrderModal, closeModal }) {
    const ref = useRef();
    const [modalTitle, setModalTitle] = useState("生产出库单");
    const [previewData, setPreviewData] = useState();
    const [previewSubmitData, setPreviewSubmitData] = useState(); //获取预览数据提交字段，后边生成出库单使用
    const [showDetail, setShowDetail] = useState(false);

    const handleCancel = () => {
        close();
        ref.current.setMergeDetail({});
    };
    const close = success => {
        setModalTitle("生产出库单");
        setShowDetail(false);
        setPreviewData(null);
        setPreviewSubmitData(null);
        closeModal(success);
    };
    const handleOk = () => {
        if (showDetail) {
            lib.request({
                url: "/ccs/handoverOrder/saveExportOrder",
                data: { ...previewSubmitData, ...{ exportItemWritingReport: previewData } },
                needMask: true,
                success: () => {
                    close(true);
                },
            });
        } else {
            ref.current.submitForm();
        }
    };
    const onConfigLoadSuccess = config => {
        ref.current.setMergeDetail({ express_name: modalData.express_name });
        config.baseInfo.children.map(item => {
            if (item.type === "single-select" && !item.fromParam) {
                ref.current.initSelect(item);
            }
        });
    };

    const onSubmitSuccess = data => {
        setModalTitle(`提交预览（修改数量${data.totalCount}）`);
        setShowDetail(true);
        setPreviewData(data);
    };
    const beforeSubmit = values => {
        values.handoverSn = modalData.handoverSn;
        values.expressCode = modalData.expressCode;
        setPreviewSubmitData(values);
        return values;
    };
    const columns1 = [
        {
            title: "序号",
            dataIndex: "idx",
            width: 80,
        },
        {
            title: "运单号",
            dataIndex: "mailNo",
        },
        {
            title: "交接单号",
            dataIndex: "handoverSn",
        },
    ];
    const columns2 = [
        {
            title: "序号",
            dataIndex: "idx",
            width: 80,
        },
        {
            title: "运单号",
            dataIndex: "mailNo",
        },
        {
            title: "交接单号",
            dataIndex: "handoverSn",
        },
        {
            title: "错误信息",
            dataIndex: "errorMsg",
        },
    ];
    const onSinglesSelectChange = (fieldsValue, changConfigCount) => {
        console.log("fieldsValue.declareCompanyId:", fieldsValue);
        if (fieldsValue.name === "declareCompanyId") {
            ref.current.config.baseInfo.children.map(item => {
                if (item.name === "accountBookId") {
                    ref.current.initSelect(item, { areaCompanyId: fieldsValue.value.value });
                }
            });
            ref.current.setMergeDetail({
                accountBookId: null,
            });
        }
    };

    return (
        <Modal
            destroyOnClose
            onCancel={handleCancel}
            onOk={handleOk}
            width={showDetail ? 600 : 416}
            title={modalTitle}
            open={showCreateStoreOutOrderModal}>
            {!showDetail && (
                <ConfigFormCenter
                    ref={ref}
                    confData={createStoreOutOrderData}
                    onConfigLoadSuccess={onConfigLoadSuccess}
                    submitUrl={"/ccs/handoverOrder/outboundOrderPreview"}
                    beforeSubmit={beforeSubmit}
                    onSubmitSuccess={onSubmitSuccess}
                    onSinglesSelectChange={onSinglesSelectChange}
                />
            )}
            {showDetail && (
                <>
                    <Tabs defaultActiveKey="0">
                        <TabPane tab={`校验成功(${previewData?.successCount})`} key="0">
                            <Table
                                dataSource={previewData?.successRecordList || []}
                                columns={columns1}
                                scroll={{ y: 400 }}
                                rowKey="idx"
                            />
                        </TabPane>
                        <TabPane tab={`校验失败(${previewData?.failCount})`} key="1">
                            <Table
                                dataSource={previewData?.failRecordList || []}
                                columns={columns2}
                                scroll={{ y: 400 }}
                                rowKey="idx"
                            />
                        </TabPane>
                    </Tabs>
                </>
            )}
        </Modal>
    );
}

//未关联出库包裹数
function UnConnextModal({ visible, closeModal, handoverSn, associateOutboundStatus, status }) {
    const [data, setData] = useState([]);
    const [total, setTotal] = useState(0);
    useEffect(() => {
        if (handoverSn && visible) {
            getData();
        }
    }, [handoverSn, associateOutboundStatus, status, visible]);

    const handleCancel = () => {
        closeModal && closeModal(false);
    };

    const getData = (page = 1, pageSize = 10) => {
        lib.request({
            url: "/ccs/handoverOrderDetail/paging",
            data: {
                handoverSn: handoverSn,
                associateOutboundStatus: associateOutboundStatus,
                currentPage: page,
                pageSize: pageSize,
            },
            success: res => {
                setData(res.dataList);
                setTotal(res.page.totalCount);
            },
        });
    };

    return (
        <Modal
            destroyOnClose
            okButtonProps={{ style: { display: "none" } }}
            onCancel={handleCancel}
            title={associateOutboundStatus === 0 ? "未关联出库包裹数" : "已关联出库包裹数"}
            width={600}
            open={visible}>
            <Table
                columns={columns}
                dataSource={data}
                pagination={{
                    total: total,
                    onChange: (page, pageSize) => {
                        getData(page, pageSize);
                    },
                }}
            />
        </Modal>
    );
}
