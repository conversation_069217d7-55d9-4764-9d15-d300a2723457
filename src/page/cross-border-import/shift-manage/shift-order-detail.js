import React from "react";
import { SearchList, lib, getConfigDataUtils } from "react-single-app";
import axios from "axios";
import { Space, Typography } from "antd";
import "./index.less";

export default class ShiftOrderDetail extends SearchList {
    constructor(props) {
        super(props);
    }
    renderLeftOperation() {
        return (
            <Space>
                <Typography.Title level={5}>交接单号：{lib.getParam("handoverSn")}</Typography.Title>
            </Space>
        );
    }
    getConfig() {
        return axios.get(getConfigDataUtils.getDataUrlByDataId(390)).then(res => res.data.data);
    }
}
