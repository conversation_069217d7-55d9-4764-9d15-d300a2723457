import React from "react";
import moment from "moment";

export const syncShiftData = () => {
    return {
        baseInfo: {
            children: [
                {
                    label: "实体仓",
                    labelCol: { span: 5 },
                    wrapperCol: { span: 19 },
                    editEnable: true,
                    type: "single-select",
                    name: "storeHouseSn",
                    from: "/ccs/handoverOrder/getWarehouseInfo",
                    rules: [
                        {
                            required: true,
                            message: "请选择实体仓!",
                        },
                    ],
                },
                {
                    label: "获取时间",
                    labelCol: { span: 5 },
                    wrapperCol: { span: 19 },
                    editEnable: false,
                    name: "getTime",
                    type: "dateInput",
                    customConfig: {
                        showTime: true,
                        isRange: true,
                        defaultValue: [moment().startOf("day"), moment().endOf("day")],
                    },
                    rules: [
                        {
                            required: true,
                            message: "请选择获取时间!",
                        },
                    ],
                },
                {
                    label: "交接单号",
                    labelCol: { span: 5 },
                    wrapperCol: { span: 19 },
                    name: "handoverSn",
                    editEnable: false,
                    type: "textInput",
                },
            ],
            label: "",
            name: "baseInfo",
            isGroup: true,
            className: "sync-shift-baseInfo",
        },
    };
};
export const createStoreOutOrderData = () => {
    return {
        baseInfo: {
            children: [
                {
                    label: "实体仓名称",
                    editEnable: true,
                    wrapperCol: { span: 16 },
                    type: "single-select",
                    name: "entityWarehouseCode",
                    from: "/ccs/entityWarehouse/listWarehouseSn",
                    customConfig: {
                        showSearch: true,
                    },
                    rules: [
                        {
                            required: true,
                            message: "请选择实体仓名称!",
                        },
                    ],
                },
                // {
                //     label: "清关企业",
                //     editEnable: true,
                //     wrapperCol: { span: 16 },
                //     type: "single-select",
                //     name: "declareCompanyId",
                //     from: "/ccs/company/listWithSBQYAll",
                //     customConfig: {
                //         showSearch: true,
                //     },
                //     rules: [
                //         {
                //             required: true,
                //             message: "请选择清关企业!",
                //         },
                //     ],
                // },
                // // fromParam
                // {
                //     label: "账册编号",
                //     editEnable: false,
                //     wrapperCol: { span: 16 },
                //     type: "single-select",
                //     name: "accountBookId",
                //     from: "/ccs/customsBook/listBookByAreaCompany",
                //     // fromParam: ,
                //     rules: [
                //         {
                //             required: true,
                //             message: "请选择账册编号!",
                //         },
                //     ],
                // },
                {
                    label: "快递公司",
                    editEnable: false,
                    type: "text",
                    name: "express_name",
                },
            ],
            label: "",
            name: "baseInfo",
            isGroup: true,
            className: "baseInfo",
        },
    };
};

export const columns = [
    {
        title: "运单号",
        dataIndex: "wayBillSn",
        key: "wayBillSn",
    },
    {
        title: "快递公司",
        dataIndex: "expressName",
        key: "expressName",
    },
];
