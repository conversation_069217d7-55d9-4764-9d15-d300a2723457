import React from "react";
export let ItemConfig = [
    {
        title: "料号",
        dataIndex: "productId",
        width: 50,
    },
    {
        title: "项号",
        dataIndex: "itemNo",
        width: 50,
    },
    {
        title: "商品编码",
        dataIndex: "goodsNo",
        width: 50,
    },
    {
        title: "商品名称",
        dataIndex: "goodsName",
        width: 50,
    },
];

export const searchConfig = [
    {
        title: "商品名称",
        dataIndex: "goodsName",
        width: 50,
    },
    {
        title: "料号",
        dataIndex: "productId",
        width: 50,
    },
    {
        title: "项号",
        dataIndex: "itemNo",
        width: 50,
    },
    {
        title: "商品编码",
        dataIndex: "goodsNo",
        width: 50,
    },
];

export const editHeaderConfig = [
    {
        title: "料号",
        dataIndex: "productId",
        width: 20,
    },
    {
        title: "项号",
        dataIndex: "itemNo",
        width: 20,
    },
    {
        title: "商品编码",
        dataIndex: "goodsNo",
        width: 20,
    },
    {
        title: "商品名称",
        dataIndex: "goodsName",
        width: 20,
    },
    {
        title: "数量",
        dataIndex: "inOutAmount",
        width: 60,
        editable: true,
        rules: [
            { required: true, message: "数量为必填" },
            {
                validator(_, value) {
                    if (!value) return Promise.resolve();
                    if (!/\d/g.test(value)) {
                        return Promise.reject("请填入整数");
                    }
                    return Promise.resolve();
                },
            },
        ],
    },
    {
        title: "库位",
        dataIndex: "storageLocation",
        width: 60,
        editable: true,
    },
    {
        title: "批次号",
        dataIndex: "batch",
        width: 60,
        editable: true,
    },
];

export const addAccessRecord = (renderText: React.FC, fn: (value: string) => void) => [
    {
        labelName: "",
        type: "TEXT",
        render: renderText,
        labelCol: { span: 0 },
        wrapperCol: { span: 20 },
    },
    {
        labelName: "出入库类型",
        labelKey: "businessType",
        type: "SELECT",
        required: true,
        list: [],
        ccs: "/ccs/fbStockInOut/listBusinessType",
        onChange: value => {
            fn(value);
        },
    },
    {
        labelName: "账册编号",
        labelKey: "customsBookId",
        type: "SELECT",
        required: true,
        list: [],
        ccs: "/ccs/customsBook/listBookNoFb",
    },
    {
        labelName: "出入库时间",
        labelKey: "inOutTime",
        type: "DATE",
        required: true,
        mode: "date",
    },
    {
        labelName: "核放单编号",
        labelKey: "fbChecklistId",
        type: "SELECT",
        list: [],
        // ccs: '/ccs/fbChecklist/listFinishJobFormIds',
        // fromData: true,
    },
];
