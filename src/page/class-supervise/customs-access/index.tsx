import React, { useEffect, useRef, useState } from "react";
import { SearchList } from "@dt/components";
import axios from "axios";
//@ts-ignore
import { DDYObject, getConfigDataUtils, lib, hooks } from "react-single-app";
import { <PERSON><PERSON>, <PERSON><PERSON>ip, message, Alert, Modal } from "antd";
import { QuestionCircleOutlined, CopyOutlined } from "@ant-design/icons";
import { request } from "@dt/networks";
import NewModal from "@/components/NewModal";
import NewTable from "../components/new-table";
import { addAccessRecord, ItemConfig, searchConfig } from "./config";
import EditTableInfo from "../components/edit-table";
import { CustomsAccessDataItem, CustomsAccessDataItemStatus } from "./type";
import copy from "copy-to-clipboard";
import UpdateStatusModal from "@/page/customs-clearance-system/components/update-status-modal";
export default () => {
    const [itemNoOpen, setItemNoOpen] = useState(false);
    const [itemType, setItemType] = useState<0 | 1>(0); // 0: 新增 1: 编辑
    const [addOpen, setAddOpen] = useState(false);
    const SearchListRef = useRef(null);
    const selectedids = useRef([]);
    const [selecteds, setSelecteds] = useState([]);
    const newModalRef = useRef(null);
    const [editrow, setEditrow] = useState<CustomsAccessDataItem>({} as CustomsAccessDataItem);
    const [systemBol, setSystemBol] = useState(false);
    const [systemContent, setSystemContent] = useState("");
    const [buttons] = hooks.useGetAuthButtons({
        systemCode: "CCS_ADMIN",
        pagePath: "/customs-access",
    });
    const [addConfigs, setAddConfigs] = useState(
        addAccessRecord(
            () => {
                return (
                    <Alert
                        message="特殊关区选择非保核放单，系统会按照核放单表体数拆分成多个出入库记录（金义综保）"
                        type="info"
                    />
                );
            },
            (value: string) => {
                console.log(value);
                request.request({
                    url: "/ccs/fbChecklist/listFinishJobFormIds",
                    data: {
                        businessType: value,
                    },
                    success: res => {
                        addConfigs[4].list = res;
                        newModalRef.current.form.setFieldsValue({ fbChecklistId: null });
                        setAddConfigs([...addConfigs]);
                    },
                });
            },
        ),
    );

    const getConfig = async () => {
        let url = getConfigDataUtils.getDataUrlByDataId(683);
        const res = await axios.get(url);
        return res.data.data;
    };

    const pushAccessRecord = () => {
        if (selectedids.current.length == 0) {
            message.warn("请选择数据");
        } else {
            request.request({
                url: "/ccs/fbStockInOut/declareStockInOut",
                data: {
                    idList: selectedids.current,
                },
                success: res => {
                    message.success("推送成功");
                    SearchListRef.current.load();
                },
            });
        }
    };

    const delAccessRecord = () => {
        if (selectedids.current.length == 0) {
            message.warn("请选择数据");
        } else {
            request.request({
                url: "/ccs/fbStockInOut/deleteDeclareStockInOut",
                data: {
                    idList: selectedids.current,
                },
                success: res => {
                    message.success("推送删单申请成功");
                    SearchListRef.current.load();
                },
            });
        }
    };

    const itemNoOk = (data: DDYObject[], arr: DDYObject[], obj: DDYObject) => {
        console.log("obj:", obj);
        const item = data[0];
        const params = {
            productId: obj.productId,
            itemNo: obj.itemNo,
            goodsNo: obj.goodsNo,
            goodsName: obj.goodsName,
            inOutAmount: item.inOutAmount,
            storageLocation: item.storageLocation,
            batch: item.batch,
            action: obj.action,
            id: editrow.id,
            mergeRelationId: itemType === 0 ? obj.id : null,
        };
        request.request({
            url: itemType === 0 ? "/ccs/fbStockInOut/addProductId" : "/ccs/fbStockInOut/editProductId",
            data: params,
            success: () => {
                message.success(itemType === 0 ? "添加成功" : "编辑成功");
                setItemNoOpen(false);
                SearchListRef.current.load();
                setEditrow(null);
            },
            fail: () => {},
        });
    };

    const addOK = (data: DDYObject) => {
        data.inOutTime = data.inOutTime?.valueOf();
        request.request({
            url: "/ccs/fbStockInOut/create",
            data: { ...data },
            success: () => {
                message.success("新增成功");
                setAddOpen(false);
                setEditrow(null);
                SearchListRef.current.load();
            },
            fail: () => {},
        });
    };

    const toVoid = (row: DDYObject) => {
        request.request({
            url: "/ccs/fbStockInOut/discard",
            data: { id: row.id },
            success: () => {
                message.success("作废成功");
                SearchListRef.current.load();
                setEditrow(null);
            },
            fail: () => {},
        });
    };

    const getDatas = () => {
        const arr = [];
        addConfigs.forEach((item, index) => {
            if (item.ccs) {
                arr.push(
                    new Promise((resolve, reject) => {
                        request.request({
                            url: item.ccs,
                            data: {},
                            success: data => {
                                resolve({
                                    data,
                                    index,
                                });
                            },
                            fail: () => {
                                reject();
                            },
                        });
                    }),
                );
            }
        });

        Promise.all(arr)
            .then(res => {
                res.map(item => {
                    addConfigs[item.index].list = item.data;
                });
                setAddConfigs(addConfigs);
            })
            .catch(err => {});
    };

    const orderNoFn = (row: any) => {
        return <Button>{row.inOutSeq}</Button>;
    };

    useEffect(() => {
        getDatas();
    }, []);

    return (
        <>
            <SearchList
                ref={SearchListRef}
                searchConditionConfig={{
                    size: "middle",
                }}
                headerMode={"sticky"}
                getConfig={getConfig}
                renderLeftOperation={() => {
                    return (
                        <>
                            <Button
                                type="primary"
                                onClick={() => {
                                    pushAccessRecord();
                                }}>
                                推送出入库记录
                            </Button>
                            <Button
                                onClick={() => {
                                    delAccessRecord();
                                }}>
                                删除出入库记录
                            </Button>
                            {buttons?.includes("UPDATE-INOROUT-STATU") && (
                                <UpdateStatusModal
                                    selected={selecteds}
                                    success={() => {
                                        SearchListRef.current.load();
                                    }}
                                    type={"inOrOut"}
                                />
                            )}
                        </>
                    );
                }}
                tableCustomFun={{
                    operationFn: (row: CustomsAccessDataItem, index: number) => {
                        return (
                            <div>
                                {row.status === CustomsAccessDataItemStatus.UNUSUAL && (
                                    <Button
                                        type="link"
                                        onClick={() => {
                                            setItemNoOpen(true);
                                            setEditrow(row);
                                            setItemType(1);
                                        }}>
                                        编辑
                                    </Button>
                                )}

                                {[CustomsAccessDataItemStatus.CREATED, CustomsAccessDataItemStatus.UNUSUAL].includes(
                                    row.status,
                                ) && (
                                    <Button
                                        type="link"
                                        onClick={() => {
                                            setEditrow(row);
                                            toVoid(row);
                                        }}>
                                        作废
                                    </Button>
                                )}
                            </div>
                        );
                    },
                    handleFn: (row: CustomsAccessDataItem, index: number) => {
                        return (
                            <>
                                {row.processResult}
                                {row.processCallback && (
                                    <Tooltip title={row.processCallback}>
                                        <QuestionCircleOutlined />
                                    </Tooltip>
                                )}
                            </>
                        );
                    },
                    systemFn: (row: CustomsAccessDataItem, index: number) => {
                        return (
                            <>
                                {row.declareCallback && (
                                    <Button
                                        type="link"
                                        onClick={() => {
                                            setSystemBol(true);
                                            setSystemContent(row.declareCallback);
                                        }}>
                                        查看
                                    </Button>
                                )}
                            </>
                        );
                    },
                    fbChecklistNoFn: (row: CustomsAccessDataItem, index: number) => {
                        return (
                            <>
                                {row.fbChecklistId && (
                                    <Button
                                        type="link"
                                        onClick={() => {
                                            lib.openPage(
                                                `/non-guaranteed-detail?page_title=非保核放单详情&id=${row.fbChecklistId}`,
                                            );
                                        }}>
                                        {row.fbChecklistNo}
                                    </Button>
                                )}
                            </>
                        );
                    },
                    orderNoFn: (row: any) => {
                        return (
                            <>
                                <Button
                                    size="small"
                                    type="link"
                                    onClick={() => {
                                        lib.openPage(`/customs-access-detail?page_title=海关出入库详情&id=${row.id}`);
                                    }}>
                                    {row.inOutSeq}
                                </Button>
                                <CopyOutlined
                                    onClick={() => {
                                        if (row.inOutSeq) {
                                            copy(row.inOutSeq);
                                            message.success("复制成功");
                                        } else {
                                            message.warning("没有可复制内容");
                                        }
                                    }}
                                />
                            </>
                        );
                    },
                }}
                renderRightOperation={() => {
                    return (
                        <>
                            <Button
                                type="primary"
                                onClick={() => {
                                    setAddOpen(true);
                                }}>
                                新建
                            </Button>
                        </>
                    );
                }}
                onTableSelected={(row: any[], rowDatas) => {
                    selectedids.current = row;
                    // setEditrow(row);
                    setSelecteds([...rowDatas]);
                }}
                renderModal={() => {
                    return (
                        <>
                            <NewModal
                                {...{
                                    visible: addOpen,
                                    configList: addConfigs,
                                    title: "新增出入库记录",
                                    onOk: addOK,
                                    ref: newModalRef,
                                    editRow: {},
                                    onCancel: () => {
                                        setAddOpen(false);
                                    },
                                }}
                            />
                            <NewTable
                                modalTitle={itemType === 0 ? "添加料号" : "编辑料号"}
                                dataURL={"/ccs/mergeRelation/paging"}
                                defaultPagingParams={["customsBookId"]}
                                tableType={itemType}
                                tableConfig={ItemConfig}
                                searchConfig={searchConfig}
                                onOk={itemNoOk}
                                editrow={editrow}
                                onCancel={() => {
                                    setItemNoOpen(false);
                                }}
                                visible={itemNoOpen}
                                //@ts-ignore
                                CustomHeader={EditTableInfo}
                                selectMode={"radio"}
                            />
                            <Modal
                                title="海关回执"
                                open={systemBol}
                                onOk={() => {
                                    setSystemBol(false);
                                    setSystemContent("");
                                }}
                                onCancel={() => {
                                    setSystemBol(false);
                                    setSystemContent("");
                                }}>
                                <p>{systemContent}</p>
                            </Modal>
                        </>
                    );
                }}
            />
        </>
    );
};
