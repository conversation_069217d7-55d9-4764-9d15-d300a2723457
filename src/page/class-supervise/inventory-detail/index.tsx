import React, { useRef, useState, useEffect } from "react";
import { SearchList } from "@dt/components";
import { DDYObject, getConfigDataUtils, lib } from "react-single-app";
import { Button, Modal, message, Row, Col } from "antd";
import axios from "axios";
import NewModal from "@/components/NewModal";
import { request } from "@dt/networks";
// import { InventoryManageDataItem } from './type';
// import { MergeRelationUpdateConfig } from './config';
// import { MergeRelationDataItem } from './type';
export default () => {
    const SearchListRef = useRef(null);
    const selecteds = useRef([]);
    const getConfig = async () => {
        let url = getConfigDataUtils.getDataUrlByDataId(688);
        const res = await axios.get(url);
        return res.data.data;
    };

    const getDetail = () => {
        request.request({
            url: "/ccs/fbInventory/detail",
            data: { id: lib.getParam("id") },
            success: res => {
                console.log("111");
            },
        });
    };

    return (
        <>
            <SearchList
                ref={SearchListRef}
                searchConditionConfig={{
                    size: "middle",
                }}
                headerMode={"sticky"}
                getConfig={getConfig}
                renderOperationTopView={() => {
                    return (
                        <Row wrap={true} gutter={[8, 8]}>
                            <Col span={8}>
                                <label>商品料号:</label>
                                <span>{lib.getParam("productId")}</span>
                            </Col>
                            <Col span={8}>
                                <label>商品编码:</label>
                                <span>{lib.getParam("goodsNo")}</span>
                            </Col>
                            <Col span={8}>
                                <label>账册编号:</label>
                                <span>{lib.getParam("customsBookNo")}</span>
                            </Col>
                            <Col span={8}>
                                <label>商品名称:</label>
                                <span>{lib.getParam("goodsName")}</span>
                            </Col>
                        </Row>
                    );
                }}
            />
        </>
    );
};
