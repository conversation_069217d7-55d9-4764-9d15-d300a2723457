import React, { useRef, useState, useEffect } from "react";
import { SearchList } from "@dt/components";
import { DDYObject, getConfigDataUtils, lib } from "react-single-app";
import { Button, Modal, message, Space } from "antd";
import axios from "axios";
import NewModal from "@/components/NewModal";
import { request } from "@dt/networks";
import ImportBtn from "./import-btn";
export default () => {
    const ref = useRef(null);
    const selecteds = useRef([]);
    const getConfig = async () => {
        let url = getConfigDataUtils.getDataUrlByDataId(869);
        const res = await axios.get(url);
        return res.data.data;
    };

    const push = row => {
        Modal.confirm({
            title: "确定",
            content: "确定推送吗？",
            onOk: () => {
                request.request({
                    url: "/ccs/gqTransmit/push",
                    data: {
                        id: row.id,
                    },
                    success: () => {
                        message.success("推送成功");
                        ref.current.load();
                    },
                });
            },
        });
    };

    const cancel = row => {
        Modal.confirm({
            title: "确定",
            content: "确定作废吗？",
            onOk: () => {
                request.request({
                    url: "/ccs/gqTransmit/discard",
                    data: {
                        id: row.id,
                    },
                    success: () => {
                        message.success("作废成功");
                        ref.current.load();
                    },
                });
            },
        });
    };

    return (
        <>
            <SearchList
                ref={ref}
                searchConditionConfig={{
                    size: "middle",
                }}
                headerMode={"sticky"}
                getConfig={getConfig}
                renderRightOperation={() => {
                    return (
                        <>
                            <ImportBtn
                                reload={() => {
                                    ref.current.load();
                                }}
                            />
                        </>
                    );
                }}
                tableCustomFun={{
                    operateFn: (row: any, index: number) => {
                        return (
                            <Space>
                                {!["FINISH", "DISCARD"].includes(row.status) && (
                                    <Button
                                        size="small"
                                        type="link"
                                        onClick={() => {
                                            push(row);
                                        }}>
                                        推送
                                    </Button>
                                )}

                                {!["FINISH", "DISCARD"].includes(row.status) && (
                                    <Button
                                        size="small"
                                        type="link"
                                        onClick={() => {
                                            cancel(row);
                                        }}>
                                        作废
                                    </Button>
                                )}
                            </Space>
                        );
                    },
                    receipt: row => {
                        if (!row.receipt) return null;
                        return (
                            <Button
                                type="link"
                                onClick={() => {
                                    Modal.confirm({
                                        title: "报文回执",
                                        content: row.receipt,
                                    });
                                }}>
                                查看
                            </Button>
                        );
                    },
                    fileUrl: (row: any, index: number) => {
                        return (
                            <Button
                                type="link"
                                onClick={() => {
                                    location.href = row.fileUrl;
                                }}>
                                {row.fileUrl}
                            </Button>
                        );
                    },
                }}
                onTableSelected={row => {
                    selecteds.current = row;
                }}
            />
        </>
    );
};
