import React, { useEffect, useRef, useState } from "react";
import { Mo<PERSON>, But<PERSON>, message, Alert } from "antd";
import { DTEditForm } from "@dt/components";
import { lib } from "react-single-app";
export default ({ reload }) => {
    const [open, setOpen] = useState(false);
    const ref = useRef(null);
    const [bizType, setBizType] = useState("");
    useEffect(() => {}, []);
    return (
        <>
            <Button
                onClick={() => {
                    setOpen(true);
                }}>
                导入
            </Button>
            <Modal
                title={"导入"}
                open={open}
                onCancel={() => {
                    setOpen(false);
                    setBizType("");
                    ref.current.form.resetFields();
                }}
                onOk={() => {
                    ref.current.form.validateFields().then(values => {
                        lib.request({
                            url: "/ccs/gqTransmit/importExcel",
                            data: { ...values, url: values.url[0].url },
                            success(data) {
                                message.success("导入成功");
                                setOpen(false);
                                setBizType("");
                                ref.current.form.resetFields();
                                reload && reload();
                            },
                        });
                    });
                }}>
                <Alert
                    message="1.导入时务必确认好业务类型模板"
                    type="error"
                    style={{
                        marginBottom: "20px",
                    }}
                />
                <DTEditForm
                    ref={ref}
                    layout={{
                        mode: "appoint",
                        colNum: 1,
                    }}
                    configs={[
                        {
                            type: "SELECT",
                            fProps: {
                                label: "业务类型",
                                name: "bizType",
                                rules: [{ required: true, message: "业务类型是必选的" }],
                            },

                            list: [],
                            dataUrl: "/ccs/gqTransmit/listBizTypeWithTemplate",
                            resDataHandleFn(response) {
                                console.log("response:", response);
                                return response.map(item => {
                                    return {
                                        ...item,
                                        name: item.bizTypeDesc,
                                    };
                                });
                            },
                        },
                        {
                            type: "FILE",
                            fProps: {
                                label: "文件上传",
                                name: "url",
                                rules: [{ required: true, message: "请上传文件" }],
                            },
                        },
                    ]}
                    onChange={(name, value) => {
                        if (name === "bizType") {
                            setBizType(value);
                        }
                    }}
                />
                {bizType === "IN_OUT_WAREHOUSE" && (
                    <a
                        target="_blank"
                        style={{ display: "inline-block", width: "100%", textAlign: "center", paddingRight: "56px" }}
                        href="https://dante-img.oss-cn-hangzhou.aliyuncs.com/33567383285.xlsx">
                        出入库导出模版
                    </a>
                )}
                {bizType === "WAREHOUSE_IN_TRANSFORM" && (
                    <a
                        target="_blank"
                        style={{ display: "inline-block", width: "100%", textAlign: "center", paddingRight: "56px" }}
                        href="https://dante-img.oss-cn-hangzhou.aliyuncs.com/33576080740.xlsx">
                        库内转化导出模版
                    </a>
                )}
            </Modal>
        </>
    );
};
