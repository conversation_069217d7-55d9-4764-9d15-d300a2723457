import { INonGuaranteedDetail, INonGuaranteedDetailItemVOS } from "../non-guaranteed-detail/type";

export interface NonGuaranteedDetailBodyProps {
    datas: INonGuaranteedDetailItemVOS[];
    reload: (unAllBol?: boolean) => void;
    status?: number;
    detail?: INonGuaranteedDetail;
}

export let NonGuaranteedStatus = {
    CREATED: 1, // 已创建
    CACHE_PENDING: 2, // 暂存中
    CACHE_FAIL: 3, // 暂存失败
    CACHE_SUCCESS: 4, // 已暂存
    PUSH_SUCCESS: 5, // 已推送
    ACCESS: 6, // 已通过
    UNUSUAL: 7, // 异常
    COMPLETE: 8, // 已完成
    VOID: 9, // 已作废
};

export let NonGuaranteedType = {
    /**
     * 二线非保货物入区
     */

    FM: "FM", // SECOND_LINE_FB_GOODS_IN("FM", "二线非保货物入区", "FM", 1),
    /**
     * 二线非保货物出区
     */
    FN: "FN", // SECOND_LINE_FB_GOODS_OUT("FN", "二线非保货物出区", "FN", 2),
    /**
     * 非保空车入区
     */
    FEI: "FEI", // FB_EMPTY_IN("FEI", "非保空车入区", null, 3),
    /**
     * 非保空车出区
     */
    FEO: "FEO", // FB_EMPTY_OUT("FEO", "非保空车出区", null, 4),
    /**
     * 保税转非保
     */
    BTF: "BTF", // BS_TO_FB("BTF", "保税转非保", null, 5),
    /**
     * 非保转保税
     */
    FTB: "FTB", // FB_TO_BS("FTB", "非保转保税", null, 6);
};
