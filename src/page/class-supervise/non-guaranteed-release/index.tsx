import React, { useRef, useState, useEffect } from "react";
import { SearchList } from "@dt/components";
//@ts-ignore
import { DDYObject, getConfigDataUtils, lib, hooks } from "react-single-app";
import { Button, Modal, message } from "antd";
import axios from "axios";
import NewModal from "@/components/NewModal";
import { addOddConfig, manualConfig } from "./config";
import { request } from "@dt/networks";
import { NonGuaranteedStatus } from "./type";
import { CopyOutlined } from "@ant-design/icons";
import { handleCopy } from "../../../common/utils";
import UpdateStatusModal from "@/page/customs-clearance-system/components/update-status-modal";
export default () => {
    const [addOpen, setAddOpen] = useState(false);
    const [manualBol, setManualBol] = useState(false);
    const [editrow, setEditrow] = useState<DDYObject>({});
    const [addConfig, setAddConfig] = useState<any[]>(addOddConfig);
    const addModalRef = useRef(null);
    const SearchListRef = useRef(null);
    const selectedIds = useRef([]);
    const [selected, setSelecteds] = useState([]);

    const [buttons] = hooks.useGetAuthButtons({
        systemCode: "CCS_ADMIN",
        pagePath: "/non-guaranteed-release",
    });
    const getConfig = async () => {
        let url = getConfigDataUtils.getDataUrlByDataId(684);
        const res = await axios.get(url);
        return res.data.data;
    };

    const addOdd = () => {
        setAddOpen(true);
    };

    const addOK = (data: DDYObject) => {
        request.request({
            url: "/ccs/fbChecklist/add",
            data: data,
            needMask: true,
            success: () => {
                message.success("新增成功");
                setAddOpen(false);
                SearchListRef.current.load();
            },
        });
    };

    const showManualOvercard = () => {
        console.log("selecteds.current", selectedIds.current);
        if (selectedIds.current.length == 0) {
            return message.warning("请勾选数据");
        }
        Modal.confirm({
            title: "确定手动过卡",
            onOk: () => {
                request.request({
                    url: "/ccs/fbChecklist/manualPassGate",
                    data: { idList: selectedIds.current },
                    needMask: true,
                    success: () => {
                        SearchListRef.current.load();
                    },
                });
            },
        });
    };

    const importData = () => {};

    const manualOk = data => {
        request.request({
            url: "/ccs/fbChecklist/manualAudit",
            data: {
                // ...data,
                id: editrow.id,
                jobFormId: data.jobFormId,
            },
            needMask: true,
            success: () => {
                setManualBol(false);
                SearchListRef.current.load();
            },
        });
    };

    const pushOrder = (detail: DDYObject) => {
        request.request({
            url: "/ccs/fbChecklist/push",
            data: { id: detail.id },
            needMask: true,
            success: () => {
                // setManualBol(false);
                message.success("推送成功");
                SearchListRef.current.load();
            },
        });
    };

    const stag = (detail: DDYObject) => {
        request.request({
            url: "/ccs/fbChecklist/temporary",
            data: { id: detail.id },
            needMask: true,
            success: () => {
                // setManualBol(false);
                message.success("暂存成功");
                SearchListRef.current.load();
            },
        });
    };

    const toVoid = (detail: DDYObject) => {
        request.request({
            url: "/ccs/fbChecklist/discard",
            data: { id: detail.id },
            needMask: true,
            success: () => {
                setManualBol(false);
                SearchListRef.current.load();
            },
        });
    };

    const getDatas = () => {
        const arr = [];
        addConfig.forEach((item, index) => {
            if (item.ccs) {
                arr.push(
                    new Promise((resolve, reject) => {
                        request.request({
                            url: item.ccs,
                            data: {},
                            success: data => {
                                resolve({
                                    data,
                                    index,
                                });
                            },
                            fail: () => {
                                reject();
                            },
                        });
                    }),
                );
            }
        });

        Promise.all(arr)
            .then(res => {
                res.map(item => {
                    addConfig[item.index].list = item.data;
                });
                setAddConfig(addConfig);
            })
            .catch(err => {});
    };

    const getinventoryOrderSn = (changed, all) => {
        // 依赖项改变时，清空清关单号的数据
        if (changed.businessType || changed.customsBookId) {
            addConfig[2].list = [];
            setAddConfig([...addConfig]);
            addModalRef.current?.form.setFieldsValue({ inventoryOrderId: null });
        }
        if ((changed.businessType || changed.customsBookId) && all.businessType && all.customsBookId) {
            request.request({
                url: "/ccs/fbChecklist/listFbInventorySn",
                data: { businessType: all.businessType, bookId: all.customsBookId },
                success: data => {
                    addConfig[2].list = data;
                    setAddConfig([...addConfig]);
                },
                fail: () => {},
            });
        }
    };

    useEffect(() => {
        getDatas();
    }, []);
    return (
        <>
            <SearchList
                ref={SearchListRef}
                searchConditionConfig={{
                    size: "middle",
                }}
                headerMode={"sticky"}
                getConfig={getConfig}
                renderLeftOperation={() => {
                    return (
                        <>
                            <Button
                                type="primary"
                                onClick={() => {
                                    addOdd();
                                }}>
                                新建
                            </Button>
                            <Button
                                onClick={() => {
                                    showManualOvercard();
                                }}>
                                手动过卡
                            </Button>
                            {buttons?.includes("UPDATE-RELEASE-STATUS") && (
                                <UpdateStatusModal
                                    //@ts-ignore
                                    selected={selected}
                                    success={() => {
                                        SearchListRef.current.load();
                                    }}
                                    type={"release"}
                                />
                            )}
                        </>
                    );
                }}
                tableCustomFun={{
                    operationFn: (row: any, index: number) => {
                        return (
                            <div>
                                {/* {[
                                    NonGuaranteedStatus.CACHE_SUCCESS,
                                    NonGuaranteedStatus.PUSH_SUCCESS,
                                    NonGuaranteedStatus.UNUSUAL,
                                ].includes(row.status) && ( */}
                                <Button
                                    size="small"
                                    type="link"
                                    onClick={() => {
                                        pushOrder(row);
                                    }}>
                                    推送
                                </Button>
                                {/* // )} */}
                                {[
                                    NonGuaranteedStatus.CREATED,
                                    NonGuaranteedStatus.CACHE_PENDING,
                                    NonGuaranteedStatus.CACHE_FAIL,
                                    NonGuaranteedStatus.CACHE_SUCCESS,
                                ].includes(row.status) && (
                                    <Button
                                        size="small"
                                        type="link"
                                        onClick={() => {
                                            stag(row);
                                        }}>
                                        暂存
                                    </Button>
                                )}
                                {[
                                    NonGuaranteedStatus.CREATED,
                                    NonGuaranteedStatus.CACHE_FAIL,
                                    NonGuaranteedStatus.CACHE_SUCCESS,
                                    NonGuaranteedStatus.UNUSUAL,
                                ].includes(row.status) && (
                                    <Button
                                        size="small"
                                        type="link"
                                        onClick={() => {
                                            setManualBol(true);
                                            setEditrow(row);
                                        }}>
                                        手动审核
                                    </Button>
                                )}
                                {[
                                    NonGuaranteedStatus.CREATED,
                                    NonGuaranteedStatus.CACHE_FAIL,
                                    NonGuaranteedStatus.CACHE_SUCCESS,
                                    NonGuaranteedStatus.UNUSUAL,
                                ].includes(row.status) && (
                                    <Button
                                        size="small"
                                        type="link"
                                        onClick={() => {
                                            toVoid(row);
                                        }}>
                                        作废
                                    </Button>
                                )}
                            </div>
                        );
                    },
                    orderNoFn: (row: any, index: number) => {
                        return (
                            <>
                                <Button
                                    size="small"
                                    type="link"
                                    onClick={() => {
                                        lib.openPage(`/non-guaranteed-detail?page_title=非保核放单详情&id=${row.id}`);
                                    }}>
                                    {row.sn}
                                </Button>
                                <CopyOutlined onClick={() => handleCopy(row.sn)} />
                            </>
                        );
                    },
                    callbackInfoFn: (row: any, index: number) => {
                        return (
                            <>
                                {row.callbackInfo && (
                                    <Button
                                        size="small"
                                        type="link"
                                        onClick={() => {
                                            Modal.confirm({
                                                title: "海关回执",
                                                content: row.callbackInfo,
                                            });
                                        }}>
                                        查看
                                    </Button>
                                )}
                            </>
                        );
                    },
                }}
                renderModal={() => {
                    return (
                        <>
                            <NewModal
                                {...{
                                    visible: addOpen,
                                    configList: addConfig,
                                    title: "新增核放单",
                                    onOk: addOK,
                                    editRow: {},
                                    onValuesChange: getinventoryOrderSn,
                                    onCancel: () => {
                                        setAddOpen(false);
                                    },
                                    ref: addModalRef,
                                }}
                            />
                            <NewModal
                                {...{
                                    title: "手动审核",
                                    visible: manualBol,
                                    configList: manualConfig,
                                    onOk: manualOk,
                                    editRow: editrow,
                                    onCancel: () => {
                                        setManualBol(false);
                                    },
                                    formItemLayout: {
                                        labelCol: { span: 24 },
                                        wrapperCol: { span: 24 },
                                    },
                                }}
                            />
                        </>
                    );
                }}
                onTableSelected={(row, rowDatas) => {
                    selectedIds.current = row;
                    setSelecteds([...rowDatas]);
                }}
            />
        </>
    );
};
