import React, { useState, useEffect, useRef, useMemo } from "react";
import { Modal, Table } from "antd";
import { DDYObject, lib } from "react-single-app";
import { editHeaderConfig } from "../customs-access/config";
import { EditableRef, EditableType, TColumns } from "./edit-table";
import SearchCondition from "./search-condition";

interface NewTableModalProps {
    dataURL: string;
    onOk: (data: any, selectedRows: DDYObject[], selectedData?: DDYObject) => void;
    onCancel: () => void;
    visible: boolean;
    tableConfig: any[];
    searchConfig: any[];
    CustomHeader?: EditableType;
    modalTitle: string;
    editrow: DDYObject;
    tableType: 0 | 1;
    selectMode?: "checkbox" | "radio";
    defaultPagingParams?: string[];
}
export default (props: NewTableModalProps) => {
    const { dataURL, onOk, onCancel, visible, CustomHeader, modalTitle, searchConfig } = props;
    let [pagination, setPagination] = useState({ pageSize: 10, currentPage: 1, totalCount: 1 });
    let [dataSource, setDataSource] = useState([]);
    let [loading, setLoading] = useState(false);
    let [selectedRows, setSelectedRows] = useState([]);
    const [selectDetail, setSelectDetail] = useState({});
    const resultData = useRef({});
    const isRadio = !!(props.selectMode || props.selectMode === "radio");
    const searchData = useRef({});
    const CustomHeaderRef = useRef<EditableRef>({} as EditableRef);
    function fetchList(params: DDYObject) {
        const defaultParams = {};
        if (Array.isArray(props.defaultPagingParams)) {
            props.defaultPagingParams.forEach((item, index) => {
                defaultParams[item] = props.editrow[item];
            });
        }
        setLoading(true);
        lib.request({
            url: dataURL,
            data: {
                ...params,
                ...searchData.current,
                ...defaultParams,
            },
            needMask: true,
            success: res => {
                setLoading(false);
                setDataSource(res.dataList);
                setPagination(res.page);
            },
            fail: () => {},
        });
    }

    function onSelect(record: DDYObject, selected: boolean) {
        console.log("record:", record, "selectd:", selected);
        select(record, selected);
        setSelectDetail(record);
    }

    function select(record: DDYObject, selected: boolean) {
        let recordKey = record.id;

        setSelectedRows(selectedRows => {
            if (!isRadio) {
                if (selected && selectedRows.every(item => item.id !== recordKey)) {
                    return [...selectedRows, record];
                } else {
                    return selectedRows.filter(item => item.id !== recordKey);
                }
            } else {
                return [record];
            }
        });
    }

    function onSelectAll(selected: boolean, selectedRows: DDYObject[], changeRows: DDYObject[]) {
        setSelectDetail({});
        changeRows.forEach(item => {
            select(item, selected);
        });
    }

    function okHandle() {
        if (CustomHeaderRef.current?.validatorAll) {
            CustomHeaderRef.current.validatorAll().then(res => {
                onOk(res, selectedRows, selectDetail);
            });
        } else {
            onOk([], selectedRows, selectDetail);
        }
    }

    function afterClose() {
        setPagination({ pageSize: 10, currentPage: 1, totalCount: 1 });
        setSelectedRows([]);
    }

    function cancelHandle() {
        let defaultPage = {
            currentPage: 1,
            pageSize: 10,
            totalCount: 1,
        };
        setPagination(defaultPage);
        if (CustomHeaderRef.current?.clear) {
            CustomHeaderRef.current.clear();
        }
        setSelectDetail(props.editrow);
        setSelectedRows([]);
        onCancel();
    }

    function handleTableChange(pagination) {
        let page = {
            currentPage: pagination.current,
            pageSize: pagination.pageSize,
            total: pagination.total,
        };
        setPagination(pagination);
        fetchList(page);
    }

    const selectedRowKeys = useMemo(() => {
        return selectedRows.reduce((prev, curr) => [...prev, curr.id], []);
    }, [selectedRows]);

    useEffect(() => {
        if (props.editrow) {
            setSelectDetail(props.editrow);
        }
    }, [props.tableType, props.editrow]);

    useEffect(() => {
        if (visible) {
            fetchList({
                currentPage: 1,
                pageSize: 10,
                totalCount: 1,
            });
        }
    }, [visible]);
    return (
        (<Modal
            title={modalTitle}
            open={visible}
            maskClosable={false}
            destroyOnClose={true}
            onOk={() => okHandle()}
            onCancel={() => cancelHandle()}
            afterClose={afterClose}
            width={900}
            wrapClassName={"role-add-account-modal config-center-v3"}>
            {CustomHeader ? (
                <CustomHeader
                    tableColumns={editHeaderConfig as unknown as TColumns}
                    dataSource={[selectDetail]}
                    ref={CustomHeaderRef}
                />
            ) : null}
            <SearchCondition
                searchConfig={searchConfig}
                searchFn={(obj: DDYObject) => {
                    searchData.current = obj;
                    fetchList(pagination);
                }}
            />
            <div className={"search-list"}>
                <Table
                    loading={loading}
                    dataSource={dataSource}
                    size={"small"}
                    columns={props.tableConfig}
                    // scroll={{ x: "max-content", y: 350 }}
                    onChange={handleTableChange}
                    rowSelection={{
                        type: props.selectMode || "checkbox",
                        selectedRowKeys,
                        onSelect: onSelect,
                        onSelectAll: onSelectAll,
                    }}
                    rowKey={"id"}
                    pagination={{
                        pageSize: pagination.pageSize,
                        current: pagination.currentPage,
                        total: pagination.totalCount,
                    }}
                />
            </div>
        </Modal>)
    );
};
