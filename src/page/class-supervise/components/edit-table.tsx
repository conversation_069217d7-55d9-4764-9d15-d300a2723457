// import React, { useContext, useEffect, useRef, useState } from 'react';
// // import type { Rule } from 'antd';
// import { Button, Form, Input, Popconfirm, Table } from 'antd';
// import type { FormInstance } from 'antd/es/form';
// import { editHeaderConfig } from '../customs-access/config';
// import { DDYObject } from 'react-single-app';

// const EditableContext = React.createContext<FormInstance<any> | null>(null);

// interface Item {
//     form: FormInstance<any>;
//     rules: any[];
//     key: string;
//     name: string;
//     age: string;
//     address: string;
// }

// interface EditableRowProps {
//     index: number;
// }

// const EditableRow: React.FC<EditableRowProps> = ({ index, ...props }) => {
//     const [form] = Form.useForm();
//     return (
//         <Form form={form} component={false}>
//             <EditableContext.Provider value={form}>
//                 <tr {...props} />
//             </EditableContext.Provider>
//         </Form>
//     );
// };

// interface EditableCellProps {
//     title: React.ReactNode;
//     editable: boolean;
//     children: React.ReactNode;
//     dataIndex: keyof Item;
//     record: Item;
//     handleSave: (record: Item) => void;
// }

// const EditableCell: React.FC<EditableCellProps> = ({
//     title,
//     editable,
//     children,
//     dataIndex,
//     record,
//     handleSave,
//     ...restProps
// }) => {
//     const [editing, setEditing] = useState(false);
//     const inputRef = useRef(null);
//     const form = useContext(EditableContext)!;

//     useEffect(() => {
//         if (editing) {
//             inputRef.current!.focus();
//         }
//     }, [editing]);

//     const save = async () => {
//         try {
//             const values = await form.validateFields();
//             handleSave({ ...record, ...values });
//         } catch (errInfo) {
//             console.log('Save failed:', errInfo);
//         }
//     };

//     let childNode = children;

//     if (editable) {
//         childNode =
//             (<Form.Item
//                 style={{ margin: 0 }}
//                 name={dataIndex}
//                 rules={record.rules}
//             >
//                 <Input ref={inputRef} onPressEnter={save} onBlur={save} />
//             </Form.Item>)
//     }
//     useEffect(() => {
//         // record && (record.form = form);
//         form.setFieldsValue(record)
//     }, [record])
//     return <td {...restProps}>{childNode}</td>;
// };

// type EditableTableProps = Parameters<typeof Table>[0];

// type ColumnTypes = Exclude<EditableTableProps['columns'], undefined>;
// // type ColumnTypesItem = typeof ColumnTypes

// interface EditTableProps {
//     data: DDYObject[],
//     onChange: (data: DDYObject[]) => void
//     columns: TColumns
//     summary?: (data: readonly any[]) => React.ReactNode
// }

export type TColumns = any[];

// const EditTableInfo: React.FC<EditTableProps> = (props) => {
//     const [dataSource, setDataSource] = useState<any[]>([]);

//     const handleSave = (row: any) => {
//         const newData = [...dataSource];
//         const index = newData.findIndex((item) => row.key === item.key);
//         const item = newData[index];
//         newData.splice(index, 1, {
//             ...item,
//             ...row,
//         });
//         props.onChange(newData)
//         setDataSource(newData);
//     };

//     const components = {
//         body: {
//             row: EditableRow,
//             cell: EditableCell,
//         },
//     };

//     const columns = props.columns.map((col) => {
//         if (!col.editable) {
//             return col;
//         }
//         return {
//             ...col,
//             onCell: (record) => ({
//                 record,
//                 editable: col.editable,
//                 dataIndex: col.dataIndex,
//                 title: col.title,
//                 handleSave,
//             }),
//         };
//     });

//     useEffect(() => {
//         setDataSource(props.data)
//     }, [props.data])

//     return (
//         <Table
//             components={components}
//             rowClassName={() => 'editable-row'}
//             bordered
//             size={'small'}
//             rowKey={'id'}
//             dataSource={dataSource}
//             columns={columns}
//             pagination={false}
//             style={{ paddingBottom: '20px' }}
//             summary={props.summary}
//         />
//     );
// };

// export default React.memo(EditTableInfo);

import React, {
    ForwardRefExoticComponent,
    useRef,
    useContext,
    useEffect,
    useImperativeHandle,
    useState,
    useMemo,
} from "react";
import { Form, Input, InputNumber, Popconfirm, Table, Typography, FormInstance, Space } from "antd";
import { DDYObject } from "react-single-app";
const EditableContext = React.createContext(null);
interface Item {
    key: string;
    name: string;
    age: number;
    address: string;
    form: any;
}

interface EditableCellProps extends React.HTMLAttributes<HTMLElement> {
    editing: boolean;
    dataIndex: string;
    title: any;
    inputType: "number" | "text";
    record: Item;
    index: number;
    children: React.ReactNode;
    col: DDYObject;
    editable?: boolean;
    onNewChange: (value: any, form: any) => void;
}

const EditableRow = ({ index, ...props }) => {
    const [form] = Form.useForm();
    return (
        <Form form={form} component={false}>
            <EditableContext.Provider value={form}>
                <tr {...props} />
            </EditableContext.Provider>
        </Form>
    );
};

const EditableCell: React.FC<EditableCellProps> = ({
    dataIndex,
    title,
    inputType,
    record,
    editable,
    col,
    children,
    onNewChange,
    ...restProps
}) => {
    const inputNode = inputType === "number" ? <InputNumber /> : <Input />;
    const form = useContext(EditableContext);

    const save = async () => {
        try {
            const values = await form.validateFields();
        } catch (errInfo) {}
    };

    let childNode = (
        <Form.Item
            style={{
                margin: 0,
            }}
            hidden={col?.hide}
            name={dataIndex}>
            {children}
        </Form.Item>
    );
    if (editable) {
        let newRules = [];
        const requireRule = {
            required: true,
            message: `${title}是必填的`,
        };
        if (col && col.required) {
            newRules = [requireRule];
        }
        if (col && col.rules) {
            newRules = col.rules;
            if (col.required) {
                newRules[0] = requireRule;
            }
            if (newRules[0].required) {
                newRules[0] = requireRule;
            }
        }
        childNode = (
            <Form.Item
                style={{
                    margin: 0,
                }}
                name={dataIndex}
                rules={newRules}
                hidden={col?.hide}>
                <Input
                    style={{ width: 100 }}
                    disabled={col.disabled}
                    onPressEnter={save}
                    onBlur={save}
                    onChange={value => {
                        onNewChange(value.target.value, form);
                    }}
                />
            </Form.Item>
        );
    }
    useEffect(() => {
        record && (record.form = form);
        form.setFieldsValue(record);
    }, [record]);
    return (
        <td {...restProps} style={{ width: "200px" }}>
            {childNode}
        </td>
    );
};

interface EditableProps {
    tableColumns: any[];
    dataSource: any[];
    scroll?: any;
    dataChange?: (data: any[]) => void;
}
export interface EditableRef {
    validatorAll: () => Promise<any>;
    getDatas: () => any[];
    setColumns: (columns: any[]) => void;
    delData: (index: number) => void;
    clear: () => void;
}

export type EditableType = React.ForwardRefExoticComponent<React.RefAttributes<EditableRef> & EditableProps>;
const Editable: EditableType = React.forwardRef((props: EditableProps, ref) => {
    const [data, setData] = useState([]);
    const [columns, setColumns] = useState([]);
    const currentData = useRef([]);
    useImperativeHandle(ref, () => {
        return {
            validatorAll: () => {
                const arr = data.map(item => item.form.validateFields());
                return Promise.all(arr);
            },
            getDatas: () => {
                return currentData.current.map(item => {
                    const result = { ...item };
                    delete result.form;
                    return result;
                });
            },
            setColumns: (columns: any[]) => {
                setColumns(proxyColumns(columns));
            },
            delData: (index: number) => {
                data.splice(index, 1);
                currentData.current = [...data];
                setData([...data]);
            },
            clear: () => {
                console.log("clear");
                currentData.current = [];
                setData([]);
            },
        };
    });

    const proxyColumns = (preColumns: any[]) => {
        return preColumns.map(col => {
            if (!col.editable) {
                return col;
            }
            return {
                ...col,
                onCell: (record: Item, index: number) => ({
                    record,
                    editable: col.editable,
                    dataIndex: col.dataIndex,
                    title: col.title,
                    col: col,
                    cellIndex: index,
                    onNewChange: (value, form) => {
                        const datas = currentData.current;
                        const item = datas[index];
                        const changeObj = col.onChange && col.onChange(value, form.getFieldsValue(), item, form);
                        form.setFieldsValue(changeObj);
                        datas[index] = { ...datas[index], ...{ [col.dataIndex]: value }, ...changeObj };
                        currentData.current = [...datas];
                        props.dataChange && props.dataChange(currentData.current);
                        // setData([...currentData.current]);
                    },
                }),
            };
        });
    };

    useEffect(() => {
        setColumns([...proxyColumns(props.tableColumns)]);
    }, [props.tableColumns]);

    useEffect(() => {
        setData(props.dataSource);
        currentData.current = props.dataSource;
    }, [props.dataSource]);

    const components = useMemo(() => {
        return {
            body: {
                row: (row, ...rest) => {
                    return <EditableRow {...row} />;
                },
                cell: (cell, ...rest) => {
                    return <EditableCell {...cell} />;
                },
            },
        };
    }, []);
    return (
        <>
            <Table
                components={components}
                bordered
                dataSource={data}
                columns={columns}
                rowClassName="editable-row"
                scroll={props.scroll}
                pagination={false}
            />
        </>
    );
});

export default Editable;
