import React, { useEffect, useState, useRef } from "react";
import { Row, Col, Form, Input, Space, Button } from "antd";
import { DDYObject } from "react-single-app";
interface ISearchConditionProps {
    searchConfig: any[];
    searchFn: (data: DDYObject) => void;
}
export default (props: ISearchConditionProps) => {
    const { searchConfig, searchFn } = props;
    const [searchData, setSearchData] = useState({});
    return (
        <Row gutter={[8, 4]} style={{ paddingTop: "10px" }}>
            {searchConfig.map((item, index) => {
                return (
                    <Col span={24} md={8} xl={8} xxl={8} key={index}>
                        <Form.Item label={item.title} labelCol={{ span: 8 }} wrapperCol={{ span: 16 }}>
                            <Input
                                value={searchData[item.dataIndex]}
                                onChange={e => {
                                    setSearchData({
                                        [item.dataIndex]: e.target.value,
                                    });
                                }}
                            />
                        </Form.Item>
                    </Col>
                );
            })}
            <Col span={24} md={8} xl={8} xxl={8}>
                <Space>
                    <Button
                        type="primary"
                        onClick={() => {
                            searchFn(searchData);
                        }}>
                        搜索
                    </Button>
                    <Button
                        onClick={() => {
                            setSearchData({});
                            searchFn({});
                        }}>
                        重置
                    </Button>
                </Space>
            </Col>
        </Row>
    );
};
