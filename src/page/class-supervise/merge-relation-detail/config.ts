import { DDYObject } from "react-single-app";

export const MergeRelationDataItemConfig = {
    baseInfo: {
        children: [
            {
                label: "料号",
                name: "productId",
                editEnable: false,
                type: "text",
                labelCol: { span: 10 },
            },
            {
                label: "账册编号",
                name: "customsBookNo",
                editEnable: false,
                type: "text",
                labelCol: { span: 10 },
            },
            {
                label: "账册项号",
                name: "itemNo",
                editEnable: false,
                type: "text",
                labelCol: { span: 10 },
            },
            {
                label: "商品编码",
                name: "goodsNo",
                editEnable: false,
                type: "text",
                labelCol: { span: 10 },
            },
            {
                label: "商品名称",
                name: "goodsName",
                editEnable: false,
                type: "text",
                labelCol: { span: 10 },
            },
            {
                label: "商品规格",
                name: "goodsSpec",
                editEnable: false,
                type: "text",
                labelCol: { span: 10 },
            },
            {
                label: "申报单位",
                name: "declareUnitDesc",
                editEnable: false,
                type: "text",
                labelCol: { span: 10 },
            },
            {
                label: "第一单位",
                name: "firstUnitDesc",
                editEnable: false,
                type: "text",
                labelCol: { span: 10 },
            },
            {
                label: "第二单位",
                name: "secondUnitDesc",
                editEnable: false,
                type: "text",
                labelCol: { span: 10 },
            },
            {
                label: "币制",
                name: "currencyTypeDesc",
                editEnable: false,
                type: "text",
                labelCol: { span: 10 },
            },
            {
                label: "料件性质",
                name: "itemTypeDesc",
                editEnable: false,
                type: "text",
                labelCol: { span: 10 },
            },
            {
                label: "状态",
                name: "useFlagDesc",
                editEnable: false,
                type: "text",
                labelCol: { span: 10 },
            },
        ],
        label: "料号归并详情",
        name: "mergeRelationDetail",
        isGroup: true,
        className: "merge-relation-detail-body",
    },
};

export const MergeRelationDetailConfig = (
    operateFn: (text: string, record: DDYObject, index: number) => JSX.Element,
) => [
    {
        title: "操作类型",
        dataIndex: "operateType",
        width: 120,
    },
    {
        title: "操作说明",
        dataIndex: "operateContent",
        width: 300,
        render: operateFn,
    },
    {
        title: "操作时间",
        dataIndex: "operateTime",
        width: 300,
    },
    {
        title: "操作人",
        dataIndex: "operateUser",
        width: 120,
    },
];

export const columnsDetail = [
    {
        title: "名称",
        dataIndex: "field",
        width: 200,
    },
    {
        title: "修改前",
        dataIndex: "before",
        width: 200,
        render: (text, record, _) => {
            return text === "null" ? "" : text;
        },
    },
    {
        title: "修改后",
        dataIndex: "after",
        width: 200,
        render: (text, record, _) => {
            return text === "null" ? "" : text;
        },
    },
];
