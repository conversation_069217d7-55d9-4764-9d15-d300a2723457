import React, { useRef, useEffect, useState } from "react";
import { MergeRelationDetailProps } from "./type";
import { Button, Descriptions, Table, Modal } from "antd";
//@ts-ignore
import { ConfigFormCenter, lib } from "react-single-app";
import { request } from "@dt/networks";
// import { NonGuaranteedConfigs } from "./config";
import "./index.less";
import { MergeRelationDataItem } from "../merge-relation/type";
import { columnsDetail, MergeRelationDataItemConfig, MergeRelationDetailConfig } from "./config";
// import DetailBody from "./detail-body";

export default (props: MergeRelationDetailProps) => {
    const ConfigFormCenterRef = useRef<any>({});
    const [dataSource, setDataSource] = useState([]);
    const [operateExtra, setOperateExtra] = useState([]);
    const [detailVisible, setDetailVisible] = useState(false);
    const save = () => {};

    const loadDetail = () => {
        request.request({
            url: "/ccs/mergeRelation/detail",
            data: { id: lib.getParam("id") },
            success: (data: MergeRelationDataItem) => {
                ConfigFormCenterRef?.current?.setMergeDetail(data);
            },
        });
    };

    const getLog = () => {
        request.request({
            url: "/ccs/mergeRelation/log",
            data: { id: lib.getParam("id") },
            success: data => {
                setDataSource(data);
            },
        });
    };

    useEffect(() => {
        loadDetail();
        getLog();
    }, []);

    return (
        (<div className="merge-relation-detail">
            <ConfigFormCenter
                ref={ConfigFormCenterRef}
                // onSinglesSelectChange={onSinglesSelectChange}
                disableEdit={false}
                // beforeSubmit={beforeSubmit}
                // beforeSetDetail={beforeSetDetail}
                // code={code}
                confData={MergeRelationDataItemConfig}
                // onConfigLoadSuccess={onConfigLoadSuccess}
                // onSetDetailSuccess={onSetDetailSuccess}
                // submitUrl={'/ccs/checklist/upset'}
                // onSubmitSuccess={onSubmitSuccess}
            />
            {/* <DetailBody /> */}
            <Descriptions style={{ display: "flex" }} title="归并日志"></Descriptions>
            <Table
                columns={MergeRelationDetailConfig((text, record, index) => {
                    return (
                        <>
                            {record.operateContent && record.operateContent !== "" ? (
                                record.operateContent
                            ) : record.operateExtra && record.operateExtra.length > 0 ? (
                                <Button
                                    onClick={() => {
                                        setOperateExtra(record.operateExtra);
                                        setDetailVisible(true);
                                    }}>
                                    点击查看变动明细
                                </Button>
                            ) : (
                                "无变动明细"
                            )}
                        </>
                    );
                })}
                dataSource={dataSource}
            />
            <Modal
                title={"明细"}
                okText={"关闭"}
                width={800}
                destroyOnClose
                open={detailVisible}
                cancelButtonProps={{ style: { display: "none" } }}
                onCancel={() => setDetailVisible(false)}
                onOk={() => setDetailVisible(false)}>
                <Table
                    dataSource={operateExtra}
                    columns={columnsDetail}
                    scroll={{ y: "320px" }}
                    pagination={false}
                    rowKey={record => JSON.stringify(record)}
                />
            </Modal>
        </div>)
    );
};
