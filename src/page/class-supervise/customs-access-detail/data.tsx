import React from "react";
import { Input, Form } from "antd";

export const BasicInformation = {
    children: [
        {
            label: "出入库记录流水号",
            dataIndex: "inOutSeq",
            key: "inOutSeq",
        },
        {
            label: "核放单编号",
            dataIndex: "fbChecklistNo",
            key: "fbChecklistNo",
        },
        {
            label: "账册编号",
            dataIndex: "customsBookNo",
            key: "customsBookNo",
        },
        {
            label: "出入库类型",
            dataIndex: "businessTypeDesc",
            key: "businessTypeDesc",
        },
        {
            label: "业务类型",
            dataIndex: "fbBusinessTypeDesc",
            key: "fbBusinessTypeDesc",
        },
        {
            label: "出入库状态",
            dataIndex: "statusDesc",
            key: "statusDesc",
        },
        {
            label: "创建时间",
            dataIndex: "createTime",
            key: "createTime",
        },
        {
            label: "申报时间",
            dataIndex: "lastDeclareTime",
            key: "lastDeclareTime",
        },
        {
            label: "出入库时间",
            dataIndex: "inOutTime",
            key: "inOutTime",
        },
        {
            label: "删除申请编号",
            dataIndex: "stockDeleteId",
            key: "stockDeleteId",
        },
        {
            label: "删除申请时间",
            dataIndex: "lastDeleteDeclareTime",
            key: "lastDeleteDeclareTime",
        },
    ],
};

export const InOutTableBody = [
    {
        title: "序号",
        dataIndex: "stockInOutId",
        key: "stockInOutId",
    },
    {
        title: "料号",
        dataIndex: "stockInOutId",
        key: "stockInOutId",
    },
    {
        title: "账册项号",
        dataIndex: "itemNo",
        key: "itemNo",
    },
    {
        title: "商品编号",
        dataIndex: "goodsNo",
        key: "goodsNo",
    },
    {
        title: "商品名称",
        dataIndex: "goodsName",
        key: "goodsName",
    },
    {
        title: "单位",
        dataIndex: "declareUnit",
        key: "declareUnit",
    },
    {
        title: "数量",
        dataIndex: "inOutAmount",
        key: "inOutAmount",
        render: (value, record, index) => {
            return (
                <Form.Item
                    rules={[
                        {
                            required: true,
                            message: "请填写数量！",
                        },
                        {
                            validator(_, value) {
                                console.log(value);
                                if (!value) return Promise.resolve();
                                if (!/^\d+$/.test(value)) {
                                    return Promise.reject("请填入整数");
                                }
                                return Promise.resolve();
                            },
                        },
                    ]}
                    initialValue={value}>
                    <Input
                        defaultValue={value}
                        onChange={e => {
                            record.quantity = e.target.value;
                        }}
                    />
                </Form.Item>
            );
        },
    },
    {
        title: "料件性质",
        dataIndex: "itemType",
        key: "itemType",
    },
    {
        title: "库位",
        dataIndex: "storageLocation",
        key: "storageLocation",
        render: (value, record, index) => {
            return (
                <Form.Item
                    rules={[
                        {
                            required: true,
                            message: "请填写库位！",
                        },
                    ]}
                    initialValue={value}>
                    <Input
                        defaultValue={value}
                        onChange={e => {
                            record.skuCode = e.target.value;
                        }}
                    />
                </Form.Item>
            );
        },
    },
    {
        title: "批次号",
        dataIndex: "batch",
        key: "batch",
        render: (value, record, index) => {
            return (
                <Form.Item
                    rules={[
                        {
                            required: true,
                            message: "请填写批次号！",
                        },
                    ]}
                    initialValue={value}>
                    <Input
                        defaultValue={value}
                        onChange={e => {
                            record.batch = e.target.value;
                        }}
                    />
                </Form.Item>
            );
        },
    },
];

export const addPartNumBody = [
    {
        title: "商品名称",
        dataIndex: "id",
        key: "id",
    },
    {
        title: "料号",
        dataIndex: "id",
        key: "id",
    },
    {
        title: "项号",
        dataIndex: "id",
        key: "id",
    },
    {
        title: "商品编码",
        dataIndex: "id",
        key: "id",
    },
];

export let ItemConfig = [
    {
        title: "料号",
        dataIndex: "productId",
        width: 50,
    },
    {
        title: "项号",
        dataIndex: "itemNo",
        width: 50,
    },
    {
        title: "商品编码",
        dataIndex: "goodsNo",
        width: 50,
    },
    {
        title: "商品名称",
        dataIndex: "goodsName",
        width: 50,
    },
];

export const searchConfig = [
    {
        title: "商品名称",
        dataIndex: "goodsName",
        width: 50,
    },
    {
        title: "料号",
        dataIndex: "productId",
        width: 50,
    },
    {
        title: "项号",
        dataIndex: "itemNo",
        width: 50,
    },
    {
        title: "商品编码",
        dataIndex: "goodsNo",
        width: 50,
    },
];
