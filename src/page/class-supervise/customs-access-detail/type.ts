export interface CustomsAccessDataItem {
    id: number;
    inOutSeq: string; // 出入库记录流水号
    businessType: string; // 业务类型
    businessTypeDesc: string;
    status: number; // 状态
    statusDesc: string;
    declareCallback: string; // 海关回执
    productId: string; // 料号,非保账册的料号，最高20位
    batch: string; // 批次号
    storageLocation: string; // 库位,库区-货架-层数-位数，库位包含（货架—层—位）,字母+数字+符号
    createTime: string; // 创建时间
    inOutAmount: number; // 出入库数量
    inOutTime: string; // 出入库时间
    itemType: string; // 料件性质,ENUM,数字0:原材料,数字1:成品,数字2:设备,数字3:残次品,数字4:边角料,数字5:半成品
    itemTypeDesc: string;
    customsBookNo: string; // 账册编号
    fbChecklistId: number; // 核放单id
    fbChecklistNo: string; // 核放单编号
    stockDeleteId: string; // 撤单申请编号,按照海关要求的规则创建单号，唯一,D+WMS系统十位标识编码+YYYYMMDD+8位流水,勾选已审批的出入库记录，点击删除出入库记录后生生成
    processResult: string; // 删单处理结果
    processCallback: string; // 处理结果-悬浮
    lastDeleteDeclareTime: string; // 推送删单申请时间
    itemNo?: string; // 料号
}

export const CustomsAccessDataItemStatus = {
    CREATED: 0, // 已创建
    APPROVE: 2, // 已审批
    PUSH_SUCCESS: 1, // 已推送
    UNUSUAL: 3, // 异常
    DELETED: 4, // 已删除
    VOID: 5, // 已作废
};
