import { DDYObject } from "react-single-app";
export const CustomsAccessConfigs = {
    baseInfo: {
        children: [
            {
                label: "出入库记录流水号",
                name: "inOutSeq",
                editEnable: false,
                type: "text",
            },
            {
                label: "核放单编号",
                name: "fbChecklistNo",
                editEnable: false,
                type: "text",
            },
            {
                label: "账册编号",
                name: "customsBookNo",
                editEnable: false,
                type: "text",
            },
            {
                label: "出入库类型",
                name: "businessTypeDesc",
                editEnable: false,
                type: "text",
            },
            {
                label: "业务类型",
                name: "fbBusinessTypeDesc",
                editEnable: false,
                type: "text",
            },
            {
                label: "出入库状态",
                name: "statusDesc",
                editEnable: false,
                type: "text",
            },
            {
                label: "创建时间",
                name: "createTime",
                editEnable: false,
                type: "text",
            },
            {
                label: "申报时间",
                name: "lastDeclareTime",
                editEnable: false,
                type: "text",
            },
            {
                label: "出入库时间",
                name: "inOutTime",
                editEnable: false,
                type: "text",
            },
            {
                label: "删除申请编号",
                name: "stockDeleteId",
                editEnable: false,
                type: "text",
            },
            {
                label: "删除申请时间",
                name: "lastDeleteDeclareTime",
                editEnable: false,
                type: "text",
            },
        ],
        label: "基本信息",
        name: "changeConsigneeInfo",
        isGroup: true,
        className: "declaration-manage changeConsigneeInfo",
    },
};

export const tableColumnsConfig: (delFn: (row: DDYObject, index: number) => void) => any[] = delFn => {
    return [
        {
            title: "序号",
            dataIndex: "index",
            width: 150,
            render: (text, record, index) => {
                return index + 1;
            },
        },
        {
            title: "料号",
            dataIndex: "productId",
            key: "productId",
            width: 100,
        },
        {
            title: "账册项号",
            dataIndex: "itemNo",
            width: 100,
        },
        {
            title: "商品编码",
            dataIndex: "goodsNo",
            width: 100,
        },
        {
            title: "商品名称",
            dataIndex: "goodsName",
            width: 100,
        },
        {
            title: "单位",
            dataIndex: "declareUnitDesc",
            width: 100,
        },
        {
            title: "数量",
            dataIndex: "inOutAmount",
            width: 200,
            editable: true,
            rules: [
                { required: true },
                {
                    validator(_, value) {
                        if (!value) return Promise.resolve();
                        if (value <= 0) return Promise.reject("请输入大于零的数");
                        if (!/^(\d{1,12}(\.\d{0,5})?|1000000000000)$/.test(value)) {
                            return Promise.reject("最多5位小数，12位整数");
                        }
                        return Promise.resolve();
                    },
                },
            ],
            // onChange: (value, item, _, form) => {

            //     console.log(value)
            // },
        },
        {
            title: "料材性质",
            dataIndex: "itemTypeDesc",
            key: "itemTypeDesc",
            width: 200,
        },
        {
            title: "库位",
            dataIndex: "storageLocation",
            key: "storageLocation",
            width: 200,
            editable: true,
            rules: [{ required: true }],
        },
        {
            title: "批次号",
            dataIndex: "batch",
            key: "batch",
            width: 100,
            editable: true,
            rules: [{ required: true }],
        },
        {
            title: "操作",
            dataIndex: "userName",
            width: 200,
            render: (_, row: DDYObject, index: number) => {
                return delFn(row, index);
            },
        },
    ];
};
