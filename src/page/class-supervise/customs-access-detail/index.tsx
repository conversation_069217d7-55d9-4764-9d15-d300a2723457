import React, { useEffect, useRef, useState } from "react";
//@ts-ignore
import { DDYObject, lib, ConfigFormCenter } from "react-single-app";
import { Descriptions, Row, Col, Button, Space, message } from "antd";
import { request } from "@dt/networks";
import NewTable from "../components/new-table";
import { BasicInformation, ItemConfig, searchConfig } from "./data";
import "./index.less";
import EditTableInfo, { EditableRef } from "../components/edit-table";
import { tableColumnsConfig, CustomsAccessConfigs } from "./config";
import { NonGuaranteedDetailBodyProps } from "../non-guaranteed-release/type";

export default (props: NonGuaranteedDetailBodyProps) => {
    const ConfigFormCenterRef = useRef(null);
    const [addOpen, setAddOpen] = useState(false);
    const [basicInformation, setBasicInformation] = useState({});
    const EditTableInfoRef = useRef<EditableRef>({} as EditableRef);
    const [datas, setDatas] = useState([]);
    const [detail, setDetail] = useState({});
    const [disableEdit, setDisableEdit] = useState(false);

    useEffect(() => {
        getDetail();
    }, []);
    //获取出入库单表体初始值
    const getDetail = () => {
        request.request({
            url: "/ccs/fbStockInOut/detail",
            data: { id: lib.getParam("id") },
            needMask: true,
            success: data => {
                setBasicInformation(data);
                setDetail(data);
                if (data.itemList.length != 0) {
                    setDatas([...data.itemList]);
                    ConfigFormCenterRef?.current?.setMergeDetail(data);
                }
            },
        });
    };
    const onConfigLoadSuccess = config => {
        config.baseInfo.children.map(item => {
            //@ts-ignore
            ConfigFormCenterRef.current?.initSelect(item);
        });
        if (detail) {
            //@ts-ignore
            ConfigFormCenterRef.current?.setMergeDetail(detail);
        }
    };
    const dataChange = (data: any[]) => {
        let totalBorrow = 0;
        let totalRepayment = 0;
        data.forEach(({ count, totalPrice }) => {
            totalBorrow += count ? Number(count) : 0;
            totalRepayment += totalPrice ? Number(totalPrice) : 0;
        });
    };

    //保存数量
    const saveNumber = () => {
        EditTableInfoRef.current.validatorAll().then(res => {
            const result = EditTableInfoRef.current.getDatas();
            result.forEach(i => {
                i.stockInOutId = lib.getParam("id");
            });
            request.request({
                url: "/ccs/fbStockInOut/editProductId",
                data: {
                    stockInOutId: lib.getParam("id"),
                    editProductList: result,
                },
                success: () => {
                    message.success("保存成功");
                    getDetail();
                },
            });
        });
    };

    const itemNoOk = (data: any, selectedRows: DDYObject[], obj: DDYObject): void => {
        if (selectedRows.length === 0) {
            return;
        }
        const result = EditTableInfoRef.current.getDatas();
        const currentProductIds = result.map(item => item.productId);
        const joinProductIds = selectedRows.map(item => item.productId);
        for (let i = 0; i < currentProductIds.length; i++) {
            for (let j = 0; j < joinProductIds.length; j++) {
                if (currentProductIds[i] === joinProductIds[j]) {
                    message.error("料号不可重复");
                    return;
                }
            }
        }

        const editProductList = selectedRows.map(i => {
            return {
                mergeRelationId: i.id,
                productId: i.productId,
                itemNo: i.itemNo,
                goodsNo: i.goodsNo,
                goodsName: i.goodsName,
                declareUnit: i.declareUnit,
                declareUnitDesc: i.declareUnitDesc,
                stockInOutId: lib.getParam("id"),
            };
        });
        EditTableInfoRef.current.validatorAll().then(res => {
            request.request({
                url: "/ccs/fbStockInOut/addProductId",
                data: {
                    stockInOutId: Number(lib.getParam("id")),
                    editProductList: editProductList,
                },
                success: () => {
                    message.success("添加成功");
                    setAddOpen(false);
                    getDetail();
                },
                fail: () => {},
            });
        });
    };

    const deleteRow = row => {
        request.request({
            url: "/ccs/fbStockInOut/deleteProductId",
            data: { id: row.id, stockInOutId: row.stockInOutId },
            success: () => {
                message.success("删除成功");
                let data = datas.filter((v, i) => {
                    return v.id !== row.id;
                });
                setDatas(data);
            },
            fail: () => {},
        });
    };
    const config = tableColumnsConfig(row => {
        return (
            <>
                {
                    <Button
                        type="link"
                        onClick={() => {
                            console.log(row);
                            deleteRow(row);
                        }}>
                        移除
                    </Button>
                }
            </>
        );
    });

    return (
        <>
            <NewTable
                modalTitle={"添加料号"}
                dataURL={"/ccs/mergeRelation/paging"}
                tableType={0}
                tableConfig={ItemConfig}
                defaultPagingParams={["customsBookId"]}
                searchConfig={searchConfig}
                onOk={itemNoOk}
                editrow={detail}
                onCancel={() => {
                    setAddOpen(false);
                }}
                visible={addOpen}
            />
            <div className="customs-access-detail">
                <ConfigFormCenter
                    ref={ConfigFormCenterRef}
                    confData={CustomsAccessConfigs}
                    onConfigLoadSuccess={onConfigLoadSuccess}
                />
                <Row justify={"space-between"} style={{ position: "relative", marginTop: 50 }}>
                    <Col>
                        <Descriptions
                            title="出入库单表体"
                            style={{ display: "inline-block", width: 150 }}></Descriptions>
                    </Col>
                    <Col>
                        <Space style={{ float: "right" }}>
                            <Button
                                type="primary"
                                onClick={() => {
                                    saveNumber();
                                }}>
                                保存数量
                            </Button>
                            <Button onClick={() => setAddOpen(true)}>添加料号</Button>
                        </Space>
                    </Col>
                </Row>

                <EditTableInfo
                    tableColumns={config}
                    dataSource={datas}
                    ref={EditTableInfoRef}
                    dataChange={dataChange}
                />
            </div>
        </>
    );
};
