import React, { useRef, useState, useEffect } from "react";
import { SearchList } from "@dt/components";
import { DDYObject, getConfigDataUtils, lib } from "react-single-app";
import { Button, Modal, message } from "antd";
import axios from "axios";
import NewModal from "@/components/NewModal";
import { request } from "@dt/networks";
import { InventoryManageDataItem } from "./type";
// import { MergeRelationUpdateConfig } from './config';
// import { MergeRelationDataItem } from './type';
export default () => {
    const SearchListRef = useRef(null);
    const selecteds = useRef([]);
    const getConfig = async () => {
        let url = getConfigDataUtils.getDataUrlByDataId(686);
        const res = await axios.get(url);
        return res.data.data;
    };

    const recordInventory = () => {
        if (selecteds.current.length === 0) {
            return message.warning("请选择数据");
        }
        request.request({
            url: "/ccs/fbInventory/declareInventory",
            data: {
                idList: selecteds.current,
            },
            success: () => {
                message.success("推送成功");
                SearchListRef.current.load();
            },
        });
    };

    const batchUpdateExport = () => {
        // let importExtendParamBase64 = window.btoa(importExtendParam)
        let url = `/excel/import-data?page_title=批量更新库位导入&code=${encodeURIComponent("IMPORT_FB_INVENTORY")}`;
        lib.openPage(url);
    };

    const adjustInventory = () => {
        let url = `/excel/import-data?page_title=调整库存导入&code=${encodeURIComponent("IMPORT_FB_INVENTORY_ADJUST")}`;
        lib.openPage(url);
    };

    return (
        <>
            <SearchList
                ref={SearchListRef}
                searchConditionConfig={{
                    size: "middle",
                }}
                headerMode={"sticky"}
                getConfig={getConfig}
                renderLeftOperation={() => {
                    return (
                        <>
                            {/* <Button type='primary' onClick={()=>{
                        asyncLocator()
                    }}>同步库位</Button> */}
                            <Button
                                type="primary"
                                onClick={() => {
                                    recordInventory();
                                }}>
                                申报库存记录
                            </Button>
                            <Button
                                type="primary"
                                onClick={() => {
                                    batchUpdateExport();
                                }}>
                                批量更新库位导入
                            </Button>
                        </>
                    );
                }}
                renderRightOperation={() => {
                    return (
                        <>
                            <Button
                                onClick={() => {
                                    adjustInventory();
                                }}>
                                调整库存导入
                            </Button>
                        </>
                    );
                }}
                tableCustomFun={{
                    operationFn: (row: InventoryManageDataItem, index: number) => {
                        return (
                            <div>
                                <Button
                                    size="small"
                                    type="link"
                                    onClick={() => {
                                        lib.openPage(
                                            `/ccs/inventory-detail?page_title=库存明细&id=${row.id}&productId=${row.productId}&goodsNo=${row.goodsNo}&customsBookNo=${row.customsBookNo}&goodsName=${row.goodsName}`,
                                        );
                                    }}>
                                    查看明细
                                </Button>
                            </div>
                        );
                    },
                }}
                // renderRightOperation={() => {
                //     return <>
                //         <Button onClick={() => {
                //             importData()
                //         }}>调整库存导入</Button>
                //     </>
                // }}
                onTableSelected={row => {
                    // selecteds.current = row;
                }}
            />
        </>
    );
};
