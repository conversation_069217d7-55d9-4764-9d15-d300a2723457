export interface InventoryManageProps {}

export interface InventoryManageDataItem {
    id: number;
    productId: string; //料号,非保账册的料号，最高20位
    itemNo: string; // 账册项号
    goodsNo: string; // 商品编码(同HsCode)
    customsBookNo: string; // 账册编号
    batch: string; // 批次号
    goodsName: string; //商品名称
    storageLocation: string; // 库位,库区-货架-层数-位数，库位包含（货架—层—位）,字母+数字+符号
    status: number; // 库存状态,enum
    goodsQuantity: number; // 存数量可用库存,15,5
    inQuantity: number; // 入库数量
    outQuantity: number; // 出库数量
    declareUnit: string; // 申报计量单位
    declareUnitDesc: string;
    itemType: string; // 料件性质,ENUM,数字0:原材料,数字1:成品,数字2:设备,数字3:残次品,数字4:边角料,数字5:半成品
    itemTypeDesc: string;
    storageTime: string; // 入库时间
    lastDeclareTime: string; // 最近库存申报时间
}
