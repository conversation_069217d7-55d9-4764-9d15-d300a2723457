import { DDYObject } from "react-single-app";
// import TColumns from "../components/edit-table";

export const NonGuaranteedConfigs = {
    baseInfo: {
        children: [
            {
                label: "企业核放单流水号",
                name: "sn",
                editEnable: false,
                type: "text",
                labelCol: { span: 10 },
                wrapperCol: { span: 14 },
                // customConfig: { style: { width: '50%' } },
            },
            {
                label: "核放单编号",
                name: "jobFormNo",
                editEnable: false,
                type: "text",
                labelCol: { span: 10 },
            },
            {
                label: "账册编号",
                name: "customsBookNo",
                editEnable: false,
                labelCol: { span: 10 },
                type: "text",
            },
            {
                label: "核放单类型",
                name: "typeDesc",
                editEnable: false,
                labelCol: { span: 10 },
                type: "text",
            },
            {
                label: "经营单位",
                name: "tradeCompanyName",
                editEnable: false,
                labelCol: { span: 10 },
                type: "text",
            },
            {
                label: "收货单位(货至单位)",
                name: "ownerCompanyName",
                editEnable: false,
                labelCol: { span: 10 },
                type: "text",
            },
            {
                label: "发货单位(货源单位)",
                name: "supplyCompanyName",
                editEnable: false,
                labelCol: { span: 10 },
                type: "text",
            },
            {
                label: "关联报关单号",
                name: "refCustomsDeclareNo",
                editEnable: false,
                labelCol: { span: 10 },
                type: "textInput",
                rules: [
                    {
                        validator(_, value) {
                            if (!value) return Promise.resolve();
                            if (!/\d*/.test(value) || value.length !== 16) {
                                return Promise.reject("关联报关单号必须是16位数字");
                            }
                            return Promise.resolve();
                        },
                    },
                ],
            },
            {
                label: "关联核放单号",
                name: "assCheckId",
                editEnable: false,
                labelCol: { span: 10 },
                type: "textInput",
                rules: [
                    {
                        validator(_, value) {
                            if (!value) return Promise.resolve();
                            if (!/([0-9a-zA-Z])$/.test(value)) {
                                return Promise.reject("核放单号只包含数字,字母");
                            }
                            return Promise.resolve();
                        },
                    },
                ],
            },
            {
                label: "包装种类",
                name: "wrapType",
                editEnable: false,
                labelCol: { span: 10 },
                type: "single-select",
                from: "/ccs/fbChecklist/listWrapType",
                list: [],
                rules: [{ required: true, message: "请填写包装种类" }],
            },
            {
                label: "毛重（kg）",
                name: "grossWeight",
                editEnable: false,
                labelCol: { span: 10 },
                type: "textInput",
                rules: [
                    { required: true, message: "请填写毛重（kg）" },
                    {
                        validator(_, value) {
                            if (!value) return Promise.resolve();
                            if (value <= 0) return Promise.reject("请输入大于零的数");
                            if (!/^(\d{1,12}(\.\d{0,5})?|1000000000000)$/.test(value)) {
                                return Promise.reject("最多5位小数，12位整数");
                            }
                            // if(!/^[0-9]{1,12}\.[0-9]{0,5}$/.test(value)){
                            //     return Promise.reject("最多5位小数，12位整数")
                            // }
                            return Promise.resolve();
                        },
                    },
                ],
            },
            {
                label: "净重（kg）",
                name: "netWeight",
                editEnable: false,
                labelCol: { span: 10 },
                type: "textInput",
                rules: [
                    { required: true, message: "请填写净重（kg）" },
                    {
                        validator(_, value) {
                            if (!value) return Promise.resolve();
                            if (value <= 0) return Promise.reject("请输入大于零的数");
                            if (!/^(\d{1,12}(\.\d{0,5})?|1000000000000)$/.test(value)) {
                                return Promise.reject("最多5位小数，12位整数");
                            }
                            return Promise.resolve();
                        },
                    },
                ],
            },
            {
                label: "车自重",
                name: "carWeight",
                editEnable: false,
                labelCol: { span: 10 },
                type: "textInput",
                rules: [
                    {
                        validator(_, value) {
                            if (!value) return Promise.resolve();
                            if (value <= 0) return Promise.reject("请输入大于零的数");
                            if (!/^(\d{1,12}(\.\d{0,5})?|1000000000000)$/.test(value)) {
                                return Promise.reject("最多5位小数，12位整数");
                            }
                            return Promise.resolve();
                        },
                    },
                ],
            },
            {
                label: "总重量",
                name: "totalWeight",
                editEnable: false,
                labelCol: { span: 10 },
                type: "textInput",
                rules: [
                    {
                        validator(_, value) {
                            if (!value) return Promise.resolve();
                            if (value <= 0) return Promise.reject("请输入大于零的数");
                            if (!/^(\d{1,12}(\.\d{0,5})?|1000000000000)$/.test(value)) {
                                return Promise.reject("最多5位小数，12位整数");
                            }
                            return Promise.resolve();
                        },
                    },
                ],
            },
            {
                label: "件数",
                name: "packNo",
                editEnable: false,
                labelCol: { span: 10 },
                type: "textInput",
                rules: [
                    { required: true, message: "请填写件数" },
                    {
                        validator(_, value) {
                            if (!value) return Promise.resolve();
                            if (!/^[0-9]{1,9}$/.test(value)) {
                                return Promise.reject("件数最多9位");
                            }
                            return Promise.resolve();
                        },
                    },
                ],
            },
            {
                label: "集装箱号",
                name: "containerNos",
                editEnable: false,
                labelCol: { span: 10 },
                type: "textInput",
                rules: [
                    {
                        validator(_, value) {
                            if (!value) return Promise.resolve();
                            if (!/([0-9a-zA-Z]|-)$/.test(value)) {
                                return Promise.reject("集装箱号只包含数字,字母和英文横杠");
                            }
                            return Promise.resolve();
                        },
                    },
                ],
            },

            {
                label: "折20尺集装箱个数",
                name: "fold20FeetContainerNum",
                editEnable: false,
                labelCol: { span: 10 },
                type: "textInput",
                rules: [
                    {
                        validator(_, value) {
                            if (!value) return Promise.resolve();
                            if (!/^[0-9]{1,9}$/.test(value)) {
                                return Promise.reject("折20尺集装箱个数最多9位");
                            }
                            return Promise.resolve();
                        },
                    },
                ],
            },
            {
                label: "集装箱自然箱个数",
                name: "naturalContainerNum",
                editEnable: false,
                labelCol: { span: 10 },
                type: "textInput",
                rules: [
                    {
                        validator(_, value) {
                            if (!value) return Promise.resolve();
                            if (!/^[0-9]{1,9}$/.test(value)) {
                                return Promise.reject("集装箱自然箱个数最多9位");
                            }
                            return Promise.resolve();
                        },
                    },
                ],
            },
            {
                label: "申请人",
                name: "applicant",
                editEnable: false,
                labelCol: { span: 10 },
                type: "text",
            },
            {
                label: "车牌号",
                name: "vehicleInfos",
                editEnable: false,
                labelCol: { span: 10 },
                type: "textarea",
                rules: [
                    { required: true, message: "请填写车牌号" },
                    // /^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领A-Z]{1}[A-Z]{1}[A-Z0-9]{4}[A-Z0-9挂学警港澳]{1}$/
                    // {
                    //     validator(_, value) {
                    //         if (!value) return Promise.resolve();
                    //         if (value.indexOf(",") > -1) {
                    //             const arr = value.split(",");
                    //             for (let i = 0; i < arr.length; i++) {
                    //                 if (
                    //                     !/^([京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领A-Z]{1}[a-zA-Z](([A-Z]((?![IO])[a-zA-Z0-9](?![IO]))[0-9]{4})|([0-9]{5}[A-Z]))|[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领A-Z]{1}[A-Z]{1}[A-Z0-9]{4}[A-Z0-9挂学警港澳]{1})$/.test(
                    //                         arr[i],
                    //                     )
                    //                 ) {
                    //                     return Promise.reject("车牌号不合法");
                    //                 }
                    //             }
                    //         } else {
                    //             if (
                    //                 !/^([京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领A-Z]{1}[a-zA-Z](([A-Z]((?![IO])[a-zA-Z0-9](?![IO]))[0-9]{4})|([0-9]{5}[A-Z]))|[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领A-Z]{1}[A-Z]{1}[A-Z0-9]{4}[A-Z0-9挂学警港澳]{1})$/.test(
                    //                     value,
                    //                 )
                    //             ) {
                    //                 return Promise.reject("车牌号不合法");
                    //             }
                    //         }
                    //         return Promise.resolve();
                    //     },
                    // },
                ],
                // customConfig: { style: { width: '100%' }, maxLength: 50 },
            },
            {
                label: "电子车牌号",
                name: "virCarCode",
                editEnable: false,
                labelCol: { span: 10 },
                type: "textarea",
                rules: [
                    { required: true, message: "请填写电子车牌号" },
                    // /^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领A-Z]{1}[A-Z]{1}[A-Z0-9]{4}[A-Z0-9挂学警港澳]{1}$/
                    // {
                    //     validator(_, value) {
                    //         if (!value) return Promise.resolve();
                    //         if (value.indexOf(",") > -1) {
                    //             const arr = value.split(",");
                    //             for (let i = 0; i < arr.length; i++) {
                    //                 if (
                    //                     !/^([京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领A-Z]{1}[a-zA-Z](([A-Z]((?![IO])[a-zA-Z0-9](?![IO]))[0-9]{4})|([0-9]{5}[A-Z]))|[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领A-Z]{1}[A-Z]{1}[A-Z0-9]{4}[A-Z0-9挂学警港澳]{1})$/.test(
                    //                         arr[i],
                    //                     )
                    //                 ) {
                    //                     return Promise.reject("车牌号不合法");
                    //                 }
                    //             }
                    //         } else {
                    //             if (
                    //                 !/^([京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领A-Z]{1}[a-zA-Z](([A-Z]((?![IO])[a-zA-Z0-9](?![IO]))[0-9]{4})|([0-9]{5}[A-Z]))|[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领A-Z]{1}[A-Z]{1}[A-Z0-9]{4}[A-Z0-9挂学警港澳]{1})$/.test(
                    //                     value,
                    //                 )
                    //             ) {
                    //                 return Promise.reject("车牌号不合法");
                    //             }
                    //         }
                    //         return Promise.resolve();
                    //     },
                    // },
                ],
                // customConfig: { style: { width: '100%' }, maxLength: 50 },
            },
            {
                label: "总价",
                name: "totalPrice",
                editEnable: false,
                labelCol: { span: 10 },
                type: "text",
                // customConfig: { style: { width: '100%' }, maxLength: 50 },
            },
            {
                label: "创建时间",
                name: "createTime",
                editEnable: false,
                labelCol: { span: 10 },
                type: "text",
                // customConfig: { style: { width: '100%' }, maxLength: 50 },
            },
            {
                label: "状态",
                name: "statusDesc",
                editEnable: false,
                labelCol: { span: 10 },
                type: "text",
                // customConfig: { style: { width: '100%' }, maxLength: 50 },
            },
            {
                label: "过卡时间",
                name: "passGateTime",
                editEnable: false,
                labelCol: { span: 10 },
                type: "text",
                // customConfig: { style: { width: '100%' }, maxLength: 50 },
            },
            {
                label: "分送集报号",
                name: "dbcdNo",
                editEnable: false,
                labelCol: { span: 10 },
                type: "textInput",
                rules: [
                    {
                        validator(_, value) {
                            if (!value) return Promise.resolve();
                            if (!/([0-9a-zA-Z])$/.test(value)) {
                                return Promise.reject("分送集报号只包含数字,字母");
                            }
                            return Promise.resolve();
                        },
                    },
                ],
            },
            {
                label: "标记唛码及备注",
                name: "remark",
                editEnable: false,
                labelCol: { span: 10 },
                type: "textarea",
                rules: [
                    {
                        validator(_, value) {
                            if (!value) return Promise.resolve();
                            if (!/[\u4e00-\u9fa5_a-zA-Z0-9_]{1,512}/.test(value)) {
                                return Promise.reject("只能由中文、英文、数字组成");
                            }
                            return Promise.resolve();
                        },
                    },
                    {
                        max: 512,
                    },
                ],
                // customConfig: { style: { width: '50%' }, maxLength: 50 },
            },
            {
                label: "空车照/磅单",
                name: "emptyCarPicUrl",
                editEnable: false,
                labelCol: { span: 10 },
                type: "uploader",
                customConfig: {
                    listType: "text",
                    maxCount: 1,
                    size: 2,
                    accept: ".jpg,.jpeg,.png",
                },
                // customConfig: { style: { width: '50%' }, maxLength: 50 },
            },
        ],
        label: "核放单表头",
        name: "changeConsigneeInfo",
        isGroup: true,
        className: "declaration-manage changeConsigneeInfo",
    },
};

export const guaranteedLogCongfig = [
    {
        title: "操作时间",
        dataIndex: "createTime",
        width: 300,
    },
    {
        title: "核放单状态",
        dataIndex: "fbChecklistStatus",
        width: 200,
    },
    {
        title: "日志描述",
        dataIndex: "detailInfo",
        width: 600,
    },
    {
        title: "操作人",
        dataIndex: "operator",
        width: 200,
    },
];

export const tableColumnsConfig: (delFn: (row: DDYObject, index: number) => void) => any[] = delFn => {
    return [
        {
            title: "序号",
            dataIndex: "index",
            width: 150,
            render: (text, record, index) => {
                return index + 1;
            },
        },
        {
            title: "料号",
            dataIndex: "productId",
            width: 150,
        },
        {
            title: "账册项号",
            dataIndex: "itemNo",
            width: 150,
        },
        {
            title: "商品编码",
            dataIndex: "goodsNo",
            width: 150,
        },
        {
            title: "商品名称",
            dataIndex: "goodsName",
            width: 150,
        },
        {
            title: "单位",
            dataIndex: "declareUnitDesc",
            width: 150,
        },
        {
            title: "数量",
            dataIndex: "count",
            width: 250,
            editable: true,
            rules: [
                { required: true },
                {
                    validator(_, value) {
                        if (!value) return Promise.resolve();
                        if (value <= 0) return Promise.reject("请输入大于零的数");
                        if (!/^(\d{1,12}(\.\d{0,5})?|1000000000000)$/.test(value)) {
                            return Promise.reject("最多5位小数，12位整数");
                        }
                        return Promise.resolve();
                    },
                },
            ],
            onChange: (value, item, _, form) => {
                if (form && value > 0) {
                    const values = form.getFieldsValue();
                    return { totalPrice: values.unitPrice * value || 0 };
                }
            },
        },
        {
            title: "单价",
            dataIndex: "unitPrice",
            width: 250,
            editable: true,
            onChange: (value, item, _, form) => {
                if (form && value > 0) {
                    const values = form.getFieldsValue();
                    return { totalPrice: values.count * value || 0 };
                }
            },
            rules: [
                // { required: true },
                {
                    validator(_, value) {
                        if (!value) return Promise.resolve();
                        if (value <= 0) return Promise.reject("请输入大于零的数");
                        if (!/^(\d{1,12}(\.\d{0,5})?|1000000000000)$/.test(value)) {
                            return Promise.reject("最多5位小数，12位整数");
                        }
                        return Promise.resolve();
                    },
                },
            ],
        },
        {
            title: "总价",
            dataIndex: "totalPrice",
            width: 150,
            editable: true,
            disabled: true,
        },
        {
            title: "币制",
            dataIndex: "currencyTypeDesc",
            width: 150,
        },
        {
            title: "出入库记录流水号",
            dataIndex: "inOutSeq",
            width: 150,
        },
        {
            title: "商品规格",
            dataIndex: "goodsModel",
            width: 150,
        },
        {
            title: "涉及知识产权",
            dataIndex: "iPRFlagDesc",
            width: 150,
        },
        {
            title: "涉证",
            dataIndex: "licenseFlagDesc",
            width: 150,
        },
        {
            title: "涉税",
            dataIndex: "taxFlagDesc",
            width: 150,
        },
        {
            title: "自主品牌",
            dataIndex: "independentBrandFlagDesc",
            width: 150,
        },
        {
            title: "料材性质",
            dataIndex: "itemTypeDesc",
            width: 150,
        },
        {
            title: "操作",
            dataIndex: "userName",
            width: 200,
            render: (_, row: DDYObject, index: number) => {
                return delFn(row, index);
            },
        },
    ];
};
