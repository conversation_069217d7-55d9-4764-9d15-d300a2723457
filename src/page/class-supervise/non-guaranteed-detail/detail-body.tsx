import React, { useEffect, useRef, useState } from "react";
import { NonGuaranteedDetailBodyProps, NonGuaranteedStatus } from "../non-guaranteed-release/type";
import { Tabs, Descriptions, Row, Space, Button, Col, Table, message } from "antd";
import { guaranteedLogCongfig, tableColumnsConfig } from "./config";
import EditTableInfo, { EditableRef } from "../components/edit-table";
import NewTable from "../components/new-table";
import { ItemConfig, searchConfig } from "../customs-access/config";
import { DDYObject, lib } from "react-single-app";
import { request } from "@dt/networks";

export default (props: NonGuaranteedDetailBodyProps) => {
    const [addOpen, setAddOpen] = useState(false);
    const [totalCount, setTotalCount] = useState(0);
    const [totalRepayment, setTotalRepayment] = useState(0);

    const [datas, setDatas] = useState([]);
    const [logs, setLogs] = useState([]);
    const EditTableInfoRef = useRef<EditableRef>({} as EditableRef);

    const [pagination, setPagination] = useState({
        currentPage: 1,
        pageSize: 10,
        page: 1,
        totalCount: 0,
        totalPage: 0,
    });

    function itemNoOk(data: any, selectedRows: DDYObject[], selectedData: DDYObject): void {
        if (selectedRows.length === 0) {
            return;
        }
        const result = EditTableInfoRef.current.getDatas();
        const currentProductIds = result.map(item => item.productId);
        const joinProductIds = selectedRows.map(item => item.productId);
        for (let i = 0; i < currentProductIds.length; i++) {
            for (let j = 0; j < joinProductIds.length; j++) {
                if (currentProductIds[i] === joinProductIds[j]) {
                    message.error("料号不可重复");
                    return;
                }
            }
        }
        setDatas([...result.concat(selectedRows)]);
        setAddOpen(false);
    }

    const saveCount = () => {
        EditTableInfoRef.current.validatorAll().then(res => {
            const result = EditTableInfoRef.current.getDatas();
            request.request({
                url: "/ccs/fbChecklist/saveItem",
                data: {
                    id: lib.getParam("id"),
                    itemVOList: result,
                },
                success: () => {
                    message.success("保存成功");
                    props.reload(true);
                },
            });
        });
    };

    useEffect(() => {
        if (Array.isArray(props.datas)) {
            setDatas(props.datas);
            dataChange(props.datas);
        }
    }, [props.datas]);

    useEffect(() => {
        getLogLists({ pageSize: 10, page: 1 });
        console.log(props.datas);
    }, []);

    const getLogLists = params => {
        request.request({
            url: "/ccs/fbChecklist/pagingTrackLog",
            data: { ...params, currentPage: params.page, id: lib.getParam("id") },
            success: data => {
                setLogs(data.dataList);
                setPagination(data.page);
            },
        });
    };
    const config = tableColumnsConfig((row, index) => {
        return (
            <>
                {[
                    NonGuaranteedStatus.CREATED,
                    NonGuaranteedStatus.CACHE_FAIL,
                    NonGuaranteedStatus.CACHE_SUCCESS,
                    NonGuaranteedStatus.UNUSUAL,
                ].includes(props.status) && (
                    <Button
                        type="link"
                        onClick={() => {
                            EditTableInfoRef.current.delData(index);
                        }}>
                        移除
                    </Button>
                )}
            </>
        );
    });
    const dataChange = (data: any[]) => {
        let totalBorrow = 0;
        let totalRepayment = 0;
        data.forEach(({ count, totalPrice }) => {
            totalBorrow += count ? Number(count) : 0;
            totalRepayment += totalPrice ? Number(totalPrice) : 0;
        });
        setTotalCount(totalBorrow);
        setTotalRepayment(totalRepayment);
    };
    return (
        <>
            <Row justify={"space-between"} style={{ position: "relative" }}>
                <Col>
                    <Descriptions style={{ display: "flex" }} title="核放单表体"></Descriptions>
                </Col>

                {[
                    NonGuaranteedStatus.CREATED,
                    NonGuaranteedStatus.CACHE_FAIL,
                    NonGuaranteedStatus.CACHE_SUCCESS,
                    NonGuaranteedStatus.UNUSUAL,
                ].includes(props.status) && (
                    <Space style={{ position: "absolute", top: 0, right: 0 }}>
                        <Button
                            onClick={() => {
                                saveCount();
                            }}>
                            保存数量
                        </Button>{" "}
                        <Button
                            onClick={() => {
                                setAddOpen(true);
                            }}>
                            添加料号
                        </Button>
                    </Space>
                )}
            </Row>

            <Tabs
                onChange={() => {
                    getLogLists({ pageSize: 10, page: 1 });
                    props.reload();
                }}>
                <Tabs.TabPane tab="核放单表体" key="item-1">
                    <EditTableInfo
                        tableColumns={config}
                        dataSource={datas}
                        ref={EditTableInfoRef}
                        dataChange={dataChange}
                        scroll={{ x: 2500 }}
                    />
                    <Space style={{ padding: "10px" }}>
                        总计： 数量（{totalCount || 0}）,总价（{totalRepayment || 0}）
                    </Space>
                </Tabs.TabPane>
                <Tabs.TabPane tab="核放单日志" key="item-2">
                    <Table
                        columns={guaranteedLogCongfig}
                        dataSource={logs}
                        pagination={{
                            // ...pagination,
                            total: pagination.totalCount,
                            current: pagination.currentPage,
                            pageSize: pagination.pageSize,
                            onChange: (page, pageSize) => {
                                getLogLists({ pageSize, page });
                            },
                        }}
                    />
                </Tabs.TabPane>
            </Tabs>
            <NewTable
                modalTitle={"添加料号"}
                dataURL={"/ccs/mergeRelation/paging"}
                tableType={0}
                tableConfig={ItemConfig}
                defaultPagingParams={["customsBookId"]}
                searchConfig={searchConfig}
                onOk={itemNoOk}
                editrow={props.detail}
                onCancel={() => {
                    setAddOpen(false);
                }}
                visible={addOpen}
            />
        </>
    );
};
