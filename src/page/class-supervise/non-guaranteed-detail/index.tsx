import React, { useRef, useEffect, useState } from "react";
import { INonGuaranteedDetail, NonGuaranteedDetail } from "./type";
import { Button, message } from "antd";
//@ts-ignore
import { ConfigFormCenter, lib } from "react-single-app";
import { NonGuaranteedConfigs } from "./config";
import "./index.less";
import DetailBody from "./detail-body";
import { request } from "@dt/networks";
import { NonGuaranteedStatus, NonGuaranteedType } from "../non-guaranteed-release/type";
export default (props: NonGuaranteedDetail) => {
    const ConfigFormCenterRef = useRef(null);
    const [disableEdit, setDisableEdit] = useState(false);
    const [detail, setDetail] = useState<INonGuaranteedDetail>({} as INonGuaranteedDetail);
    const save = () => {
        ConfigFormCenterRef.current?.getForm.validateFields().then(res => {
            ConfigFormCenterRef.current?.submitForm();
        });
    };

    const beforeSubmit = data => {
        console.log(data.emptyCarPicUrl);
        return {
            ...data,
            id: lib.getParam("id"),
            emptyCarPicUrl: data.emptyCarPicUrl?.[0]?.url,
            emptyCarPicName: data.emptyCarPicUrl?.[0]?.name,
        };
    };

    const onSubmitSuccess = () => {
        message.success("保存成功");
        setDisableEdit(!disableEdit);
        getDetail();
    };

    const getDetail = (unAllBol?: boolean) => {
        request.request({
            url: "/ccs/fbChecklist/detail",
            data: { id: lib.getParam("id") },
            needMask: true,
            success: data => {
                setDetail(data);
                if (!unAllBol) {
                    if (data.emptyCarPicUrl) {
                        data.emptyCarPicUrl = [{ url: data.emptyCarPicUrl, name: data.emptyCarPicName }];
                    } else {
                        data.emptyCarPicUrl = [];
                    }

                    //@ts-ignore
                    ConfigFormCenterRef?.current?.setMergeDetail(data);
                }
            },
        });
    };

    useEffect(() => {
        getDetail();
    }, []);

    const onConfigLoadSuccess = config => {
        config.baseInfo.children.map(item => {
            if (item.type === "single-select" && !item.fromParam) {
                //@ts-ignore
                ConfigFormCenterRef.current?.initSelect(item);
            }
        });
        if (detail) {
            //@ts-ignore
            ConfigFormCenterRef.current?.setMergeDetail(detail);
        }
    };
    const onSetDetailSuccess = (fieldsValue, changConfigCount) => {
        if (changConfigCount == 2) {
            // let config = ref.current.config;
            // let secondUnit = ref.current?.getFormFiled("secondUnit");
            // let secondUnitAmount = ref.current?.getFormFiled("secondUnitAmount");
            NonGuaranteedConfigs.baseInfo.children.map(item => {
                if (item.name === "emptyCarPicUrl") {
                    console.log(fieldsValue, fieldsValue.type, detail);
                    item.rules = [
                        NonGuaranteedType.FM,
                        NonGuaranteedType.FN,
                        NonGuaranteedType.FEI,
                        NonGuaranteedType.FEO,
                    ].includes(detail?.type)
                        ? [
                              {
                                  required: true,
                                  message: "请上传空车照/磅单",
                              },
                          ]
                        : [];
                }
            });
            ConfigFormCenterRef.current.changeConfig(NonGuaranteedConfigs);
        }
    };

    return (
        <div className="non-guaranteed-detail">
            <div className="right-btn-rect">
                {[
                    NonGuaranteedStatus.CREATED,
                    NonGuaranteedStatus.CACHE_FAIL,
                    NonGuaranteedStatus.CACHE_SUCCESS,
                    NonGuaranteedStatus.UNUSUAL,
                ].includes(detail.status) && (
                    <Button
                        onClick={() => {
                            if (disableEdit) {
                                save();
                            } else {
                                setDisableEdit(!disableEdit);
                            }
                        }}>
                        {disableEdit ? "保存" : "编辑"}
                    </Button>
                )}
            </div>
            <ConfigFormCenter
                ref={ConfigFormCenterRef}
                // onSinglesSelectChange={onSinglesSelectChange}
                disableEdit={!disableEdit}
                submitUrl={"/ccs/fbChecklist/saveHead"}
                beforeSubmit={beforeSubmit}
                // beforeSetDetail={beforeSetDetail}
                // code={code}
                confData={NonGuaranteedConfigs}
                onConfigLoadSuccess={onConfigLoadSuccess}
                onSetDetailSuccess={onSetDetailSuccess}
                // submitUrl={'/ccs/checklist/upset'}
                onSubmitSuccess={onSubmitSuccess}
            />
            <DetailBody
                datas={detail.itemVOS}
                status={detail.status}
                reload={unAllBol => {
                    getDetail(unAllBol);
                }}
                detail={detail}
            />
        </div>
    );
};
