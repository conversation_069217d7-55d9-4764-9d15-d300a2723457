export interface NonGuaranteedDetail {
    id: string;
    jobFormId: string;
}
export interface NonGuaranteedDetailBodyProps {}

export interface INonGuaranteedDetail {
    id: string;
    jobFormId: string;
    customsBookId: string;
    customsBookNo: string;
    type: string; //核放单类型
    typeDesc: string;
    status: number; // 核放单状态
    statusDesc: string;
    vehicleInfos: string; // 车牌号
    createTime: string; // 创建时间
    passGateTime: string; // 过卡时间
    packNo: number; // 件数
    grossWeight: number; // 毛重
    netWeight: number; // 净重
    tradeCompanyId: number; // 经营单位
    tradeCompanyCode: string;
    tradeCompanyName: string;
    ownerCompanyId: number; // 收货单位（货至单位）
    ownerCompanyCode: string;
    ownerCompanyName: string;
    supplyCompanyId: number; // 发货单位（货源单位）
    supplyCompanyCode: string;
    supplyCompanyName: string;
    refCustomsDeclareNo: string; // 关联报关单号
    wrapType: number; // 包装种类Code
    wrapTypeDesc: string;
    containerNos: string; //集装箱号
    fold20FeetContainerNum: number; // 折20尺集装箱个数
    naturalContainerNum: number; // 集装箱自然个数
    remark: string; // 标记唛码及备注
    applicant: string; // 申请人
    itemVOS: INonGuaranteedDetailItemVOS[];
}

export interface INonGuaranteedDetailItemVOS {
    productId: string; // 料号
    itemNo: string; // 账册项号取归并关系
    goodsNo: string; // 商品编码
    goodsName: string; // 商品名称

    unit: string; // 单位
    unitDesc: string;
    count: number; //数量
    unitPrice: number; // 单价
    totalPrice: number; // 总价
    currency: string; //
}
