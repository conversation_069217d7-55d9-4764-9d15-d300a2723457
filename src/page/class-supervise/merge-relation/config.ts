export const MergeRelationUpdateConfig = (renderTips: () => JSX.Element) => [
    {
        labelName: "",
        render: renderTips,
        editEnable: false,
        type: "TEXT",
        required: true,
        labelCol: { span: 0 },
        wrapperCol: { span: 24 },
    },
    {
        labelName: "账册项号",
        labelKey: "itemNo",
        editEnable: false,
        type: "INPUT",
        required: true,
        labelCol: { span: 6 },
    },
    {
        labelName: "商品编码",
        labelKey: "goodsNo",
        editEnable: false,
        type: "SELECT",
        required: true,
        showValue: true,
        list: [],
        ccs: "/ccs/customs/listHs",
        labelCol: { span: 6 },
    },
    {
        labelName: "商品名称",
        labelKey: "goodsName",
        editEnable: false,
        type: "INPUT",
        required: true,
        labelCol: { span: 6 },
    },
    {
        labelName: "商品规格",
        labelKey: "goodsSpec",
        editEnable: false,
        type: "INPUT",
        labelCol: { span: 6 },
    },
    {
        labelName: "申报单位",
        labelKey: "declareUnit",
        editEnable: false,
        type: "SELECT",
        required: true,
        showValue: true,
        list: [],
        ccs: "/ccs/customs/listUom",
        labelCol: { span: 6 },
    },
    {
        labelName: "币制",
        labelKey: "currencyType",
        editEnable: false,
        type: "SELECT",
        showValue: true,
        required: true,
        list: [],
        ccs: "/ccs/customs/listCurrency",
        labelCol: { span: 6 },
    },
    {
        labelName: "第一单位",
        labelKey: "firstUnit",
        editEnable: false,
        type: "SELECT",
        showValue: true,
        required: true,
        list: [],
        ccs: "/ccs/customs/listUom",
        labelCol: { span: 6 },
    },
    {
        labelName: "第二单位",
        labelKey: "secondUnit",
        editEnable: false,
        type: "SELECT",
        showValue: true,
        list: [],
        ccs: "/ccs/customs/listUom",
        labelCol: { span: 6 },
        wrapperCol: { span: 6 },
    },
];
