import React, { useRef, useState, useEffect } from "react";
import { SearchList } from "@dt/components";
import { DDYObject, getConfigDataUtils, lib } from "react-single-app";
import { Button, Modal, message } from "antd";
import axios from "axios";
import NewModal from "@/components/NewModal";
import { request } from "@dt/networks";
import { MergeRelationUpdateConfig } from "./config";
import { MergeRelationDataItem } from "./type";
import { CopyOutlined } from "@ant-design/icons";
import { handleCopy } from "../../../common/utils";

export default () => {
    const [updateOpen, setUpdateOpen] = useState(false);
    const [editrow, setEditrow] = useState<MergeRelationDataItem>({} as MergeRelationDataItem);
    const SearchListRef = useRef(null);
    const [configs, setConfigs] = useState(
        MergeRelationUpdateConfig(() => (
            <div style={{ color: "red", textAlign: "center" }}>请谨慎修改账册信息，修改时需确认与海关保持一致</div>
        )),
    );
    const selecteds = useRef([]);
    const getConfig = async () => {
        let url = getConfigDataUtils.getDataUrlByDataId(685);
        const res = await axios.get(url);
        return res.data.data;
    };

    const dels = () => {
        if (selecteds.current.length <= 0) {
            return message.warn("请选择数据");
        }
        Modal.confirm({
            title: "确认删除归并关系吗？",
            onOk: () => {
                request.request({
                    url: "/ccs/mergeRelation/delete",
                    data: {
                        idList: selecteds.current,
                    },
                    needMask: true,
                    success: () => {
                        message.success("删除成功");
                        SearchListRef.current.load();
                    },
                });
            },
        });
    };

    const importData = () => {};

    const update = (data: MergeRelationDataItem) => {
        setUpdateOpen(true);
        setEditrow(data);
    };

    const del = (data: MergeRelationDataItem) => {
        Modal.confirm({
            title: "确认删除吗？",
            onOk: () => {
                request.request({
                    url: "/ccs/mergeRelation/delete",
                    data: {
                        idList: [data.id],
                    },
                    needMask: true,
                    success: () => {
                        message.success("删除成功");
                        SearchListRef.current.load();
                    },
                });
            },
        });
    };

    const updateItem = (params: MergeRelationDataItem) => {
        request.request({
            url: "/ccs/mergeRelation/update",
            data: { ...params, id: editrow.id },
            success: () => {
                message.success("修改成功");
                setEditrow({} as MergeRelationDataItem);
                setUpdateOpen(false);
                SearchListRef.current.load();
            },
        });
    };

    const getDatas = () => {
        const arr = [];
        configs.forEach((item, index) => {
            if (item.ccs) {
                arr.push(
                    new Promise((resolve, reject) => {
                        request.request({
                            url: item.ccs,
                            data: {},
                            success: data => {
                                resolve({
                                    data,
                                    index,
                                });
                            },
                            fail: () => {
                                reject();
                            },
                        });
                    }),
                );
            }
        });

        Promise.all(arr)
            .then(res => {
                res.map(item => {
                    configs[item.index].list = item.data;
                });
                setConfigs([...configs]);
            })
            .catch(err => {});
    };

    useEffect(() => {
        getDatas();
    }, []);

    return (
        <>
            <SearchList
                ref={SearchListRef}
                searchConditionConfig={{
                    size: "middle",
                }}
                headerMode={"sticky"}
                getConfig={getConfig}
                renderLeftOperation={() => {
                    return (
                        <>
                            <Button
                                type="primary"
                                onClick={() => {
                                    dels();
                                }}>
                                批量删除
                            </Button>
                        </>
                    );
                }}
                tableCustomFun={{
                    operationFn: (row: MergeRelationDataItem, index: number) => {
                        return (
                            <div>
                                <Button
                                    size="small"
                                    type="link"
                                    onClick={() => {
                                        update(row);
                                    }}>
                                    修改
                                </Button>
                                {/* [
                            {id:'0',name:'停用'},
                            {id:'1',name:'消除'},
                            {id:'2',name:'正常'}
                        ] */}
                                {row.useFlag === "1" && (
                                    <Button
                                        size="small"
                                        type="link"
                                        onClick={() => {
                                            del(row);
                                        }}>
                                        删除
                                    </Button>
                                )}
                            </div>
                        );
                    },
                    productFn: (row: MergeRelationDataItem, index: number) => {
                        return (
                            <>
                                <Button
                                    size="small"
                                    type="link"
                                    onClick={() => {
                                        lib.openPage(`/merge-relation-detail?page_title=归并关系详情&id=${row.id}`);
                                    }}>
                                    {row.productId}
                                </Button>
                                <CopyOutlined onClick={() => handleCopy(row.productId)} />
                            </>
                        );
                    },
                }}
                renderModal={() => {
                    return (
                        <>
                            <NewModal
                                visible={updateOpen}
                                onOk={(obj: MergeRelationDataItem) => {
                                    updateItem(obj);
                                }}
                                configList={configs}
                                title={`修改（料号：${editrow.productId}）`}
                                editRow={editrow}
                                onCancel={() => {
                                    setUpdateOpen(false);
                                }}
                                modalStyle={{ width: 600 }}
                                okText={"保存"}
                            />
                        </>
                    );
                }}
                onTableSelected={row => {
                    selecteds.current = row;
                }}
            />
        </>
    );
};
