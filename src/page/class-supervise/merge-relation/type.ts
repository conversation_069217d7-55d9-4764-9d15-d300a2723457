export interface MergeRelationProps {}

export interface MergeRelationDataItem {
    id: number;
    productId: string;
    // 账册项号,料件项号，海关辅助系统上按照商品编码创建项号
    itemNo: number;
    // 商品编码(同HsCode)
    goodsNo: string;
    customsBookId: number;
    // 账册编号
    customsBookNo: string;
    // 商品名称
    goodsName: string;
    // 商品规格
    goodsSpec: string;
    // 申报计量单位
    declareUnit: string;
    declareUnitDesc: string;
    // 法定第一单位
    firstUnit: string;
    firstUnitDesc: string;
    // 法定第二单位
    secondUnit: string;
    secondUnitDesc: string;
    // 币制
    currencyType: string;
    currencyTypeDesc: string;
    // 料件性质
    itemType: string;
    itemTypeDesc: string;
    // 状态
    useFlag: string;
    useFlagDesc: string;

    tenantryId: string;
    createBy: number;
    updateBy: number;
    createTime: string;
    updateTime: string;
    deleted: number;
}
