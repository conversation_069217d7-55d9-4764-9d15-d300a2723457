import React from "react";
import { Config<PERSON>enter, lib, SearchList, getConfigDataUtils } from "react-single-app";
import { Button, Space, Modal } from "antd";
import { getPrivileges } from "../common/auth";

class App extends SearchList {
    constructor(props) {
        super(props);
    }
    // 934
    componentDidMount() {
        this.getPrivilegesCode();
    }
    getConfig() {
        return axios.get(getConfigDataUtils.getDataUrlByDataId(934)).then(e => e.data.data);
    }
    //判断权限
    getPrivilegesCode() {
        getPrivileges().then(list => {
            ["HS-CODE-LIST-DELETE"].map(code => {
                switch (code) {
                    case "HS-CODE-LIST-DELETE":
                        this.setState({ hsCodeListDelete: !!list.find(item => item.code == code) });
                        break;
                    default:
                        break;
                }
            });
        });
    }

    renderLeftOperation() {
        return (
            <React.Fragment>
                <Button
                    type="primary"
                    onClick={() => {
                        {
                            lib.openPage(`/hs-code-detail?pageTitle=新增HS编码&type=add`, () => {
                                this.load(true);
                            });
                        }
                    }}>
                    新增
                </Button>
            </React.Fragment>
        );
    }

    delete(id) {
        Modal.confirm({
            title: "提示",
            content: "确定删除吗？",
            onOk: () => {
                return new Promise((resolve, reject) => {
                    lib.request({
                        url: "/ccs/hsCode/delete",
                        needMask: true,
                        data: { id },
                        success: res => {
                            resolve(res);
                            this.load();
                            message.success("删除成功");
                        },
                        fail: e => {
                            reject(e);
                        },
                    });
                });
            },
        });
    }

    myOperation(row) {
        let { hsCodeListDelete } = this.state;
        return (
            <React.Fragment>
                <Space>
                    <a
                        onClick={() =>
                            lib.openPage(`/hs-code-detail?pageTitle=查看HS编码&id=${row.id}&type=watch`, () => {
                                this.load(true);
                            })
                        }>
                        查看
                    </a>
                    <a
                        onClick={() =>
                            lib.openPage(`/hs-code-detail?pageTitle=编辑HS编码&id=${row.id}&type=edit`, () => {
                                this.load(true);
                            })
                        }>
                        编辑
                    </a>
                    {hsCodeListDelete && (
                        <a
                            onClick={() => {
                                this.delete(row.id);
                            }}>
                            删除
                        </a>
                    )}
                </Space>
            </React.Fragment>
        );
    }
}

export default App;
