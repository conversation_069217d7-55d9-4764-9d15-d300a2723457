import React, { useState, useEffect } from "react";
import { Config<PERSON>enter, SearchList, Uploader, lib, event, getConfigDataUtils } from "react-single-app";
import { Checkbox, Button, Modal, Drawer, Descriptions, Table, Input, Select, Form, message, Tooltip } from "antd";
import axios from "axios";
import { QuestionCircleOutlined } from "@ant-design/icons";
import { Resizable } from "react-resizable";
import "./check-detail-mange.less";
const ResizableTitle = props => {
    const { onResize, ...restProps } = props;
    let [width, setWidth] = useState(props.width);
    let [resizing, setResizing] = useState(false);
    if (!width) {
        return <th {...restProps} />;
    }
    let thStyle = { ...restProps.style },
        handleStyle = {};
    if (resizing) {
        handleStyle = { left: `${width - 11}px` };
        thStyle.zIndex = 10;
    }
    return (
        <Resizable
            width={width}
            height={0}
            handle={
                <div
                    className={`resizable-handle ${resizing && "resizing"}`}
                    onClick={e => {
                        e.stopPropagation();
                    }}
                    style={handleStyle}></div>
            }
            onResizeStart={() => setResizing(true)}
            onResize={(e, { size }) => {
                if (size.width > 80 && size.width < 800) {
                    setWidth(size.width);
                }
            }}
            onResizeStop={(e, p) => {
                setResizing(false);
                onResize(e, p);
            }}
            draggableOpts={{ enableUserSelectHack: false }}>
            <th {...restProps} style={thStyle}>
                <div className="title">{restProps.children}</div>
            </th>
        </Resizable>
    );
};
class App extends SearchList {
    constructor(props) {
        super(props);
        let detail = JSON.parse(window.atob(lib.getParam("detail")));
        this.titleList = [
            {
                title: "商品料号",
                dataIndex: detail.productId,
            },
            {
                title: "金二序号",
                dataIndex: detail.goodsSeqNo,
            },
            {
                title: "账册编号",
                dataIndex: detail.customsBookNo,
            },
            {
                title: "商品名称",
                dataIndex: decodeURIComponent(detail.goodsName),
            },
            {
                title: "HS编码",
                dataIndex: detail.hsCode,
            },
            {
                title: "口岸",
                dataIndex: decodeURIComponent(detail.customsDistrictName),
            },
        ];
    }
    componentDidMount() {}
    getConfig() {
        // 262
        return axios.get(getConfigDataUtils.getDataUrlByDataId(262)).then(res => res.data.data);
    }

    renderLeftOperation() {
        return (
            <div>
                <Descriptions>
                    {this.titleList.map((item, index) => (
                        <Descriptions.Item key={index} label={item.title}>
                            {item.dataIndex}
                        </Descriptions.Item>
                    ))}
                </Descriptions>
            </div>
        );
    }

    load(_, toTop) {
        let { pagination, search, config } = this.state;
        var data = {};
        this.setState({ _loading: true });
        Object.assign(data, pagination, search);
        delete data.productId;
        delete data.goodsSeqNo;
        delete data.customsBookNo;
        delete data.goodsName;
        delete data.hsCode;
        delete data.customsDistrictName;
        console.log(data);
        // data.id = Number(data.id)
        // return
        lib.request({
            url: "/ccs/customsBookItemStock/paging",
            data: data,
            success: data => {
                // console.log(data, "data")
                if (data) {
                    this.setState({
                        pagination: data.page,
                        dataList: data.dataList || [],
                        _loading: false,
                    });
                    toTop && (document.querySelector(".table-panel .ant-table-body").scrollTop = 0);
                } else {
                    this.setState({
                        pagination: data ? data.page : { currentPage: 1, pageSize: 20 },
                        dataList: [],
                        _loading: false,
                    });
                    message.warning("未查询到变更信息");
                    toTop && (document.querySelector(".table-panel .ant-table-body").scrollTop = 0);
                }

                setTimeout(this.resetSize, 100);
            },
            fail: e => {
                console.log(e);
                this.setState({
                    _loading: false,
                    dataList: [],
                });
            },
        });
    }

    renderTable() {
        let { config, dataList, table, selectedIdList, pagination, _loading } = this.state;
        let maxWidth = 0;
        let list = config.tableFieldList
            .filter(item => item.fixed != "hide")
            .sort((a, b) => {
                var type = ["left", "show", "right"];
                return type.indexOf(a.fixed) - type.indexOf(b.fixed);
            });
        let columns = list.map(item => {
            var column = {
                title: item.tooltip ? (
                    <Tooltip placement="topLeft" title={item.tooltip} arrowPointAtCenter>
                        {item.title} <QuestionCircleOutlined />
                    </Tooltip>
                ) : (
                    item.title
                ),
                dataIndex: item.key,
                width: item.width,
                ellipsis: { showTitle: false },
                fixed: item.fixed,
                onHeaderCell: column => {
                    return {
                        width: column.width,
                        onResize: (e, { size }) => {
                            item.width = size.width;
                            this.setState({ config });
                        },
                    };
                },
            };
            maxWidth += column.width;
            if (item.type == "function" || item.type == "js") {
                column.render = (_, row, index) => {
                    try {
                        return item.type == "js" ? eval(item.key) : this[item.key](row, index);
                    } catch (e) {
                        console.error(new Error(`error expression ${item.key}`));
                    }
                };
            }
            return column;
        });
        let rowSelection = config.page.isBatch && {
            onChange: (selectedIdList, selectedRows) => {
                this.setState({ selectedIdList, selectedRows });
            },
            fixed: true,
            preserveSelectedRowKeys: true,
            selectedRowKeys: selectedIdList,
        };
        //列表展开
        let expandable = !this.renderExpandRow
            ? null
            : {
                  rowExpandable: row => this.renderExpandRow(row, false) != null,
                  expandedRowRender: (row, _, __, expanded) => this.renderExpandRow(row, expanded),
              };
        return (
            <div className="search-list-table check-detail-mange">
                <Table
                    rowSelection={rowSelection}
                    rowKey="id"
                    size="small"
                    pagination={false}
                    expandable={expandable}
                    components={{ header: { cell: ResizableTitle } }}
                    dataSource={dataList}
                    columns={columns}
                    scroll={{ x: maxWidth, y: table.height }}></Table>
            </div>
        );
    }
    // renderRightOperation() {
    //   return (
    //     <div className="right-button">
    //       <Button>导出</Button>
    //     </div>
    //   )
    // }

    // renderRightOperation() { //渲染自己的按钮
    //   return (
    //     <React.Fragment>
    //       {/* <Button onClick={() => this.exportFunc()}>导出</Button> */}
    //     </React.Fragment>
    //   )
    // }

    // 导出数据
    // exportFunc() {
    //   var data = {};
    //   data.currentPage = this.state.pagination.currentPage;
    //   data.pageSize = this.state.pagination.pageSize;
    //   var page = this.state.page;
    //   for (var key in this.state.page) {
    //     if (page[key].value !== "") {
    //       data[key] = page[key].value;
    //     }
    //   }
    //   data.bookItemId = lib.getParam("bookItemId")
    //   lib.request({
    //     url: "/ccs/itemRegionLog/exportExcelByDownLoadCenter",
    //     data,
    //     needMask: true,
    //     success: res => {
    //       if (res) {
    //         let url = `/download-center/${new Date().getTime()}?page_title=下载中心&config_id=1593580312171226&refresh_event=${new Date().getTime()}`
    //         window.indexProps.history.push(url);
    //         event.emit('add-page', {
    //           url
    //         });
    //       }
    //     }
    //   })
    // }
}
export default App;
