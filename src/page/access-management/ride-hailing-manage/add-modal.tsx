import React, { useEffect, useState, useRef } from "react";
import { DTEditForm, DTEditFormConfigs, DTEditFormRefs } from "@dt/components";
import { Button, Modal, message } from "antd";
import { lib } from "react-single-app";

interface GroundModalProps {
    // selecteds: number[];
    load: () => void;
}
export default (props: GroundModalProps) => {
    // const { selecteds } = props;
    const [open, setOpen] = useState(false);
    const ref = useRef<DTEditFormRefs>({} as DTEditFormRefs);

    const configs: DTEditFormConfigs = [
        {
            type: "INPUT",
            fProps: {
                label: "清关单号",
                name: "inveCustomsSn",
                rules: [{ required: true, message: "请输入清关单号" }],
                labelCol: { span: 6 },
            },
        },
    ];

    const onOk = () => {
        ref.current.form.validateFields().then(values => {
            lib.request({
                url: "/danding-wcms/api/order/create/ccs",
                data: {
                    ...values,
                },
                success: () => {
                    message.success("新增成功");
                    ref.current.form.resetFields();
                    props.load && props.load();
                    setOpen(false);
                },
            });
        });
    };

    return (
        <>
            <Button
                type="primary"
                onClick={() => {
                    // if (selecteds.length === 0) return message.error("请选择数据")
                    setOpen(true);
                }}>
                新增
            </Button>
            <Modal
                open={open}
                title={"新增"}
                onOk={onOk}
                onCancel={() => {
                    setOpen(false);
                }}>
                <DTEditForm
                    ref={ref}
                    configs={configs}
                    layout={{
                        mode: "appoint",
                        colNum: 1,
                    }}
                />
            </Modal>
        </>
    );
};
