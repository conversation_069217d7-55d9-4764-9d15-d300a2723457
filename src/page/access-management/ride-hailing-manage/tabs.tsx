import React, { useState } from "react";
import { useEffect } from "react";
import { Tabs } from "antd";
import type { TabsProps } from "antd";
import { lib } from "react-single-app";
import { DetailStatusCode } from "../ride-hailing-detail/configs";

export default props => {
    const [status, setStatus] = useState<{
        orderCreated: number;
        quoteInProgress: number;
        quoteCompleted: number;
        schedulingPending: number;
        orderOver: number;
        orderClosed: number;
    }>();
    const onChange = (key: string) => {
        props.onLoad(key);
    };

    const items = [
        {
            key: null,
            label: "全部",
        },
        {
            key: DetailStatusCode.CREATED,
            label: `已创建(${status?.orderCreated || 0})`,
        },
        {
            key: DetailStatusCode.AUDITING,
            label: `审核中(${status?.quoteInProgress || 0})`,
        },
        {
            key: DetailStatusCode.OFFER,
            label: `已报价(${status?.quoteCompleted || 0})`,
        },
        {
            key: DetailStatusCode.WAIT,
            label: `等待车辆信息(${status?.schedulingPending || 0})`,
        },
        {
            key: DetailStatusCode.FINISHED,
            label: `约车完成(${status?.orderOver || 0})`,
        },
        {
            key: DetailStatusCode.CANCEL,
            label: `已取消(${status?.orderClosed || 0})`,
        },
    ];
    useEffect(() => {
        lib.request({
            url: "/danding-wcms/api/order/stat/ccs",
            data: { ...props.search },
            success: data => {
                console.log(data);
                setStatus(data);
            },
        });
    }, [props.search]);
    return (
        <>
            <Tabs
                //@ts-ignore
                items={items}
                onChange={onChange}
            />
        </>
    );
};
