import React, { useState, useRef } from "react";
import { SearchList } from "@dt/components";
import axios from "axios";
//@ts-ignore
import { DDYObject, getConfigDataUtils, hooks, lib } from "react-single-app";
import { Button, Space, message, Modal } from "antd";
import { SearchListRef } from "@dt/components/esm/business/SearchList/type";
import AddModal from "./add-modal";
import QuotationModal from "./quotation-modal";
import UpdateStatusModal from "@/page/customs-clearance-system/components/update-status-modal";
import { DetailStatusCode } from "../ride-hailing-detail/configs";
import Tabs from "./tabs";
// import "./index.less"
export default () => {
    const [selecteds, setSelecteds] = useState<any[]>([]);
    const [detail, setDetail] = useState({});
    const [search, setSearch] = useState<DDYObject>();
    const [updateOpen, setUpdateOpen] = useState(false);
    const [quotationOpen, setQuotationOpen] = useState(false);
    const [buttons] = hooks.useGetAuthButtons({ systemCode: "CCS_ADMIN", pagePath: "/ride-hailing-manage" });
    const tabState = useRef();
    const searchList = useRef<SearchListRef>({} as SearchListRef);
    const getConfig = () => {
        return axios.get(getConfigDataUtils.getDataUrlByDataId(834)).then(res => res.data.data);
    };

    const exportFile = () => {
        //@ts-ignore
        const params = searchList.current?.getSearchData();
        lib.request({
            url: "/danding-wcms/api/excel/export",
            data: {
                funcCode: "CCS_ORDER_EXPORT",
                param: { ...params, state: tabState.current },
            },
            success(data) {
                lib.openPage("/excel/download-center?page_title=下载中心");
            },
        });
    };

    const cancel = row => {
        Modal.confirm({
            title: "取消约车",
            content: "确定取消约车吗？",
            onOk: () => {
                lib.request({
                    url: "/danding-wcms/api/order/cancel/ccs",
                    data: {
                        orderNo: row.orderNo,
                    },
                    success(data) {
                        message.success("取消成功");
                        //@ts-ignore
                        searchList.current.load();
                    },
                });
            },
        });
    };

    const submit = row => {
        Modal.confirm({
            title: "提交约车",
            content: "确定提交约车吗？",
            onOk: () => {
                lib.request({
                    url: "/danding-wcms/api/order/pushToTms/ccs",
                    data: {
                        orderNo: row.orderNo,
                    },
                    success(data) {
                        message.success("提交成功");
                        //@ts-ignore
                        searchList.current.load();
                    },
                });
            },
        });
    };

    return (
        <SearchList
            maxLayoutCount={4}
            scrollMode="tableScroll"
            //@ts-ignore
            ref={searchList}
            searchConditionConfig={{
                size: "middle",
            }}
            renderModal={() => (
                <>
                    <QuotationModal
                        row={detail}
                        open={quotationOpen}
                        load={bol => {
                            if (bol) {
                                //@ts-ignore
                                searchList.current?.load();
                            }
                            setQuotationOpen(false);
                        }}
                    />
                </>
            )}
            tableCustomFun={{
                operateFn: (row, index) => {
                    return (
                        <Space>
                            {[
                                DetailStatusCode.CREATED,
                                DetailStatusCode.OFFER,
                                DetailStatusCode.WAIT,
                                DetailStatusCode.AUDITING,
                            ].includes(row.state) && (
                                <a
                                    type="link"
                                    onClick={() => {
                                        cancel(row);
                                    }}>
                                    取消
                                </a>
                            )}

                            {row.state === DetailStatusCode.CREATED && (
                                <a
                                    onClick={() => {
                                        submit(row);
                                    }}>
                                    提交约车
                                </a>
                            )}
                            {row.state === DetailStatusCode.OFFER && (
                                <a
                                    onClick={() => {
                                        setDetail(row);
                                        setQuotationOpen(true);
                                    }}>
                                    确认报价
                                </a>
                            )}
                        </Space>
                    );
                },
                orderNoFn: (row, index) => {
                    return (
                        <a
                            onClick={() => {
                                lib.openPage(`/ride-hailing-detail?pageTitle=约车详情&id=${row.orderNo}`);
                            }}>
                            {row.orderNo}
                        </a>
                    );
                },
            }}
            renderLeftOperation={() => {
                return (
                    <Space>
                        {/* {buttons.includes("add-ride-car") && ( */}
                        <AddModal
                            // selecteds={selecteds}
                            load={() => {
                                //@ts-ignore
                                searchList.current?.load();
                            }}
                        />
                        {/* )} */}

                        {buttons.includes("update-ride-car-status") && (
                            <UpdateStatusModal
                                //@ts-ignore
                                selected={selecteds}
                                selectedType={"array"}
                                success={() => {
                                    //@ts-ignore
                                    searchList.current.resetSelected();
                                    //@ts-ignore
                                    searchList.current.load();
                                }}
                                type={"hailing"}
                            />
                        )}
                    </Space>
                );
            }}
            renderOperationTopView={() => {
                return (
                    <Tabs
                        search={search}
                        onLoad={data => {
                            tabState.current = data;
                            //@ts-ignore
                            searchList.current.changeImmutable({
                                state: data,
                            });
                        }}
                    />
                );
            }}
            renderRightOperation={() => {
                return (
                    <Space>
                        <Button
                            onClick={() => {
                                exportFile();
                            }}>
                            导出
                        </Button>
                    </Space>
                );
            }}
            onTableSelected={(ids, rows) => {
                setSelecteds([...rows]);
            }}
            tableConfig={{
                // size: 'small',
                rowKey: "id",
            }}
            onSearch={search => {
                setSearch({ ...search });
            }}
            getConfig={getConfig}
        />
    );
};
