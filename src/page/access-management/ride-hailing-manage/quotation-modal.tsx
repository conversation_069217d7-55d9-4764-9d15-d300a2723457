import React, { useEffect, useState, useRef } from "react";
import { DTEditForm, DTEditFormConfigs, DTEditFormRefs } from "@dt/components";
import { Button, Modal, message } from "antd";
import { DDYObject, lib } from "react-single-app";

interface GroundModalProps {
    row?: DDYObject;
    open: boolean;
    load: (load?: boolean) => void;
}
export default (props: GroundModalProps) => {
    const ref = useRef<DTEditFormRefs>({} as DTEditFormRefs);

    const configs: DTEditFormConfigs = [
        {
            type: "INPUT",
            fProps: {
                label: "仓单号",
                name: "erpInOutOrderNo",
            },
        },
        {
            type: "INPUT",
            fProps: {
                label: "车辆来源",
                name: "quoteTruckSourceDesc",
            },
        },
        {
            type: "INPUT",
            fProps: {
                label: "车型",
                name: "quoteTruckModel",
            },
        },
        {
            type: "INPUT",
            fProps: {
                label: "报价(元)",
                name: "quoteContractQuote",
            },
        },
    ];

    const onOk = () => {
        ref.current.form.validateFields().then(values => {
            lib.request({
                url: "/danding-wcms/api/order/confirmQuote/ccs",
                data: {
                    orderNo: props.row.orderNo,
                },
                success: () => {
                    message.success("确认成功");
                    ref.current.form.resetFields();
                    props.load && props.load(true);
                },
                fail: (code: number, msg: string) => {
                    message.error(msg);
                },
            });
        });
    };

    useEffect(() => {
        if (props.open) {
            ref.current.setDetail && ref.current.setDetail(props.row);
        }
    }, [props.open, props.row]);

    return (
        <Modal
            open={props.open}
            title={"确认报价"}
            onOk={onOk}
            onCancel={() => {
                props.load && props.load();
            }}>
            <DTEditForm
                ref={ref}
                totalMode="read"
                configs={configs}
                layout={{
                    mode: "appoint",
                    colNum: 1,
                }}
            />
        </Modal>
    );
};
