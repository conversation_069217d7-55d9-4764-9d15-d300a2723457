import React, { useState, useEffect } from "react";
import { lib, event, SearchList, ConfigFormCenter, getConfigDataUtils } from "react-single-app";
import { Button, Modal, Switch, message, Space, Form, Select, Descriptions } from "antd";
import "./marias-stock-detail.less";
import axios from "axios";
// import {BooksInventoryDetailConfig, BooksInventoryEditConfig} from './view-config'

const FormItem = Form.Item;
const { Option } = Select;

class App extends SearchList {
    constructor(props) {
        super(props);
        this.state.modalTitle = "料号库存详情";
        // 编辑/新建url
        // this.state.upUrl = "/ccs/customsBookItem/upset";
        // this.state.unitList = [];
        // this.state.hsList = [];
        // this.state.activeKey = "All"
        // this.params = lib.getParam('id')
        console.log(lib.getParam("id"));
    }

    getConfig() {
        return axios.get(getConfigDataUtils.getDataUrlByDataId(575)).then(res => res.data.data);
    }

    renderLeftOperation() {
        const goodsName = lib.getParam("goodsName");
        const productId = lib.getParam("productId");
        const customsBookNo = lib.getParam("customsBookNo");
        return (
            <Descriptions column={2}>
                <Descriptions.Item label={"商品名称"}>{goodsName}</Descriptions.Item>
                <Descriptions.Item label={"统一料号"}>{productId}</Descriptions.Item>
                <Descriptions.Item label={"账册编号"}>{customsBookNo}</Descriptions.Item>
            </Descriptions>
        );
    }

    load(_, toTop) {
        let { pagination, search, config } = this.state;
        var data = {};
        this.setState({ _loading: true });
        Object.assign(data, pagination, search);
        delete data.productId;
        delete data.goodsName;
        console.log(data);
        // data.id = Number(data.id)
        // return
        lib.request({
            url: "/ccs/customsBookItemStock/paging",
            data: data,
            success: data => {
                // console.log(data, "data")
                if (data) {
                    this.setState({
                        pagination: data.page,
                        dataList: data.dataList || [],
                        _loading: false,
                    });
                    toTop && (document.querySelector(".table-panel .ant-table-body").scrollTop = 0);
                } else {
                    this.setState({
                        pagination: data ? data.page : { currentPage: 1, pageSize: 20 },
                        dataList: [],
                        _loading: false,
                    });
                    message.warning("未查询到变更信息");
                    toTop && (document.querySelector(".table-panel .ant-table-body").scrollTop = 0);
                }

                setTimeout(this.resetSize, 100);
            },
            fail: e => {
                console.log(e);
                this.setState({
                    _loading: false,
                    dataList: [],
                });
            },
        });
    }
}

export default App;
