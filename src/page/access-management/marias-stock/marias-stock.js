import React from "react";
import { lib, SearchList, getConfigDataUtils } from "react-single-app";
import { Space, Drawer, Table } from "antd";
import axios from "axios";
import { AccountNumDetail } from "../book-inventory/books-inventory";

class App extends SearchList {
    constructor(props) {
        super(props);
        this.state.modalTitle = "料号库存";
    }

    occupiedNumFunc(row) {
        return <span>{row.occupiedNum}</span>;
    }

    getConfig() {
        return axios.get(getConfigDataUtils.getDataUrlByDataId(574)).then(res => res.data.data);
    }

    occupiedNumFunc(row) {
        return (
            <a className={"link"} onClick={() => this.showAccountNumDetail(row)}>
                {row.occupiedNum}
            </a>
        );
    }

    myOperation(row) {
        return (
            <Space>
                <span
                    className="link"
                    onClick={e => {
                        lib.openPage(
                            `/marias-stock-detail?pageTitle=查看明细&productStockId=${row.id}&productId=${row.productId}&goodsName=${row.goodsName}&customsBookNo=${row.customsBookNo}`,
                        );
                    }}>
                    查看明细
                </span>
            </Space>
        );
    }

    showAccountNumDetail(row) {
        this.setState({
            recordProductStockId: row.id,
            showAccountNumDetail: true,
        });
    }

    renderModal() {
        return (
            <>
                <AccountNumDetail
                    param={{ recordProductStockId: this.state.recordProductStockId }}
                    url={"/ccs/customsBookItemStock/getStockOccupiedDetail"}
                    visible={this.state.showAccountNumDetail}
                    onClose={() => {
                        this.setState({
                            showAccountNumDetail: false,
                        });
                    }}
                />
            </>
        );
    }
}

export default App;
