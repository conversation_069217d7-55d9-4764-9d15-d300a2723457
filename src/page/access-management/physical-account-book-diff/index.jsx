import React, { useState, useEffect } from "react";
import { lib, event, SearchList, ConfigFormCenter, getConfigDataUtils } from "react-single-app";
import { Button, Modal, Switch, message, Space, Form, Select, Descriptions } from "antd";
// import "./marias-stock-detail.less";
import axios from "axios";

class App extends SearchList {
    getConfig() {
        return axios.get(getConfigDataUtils.getDataUrlByDataId(750)).then(res => res.data.data);
    }

    renderLeftOperation() {
        return (
            <Space>
                <Button
                    onClick={() => {
                        lib.openPage(`/excel/import-data?page_title=CW导入&code=IMPORT_CW_INVENTORY`, () =>
                            this.load(),
                        );
                    }}>
                    CW导入 核对库存
                </Button>
            </Space>
        );
    }

    renderLink(row) {
        return <a href={row.fileUrl}>{row.fileUrl}</a>;
    }
}

export default App;
