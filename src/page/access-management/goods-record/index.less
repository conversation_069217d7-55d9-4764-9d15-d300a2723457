.goods-record-check-detail-root {
    height: 100%;

    .search-list {
        height: 100%;
        display: flex;
        flex-direction: column;

        .customs-tab,
        .goods-record-tab {
            height: 100%;

            .ant-tabs-content {
                height: 100%;
            }
        }
    }
}

.goods-record-check-detail {
    height: 100%;
    overflow: scroll;

    .top-alert {
        min-height: 64px;

        .ant-alert-icon {
            font-size: 32px !important;
        }

        .ant-alert-message {
            font-size: 16px !important;
        }

        .ant-alert-description {
            color: rgba(0, 0, 0, 0.45);
        }
    }

    td {
        white-space: break-spaces;
    }

    .ant-alert-info,
    .ant-alert-warning,
    .ant-alert-error {
        background: #fff;
        border: unset;
    }

    .top-panel {
        padding-right: 40px;
        padding-top: 32px;
    }

    .content-form-main {
        padding: 0 16px 16px;
        background: #fff;
    }

    .table-tab {
        height: 100%;

        .ant-tabs-content {
            height: 100%;
        }
    }

    .customsWarehouseInfo {
        .ant-form-item {
            width: 100%;
            margin-bottom: 0 !important;
        }
    }

    .rulesInfo {
        margin-left: 20px;

        .title {
            .subtitle {
                font-weight: 400;
                font-size: 14px;
            }
        }

        .ant-form-item {
            width: 100%;
            margin-bottom: 0 !important;
        }
    }
}

.main-page .goods-record .table-panel {
    .ant-table-cell-ellipsis {
        word-break: break-all !important;
        white-space: break-spaces !important;
    }
}
