import React, { useState, useEffect } from "react";
import { event, lib } from "react-single-app";
import { ExclamationCircleOutlined } from "@ant-design/icons";
import { Form, Input, message, Modal, Tooltip, Select, Radio, Alert } from "antd";

const FormItem = Form.Item;
const { Option } = Select;
const { TextArea } = Input;

/**
 * 审核通过弹框
 * @param detail
 * @param auditModalVisible
 * @param close
 * @returns
 * @constructor
 */
function AuditModal({ detail, auditModalVisible, close }) {
    const [form] = Form.useForm();
    const [units, setUnits] = useState([]);
    const [hsCode, setHsCode] = useState([]);
    useEffect(() => {
        if (auditModalVisible) {
            lib.request({
                url: "/ccs/customs/listHsV2",
                success: res => {
                    setHsCode(res);
                },
            });
            lib.request({
                url: "/ccs/customs/listUom",
                success: res => {
                    setUnits(res);
                },
            });
            form.setFieldsValue(detail);
        }
    }, [auditModalVisible]);
    const handleOk = () => {
        form.submit();
    };
    const onfinish = value => {
        detail.customsWarehouseInfoVO.opinion = 1;
        detail.baseInfoVO.externalProductId = value.baseInfoVO.externalProductId;
        lib.request({
            url: "/ccs/goodsRecord/auditV2",
            method: "POST",
            data: detail,
            needMask: true,
            success: res => {
                if (res) {
                    handleClose(true);
                    message.success("审核通过");
                }
                let refresh_event = lib.getParam("refresh_event");
                if (refresh_event) {
                    event.emit(refresh_event, true);
                }
            },
        });
    };

    function handleClose(success) {
        form.resetFields();
        close(success);
        setUnits([]);
    }

    const handleCancel = () => {
        handleClose();
    };
    const FormText = ({ value }) => {
        return <span>{value}</span>;
    };
    return (
        <Modal
            cancelText="取消"
            okText="确定"
            title="审核通过"
            zIndex={1000}
            destroyOnClose={true}
            open={auditModalVisible}
            onOk={handleOk}
            onCancel={handleCancel}>
            <div style={{ padding: 10 }}>审核通过选中备案(请输入料号并确认信息完整)</div>
            <Form form={form} layout="vertical" onFinish={onfinish}>
                <div className={"form-item-float"}>
                    <FormItem
                        style={{ width: "45%" }}
                        label="海关备案料号:"
                        name={["baseInfoVO", "productId"]}
                        rules={[{ required: true, message: "请输入海关备案料号" }]}>
                        <FormText />
                    </FormItem>
                    <FormItem style={{ width: "45%" }} label="外部料号:" name={["baseInfoVO", "externalProductId"]}>
                        <Input />
                    </FormItem>
                    <FormItem style={{ width: "45%" }} label="口岸:" name={["customsWarehouseInfoVO", "customs"]}>
                        <FormText />
                    </FormItem>
                    <FormItem style={{ width: "45%" }} label="HSCode:" name={["baseInfoVO", "hsCode"]}>
                        <FormText />
                    </FormItem>
                    <FormItem style={{ width: "45%" }} label="法定第一数量：" name={["baseInfoVO", "firstUnitAmount"]}>
                        <Input disabled={true} />
                    </FormItem>
                    <FormItem style={{ width: "45%" }} label="法定第一计量单位:" name={["baseInfoVO", "firstUnit"]}>
                        <Select disabled={true} showSearch optionFilterProp="children">
                            {units.map((item, index) => {
                                return (
                                    <Option key={index} value={item.id}>
                                        {item.name}
                                    </Option>
                                );
                            })}
                        </Select>
                    </FormItem>
                    <FormItem style={{ width: "45%" }} label="法定第二数量:" name={["baseInfoVO", "secondUnitAmount"]}>
                        <Input disabled={true} />
                    </FormItem>
                    <FormItem style={{ width: "45%" }} label="法定第二计量单位:" name={["baseInfoVO", "secondUnit"]}>
                        <Select disabled={true}>
                            {units.map((item, index) => {
                                return (
                                    <Option key={index} value={item.id}>
                                        {item.name}
                                    </Option>
                                );
                            })}
                        </Select>
                    </FormItem>
                </div>
            </Form>
        </Modal>
    );
}

function RejectModal({ modalVisible, detail, close }) {
    const [list, setList] = useState([]);
    const [form] = Form.useForm();
    const batchRefuse = () => {
        form.submit();
    };
    const handleCancel = () => {
        handleClose();
    };
    const handleClose = success => {
        form.resetFields();
        close(success);
    };
    const onfinish = values => {
        detail.customsWarehouseInfoVO.opinion = 0;
        let reason = "";
        list.map(item => {
            if (item.id === values.reasonId) reason = item.reason;
        });
        if (values.reason) {
            reason = reason + "【" + values.reason + "】";
        }
        detail.customsWarehouseInfoVO.reason = reason;
        lib.request({
            url: "/ccs/goodsRecord/auditV2",
            method: "POST",
            data: detail,
            needMask: true,
            success: res => {
                if (res) {
                    handleClose(true);
                    message.success("审核驳回成功");
                }
            },
        });
    };

    const getRadioData = () => {
        lib.request({
            url: "/ccs/goodsRecord/getRejectReason",
            success: data => {
                setList(data);
            },
        });
    };

    useEffect(() => {
        getRadioData();
    }, []);
    return (
        <Modal
            cancelText="取消"
            okText="确定"
            title="审核驳回"
            open={modalVisible}
            onOk={batchRefuse}
            onCancel={handleCancel}
            destroyOnClose={true}>
            <Form form={form} layout={"vertical"} onFinish={onfinish}>
                <Alert
                    className={"top-alert"}
                    type="error"
                    showIcon
                    style={{ marginBottom: "10px" }}
                    message={"请按照实际情况驳回备案，若类型与实际不符，则补充说明"}
                />
                <Form.Item name={"reasonId"} label={"驳回原因"} labelCol={{ span: 4 }} wrapperCol={{ span: 24 }}>
                    <Radio.Group>
                        {list.map((item, index) => {
                            return (
                                <Radio value={item.id} key={index}>
                                    {item.reason}
                                </Radio>
                            );
                        })}
                    </Radio.Group>
                </Form.Item>
                <Form.Item name={"reason"}>
                    <TextArea maxLength={255} showCount />
                </Form.Item>
            </Form>
        </Modal>
    );
}

function AddModal({ modalVisible, close, id, customsCode }) {
    // const [modalVisible,setModalVisible] = useState(false);
    const [data, setData] = useState([]);
    // const [value,setValue] = useState(null);
    const [form] = Form.useForm();
    const onOk = () => {
        const value = form.getFieldValue("warehouse");
        form.validateFields().then(res => {
            console.log(value);
            lib.request({
                url: "/ccs/goodsRecord/saveNewWarehouseInfo",
                data: {
                    recordCustomsId: id,
                    entityWarehouseSn: value,
                },
                success: () => {
                    close && close(true);
                },
            });
        });
    };

    const onCancel = () => {
        close && close(false);
    };

    const onFinish = () => {
        // close && close()
    };

    useEffect(() => {
        getSelectData();
        return () => {
            form.resetFields();
        };
    }, []);

    const getSelectData = () => {
        lib.request({
            url: "/ccs/entityWarehouse/listWarehouseByRecordCustomsId",
            data: {
                recordCustomsId: id,
                customsCode: customsCode,
            },
            needMask: true,
            success: res => {
                setData(res);
            },
        });
    };

    return (
        <Modal
            cancelText="取消"
            okText="确定"
            title="新增配置"
            open={modalVisible}
            onOk={onOk}
            onCancel={onCancel}
            destroyOnClose={true}>
            <Form form={form} onFinish={onFinish}>
                <Form.Item
                    name={"warehouse"}
                    label={"仓库信息"}
                    rules={[
                        {
                            required: true,
                            message: "请选择仓库信息!",
                        },
                    ]}>
                    <Select
                        showSearch
                        allowClear
                        filterOption={(input, option) => {
                            return option.props.children.toLowerCase().indexOf(input.toLowerCase()) >= 0;
                        }}>
                        {data.map((item, index) => {
                            return (
                                <Option key={index} value={item.id}>
                                    {item.name}
                                </Option>
                            );
                        })}
                    </Select>
                </Form.Item>
            </Form>
        </Modal>
    );
}

export { AuditModal, RejectModal, AddModal };
