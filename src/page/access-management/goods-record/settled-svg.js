import React from "react";
import Icon from "@ant-design/icons";

const HeartSvg = () => (
    <svg width="1em" height="1em" fill="currentColor" viewBox="0 0 1024 1024">
        <path
            d="M512 64a448 448 0 1 1 0 896A448 448 0 0 1 512 64z m-56.96 262.08L357.504 399.232a29.248 29.248 0 0 0 0 46.72L455.04 519.232a29.248 29.248 0 0 0 46.784-23.424v-47.552h101.504c55.68 0 100.864 45.76 100.864 102.4 0 56.64-45.248 102.4-100.864 102.4H371.392a25.6 25.6 0 1 0 0 51.2h231.936c84.032 0 152.064-68.8 152.064-153.6 0-84.736-68.032-153.6-152.064-153.6H501.76V349.44a29.248 29.248 0 0 0-46.784-23.36z"
            p-id="22165"
            fill="#FF4D4F"
        />
    </svg>
);
export const FallBackIcon = props => <Icon component={HeartSvg} {...props} />;
