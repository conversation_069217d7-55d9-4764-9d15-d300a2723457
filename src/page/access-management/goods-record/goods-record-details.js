import React, { useState, useEffect, createContext } from "react";
import "../../shop-good.less";
import { Tabs } from "antd";

import GoodsRecordAssociationDetail from "./goods-record-association-detail";
import GoodsRecordBooksDetail from "./goods-record-books-info";
import GoodsRecordLog from "./goods-record-log";
import GoodsRecordCustomsDetails from "./goods-record-customs-details";

function App() {
    return (
        <div className={"goods-record-check-detail-root"}>
            <div className={"search-list"}>
                <div className={"goods-record-tab"}>
                    <Tabs defaultActiveKey={"1"} destroyInactiveTabPane={true} style={{ height: "100%" }}>
                        <Tabs.TabPane tab={"备案审核详情"} key="1" style={{ height: "100%" }}>
                            <GoodsRecordCustomsDetails />
                        </Tabs.TabPane>
                        <Tabs.TabPane tab={"跨境通关信息"} key="2">
                            <GoodsRecordAssociationDetail />
                        </Tabs.TabPane>
                        {/*<Tabs.TabPane tab={'账册信息'} key="3">*/}
                        {/*    <GoodsRecordBooksDetail/>*/}
                        {/*</Tabs.TabPane>*/}
                        <Tabs.TabPane tab={"备案日志"} key="4">
                            <GoodsRecordLog />
                        </Tabs.TabPane>
                    </Tabs>
                </div>
            </div>
        </div>
    );
}

export default App;
