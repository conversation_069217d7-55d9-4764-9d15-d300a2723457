import React, { useState, useEffect, Fragment } from "react";
import { Form, Input, Button, Table, Modal, message, Row, ConfigProvider, Empty, Select } from "antd";
import { ConfigFormCenter, lib } from "react-single-app";
import { goodsRecordLinkDetailConfig } from "./view-config";
import "./index.less";

//关联清单信息
export default function GoodsRecordAssociationDetail() {
    let [showAssociationProductIdModal, setAssociationProductIdModal] = useState(false);
    let [detailData, setDetailData] = useState();
    const code = lib.getParam("id");
    const configData = goodsRecordLinkDetailConfig({
        onClick: item => {
            setAssociationProductIdModal(true);
        },
    });
    const ref = React.useRef();

    const disableEdit = lib.getParam("disableEdit");

    function getDetail() {
        ref.current.getDetail({ url: "/ccs/goodsRecord/viewAssociatedInfo", data: { id: code } });
    }

    useEffect(() => {
        getDetail();
    }, []);

    const onConfigLoadSuccess = config => {
        config.baseInfo.children.map(item => {
            if (item.type === "single-select" && !item.fromParam) {
                ref.current.initSelect(item);
            }
        });
    };
    const beforeSetDetail = data => {
        setDetailData(data);
        return data;
    };
    const onSubmitSuccess = () => {
        getDetail();
        message.success("提交成功！");
    };

    function renderModal() {
        return (
            <Fragment>
                <AssociationProductIdModal
                    showModal={showAssociationProductIdModal}
                    detailData={detailData}
                    dialogClose={success => {
                        if (success) {
                            getDetail();
                        }
                        setAssociationProductIdModal(false);
                    }}
                />
            </Fragment>
        );
    }

    return (
        <div className="goods-record-check-detail">
            {renderModal()}
            <ConfigFormCenter
                ref={ref}
                disableEdit={disableEdit}
                code={code}
                confData={configData}
                onConfigLoadSuccess={onConfigLoadSuccess}
                beforeSetDetail={beforeSetDetail}
                submitUrl={"/ccs/checklist/upset"}
                onSubmitSuccess={onSubmitSuccess}
            />
        </div>
    );
}

//关联核注清单
function AssociationProductIdModal({ detailData, showModal, dialogClose }) {
    let [dataSource, setDataSource] = useState();
    let [accountBookNoList, setAccountBookNoList] = useState([]);
    useEffect(() => {
        if (showModal) {
            lib.request({
                url: "/ccs/goodsRecord/recordCustomsBookList",
                data: { id: lib.getParam("id") },
                success: res => {
                    setAccountBookNoList(res);
                },
            });
            form.setFieldsValue({ productId: detailData.associatedProductId });
        }
    }, [showModal]);

    const handleClose = success => {
        dialogClose(success);
    };
    const handleCancel = () => {
        handleClose();
    };
    const handleOk = () => {
        form.validateFields().then(values => {
            lib.request({
                url: "/ccs/goodsRecord/modifyAssociatedInfo",
                needMask: true,
                data: {
                    goodsRecordId: lib.getParam("id"),
                    associateInfoId: detailData.id,
                    ...values,
                },
                success: data => {
                    handleClose(true);
                },
            });
        });
    };
    /**
     * 检查账册库存
     */
    const checkAccountBook = () => {
        setDataSource(null);
        form.submit();
    };
    const onFinish = value => {
        lib.request({
            url: "/ccs/customsBookItem/viewItemListByProductId",
            needMask: true,
            data: value,
            success: data => {
                setDataSource(data);
            },
        });
    };

    const columns = [
        {
            title: "金二序号",
            dataIndex: "goodsSeqNo",
            width: 130,
        },
        {
            title: "商品名称",
            dataIndex: "goodsName",
            width: 150,
        },
        {
            title: "HS编码",
            dataIndex: "hsCodeDesc",
            width: 100,
        },
        {
            title: "规格",
            dataIndex: "model",
            width: 80,
        },
        {
            title: "原产国",
            dataIndex: "originCountryDesc",
            width: 100,
        },
        {
            title: "申报单位",
            dataIndex: "declareUnitDesc",
            width: 100,
        },
        {
            title: "法定计量单位",
            dataIndex: "firstUnitDesc",
            width: 130,
        },
        {
            title: "法定数量",
            dataIndex: "firstUnitDesc",
            width: 130,
        },

        {
            title: "法定第二计量单位",
            dataIndex: "secondUnitDesc",
            width: 150,
        },
        {
            title: "第二法定数量",
            dataIndex: "firstUnitDesc",
            width: 130,
        },
    ];
    const [form] = Form.useForm();
    return (
        (<Modal
            width={dataSource ? 920 : 619}
            destroyOnClose
            okText={"更改保存"}
            onCancel={handleCancel}
            onOk={handleOk}
            title={"更改"}
            open={showModal}>
            <Form form={form} onFinish={onFinish}>
                <Row>
                    <Form.Item
                        name="accountBookNoId"
                        label={"账册编号"}
                        rules={[{ required: true, message: "请选择账册编号!" }]}>
                        <Select
                            showSearch
                            allowClear
                            optionFilterProp="children"
                            style={{ width: "140px", marginRight: "10px" }}>
                            {accountBookNoList.map(item => (
                                <Select.Option value={item.id} key={item.id}>
                                    {item.name}
                                </Select.Option>
                            ))}
                        </Select>
                    </Form.Item>
                    <Form.Item label={"料号"} name={"productId"}>
                        <Input style={{ width: "140px", marginRight: "10px" }} />
                    </Form.Item>
                    <Button type={"primary"} onClick={checkAccountBook}>
                        检查账册
                    </Button>
                </Row>
            </Form>
            {dataSource && (
                <ConfigProvider
                    renderEmpty={() => {
                        return <Empty description={"暂无账册库存信息"} image={Empty.PRESENTED_IMAGE_SIMPLE} />;
                    }}>
                    <Table scroll={{ y: 400 }} rowKey={"customsBookItemId"} columns={columns} dataSource={dataSource} />
                </ConfigProvider>
            )}
        </Modal>)
    );
}
