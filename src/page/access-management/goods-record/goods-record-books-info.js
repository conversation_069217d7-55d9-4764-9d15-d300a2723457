import React, { useState, useEffect, Fragment } from "react";
import { Typography, Divider } from "antd";
import { ConfigFormCenter, event, lib } from "react-single-app";
import { goodsRecordBookDetailConfig } from "./view-config";
import "./index.less";

const { Title } = Typography;

//账册信息
export default function GoodsRecordBooksDetail() {
    let [detailData, setDetailData] = useState([]);
    let [onBookChange, setOnBookChange] = useState(0);
    const code = lib.getParam("id");

    function getDetail() {
        lib.request({
            url: "/ccs/goodsRecord/viewItemListByGoodsRecordId",
            data: { id: code },
            success: data => {
                setDetailData(data);
            },
        });
    }

    useEffect(() => {
        event.on("onBookChange", onBookChangeEvent);
        console.log(" on onBookChangeEvent");
        getDetail();
        return () => {
            console.log(" off onBookChangeEvent ");
            event.off("onBookChange", onBookChangeEvent);
        };
    }, [onBookChange]);

    function onBookChangeEvent() {
        console.log("onBookChangeEvent");
        setOnBookChange(onBookChange++);
    }

    return (
        <div className="goods-record-check-detail">
            {detailData &&
                detailData.map((item, index) => {
                    return <BooksItem data={item} index={index} key={index} />;
                })}
        </div>
    );
}

function BooksItem({ data, index }) {
    const ref = React.useRef();
    const configData = goodsRecordBookDetailConfig();
    const onConfigLoadSuccess = () => {
        ref.current.setMergeDetail(data);
    };
    return (
        <>
            {index > 1 && <Divider />}
            <Title level={5}>账册信息{index + 1} </Title>
            <ConfigFormCenter ref={ref} confData={configData} onConfigLoadSuccess={onConfigLoadSuccess} />
        </>
    );
}
