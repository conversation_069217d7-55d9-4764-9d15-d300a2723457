import React from "react";
import { Typography } from "antd";
import { lib } from "react-single-app";
import "./index.less";
import LogTable from "../../../components/LogTable";

const { Title } = Typography;
export default function GoodsRecordLog() {
    const columns = [
        {
            title: "口岸",
            dataIndex: "customs",
            width: 200,
        },
    ];
    return (
        <>
            <Title level={5} style={{ paddingLeft: 20 }}>
                备案日志
            </Title>
            <div className="goods-record-check-detail">
                <LogTable
                    api={"/ccs/goodsRecord/pagingTrackLog"}
                    insertColumns={{
                        position: 2,
                        columns,
                    }}
                    param={{ goodsRecordId: lib.getParam("id") }}
                />
            </div>
        </>
    );
}
