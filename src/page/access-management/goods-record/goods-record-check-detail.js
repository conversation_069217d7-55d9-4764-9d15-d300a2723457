import React, { useState, useEffect, Fragment, useMemo } from "react";
import { Row, Button, Space, message, Alert, Typography, Modal, Tag, Tooltip } from "antd";
import { ConfigFormCenter, event, lib, hooks } from "react-single-app";
import { customsWarehouseInfoConfig, goodsRecordCheckDetailConfig } from "./view-config";
import "./index.less";
import { AuditModal, RejectModal, AddModal } from "./audit-modal";
import { ClockCircleFilled, CheckCircleFilled } from "@ant-design/icons";
import { FallBackIcon } from "./settled-svg";
import _ from "lodash";
import CustomsDeclareProductIdEditModal from "./components/customs-declare-productId-edit-modal";
// import {} from
const { useGetAuthButtons } = hooks;

export default function GoodsRecordCheckDetail({ customs, getTabList }) {
    /**
     * 顶部的提示条
     */
    const [topAlert, setAlert] = useState();
    let [auditModalVisible, setAuditModalVisible] = useState(false);
    let [refuseModalVisible, setRefuseModalVisible] = useState(false);
    let [warehouseModalVisible, setWarehouseModalVisible] = useState(false);
    let [customsDeclareDetail, setCustomsDeclareDetail] = useState();
    let [customsDeclareProductIdEditModalVisible, setCustomsDeclareProductIdEditModalVisible] = useState(false);
    let detailData = React.useRef();
    let [auditInfo, setAuditInfo] = useState();

    const [buttons] = useGetAuthButtons({ systemCode: "CCS_ADMIN", pagePath: "/goods-record" });
    //GOODS-RECORD-ADD-WAREHOUSE
    // console.log(buttons)
    const configData = goodsRecordCheckDetailConfig({
        onClick: item => {
            switch (item.name.join()) {
                case ["baseInfoVO", "attachmentName"].join():
                    window.open(detailData.current.baseInfoVO.attachmentUrl);
                    break;
                default:
                    break;
            }
        },
        goodsInfoHandleDelete: record => {
            return new Promise((resolve, reject) => {
                Modal.confirm({
                    title: "提示",
                    content: "删除商品列表将取消统一料号与海关备案料号的库存关联，确认剧除？",
                    onOk: () =>
                        lib.request({
                            url: "/ccs/goodsRecord/deleteRecordCustomsProductById",
                            data: { id: record.id },
                            success: res => {
                                resolve();
                            },
                            fail: e => {
                                reject(e);
                            },
                        }),
                });
            });
        },
        renderOperation: (text, record, index) => {
            return (
                <Button
                    onClick={() => {
                        setCustomsDeclareDetail({
                            productId: detailData.current.baseInfoVO.productId,
                            ...record,
                            recordCustomsId: customs.id,
                            recordId: code,
                        });
                        setCustomsDeclareProductIdEditModalVisible(true);
                    }}>
                    编辑
                </Button>
            );
        },
        titleAction: () => null,
        showBarCode: (item, bol, value, id) => {
            return (
                <>
                    {value}
                    {detailData.current &&
                        detailData.current?.baseInfoVO &&
                        detailData.current?.baseInfoVO?.goodsRecordTag === 2 && (
                            <Space>
                                <Tooltip
                                    title={
                                        detailData.current &&
                                        detailData.current?.baseInfoVO &&
                                        detailData.current?.baseInfoVO.goodsRecordTagDesc
                                    }>
                                    <Tag color={"red"}>条码不符合规则</Tag>
                                </Tooltip>
                            </Space>
                        )}
                </>
            );
        },
    });

    const ref = React.useRef();

    const code = lib.getParam("id");
    const type = lib.getParam("type");
    const update = lib.getParam("update");

    function getDetail() {
        if (customs?.customsCode) {
            ref.current.getDetail({
                url: "/ccs/goodsRecord/detailV2",
                data: { recordId: code, customsCode: customs.customsCode },
            });
        }
    }

    useEffect(() => {
        getDetail();
    }, []);

    useEffect(() => {
        ref.current.config.customsWarehouseInfo.titleAction = setTitleAction;
        ref.current.changeConfig(ref.current.config);
    }, [buttons]);

    function setTitleAction() {
        if (!buttons.includes("GOODS-RECORD-ADD-WAREHOUSE")) {
            return null;
        }
        return (
            <Button
                onClick={() => {
                    setWarehouseModalVisible(true);
                }}>
                新增
            </Button>
        );
    }

    function getDetailFormData() {
        return ref.current.getForm.validateFields().then(values => {
            detailData.current.baseInfoVO = Object.assign(detailData.current.baseInfoVO, values.baseInfoVO);
            if (detailData.current.baseInfoVO.firstUnitAndAmount) {
                detailData.current.baseInfoVO.firstUnit = detailData.current.baseInfoVO.firstUnitAndAmount.select;
                detailData.current.baseInfoVO.firstUnitAmount = detailData.current.baseInfoVO.firstUnitAndAmount.input;
            }
            if (detailData.current.baseInfoVO.secondUnitAndAmount) {
                detailData.current.baseInfoVO.secondUnit = detailData.current.baseInfoVO.secondUnitAndAmount.select;
                detailData.current.baseInfoVO.secondUnitAmount =
                    detailData.current.baseInfoVO.secondUnitAndAmount.input;
            }
            if (detailData.current.baseInfoVO.secondUnitAmount == "") {
                detailData.current.baseInfoVO.secondUnitAmount = null;
            }
            delete detailData.current.baseInfoVO.createTime;
            delete detailData.current.baseInfoVO.recordFinishTime;
            delete detailData.current.baseInfoVO.updateTime;
            delete detailData.current.baseInfoVO.declareCurrencyDesc;
            delete detailData.current.baseInfoVO.originCountryName;
            return detailData.current;
        });
    }

    const authButton = () => {
        return (
            <Space>
                <Button
                    type="primary"
                    onClick={() => {
                        getDetailFormData()
                            .then(value => {
                                let { grossWeight, netWeight } = value.baseInfoVO;
                                if (grossWeight >= netWeight) {
                                    setAuditInfo(value);
                                    setAuditModalVisible(true);
                                } else {
                                    message.error(
                                        `发起审核失败：商品备案的毛重（${grossWeight}kg）小于净重（${netWeight}kg）`,
                                    );
                                }
                            })
                            .catch(errorInfo => {
                                message.error(errorInfo.errorFields[0].errors);
                            });
                    }}>
                    审核通过
                </Button>
                <Button
                    onClick={() => {
                        getDetailFormData()
                            .then(value => {
                                setAuditInfo(value);
                                setRefuseModalVisible(true);
                            })
                            .catch(errorInfo => {
                                message.error(errorInfo.errorFields[0].errors);
                            });
                    }}>
                    审核驳回
                </Button>
            </Space>
        );
    };
    const editButton = () => {
        return (
            <Space>
                <Button
                    onClick={() => {
                        console.log("customs:", customs);
                        if (customs.status === 2) {
                            Modal.confirm({
                                title: "确定备案更新至商家端？",
                                onOk: () => {
                                    ref.current.submitForm();
                                },
                            });
                            return;
                        }
                        ref.current.submitForm();
                    }}>
                    保存
                </Button>
            </Space>
        );
    };
    const switchTopStatusView = detail => {
        let { status, statusDesc, reason } = detail.customsWarehouseInfoVO;
        let topAlert;
        let renderTopAction;
        if (type === "edit") {
            renderTopAction = editButton();
        } else if (type === "auth") {
            renderTopAction = authButton();
        }
        if (update === "edit") {
            renderTopAction = editButton();
        }
        switch (status) {
            case 1: //待审核-
                topAlert = {
                    message: statusDesc,
                    icon: <ClockCircleFilled style={{ fontSize: "16px", color: "#FAAD14" }} />,
                    renderTopAction: renderTopAction,
                };
                break;
            case 2: //已审核
                if (update === "edit") {
                    topAlert = {
                        message: statusDesc,
                        icon: <ClockCircleFilled style={{ fontSize: "16px", color: "#FAAD14" }} />,
                        renderTopAction: renderTopAction,
                    };
                } else {
                    topAlert = {
                        message: statusDesc,
                        icon: <CheckCircleFilled style={{ fontSize: "16px", color: "#73D13D" }} />,
                    };
                }

                break;
            case 4: //审核驳回
                topAlert = {
                    message: statusDesc,
                    description: `驳回原因：${reason}`,
                    icon: <FallBackIcon style={{ fontSize: "16px", color: "#FF4D4F" }} />,
                };
                break;
        }
        setAlert(topAlert);
    };
    const onConfigLoadSuccess = config => {
        config.baseInfo.children.map(item => {
            if (!item.fromParam) {
                ref.current.initSelect(item);
            }
        });
        config.declareInfo.children.map(item => {
            if (!item.fromParam) {
                ref.current.initSelect(item);
            }
        });
    };
    const onSubmitSuccess = () => {
        ref.current.resetChangConfigCount();
        getDetail();
        message.success("提交成功！");
        let refresh_event = lib.getParam("refresh_event");
        if (refresh_event) {
            event.emit(refresh_event, true);
        }
    };

    //目前审核即提交表单
    const beforeSubmit = values => {
        let baseInfoVO = Object.assign(detailData.current.baseInfoVO, values.baseInfoVO);
        if (baseInfoVO.firstUnitAndAmount) {
            baseInfoVO.firstUnitAmount = baseInfoVO.firstUnitAndAmount.input;
            baseInfoVO.firstUnit = baseInfoVO.firstUnitAndAmount.select;
        }
        if (baseInfoVO.secondUnitAndAmount) {
            baseInfoVO.secondUnitAmount = baseInfoVO.secondUnitAndAmount.input;
            baseInfoVO.secondUnit = baseInfoVO.secondUnitAndAmount.select;
        }
        if (baseInfoVO.secondUnitAmount === "") {
            baseInfoVO.secondUnitAmount = null;
        }
        delete baseInfoVO.createTime;
        delete baseInfoVO.recordFinishTime;
        delete baseInfoVO.updateTime;
        delete baseInfoVO.declareCurrencyDesc;
        delete baseInfoVO.originCountryName;
        baseInfoVO.customsCode = customs.customsCode;
        baseInfoVO.customs = customs.customs;
        baseInfoVO.guanWuRemark = values.customsWarehouseInfoVO.guanWuRemark;
        if (customs.status === 2) {
            const result = {
                id: baseInfoVO.id,
                customsCode: customs.customsCode,
                goodsName: baseInfoVO.goodsRecordName,
                hsCode: baseInfoVO.hsCode,
                firstUnitAmount: baseInfoVO.firstUnitAmount,
                firstUnit: baseInfoVO.firstUnit,
                secondUnitAmount: baseInfoVO.secondUnitAmount,
                secondUnit: baseInfoVO.secondUnit,
                originCountry: baseInfoVO.originCountry,
                declareUnit: baseInfoVO.declareUnit,
                declarePrice: baseInfoVO.declarePrice,
                declareCurrency: baseInfoVO.declareCurrency,
                model: baseInfoVO.model,
                composition: baseInfoVO.composition,
                hgsbys: baseInfoVO.hgsbys,
                netWeight: baseInfoVO.netWeight,
                grossWeight: baseInfoVO.grossWeight,
                externalGoodsId: baseInfoVO.externalGoodsId,
                cainiaoGoodsId: baseInfoVO.cainiaoGoodsId,
                barCode: baseInfoVO.barCode,
                guanWuRemark: baseInfoVO.guanWuRemark,
            };
            return result;
        } else {
            return baseInfoVO;
        }
    };

    const beforeSetDetail = data => {
        data.baseInfoVO.firstUnitAndAmount = {
            input: data.baseInfoVO.firstUnitAmount,
            select: data.baseInfoVO.firstUnit,
        };
        data.baseInfoVO.secondUnitAndAmount = {
            input: data.baseInfoVO.secondUnitAmount,
            select: data.baseInfoVO.secondUnit,
        };
        data.customsWarehouseInfoVO.customsWarehouseResVOList.map(
            item => (item.customsDeclareProductIdListDesc = item.customsDeclareProductIdList?.join("\r\n")),
        );
        detailData.current = { ...data };
        switchTopStatusView(detailData.current);
        return data;
    };
    const onSinglesSelectChange = desc => {
        switch (desc.name.join()) {
            case ["baseInfoVO", "hsCode"].join():
                taxChange(desc.value.value);
                break;
        }
    };

    const onSetDetailSuccess = (fieldsValue, changConfigCount) => {
        if (changConfigCount == 1) {
        }
    };

    function taxChange(id) {
        lib.request({
            url: "/ccs/customs/hsTaxDetailV2",
            data: {
                id,
            },
            needMask: true,
            success: res => {
                let { consumptionNumTax, consumptionTax, vat, consumptionFlag, firstLegalUnit, secondLegalUnit } = res;
                const KeyDist = {
                    0: "",
                    5: consumptionNumTax,
                    10: consumptionTax,
                };
                let formFieldsValue = ref.current.getFormFieldsValue();
                formFieldsValue.baseInfoVO = Object.assign(formFieldsValue.baseInfoVO, {
                    vatRate: vat * 100,
                    taxRate: KeyDist[consumptionFlag] * 100,
                    firstUnit: firstLegalUnit,
                    secondUnit: secondLegalUnit,
                });
                ref.current.setMergeDetail(formFieldsValue);
            },
        });
    }

    function renderModal() {
        return (
            <Fragment>
                {
                    <AuditModal
                        auditModalVisible={auditModalVisible}
                        detail={auditInfo}
                        close={success => {
                            setAuditModalVisible(false);
                            if (success) {
                                getTabList(customs.customsCode);
                                getDetail();
                            }
                        }}
                    />
                }
                {
                    <RejectModal
                        modalVisible={refuseModalVisible}
                        detail={auditInfo}
                        close={success => {
                            if (success) {
                                getTabList(customs.customsCode);
                                getDetail();
                            }
                            setRefuseModalVisible(false);
                        }}
                    />
                }
                {
                    <CustomsDeclareProductIdEditModal
                        detail={customsDeclareDetail}
                        visible={customsDeclareProductIdEditModalVisible}
                        close={success => {
                            if (success) {
                                getDetail();
                            }
                            setCustomsDeclareProductIdEditModalVisible(false);
                        }}
                    />
                }

                {/* 仓库信息新增弹框 */}
                {
                    <AddModal
                        modalVisible={warehouseModalVisible}
                        id={customs.id}
                        customsCode={customs.customsCode}
                        close={success => {
                            if (success) {
                                getTabList(customs.customsCode);
                                getDetail();
                            }
                            setWarehouseModalVisible(false);
                        }}
                    />
                }
            </Fragment>
        );
    }

    return (
        <div className="goods-record-check-detail">
            {renderModal()}
            <Space style={{ width: "100%", position: "relative" }}>
                {topAlert && (
                    <Alert
                        className={"top-alert"}
                        message={topAlert.message}
                        description={topAlert.description}
                        type={topAlert.type}
                        icon={topAlert.icon}
                        showIcon
                        // action={topAlert.renderTopAction}
                    />
                )}
                {detailData.current?.baseInfoVO.goodsRecordTagList?.map(item => {
                    return (
                        <Tooltip title={item}>
                            <Tag color={item === "四类措施商品" ? "blue" : "red"}>{item}</Tag>
                        </Tooltip>
                    );
                })}
                <div style={{ position: "absolute", right: "100px", top: "20px" }}>{topAlert?.renderTopAction}</div>
            </Space>

            <ConfigFormCenter
                ref={ref}
                onSinglesSelectChange={onSinglesSelectChange}
                disableEdit={update === "edit" ? false : detailData.current?.customsWarehouseInfoVO.status != 1}
                beforeSubmit={beforeSubmit}
                code={code}
                confData={configData}
                // submitUrl={'/ccs/goodsRecord/updateGoodsRecord'}
                submitUrl={
                    customs.status === 2
                        ? "/ccs/goodsRecord/updateFinishGoodsRecord"
                        : "/ccs/goodsRecord/updateGoodsRecord"
                }
                onConfigLoadSuccess={onConfigLoadSuccess}
                beforeSetDetail={beforeSetDetail}
                onSubmitSuccess={onSubmitSuccess}
                onSetDetailSuccess={onSetDetailSuccess}
            />
        </div>
    );
}
