import React from "react";
import { Modal } from "antd";
import { ConfigFormCenter } from "react-single-app";

export default function CustomsDeclareEditModal({ visible, close, detail }) {
    const ref = React.useRef();

    const handleOk = () => {
        ref.current.submitForm();
    };

    const onConfigLoadSuccess = config => {
        if (detail) {
            // 如果有通关料号列表，转换为表单需要的格式
            if (detail.customsNos && detail.customsNos.length > 0) {
                detail.productIdSubmitList = detail.customsNos.map(item => {
                    return { name: item };
                });
            } else {
                // 如果没有通关料号，初始化一个空的输入框
                detail.productIdSubmitList = [{ name: '' }];
            }
            ref.current.setMergeDetail(detail);
        }
    };

    const beforeSubmit = values => {
        return {
             recordId: detail.code,
            recordCustomsId: detail.recordCustomsId,
            customsBookId: detail.customsBookId,
            recordWarehouseId: detail.id,
            productIdSubmitList: values.productIdSubmitList?.reduce((prev, curr) => {
                const trimmedName = curr.name?.trim();
                if (trimmedName) {
                    return [...prev, trimmedName];
                }
                return prev;
            }, []),
        };
    };

    const onSubmitSuccess = () => {
        close(true);
    };

    function handleClose(success) {
        close(success);
    }

    const handleCancel = () => {
        handleClose();
    };

    return (
        <Modal
            cancelText={"取消"}
            okText={"保存"}
            title={"编辑"}
            destroyOnClose={true}
            open={visible}
            onOk={handleOk}
            onCancel={handleCancel}
            width={600}
        >
            <ConfigFormCenter
                ref={ref}
                confData={formConfig}
                onConfigLoadSuccess={onConfigLoadSuccess}
                beforeSubmit={beforeSubmit}
                submitUrl={"/ccs/recordWarehouse/updateCustomsDeclareProductId"} // 替换为您的实际API
                onSubmitSuccess={onSubmitSuccess}
            />
        </Modal>
    );
}

// 表单配置
const formConfig = {
    baseInfo: {
        children: [
            {
                label: "统一料号",
                editEnable: false,
                name: "productId",
                type: "text",
                labelCol: { span: 4 },
            },
            {
                label: "账册编号",
                editEnable: false,
                name: "customsBookNo",
                type: "text",
                labelCol: { span: 4 },
            },
        ],
        label: "",
        name: "baseInfo",
        isGroup: true,
        className: "baseInfo-proxyCode",
    },
    contractInfo: {
        label: "添加通关料号时，请确认属于同一实物",
        type: "formList",
        name: "productIdSubmitList",
        editEnable: true,
        isGroup: false,
        children: [
            {
                label: "通关料号",
                name: "name",
                type: "textInput",
                labelCol: { span: 5 },
                editEnable: true,
                rules: [
                    {
                        required: true,
                        message: "请输入通关料号"
                    },
                ],
                placeholder: "请输入通关料号"
            },
        ],
    },
};
