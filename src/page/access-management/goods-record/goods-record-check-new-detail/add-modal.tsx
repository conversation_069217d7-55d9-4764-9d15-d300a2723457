// import React, { useState, useEffect, useMemo } from "react";
// import { event, lib } from "react-single-app";
// import { ExclamationCircleOutlined } from "@ant-design/icons";
// import { Form, Input, message, Modal, Tooltip, Select, Radio, Alert } from "antd";

// const FormItem = Form.Item;
// const { Option } = Select;
// const { TextArea } = Input;

// function AddModal({ modalVisible, close, id, customsCode }) {
//     // const [modalVisible,setModalVisible] = useState(false);
//     const [data, setData] = useState([]);
//     // const [value,setValue] = useState(null);
//     const [form] = Form.useForm();
//     const onOk = () => {
//         const value = form.getFieldValue("warehouse");
//         form.validateFields().then(res => {
//             console.log(value);
//             lib.request({
//                 url: "/ccs/goodsRecord/saveNewWarehouseInfo",
//                 data: {
//                     recordCustomsId: id,
//                     entityWarehouseSn: value,
//                 },
//                 success: () => {
//                     close && close(true);
//                 },
//             });
//         });
//     };

//     const onCancel = () => {
//         close && close(false);
//     };

//     const onFinish = () => {
//         // close && close()
//     };

//     useEffect(() => {
//         getSelectData();
//         console.log(customsCode, id);
//         return () => {
//             // form?.resetFields();
//         };
//     }, []);

//     const getSelectData = () => {
//         lib.request({
//             url: "/ccs/entityWarehouse/listWarehouseByRecordCustomsId",
//             data: {
//                 recordCustomsId: id,
//                 customsCode: customsCode,
//             },
//             needMask: true,
//             success: res => {
//                 console.log("res:", res);
//                 setData(res);
//             },
//         });
//     };

//     return (
//         <Modal
//             // cancelText="取消"
//             // okText="确定"
//             title="新增配置"
//             open={modalVisible}
//             onOk={onOk}
//             onCancel={onCancel}>
//             <Form form={form} onFinish={onFinish}>
//                 <Form.Item
//                     name={"warehouse"}
//                     label={"仓库信息"}
//                     rules={[
//                         {
//                             required: true,
//                             message: "请选择仓库信息!",
//                         },
//                     ]}>
//                     <Select
//                         showSearch
//                         allowClear
//                         filterOption={(input, option) => {
//                             return option?.props?.children.toLowerCase().indexOf(input.toLowerCase()) >= 0;
//                         }}>
//                         {data.map((item, index) => {
//                             return (
//                                 <Option key={index} value={item.id}>
//                                     {item.name}
//                                 </Option>
//                             );
//                         })}
//                     </Select>
//                 </Form.Item>
//             </Form>
//         </Modal>
//     );
// }

// export default AddModal;
import React, { useEffect, useState, useRef } from "react";
import { DTEditForm, DTEditFormConfigs, DTEditFormRefs } from "@dt/components";
import { Button, Modal, message } from "antd";
import { lib } from "react-single-app";

interface GroundModalProps {
    // selecteds: number[];
    load: () => void;
}
export default ({ modalVisible, close, id, customsCode }) => {
    // const { selecteds } = props;
    // const [open, setOpen] = useState(false);
    const ref = useRef<DTEditFormRefs>({} as DTEditFormRefs);

    const configs: DTEditFormConfigs = [
        {
            type: "SELECT",
            fProps: {
                label: "仓库信息",
                name: "warehouse",
                rules: [{ required: true, message: "请输入清关单号" }],
                labelCol: { span: 6 },
            },
            list: [],
            dataUrl: '/ccs/entityWarehouse/listWarehouseByRecordCustomsId',
            defaultParams: {
                recordCustomsId: id,
                customsCode: customsCode,
            }
        },
    ];

    const onOk = () => {
        const value = ref.current.form.getFieldValue("warehouse");
        ref.current.form.validateFields().then(res => {
            console.log(value);
            lib.request({
                url: "/ccs/goodsRecord/saveNewWarehouseInfo",
                data: {
                    recordCustomsId: id,
                    entityWarehouseSn: value,
                },
                success: () => {
                    message.success("新增成功")
                    close && close(true);
                },
            });
        });
    };

    return (
        <>
            {/* <Button
                type="primary"
                onClick={() => {
                    // if (selecteds.length === 0) return message.error("请选择数据")
                    // setOpen(true);
                }}>
                新增
            </Button> */}
            <Modal
                open={modalVisible}
                title={"新增"}
                onOk={onOk}
                onCancel={() => {
                    close && close(false);
                }}>
                <DTEditForm
                    ref={ref}
                    configs={configs}
                    layout={{
                        mode: "appoint",
                        colNum: 1,
                    }}
                />
            </Modal>
        </>
    );
};
