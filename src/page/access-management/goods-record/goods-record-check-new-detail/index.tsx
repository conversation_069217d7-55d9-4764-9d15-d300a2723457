import React, { useState, useEffect, useRef } from "react";
import { <PERSON>ton, Space, message, Alert, Modal, Tag, Tooltip, Select, Table } from "antd";
//@ts-ignore
import { event, lib, hooks, DDYObject } from "react-single-app";
import { DTEditForm, DTEditFormRefs, DTEditFormModuleConfigs } from "@dt/components";
import { ClockCircleFilled, CheckCircleFilled, QuestionCircleOutlined } from "@ant-design/icons";
import { FallBackIcon } from "../settled-svg";
import _ from "lodash";
import "../index.less";
import AddModal from "./add-modal";
import HscodeSelect from "./hscode-select";
import InputSelect from "./input-select";

const { useGetAuthButtons } = hooks;

export default function GoodsRecordCheckDetail({ customs, getTabList }) {
    /**
     * 顶部的提示条
     */
    const [topAlert, setAlert] = useState<null | DDYObject>();
    const [mode, setMode] = useState<"read" | "edit">("read"); // 新增状态控制：read 或 edit
    const detailData = useRef<DDYObject>();
    const ref = useRef<DTEditFormRefs>();

    const code = lib.getParam("id");
    const type = lib.getParam("type");
    const update = lib.getParam("update");

    const [warehouseModalVisible, setWarehouseModalVisible] = useState(false);

    const [buttons] = useGetAuthButtons({ systemCode: "CCS_ADMIN", pagePath: "/goods-record" });

    // 配置定义
    const configData: DTEditFormModuleConfigs = {
        header: {
            title: "",

            configs: [
                {
                    type: "INPUT",
                    fProps: {
                        label: "统一料号",
                        name: ["baseInfoVO", "productId"],
                    },
                    editable: false,
                    isCopy: true,
                    colSpan: 6,
                },
                {
                    type: "INPUT",
                    fProps: {
                        label: "货品ID",
                        name: ["baseInfoVO", "goodsCode"],
                    },
                    editable: false,
                    isCopy: true,
                    colSpan: 6,
                },
                {
                    type: "INPUT",
                    fProps: {
                        label: "用户",
                        name: ["baseInfoVO", "tenantNameDesc"],
                    },
                    editable: false,
                    isCopy: true,
                    colSpan: 6,
                },
                {
                    type: "DATE",
                    fProps: {
                        label: "创建时间",
                        name: ["baseInfoVO", "createTime"],
                    },
                    editable: false,
                    colSpan: 6,
                },
            ],
        },
        baseInfo: {
            moduleMode: mode,
            title: "基本信息",
            configs: [
                {
                    type: "INPUT",
                    fProps: {
                        label: "备案名称",
                        name: ["baseInfoVO", "goodsRecordName"],
                        labelCol: { span: 4 },
                        rules: [{ required: true, message: "请输入备案名称" }],
                    },
                    cProps: {
                        disabled: mode === "read",
                    },
                    colSpan: 24,
                },
                {
                    type: "INPUT",
                    fProps: {
                        label: "条形码",
                        name: ["baseInfoVO", "barCode"],
                    },
                    cProps: {
                        disabled: mode === "read",
                    },
                },
                {
                    type: "INPUT",
                    fProps: {
                        label: "SKU",
                        name: ["baseInfoVO", "sku"],
                    },
                },
                {
                    type: "INPUT",
                    fProps: {
                        label: "净重(kg)",
                        name: ["baseInfoVO", "sku"],
                    },
                },
                {
                    type: "INPUT",
                    fProps: {
                        label: "毛重(kg)",
                        name: ["baseInfoVO", "sku"],
                    },
                },
                // 可以根据需要添加更多字段
            ],
        },
        customsWarehouseInfo: {
            moduleMode: mode,
            title: "申报信息",
            // isHidden: true,
            configs: [
                // 添加海关仓库相关字段
                {
                    type: "CUSTOM",
                    fProps: {
                        label: "HsCode",
                        name: ["baseInfoVO", "hsCode"],
                    },
                    render: ({ value, onChange }) => <HscodeSelect value={value} onChange={onChange} />,
                },
                {
                    type: "SELECT",
                    fProps: {
                        label: "原产国",
                        name: ["baseInfoVO", "originCountry"],
                    },
                    list: [],
                    dataUrl: "/ccs/customs/listCountry",
                },
                {
                    type: "CUSTOM",
                    fProps: {
                        label: "法一数量/单位",
                        name: ["baseInfoVO", "firstObj"],
                    },
                    render: ({ value, onChange }) => (
                        <InputSelect value={value} onChange={onChange} url="/ccs/customs/listUom" mode="edit" />
                    ),
                    renderRead: ({ value }) => {
                        console.log(value);
                        return <InputSelect value={value} url="/ccs/customs/listUom" mode="read" onChange={() => { }} />;
                    },
                },
                {
                    type: "CUSTOM",
                    fProps: {
                        label: "法二数量/单位",
                        name: ["baseInfoVO", "secondObj"],
                    },
                    render: ({ value, onChange }) => (
                        <InputSelect value={value} onChange={onChange} url="/ccs/customs/listUom" mode="edit" />
                    ),
                    renderRead: ({ value }) => {
                        console.log(value);
                        return <InputSelect value={value} onChange={() => { }} url="/ccs/customs/listUom" mode="read" />;
                    },
                },
                {
                    type: "TEXTAREA",
                    fProps: {
                        label: "规格型号",
                        name: ["baseInfoVO", "model"],
                    },
                },
                {
                    type: "SELECT",
                    fProps: {
                        label: "申报单位",
                        name: ["baseInfoVO", "declareUnit"],
                    },
                    list: [],
                    dataUrl: "/ccs/customs/listUom",
                },
                {
                    type: "TEXTAREA",
                    fProps: {
                        label: "成分",
                        name: ["baseInfoVO", "composition"],
                        labelCol: { span: 4 },
                        wrapperCol: { span: 20 },
                    },
                    colSpan: 24,
                },
                {
                    type: "TEXTAREA",
                    fProps: {
                        label: "申报要素",
                        name: ["baseInfoVO", "hgsbys"],
                        labelCol: { span: 4 },
                        wrapperCol: { span: 20 },
                    },
                    colSpan: 24,
                },
                {
                    type: "INPUT",
                    fProps: {
                        label: "申报单价",
                        name: "declarePrice",
                    },
                },
                {
                    type: "SELECT",
                    fProps: {
                        label: "申报币制",
                        name: ["baseInfoVO", "declareCurrency"],
                    },
                    list: [],
                    dataUrl: "/ccs/customs/listCurrency",
                    selectShowMode: "together",
                },
                {
                    type: "TEXT",
                    fProps: {
                        label: "品牌(中文)",
                        name: ["baseInfoVO", "brand"],
                    },
                },
                {
                    type: "TEXT",
                    fProps: {
                        label: "品牌(英文)",
                        name: ["baseInfoVO", "brandEn"],
                    },
                },
                {
                    type: "TEXT",
                    fProps: {
                        label: "功能",
                        name: ["baseInfoVO", "recordFunction"],
                    },
                },
                {
                    type: "TEXT",
                    fProps: {
                        label: "用途",
                        name: ["baseInfoVO", "recordUsage"],
                    },
                },
                {
                    type: "FILE",
                    fProps: {
                        label: "正面图片",
                        name: ["baseInfoVO", "frontImage"],
                    },
                },
                {
                    type: "FILE",
                    fProps: {
                        label: "侧面图片",
                        name: ["baseInfoVO", "sideImage"],
                    },
                },
                {
                    type: "FILE",
                    fProps: {
                        label: "背面图片",
                        name: ["baseInfoVO", "backImage"],
                    },
                },
                {
                    type: "FILE",
                    fProps: {
                        label: "标签图片",
                        name: ["baseInfoVO", "labelImage"],
                    },
                },
            ],
        },
        // 可以添加更多模块
        goodsList: {
            title: "商品列表",
            configs: [],
            useChildren: true,
        },
        otherInfo: {
            title: "补充信息",
            configs: [
                {
                    type: "TEXT",
                    fProps: {
                        label: "生产企业名称",
                        name: ["baseInfoVO", "productCompanyName"],
                    },
                },
                {
                    type: "TEXT",
                    fProps: {
                        label: "生产企业注册编号",
                        name: ["baseInfoVO", "productCompanyRegisterNumber"],
                    },
                },
                {
                    type: "TEXT",
                    fProps: {
                        label: "生产企业地址",
                        name: ["baseInfoVO", "productCompanyAddress"],
                    },
                },
                {
                    type: "TEXT",
                    fProps: {
                        label: "商品链接",
                        name: ["baseInfoVO", "productLink"],
                    },
                },
                {
                    type: "FILE",
                    fProps: {
                        label: "附件",
                        name: ["baseInfoVO", "attachment"],
                    },
                    cProps: {
                        listType: "text",
                    },
                },
            ],
        },
        warehouseInfo: {
            title: "仓库信息",
            useChildren: true,
            configs: [],
            renderHeadRight: () => (
                <Button
                    onClick={() => {
                        setWarehouseModalVisible(true);
                    }}>
                    新增
                </Button>
            ),
        },
    };

    // 获取详情数据
    const getDetail = () => {
        if (customs?.customsCode) {
            lib.request({
                url: "/ccs/goodsRecord/detailV2",
                data: { recordId: code, customsCode: customs.customsCode },
                success(data) {
                    if (data.baseInfoVO?.frontImage) {
                        data.baseInfoVO.frontImage = [{ url: data.baseInfoVO?.frontImage }];
                    }
                    if (data.baseInfoVO?.sideImage) {
                        data.baseInfoVO.sideImage = [{ url: data.baseInfoVO?.sideImage }];
                    }
                    if (data.baseInfoVO?.backImage) {
                        data.baseInfoVO.backImage = [{ url: data.baseInfoVO?.backImage }];
                    }
                    if (data.baseInfoVO?.labelImage) {
                        data.baseInfoVO.labelImage = [{ url: data.baseInfoVO?.labelImage }];
                    }
                    data.baseInfoVO.firstObj = {
                        unit: data.baseInfoVO?.firstUnit,
                        amount: data.baseInfoVO?.firstUnitAmount,
                    };
                    data.baseInfoVO.secondObj = {
                        amount: data.baseInfoVO?.secondUnitAmount,
                        unit: data.baseInfoVO?.secondUnit,
                    };
                    if (data.baseInfoVO.attachmentUrl) {
                        data.baseInfoVO.attachment = [
                            {
                                url: data.baseInfoVO.attachmentUrl,
                                name: data.baseInfoVO.attachmentName,
                            },
                        ];
                    }

                    ref.current.form.setFieldsValue(data);
                    detailData.current = data;
                },
            });
        }
    };

    // 初始化加载
    useEffect(() => {
        getDetail();
    }, []);

    return (
        <div className="goods-record-check-detail">
            {/* {renderModal()} */}
            <div style={{ width: "100%" }}>
                {topAlert && (
                    <Alert
                        className={"top-alert"}
                        message={topAlert.message}
                        description={topAlert.description}
                        type={topAlert.type}
                        icon={topAlert.icon}
                        showIcon
                        action={topAlert.renderTopAction}
                    />
                )}

                <DTEditForm
                    ref={ref}
                    configs={configData}
                    totalMode={mode}
                    layout={{
                        mode: "appoint",
                        colNum: 2,
                    }}>
                    {({ moduleType, colProps, layout, formItemColProps }) => (
                        <>
                            {moduleType === "goodsList" && (
                                <>
                                    <Table
                                        dataSource={detailData.current?.productInfoVO || []}
                                        // dataSource={[]}
                                        columns={[
                                            {
                                                title: "账册编号",
                                                dataIndex: "customsBookNo",
                                                width: 180,
                                            },
                                            {
                                                title: (
                                                    <Tooltip
                                                        title={
                                                            "【海关备案料号】赋值优先级：海关备案料号>跨境通关关联料号>通关料号>统一料号"
                                                        }>
                                                        海关备案料号
                                                        <QuestionCircleOutlined
                                                            style={{ margin: "0px 10px 0px 5px" }}
                                                        />
                                                    </Tooltip>
                                                ),
                                                dataIndex: "customsRecordProductId",
                                                width: 230,
                                            },
                                            {
                                                title: "原产国",
                                                dataIndex: "originCountry",
                                                width: 150,
                                            },
                                            {
                                                title: "金二序号",
                                                dataIndex: "goodsSeqNo",
                                                width: 120,
                                            },
                                            {
                                                title: "操作",
                                                render(value, record, index) {
                                                    return (
                                                        <>
                                                            <Button
                                                                onClick={() => {
                                                                    Modal.confirm({
                                                                        title: "提示",
                                                                        content:
                                                                            "删除商品列表将取消统一料号与海关备案料号的库存关联，确认剧除？",
                                                                        onOk: () =>
                                                                            lib.request({
                                                                                url: "/ccs/goodsRecord/deleteRecordCustomsProductById",
                                                                                data: { id: record.id },
                                                                                success: res => {
                                                                                    message.success("删除成功");
                                                                                    getDetail();
                                                                                },
                                                                                fail: e => { },
                                                                            }),
                                                                    });
                                                                }}>
                                                                删除
                                                            </Button>
                                                        </>
                                                    );
                                                },
                                            },
                                        ]}
                                        pagination={false}
                                    />
                                </>
                            )}
                            {moduleType === "warehouseInfo" && (
                                <>
                                    <Table
                                        dataSource={detailData.current?.customsWarehouseInfoVO?.customsWarehouseResVOList || []}
                                        columns={[
                                            {
                                                title: "实体仓名称",
                                                dataIndex: "erpWarehouseName",
                                                width: 190,
                                            },
                                            {
                                                title: "账册编号",
                                                dataIndex: "customsBookNo",
                                                width: 190,
                                            },
                                            {
                                                title: (
                                                    <Tooltip
                                                        title={
                                                            "【海关备案料号】赋值优先级：海关备案料号>跨境通关关联料号>通关料号>统一料号"
                                                        }>
                                                        通关料号
                                                        <QuestionCircleOutlined
                                                            style={{ margin: "0px 10px 0px 5px" }}
                                                        />
                                                    </Tooltip>
                                                ),
                                                dataIndex: "customsDeclareProductIdListDesc",
                                                width: 180,
                                            },
                                            {
                                                title: "操作",
                                                render(value, record, index) {
                                                    return (
                                                        <>
                                                            <Button onClick={() => {
                                                                setWarehouseModalVisible(true)
                                                            }}>编辑</Button>
                                                        </>
                                                    );
                                                },
                                            },
                                        ]}
                                        pagination={false}
                                        size="middle"
                                    />
                                </>
                            )}
                        </>
                    )}
                </DTEditForm>
            </div>
            <AddModal
                modalVisible={warehouseModalVisible}
                close={load => {
                    if (load) {
                        getDetail();
                    }
                    setWarehouseModalVisible(false);
                }}
                id={customs?.id}
                customsCode={customs?.customsCode}
            />
        </div>
    );
}
