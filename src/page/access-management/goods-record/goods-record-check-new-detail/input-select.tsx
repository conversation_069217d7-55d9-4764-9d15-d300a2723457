import React, { useEffect, useState } from "react";
import { Button, Input, Select } from "antd";
import { lib } from "react-single-app";

export default ({ value, onChange, url, mode = "read" }) => {
    const [list, setList] = useState([]);

    useEffect(() => {
        if (url) {
            lib.request({
                url: url,
                success(data) {
                    setList(data);
                },
            });
        }
    }, []);
    return (
        <div>
            {mode === "read" ? (
                value?.amount
            ) : (
                <Input
                    value={value?.amount}
                    onChange={e => {
                        onChange({
                            amount: e.target.value,
                            unit: value?.unit,
                        });
                    }}
                />
            )}
            {mode === "read" ? (
                list.filter(item => item.id === value?.unit)?.[0]?.name
            ) : (
                <Select
                    value={value?.unit}
                    onChange={val => {
                        onChange({
                            unit: val,
                            amount: value?.amount,
                        });
                    }}
                    showSearch>
                    {list.map(item => {
                        return (
                            <Select.Option key={item.id}>
                                {item.id}:{item.name}
                            </Select.Option>
                        );
                    })}
                </Select>
            )}
        </div>
    );
};
