import React, { useEffect, useState } from "react";
import { Button, Select } from "antd";
import { lib } from "react-single-app";

export default ({ value, onChange }) => {
    const [list, setList] = useState([]);

    useEffect(() => {
        lib.request({
            url: "/ccs/customs/listHsV2",
            success(data) {
                setList(data);
            },
        });
    }, []);
    return (
        <>
            <Select
                value={value}
                onChange={value => {
                    onChange(value);
                }}
                showSearch>
                {list.map(item => {
                    return (
                        <Select.Option key={item.id}>
                            {item.id}:{item.name}
                        </Select.Option>
                    );
                })}
            </Select>

            <Button onClick={() => {}}>AI推荐</Button>
        </>
    );
};
