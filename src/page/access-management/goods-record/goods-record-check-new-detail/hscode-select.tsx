import React, { useEffect, useState } from "react";
import { Button, Select, Space } from "antd";
import { lib } from "react-single-app";

export default ({ value, onChange, mode }) => {
    const [list, setList] = useState([]);

    useEffect(() => {
        lib.request({
            url: "/ccs/customs/listHsV2",
            success(data) {
                setList(data);
            },
        });
    }, []);
    return (
        <>

            <Space>
                {

                    mode === 'edit' ?
                        <Select
                            value={value}
                            onChange={value => {
                                onChange(value);
                            }}
                            showSearch>
                            {list.map(item => {
                                return (
                                    <Select.Option key={item.id}>
                                        {item.id}:{item.name}
                                    </Select.Option>
                                );
                            })}
                        </Select>
                        :
                        list.filter(item => item.id === value)?.[0]?.name
                }
                {
                    mode === 'edit' ?
                        <Button onClick={() => { }}>AI推荐</Button>
                        :
                        null

                }

            </Space>
        </>
    );
};
