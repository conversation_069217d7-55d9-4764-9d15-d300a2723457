import React, { useState, useEffect } from "react";
import { Modal } from "antd";
import { ConfigFormCenter } from "react-single-app";

export default function CustomsDeclareProductIdEditModal({ visible, close, detail }) {
    const ref = React.useRef();
    const handleOk = () => {
        ref.current.submitForm();
    };

    const onConfigLoadSuccess = config => {
        if (detail) {
            if (detail.customsDeclareProductIdList) {
                detail.productIdSubmitList = detail.customsDeclareProductIdList.map(item => {
                    return { name: item };
                });
            }
            ref.current.setMergeDetail(detail);
        }
    };

    const beforeSubmit = values => {
        return {
            recordId: detail.recordId,
            recordCustomsId: detail.recordCustomsId,
            customsBookId: detail.customsBookId,
            recordWarehouseId: detail.id,
            productIdSubmitList: values.productIdSubmitList?.reduce((prev, curr) => [...prev, curr.name.trim()], []),
        };
    };
    const onSubmitSuccess = () => {
        close(true);
    };

    function handleClose(success) {
        close(success);
    }

    const handleCancel = () => {
        handleClose();
    };
    return (
        (<Modal
            cancelText={"取消"}
            okText={"保存"}
            title={"编辑"}
            destroyOnClose={true}
            open={visible}
            onOk={handleOk}
            onCancel={handleCancel}>
            <ConfigFormCenter
                ref={ref}
                confData={data}
                onConfigLoadSuccess={onConfigLoadSuccess}
                beforeSubmit={beforeSubmit}
                submitUrl={"/ccs/recordWarehouse/updateCustomsDeclareProductId"}
                onSubmitSuccess={onSubmitSuccess}
            />
        </Modal>)
    );
}

const data = {
    baseInfo: {
        children: [
            {
                label: "统一料号",
                editEnable: false,
                name: "productId",
                type: "text",
                labelCol: { span: 4 },
            },
            {
                label: "账册编号",
                editEnable: false,
                name: "customsBookNo",
                type: "text",
                labelCol: { span: 4 },
            },
        ],
        label: "",
        name: "baseInfo",
        isGroup: true,
        className: "baseInfo-proxyCode",
    },
    contractInfo: {
        label: "添加通关料号时，请确认属于同一实物",
        type: "formList",
        name: "productIdSubmitList",
        editEnable: true,
        isGroup: false,
        children: [
            {
                label: "通关料号",
                name: "name",
                type: "textInput",
                labelCol: { span: 5 },
                editEnable: true,
                rules: [
                    {
                        required: true,
                    },
                ],
            },
        ],
    },
};
