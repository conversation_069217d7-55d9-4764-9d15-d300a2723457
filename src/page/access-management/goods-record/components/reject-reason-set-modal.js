import React, { useState, useRef, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON>, Alert, Input, Row, Col, message } from "antd";
import { lib } from "react-single-app";
import { DeleteOutlined, PlusOutlined } from "@ant-design/icons";
export default ({ visible, closeVisible }) => {
    const [sets, setSets] = useState([{}, {}, {}, {}]);

    const del = (index, row) => {
        if (row.id) {
            lib.request({
                url: "/ccs/customs/dictionary/delete",
                data: { id: row.id },
                success: () => {
                    sets.splice(index, 1);
                    message.success("删除成功");
                    setSets([...sets]);
                },
            });
        } else {
            sets.splice(index, 1);
            setSets([...sets]);
        }
    };

    const add = () => {
        setSets([...sets, {}]);
    };

    const getDetail = () => {
        lib.request({
            url: "/ccs/goodsRecord/getRejectReason",
            success: res => {
                setSets(res);
            },
        });
    };

    const onOK = () => {
        const data = sets.filter(item => {
            return !item.id;
        });
        const arr = data.map(item => item.reason).filter(item => item);
        if (arr.length === 0) {
            return message.warning("请配置原因");
        }
        lib.request({
            url: "/ccs/goodsRecord/addRejectReason",
            data: {
                addRejectReasonList: arr,
            },
            success: () => {
                message.success("配置成功");
                closeVisible();
            },
        });
    };

    useEffect(() => {
        if (visible) {
            getDetail();
        }
    }, [visible]);

    return (
        <>
            <Modal
                title={"驳回原因配置"}
                visible={visible}
                onOk={onOK}
                onCancel={() => {
                    closeVisible();
                }}>
                <div>
                    <Alert
                        showIcon
                        type="warning"
                        style={{ marginBottom: "15px" }}
                        message="以下是驳回原因的配置，已创建的驳回原因只可删除不可编辑，
删除后的驳回原因不再出现于可选列表中，不影响历史订单的驳回原因"
                    />
                    {sets.map((item, index) => {
                        return (
                            <Row
                                key={index}
                                style={{ alignItems: "center", padding: "4px 10px", justifyContent: "center" }}>
                                <Col sm={4}>驳回原因{index + 1}:</Col>
                                <Col sm={10}>
                                    <Input
                                        placeholder={`请输入驳回原因${index + 1}`}
                                        value={item.reason}
                                        disabled={!!item.id}
                                        onChange={e => {
                                            sets[index].reason = e.target.value;
                                            setSets([...sets]);
                                        }}
                                    />
                                </Col>
                                <Col sm={2}>
                                    <Button
                                        style={{ marginLeft: "10px" }}
                                        icon={<DeleteOutlined />}
                                        onClick={() => {
                                            del(index, item);
                                        }}></Button>
                                </Col>
                            </Row>
                        );
                    })}
                    <Button
                        style={{ width: "100%", marginTop: "15px" }}
                        onClick={() => {
                            add();
                        }}>
                        <PlusOutlined />
                        新增
                    </Button>
                </div>
            </Modal>
        </>
    );
};
