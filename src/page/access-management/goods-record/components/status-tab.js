import React, { useState, useEffect, useImperativeHandle } from "react";
import { Tabs } from "antd";
import { lib } from "react-single-app";

export const StatusTabs = React.forwardRef(({ tabOnChange, type }, ref) => {
    const [statusCount, setStatusCount] = useState();
    const [activeKey, setActiveKey] = useState();
    useEffect(() => {}, []);
    function getDetail(search) {
        lib.request({
            url: "/ccs/goodsRecord/getGoodsRecordStatusCount",
            data: search,
            success: res => {
                setStatusCount(res);
            },
        });
    }
    useImperativeHandle(ref, () => ({
        getDetail: getDetail,
        setActiveKey: setActiveKey,
    }));
    return (
        <Tabs
            defaultActiveKey={"0"}
            activeKey={activeKey}
            onChange={e => {
                setActiveKey(e);
                tabOnChange(e);
            }}>
            <Tabs.TabPane tab={"全部"} key={0} />
            <Tabs.TabPane tab={`待审核新品(${statusCount?.waitNewCount || 0})`} key={1} />
            <Tabs.TabPane tab={`待审核更新(${statusCount?.waitUpdateCount || 0})`} key={2} />
            <Tabs.TabPane tab={`待审核超时(${statusCount?.overTimeCount || 0})`} key={3} />
            <Tabs.TabPane tab={"备案完成"} key={4} />
            <Tabs.TabPane tab={"审核驳回"} key={5} />
        </Tabs>
    );
});
