import React from "react";
import { Button, Space, Tooltip, Image, Form, Input } from "antd";
import { QuestionCircleOutlined, CheckCircleFilled, CloseCircleFilled } from "@ant-design/icons";
export function goodsRecordCheckDetailConfig(prop) {
    return {
        baseInfo: {
            children: [
                {
                    label: "货品ID",
                    editEnable: true,
                    name: ["baseInfoVO", "goodsCode"],
                    type: "text",
                    labelCol: { span: 12 },
                    customConfig: { style: { width: "740px" } },
                },
                {
                    label: "备案名称",
                    editEnable: false,
                    name: ["baseInfoVO", "goodsRecordName"],
                    type: "textInput",
                    labelCol: { span: 12 },
                    customConfig: { style: { width: "740px" } },
                    rules: [
                        {
                            required: true,
                            message: "请输入备案名称!",
                        },
                    ],
                },
                {
                    label: "SKU",
                    editEnable: true,
                    type: "text",
                    name: ["baseInfo<PERSON>", "skuId"],
                    labelCol: { span: 12 },
                    customConfig: { style: { width: "740px" } },
                },
                {
                    label: "用户",
                    editEnable: false,
                    labelCol: { span: 12 },
                    name: ["baseInfoVO", "tenantNameDesc"],
                    customConfig: { style: { width: "740px" } },
                    type: "text",
                },
                {
                    label: "条码",
                    editEnable: false,
                    name: ["baseInfoVO", "barCode"],
                    type: "textInput",
                    // render: prop.showBarCode,
                    rules: [
                        {
                            pattern: RegExp(
                                '^[0-9a-zA-Z.。~/·=\\[【\\]】《|…—》\\-\\\\、`！!@#￥$%^&*（({})）_+:：；;“”’,，"<>？?‘’…]{1,32}$',
                            ),
                            // max: 32,
                            message: "请输入最大长度32位的字母、数字、符号",
                        },
                    ],
                },
                {
                    label: "品牌(中文)",
                    editEnable: false,
                    name: ["baseInfoVO", "brand"],
                    type: "text",
                },
                {
                    label: "品牌(英文)",
                    name: ["baseInfoVO", "brandEn"],
                    editEnable: true,
                    type: "text",
                },
                {
                    label: "审核方式",
                    name: ["customsWarehouseInfoVO", "auditWayDesc"],
                    editEnable: true,
                    type: "text",
                },
                {
                    label: "净重(kg)",
                    editEnable: false,
                    name: ["baseInfoVO", "netWeight"],
                    type: "textInput",
                    rules: [
                        {
                            required: true,
                            message: "请输入净重!",
                        },
                        {
                            validator: (_, val, fn) => {
                                if (!val) return Promise.resolve();
                                if (!/(^0(.\d{1,5})?$)|(^[1-9]\d*(.\d{0,5})?$)/.test(val)) {
                                    return Promise.reject("请输入小数点最多5位的数字");
                                }
                                return Promise.resolve();
                            },
                        },
                    ],
                },
                {
                    label: "毛重(kg)",
                    editEnable: false,
                    name: ["baseInfoVO", "grossWeight"],
                    type: "textInput",
                    rules: [
                        {
                            required: true,
                            message: "请输入毛重!",
                        },
                        {
                            validator: (_, val, fn) => {
                                if (!val) return Promise.resolve();
                                if (!/(^0(.\d{1,5})?$)|(^[1-9]\d*(.\d{0,5})?$)/.test(val)) {
                                    return Promise.reject("请输入小数点最多5位的数字");
                                }
                                return Promise.resolve();
                            },
                        },
                    ],
                },
                /*保质期*/
                {
                    label: "保质期",
                    name: ["baseInfoVO", "shelfLife"],
                    editEnable: true,
                    type: "text",
                },
                /*占位*/
                {
                    label: "",
                    name: "",
                    editEnable: true,
                    type: "text",
                },
                {
                    label: "长",
                    name: ["baseInfoVO", "length"],
                    editEnable: false,
                    type: "text",
                },
                {
                    label: "宽",
                    name: ["baseInfoVO", "width"],
                    editEnable: false,
                    type: "text",
                },
                {
                    label: "高",
                    name: ["baseInfoVO", "height"],
                    editEnable: false,
                    type: "text",
                } /*占位*/,
                {
                    label: "",
                    name: "",
                    editEnable: true,
                    type: "text",
                },
                {
                    label: "正面图片",
                    editEnable: true,
                    type: "renderContainer",
                    name: ["baseInfoVO", "frontImage"],
                    render: (item, disabled, fieldValue) => {
                        return fieldValue ? <Image src={fieldValue} width={80} height={80} /> : null;
                    },
                },
                {
                    label: "侧面图片",
                    editEnable: true,
                    type: "renderContainer",
                    name: ["baseInfoVO", "sideImage"],
                    render: (item, disabled, fieldValue) => {
                        return fieldValue ? <Image src={fieldValue} width={80} height={80} /> : null;
                    },
                },
                {
                    label: "背面图片",
                    editEnable: true,
                    type: "renderContainer",
                    name: ["baseInfoVO", "backImage"],
                    render: (item, disabled, fieldValue) => {
                        return fieldValue ? <Image src={fieldValue} width={80} height={80} /> : null;
                    },
                },
                {
                    label: "标签图片",
                    editEnable: true,
                    type: "renderContainer",
                    name: ["baseInfoVO", "labelImage"],
                    render: (item, disabled, fieldValue) => {
                        return fieldValue ? <Image src={fieldValue} width={80} height={80} /> : null;
                    },
                },
            ],
            label: "基本信息",
            name: "baseInfo",
            isGroup: true,
            className: "baseInfo",
        },

        declareInfo: {
            children: [
                {
                    label: "统一料号",
                    editEnable: true,
                    type: "text",
                    tooltip: "【海关备案料号】赋值优先级：海关备案料号>跨境通关关联料号>通关料号>统一料号",
                    name: ["baseInfoVO", "productId"],
                },
                {
                    label: "外部货品ID",
                    name: ["baseInfoVO", "externalGoodsId"],
                    editEnable: false,
                    type: "textInput",
                    // labelCol: { span: 4 },
                    // customConfig: { style: { width: "740px" } },
                    rules: [
                        {
                            // /^([\x21-\x2f\x3a-\x40\x5b-\x60\x7B-\x7F]|\w)+$/
                            pattern: /^[a-zA-Z0-9]{1,32}$/,
                            message: "请输入不超过32位字母数字",
                        },
                    ],
                },
                {
                    label: "菜鸟货品ID",
                    name: ["baseInfoVO", "cainiaoGoodsId"],
                    editEnable: false,
                    type: "textInput",
                    // labelCol: { span: 4 },
                    // customConfig: { style: { width: "740px" } },
                    rules: [
                        {
                            // /^([\x21-\x2f\x3a-\x40\x5b-\x60\x7B-\x7F]|\w)+$/
                            pattern: /^[a-zA-Z0-9]{1,32}$/,
                            message: "请输入不超过32位字母数字",
                        },
                    ],
                },
                {
                    label: "外部料号",
                    editEnable: false,
                    type: "text",
                    name: ["baseInfoVO", "externalProductId"],
                },
                // /*占位*/
                // {
                //     label: "",
                //     name: "",
                //     editEnable: true,
                //     type: "text",
                // },
                // /*占位*/
                // {
                //     label: "",
                //     name: "",
                //     editEnable: true,
                //     type: "text",
                // },
                {
                    label: "HSCode",
                    editEnable: false,
                    name: ["baseInfoVO", "hsCode"],
                    type: "single-select",
                    from: "/ccs/customs/listHsV2",
                    customConfig: {
                        together: true,
                        showSearch: true,
                        copyTag: true,
                        // style: { width: 350 },
                        iconStyle: { height: 29 },
                    },
                    rules: [
                        {
                            required: true,
                            message: "请选择HSCode!",
                        },
                    ],
                },

                {
                    label: "增值税率%",
                    editEnable: true,
                    name: ["baseInfoVO", "vatRate"],
                    type: "text",
                },
                {
                    label: "消费税率%",
                    editEnable: true,
                    name: ["baseInfoVO", "taxRate"],
                    type: "text",
                },
                // /*占位*/
                // {
                //     label: "",
                //     name: "",
                //     editEnable: true,
                //     type: "text",
                // },
                {
                    label: "法一数量/单位",
                    editEnable: false,
                    name: ["baseInfoVO", "firstUnitAndAmount"],
                    type: "input-and-select",
                    from: "/ccs/customs/listUom",
                    labelCol: { span: 4 },
                    customConfig: { style: { width: "740px" }, together: true, showSearch: true },
                    rules: [
                        {
                            required: true,
                        },
                        {
                            validator: (_, value) => {
                                if (!value.input && !value.select) {
                                    return Promise.reject(new Error("请输入法定第一数量和单位"));
                                }
                                if (!value.input) {
                                    return Promise.reject(new Error("请输入法定第一数量"));
                                }
                                if (!RegExp("^[1-9]\\d*$|^[1-9]\\d*\\.\\d*|0\\.\\d*[1-9]\\d*$").test(value.input)) {
                                    return Promise.reject(new Error("清输入大于0的数字"));
                                }
                                if (!value.select) {
                                    return Promise.reject(new Error("请输入法定第一单位"));
                                }
                                return Promise.resolve();
                            },
                        },
                    ],
                },
                {
                    label: "法二数量/单位",
                    editEnable: false,
                    name: ["baseInfoVO", "secondUnitAndAmount"],
                    type: "input-and-select",
                    from: "/ccs/customs/listUom",
                    labelCol: { span: 4 },
                    customConfig: { style: { width: "740px" }, together: true, allowClear: true, showSearch: true },
                    rules: [
                        {
                            validator: (_, value) => {
                                if (value.select && value.select.length > 0 && !value.input) {
                                    return Promise.reject(new Error("请输入法定第二数量"));
                                }
                                if (
                                    value.input &&
                                    !RegExp("^[1-9]\\d*$|^[1-9]\\d*\\.\\d*|0\\.\\d*[1-9]\\d*$").test(value.input)
                                ) {
                                    return Promise.reject(new Error("清输入大于0的数字"));
                                }
                                if (value.input && !value.select) {
                                    return Promise.reject(new Error("请选择法定第二单位"));
                                }
                                return Promise.resolve();
                            },
                        },
                    ],
                },
                {
                    label: "原产国",
                    editEnable: false,
                    name: ["baseInfoVO", "originCountry"],
                    type: "single-select",
                    list: [],
                    from: "/ccs/customs/listCountry",
                    rules: [
                        {
                            required: true,
                            message: "请输入原产国!",
                        },
                    ],
                    customConfig: { showSearch: true, together: true },
                },
                {
                    label: "申报单位",
                    editEnable: false,
                    name: ["baseInfoVO", "declareUnit"],
                    type: "single-select",
                    from: "/ccs/customs/listUom",
                    customConfig: { showSearch: true, together: true },
                    rules: [
                        {
                            required: true,
                            message: "请选择申报单位!",
                        },
                    ],
                },
                {
                    label: "申报单价",
                    name: ["baseInfoVO", "declarePrice"],
                    editEnable: false,
                    type: "textInput",
                    rules: [
                        {
                            required: true,
                            message: "请输入申报单价!",
                        },
                    ],
                },
                {
                    label: "申报币制",
                    name: ["baseInfoVO", "declareCurrency"],
                    editEnable: false,
                    type: "single-select",
                    customConfig: { showSearch: true, together: true },
                    list: [],
                    from: "/ccs/customs/listCurrency",
                },
                {
                    label: "规格型号",
                    name: ["baseInfoVO", "model"],
                    editEnable: false,
                    type: "textInput",
                    labelCol: { span: 4 },
                    customConfig: { style: { width: "740px" } },
                    rules: [
                        {
                            required: true,
                            message: "请输入规格型号!",
                        },
                    ],
                },
                {
                    label: "国检备案号",
                    name: ["baseInfoVO", "countryRecordNo"],
                    editEnable: false,
                    type: "textInput",
                    labelCol: { span: 4 },
                    customConfig: { style: { width: "740px" } },
                },
                {
                    label: "功能",
                    name: ["baseInfoVO", "recordFunction"],
                    editEnable: false,
                    type: "text",
                    labelCol: { span: 4 },
                    customConfig: { style: { width: "740px" } },
                },
                {
                    label: "用途",
                    name: ["baseInfoVO", "recordUsage"],
                    editEnable: false,
                    type: "text",
                    labelCol: { span: 4 },
                    customConfig: { style: { width: "740px" } },
                },
                {
                    label: "成分",
                    name: ["baseInfoVO", "composition"],
                    editEnable: false,
                    type: "textarea",
                    labelCol: { span: 4 },
                    customConfig: { style: { width: "740px" } },
                },
                {
                    label: "申报要素",
                    name: ["baseInfoVO", "hgsbys"],
                    editEnable: false,
                    type: "textarea",
                    labelCol: { span: 4 },
                    customConfig: { style: { width: "740px" } },
                },
            ],
            label: "申报信息",
            name: "baseInfo",
            isGroup: true,
            className: "declareInfo",
        },
        rulesInfo: {
            type: "tableInput",
            name: ["baseInfoVO", "rules"],
            customConfig: { tableLayout: "fixed" },
            columns: [
                {
                    title: "规则名称",
                    name: "ruleName",
                    width: 230,
                    type: "text",
                },
                {
                    title: "成分名称",
                    name: "endangeredFactors",
                    width: 310,
                    type: "text",
                },
                {
                    title: "系统判定",
                    name: "isEndangered",
                    width: 110,
                    editEnable: true,
                    type: "renderContainer",
                    render: (item, disabled, fieldValue) => {
                        // console.log(item, disabled, fieldValue)
                        return (
                            <Space wrap>
                                {!item ? (
                                    <CheckCircleFilled style={{ color: "#06d538", fontSize: "20px" }} />
                                ) : (
                                    <CloseCircleFilled style={{ color: "red", fontSize: "20px" }} />
                                )}
                            </Space>
                        );
                    },
                },
            ],
            label: "备案规则：",
            isGroup: false,
            className: "rulesInfo",
        },
        goodsInfo: {
            type: "tableInput",
            name: ["productInfoVO"],
            delete: true,
            customConfig: { tableLayout: "fixed", operationConfig: { width: 180 } },
            handleDelete: prop.goodsInfoHandleDelete,
            columns: [
                {
                    title: "账册编号",
                    name: "customsBookNo",
                    width: 180,
                    type: "text",
                    wrapperCol: { span: 24 },
                },
                {
                    title: (
                        <Tooltip title={"【海关备案料号】赋值优先级：海关备案料号>跨境通关关联料号>通关料号>统一料号"}>
                            海关备案料号
                            <QuestionCircleOutlined style={{ margin: "0px 10px 0px 5px" }} />
                        </Tooltip>
                    ),
                    name: "customsRecordProductId",
                    width: 230,
                    type: "text",
                    wrapperCol: { span: 24 },
                },
                {
                    title: "原产国",
                    name: "originCountry",
                    width: 150,
                    type: "text",
                    wrapperCol: { span: 24 },
                },
                {
                    title: "金二序号",
                    name: "goodsSeqNo",
                    width: 120,
                    type: "text",
                    wrapperCol: { span: 24 },
                },
            ],
            label: "商品列表",
            isGroup: false,
            className: "goodsInfo",
        },
        extraInfo: {
            children: [
                {
                    label: "生产企业名称",
                    editEnable: true,
                    type: "text",
                    name: ["baseInfoVO", "productCompanyName"],
                    labelCol: { span: 4 },
                    customConfig: { style: { width: "740px" } },
                },
                {
                    label: "生产企业注册编号",
                    editEnable: true,
                    type: "text",
                    name: ["baseInfoVO", "productCompanyRegisterNumber"],
                    labelCol: { span: 4 },
                    customConfig: { style: { width: "740px" } },
                },
                {
                    label: "生产企业地址",
                    editEnable: true,
                    type: "text",
                    name: ["baseInfoVO", "productCompanyAddress"],
                    labelCol: { span: 4 },
                    customConfig: { style: { width: "740px" } },
                },
                {
                    label: "商品链接",
                    editEnable: true,
                    type: "text",
                    name: ["baseInfoVO", "productLink"],
                },
                {
                    label: "附件",
                    editEnable: true,
                    type: "renderContainer",
                    name: ["baseInfoVO", "attachmentName"],
                    labelCol: { span: 4 },
                    customConfig: { style: { width: "740px" } },
                    render: (item, disabled, fieldValue) => {
                        return (
                            <Button type={"link"} style={{ padding: "unset" }} onClick={() => prop.onClick(item)}>
                                {fieldValue}
                            </Button>
                        );
                    },
                },
            ],
            label: "补充信息",
            name: "extraInfo",
            isGroup: true,
            className: "baseInfo",
        },
        customsWarehouseInfo: {
            type: "tableInput",
            name: ["customsWarehouseInfoVO", "customsWarehouseResVOList"],
            renderOperation: prop.renderOperation,
            columns: [
                {
                    title: "实体仓名称",
                    name: "erpWarehouseName",
                    width: 190,
                    type: "text",
                    config: [
                        {
                            required: true,
                            message: "请选择商品代码",
                        },
                    ],
                },
                {
                    title: "账册编号",
                    name: "customsBookNo",
                    width: 190,
                    type: "text",
                    config: [
                        {
                            required: true,
                            message: "请选择商品代码",
                        },
                    ],
                },
                {
                    title: (
                        <Tooltip title={"【海关备案料号】赋值优先级：海关备案料号>跨境通关关联料号>通关料号>统一料号"}>
                            通关料号
                            <QuestionCircleOutlined style={{ margin: "0px 10px 0px 5px" }} />
                        </Tooltip>
                    ),
                    name: "customsDeclareProductIdListDesc",
                    width: 180,
                    editEnable: true,
                    type: "text",
                },
            ],
            label: "仓库信息",
            isGroup: false,
            className: "customsWarehouseInfo",
            titleAction: prop.titleAction,
        },
    };
}

export const goodsRecordLinkDetailConfig = prop => {
    return {
        baseInfo: {
            children: [
                {
                    label: "关联料号",
                    editEnable: true,
                    type: "renderContainer",
                    labelCol: { span: 4 },
                    name: "associatedProductId",
                    customConfig: { style: { width: "740px" } },
                    render: (item, disabled, fieldValue) => {
                        return (
                            <Space>
                                <span style={{ width: "150px", display: "inline-block" }}>{fieldValue}</span>
                                {
                                    <>
                                        <Tooltip title={"系统关联的新品入仓申报基础数据和跨境电商进口申报的基础数据"}>
                                            <QuestionCircleOutlined style={{ margin: "0px 10px 0px 5px" }} />
                                        </Tooltip>
                                        <Button
                                            type="primary"
                                            size={"small"}
                                            shape={"round"}
                                            disabled={disabled}
                                            onClick={() => prop.onClick(item)}>
                                            检查账册
                                        </Button>
                                    </>
                                }
                            </Space>
                        );
                    },
                },
                {
                    labelCol: { span: 4 },
                    label: "关联商品名称",
                    editEnable: true,
                    name: "productName",
                    type: "text",
                    customConfig: { style: { width: "740px" } },
                },
                {
                    label: "法定计量单位",
                    editEnable: false,
                    name: "firstUnit",
                    type: "text",
                },
                {
                    label: "法定数量",
                    editEnable: true,
                    name: "firstUnitAmount",
                    type: "text",
                },
                {
                    label: "法定第二计量单位",
                    editEnable: true,
                    name: "secondUnit",
                    type: "text",
                },
                {
                    label: "第二法定数量",
                    editEnable: false,
                    name: "secondUnitAmount",
                    type: "text",
                },
                {
                    label: "原产国",
                    editEnable: false,
                    name: "originCountry",
                    type: "text",
                },
                {
                    label: "HS编码",
                    editEnable: false,
                    name: "hsCode",
                    type: "text",
                },
                {
                    label: "增值税率%",
                    name: "vatRate",
                    editEnable: true,
                    type: "text",
                },
                {
                    label: "消费税率%",
                    name: "taxRate",
                    editEnable: false,
                    type: "text",
                },
                {
                    label: "规格型号",
                    name: "model",
                    editEnable: false,
                    type: "text",
                },
                {
                    label: "申报单位",
                    name: "declareUnit",
                    editEnable: false,
                    type: "text",
                },
                {
                    label: "申报单价",
                    name: "declarePrice",
                    editEnable: false,
                    type: "text",
                },
                {
                    label: "申报币制",
                    name: "declareCurrency",
                    editEnable: false,
                    type: "text",
                },
                {
                    label: "净重",
                    name: "netWeight",
                    editEnable: false,
                    type: "text",
                },
                {
                    label: "毛重",
                    name: "grossWeight",
                    editEnable: false,
                    type: "text",
                },
            ],
            label: "跨境申报-进口信息",
            name: "baseInfo",
            isGroup: true,
            className: "baseInfo",
        },
    };
};

export const goodsRecordBookDetailConfig = () => {
    return {
        baseInfo: {
            children: [
                {
                    label: "金二序号",
                    editEnable: true,
                    name: "goodsSeqNo",
                    type: "text",
                },
                {
                    label: "料号",
                    editEnable: true,
                    name: "productId",
                    type: "text",
                },
                {
                    label: "商品名称",
                    editEnable: true,
                    name: "goodsName",
                    type: "text",
                },
                {
                    label: "法定计量单位",
                    editEnable: false,
                    name: "firstUnitDesc",
                    type: "text",
                },
                {
                    label: "法定数量",
                    editEnable: true,
                    name: "firstUnitAmount",
                    type: "text",
                },
                {
                    label: "法定第二计量单位",
                    editEnable: true,
                    name: "secondUnitDesc",
                    type: "text",
                },
                {
                    label: "第二法定数量",
                    editEnable: false,
                    name: "secondUnitAmount",
                    type: "text",
                },
                {
                    label: "HS编码",
                    editEnable: false,
                    name: "hsCodeDesc",
                    type: "text",
                },
                {
                    label: "原产国",
                    name: "originCountryDesc",
                    editEnable: true,
                    type: "text",
                },
                {
                    label: "规格型号",
                    name: "model",
                    editEnable: false,
                    type: "text",
                },
                {
                    label: "申报单位",
                    name: "declareUnitDesc",
                    editEnable: false,
                    type: "text",
                },
                {
                    label: "申报单价",
                    name: "declarePrice",
                    editEnable: false,
                    type: "text",
                },
                {
                    label: "申报币制",
                    name: "declareCurrencyDesc",
                    editEnable: false,
                    type: "text",
                },
                {
                    label: "记账清单编号",
                    name: "invtNo",
                    editEnable: false,
                    type: "text",
                },
            ],
            name: "baseInfo",
            isGroup: true,
            className: "baseInfo",
        },
    };
};
