import React from "react";
import "../../shop-good.less";
import moment from "moment";
import BottomTag from "../../../components/BottomTag";
import { message, Modal, Space, Switch, Tabs, Tag, Button, Tooltip } from "antd";
import { lib, SearchList, event, HOC, getConfigDataUtils } from "react-single-app";
import axios from "axios";
import "./index.less";
import { AuditModal, RejectModal } from "./audit-modal";
import { StatusTabs } from "./components/status-tab";
import NewModal from "../../../components/NewModal";
import RejectReasonSetModal from "./components/reject-reason-set-modal";

@HOC.mapAuthButtonsToState({})
class App extends SearchList {
    constructor(props) {
        super(props);
        this.formRef = React.createRef();
        this.statusTabRef = React.createRef();
        this.state.modalTitle = "创建商品档案";
        // 编辑/新建url
        this.state.upUrl = "/ccs/goodsRecord/upset";
        // 获取详情url
        this.state.detailUrl = "/ccs/goodsRecord/detail";
        this.state.hsCodes = [];
        this.state.units = [];
        this.state.currency = [];
        this.state.countrys = [];
        this.state.buttonAuth = { importAuth: false };
        this.state.customs = [];
        this.state.warehouses = [];
        this.state.flag = false;
        this.state.tabsList = [
            {
                tab: "审核状态",
                dataList: [],
                type: "TABLE",
                columns: [
                    {
                        title: "审核状态",
                        dataIndex: "recordStatusDesc",
                        align: "center",
                        width: "",
                        formType: "TEXT",
                        xhr: "",
                    },
                    {
                        title: "用户",
                        dataIndex: "lesseeName",
                        align: "center",
                        width: "",
                        formType: "TEXT",
                        xhr: "",
                    },
                    {
                        title: "仓库",
                        dataIndex: "warehouseName",
                        align: "center",
                        width: "",
                        formType: "TEXT",
                        xhr: "",
                    },
                    {
                        title: "账册编号",
                        dataIndex: "customsBookNo",
                        align: "center",
                        width: "100",
                        formType: "SELECT",
                        xhr: "/ccs/customsBook/listBookNoByAccBookAuth",
                        together: "true",
                        extra: "/ccs/customsBook/listAllBookNo",
                    },
                    {
                        title: "国检备案号",
                        dataIndex: "countryRecordNo",
                        align: "center",
                        width: "",
                        formType: "TEXT",
                        required: "false",
                        numberType: "null",
                        extra: "",
                    },
                    {
                        title: "完成时间",
                        dataIndex: "recordFinishTime",
                        align: "center",
                        width: "",
                        formType: "TEXT",
                        xhr: "",
                    },
                ],
                tabsList: [],
            },
            {
                tab: "商品基础信息",
                type: "TABLE",
                columns: [
                    {
                        title: "SKU",
                        dataIndex: "skuId",
                        align: "center",
                        width: "200",
                        formType: "INPUT",
                        xhr: "",
                    },
                    {
                        title: "备案名称",
                        dataIndex: "goodsRecordName",
                        align: "center",
                        width: "200",
                        formType: "INPUT",
                        xhr: "",
                    },
                    {
                        title: "料号",
                        dataIndex: "productId",
                        align: "center",
                        width: "200",
                        formType: "INPUT",
                        xhr: "",
                    },
                    {
                        title: "条码",
                        dataIndex: "barCode",
                        align: "center",
                        width: "200",
                        formType: "INPUT",
                        xhr: "",
                    },
                    {
                        title: "型号",
                        dataIndex: "model",
                        align: "center",
                        width: "200",
                        formType: "INPUT",
                        xhr: "",
                    },
                    {
                        title: "品牌(中文)",
                        dataIndex: "brand",
                        align: "center",
                        width: "200",
                        formType: "INPUT",
                        xhr: "",
                    },
                    {
                        title: "品牌(英文)",
                        dataIndex: "brandEn",
                        align: "center",
                        width: "200",
                        formType: "INPUT",
                        xhr: "",
                    },
                    {
                        title: "申报单价",
                        dataIndex: "declarePrice",
                        align: "center",
                        width: "140",
                        formType: "INPUTNUMBER",
                        xhr: "",
                        numberType: "integer",
                        required: "false",
                        together: "false",
                    },
                    {
                        title: "申报单位",
                        dataIndex: "declareUnit",
                        align: "center",
                        width: "140",
                        formType: "SELECT",
                        xhr: "/ccs/customs/listUom",
                        together: "true",
                        extra: "/ccs/customs/listUom",
                    },
                    {
                        title: "净重(KG)",
                        dataIndex: "netWeight",
                        align: "center",
                        width: "140",
                        formType: "INPUTNUMBER",
                        xhr: "",
                        numberType: "decimal",
                    },
                    {
                        title: "毛重(KG)",
                        dataIndex: "grossWeight",
                        align: "center",
                        width: "140",
                        formType: "INPUTNUMBER",
                        xhr: "",
                        numberType: "decimal",
                    },
                    {
                        title: "长(MM)",
                        dataIndex: "length",
                        align: "center",
                        width: "140",
                        formType: "INPUTNUMBER",
                        xhr: "",
                        numberType: "integer",
                    },
                    {
                        title: "宽(MM)",
                        dataIndex: "width",
                        align: "center",
                        width: "140",
                        formType: "INPUTNUMBER",
                        xhr: "",
                        numberType: "integer",
                    },
                    {
                        title: "高(MM)",
                        dataIndex: "height",
                        align: "center",
                        width: "140",
                        formType: "INPUTNUMBER",
                        xhr: "",
                        numberType: "integer",
                    },
                ],
                tabsList: [],
            },
            {
                tab: "申报信息",
                type: "TABLE",
                columns: [
                    {
                        title: "海关申报要素",
                        dataIndex: "hgsbys",
                        align: "center",
                        width: "700",
                        formType: "INPUT",
                        xhr: "",
                    },
                    {
                        title: "用途",
                        dataIndex: "recordUsage",
                        align: "center",
                        width: "200",
                        formType: "INPUT",
                        xhr: "",
                    },
                    {
                        title: "HsCode",
                        dataIndex: "hsCode",
                        align: "center",
                        width: "600",
                        formType: "SELECT",
                        xhr: "/ccs/customs/listHs",
                        together: "true",
                        extra: "/ccs/customs/listHs",
                    },
                    {
                        title: "增值税率",
                        dataIndex: "vatRate",
                        align: "center",
                        width: "140",
                        formType: "TEXT",
                        xhr: "",
                    },
                    {
                        title: "消费税率",
                        dataIndex: "taxRate",
                        align: "left",
                        width: "140",
                        formType: "INPUT",
                        xhr: "",
                    },
                    {
                        title: "成分",
                        dataIndex: "composition",
                        align: "center",
                        width: "700",
                        formType: "INPUT",
                        xhr: "",
                    },
                    {
                        title: "海关原产国",
                        dataIndex: "originCountry",
                        align: "center",
                        width: "140",
                        formType: "SELECT",
                        xhr: "/ccs/customs/listCountry",
                        together: "true",
                        extra: "/ccs/customs/listCountry",
                    },
                    {
                        title: "申报币制",
                        dataIndex: "declareCurrency",
                        align: "center",
                        width: "140",
                        formType: "INPUT",
                    },
                    {
                        title: "功能",
                        dataIndex: "recordFunction",
                        align: "center",
                        width: "200",
                        formType: "INPUT",
                        xhr: "",
                    },
                    {
                        title: "法定第一计量单位",
                        dataIndex: "firstUnit",
                        align: "center",
                        width: "200",
                        formType: "SELECT",
                        xhr: "/ccs/customs/listUom",
                        together: "true",
                        extra: "/ccs/customs/listUom",
                    },
                    {
                        title: "法定第一计量数量",
                        dataIndex: "firstUnitAmount",
                        align: "center",
                        width: "140",
                        formType: "INPUTNUMBER",
                        xhr: "",
                        numberType: "integer",
                    },
                    {
                        title: "法定第二计量单位",
                        dataIndex: "secondUnit",
                        align: "center",
                        width: "200",
                        formType: "SELECT",
                        xhr: "/ccs/customs/listUom",
                        together: "true",
                        extra: "/ccs/customs/listUom",
                    },
                    {
                        title: "法定第二计量数量",
                        dataIndex: "secondUnitAmount",
                        align: "center",
                        width: "200",
                        formType: "INPUTNUMBER",
                        xhr: "",
                        numberType: "integer",
                    },
                ],
                tabsList: [],
            },
        ];
        this.onSearchReset = this.onSearchReset.bind(this);
        this.state.importOpen = false;
        this.state.importConfig = [
            {
                labelName: "口岸",
                labelKey: "customDistrict",
                type: "SELECT",
                required: true,
                list: [],
                from: "/ccs/customs/listDistrict",
            },
        ];
    }

    componentDidMount() {
        this.getConfigList();
        event.on("onSearchReset", this.onSearchReset);
        this.getButtons("CCS_ADMIN", "/goods-record");
    }

    async getButtons(systemCode, pagePath) {
        const data = { systemCode, pagePath };
        const res = await lib.request({
            url: "/ucenter-account/current/lastButtonList",
            data,
        });
        if (res && Array.isArray(res)) {
            const formatBtns = res.map(i => i.code);
            this.setState({
                buttonAuth: { importAuth: formatBtns.includes("GOOD-RECORD-IMPORT") },
            });
        }
    }

    componentWillUnmount() {
        event.off("onSearchReset", this.onSearchReset);
    }

    onSearchReset() {
        this.statusTabRef.current?.setActiveKey("0");
        this.changeImmutable({ recordStatus: "" });
    }

    getConfigList() {
        let tabsList = this.state.tabsList;
        tabsList.map(item => {
            if (item.columns) {
                item.columns.map(ite => {
                    ite.formType === "SELECT" &&
                        lib.request({
                            url: ite.extra,
                            needMask: false,
                            success: res => {
                                ite.selectData = res || [];
                                this.setState({
                                    tabsList,
                                });
                            },
                        });
                });
            }
        });
        this.setState({
            tabsList,
        });
    }

    productIdFn(row) {
        return (
            <>
                {row.productId}
                <br />
                {row.goodsRecordTagList && (
                    <Space wrap>
                        {row.goodsRecordTagList.map(item => {
                            return (
                                <Tooltip title={row.goodsRecordTagDesc}>
                                    <Tag color={"red"}>{item}</Tag>
                                </Tooltip>
                            );
                        })}
                    </Space>
                )}
            </>
        );
    }

    renderProductId(row) {
        return (
            <a style={{ whiteSpace: "pre" }} className="link" onClick={() => this.showDetail(row)}>
                {row.productId}
            </a>
        );
    }

    showDetail(row) {
        this.setState({
            id: row.id,
        });
        this.getConfigList();
        lib.request({
            url: this.state.detailUrl,
            data: {
                id: row.id,
            },
            needMask: true,
            success: res => {
                let result = JSON.parse(JSON.stringify(res));
                result.inDate = moment(result.inDate);
                let { tabsList } = this.state;
                tabsList.map(item => {
                    item.dataList = [result];
                });
                this.setDetail(tabsList);
            },
        });
    }

    renderDetail(tabsList, id) {
        let props = {
            tabsList,
            showSubmitBtn: false,
            id: tabsList[0].dataList[0].id,
            tabSubmit: this.changeDetail.bind(this),
        };
        return <BottomTag {...props}></BottomTag>;
    }

    getConfig() {
        return axios.get(getConfigDataUtils.getDataUrlByDataId(396)).then(res => res.data.data);
    }

    renderOperationTopView() {
        return (
            <StatusTabs
                ref={this.statusTabRef}
                tabOnChange={e => {
                    if (e === 0) {
                        this.changeImmutable({ recordStatus: "" });
                    } else {
                        this.changeImmutable({ recordStatus: Number(e) });
                    }
                }}
            />
        );
    }

    renderLeftOperation() {
        const { buttons } = this.state;
        return (
            <>
                <Button
                    onClick={() => {
                        lib.openPage(`/ccs/import-data?page_title=导入平台备案&code=IMPORT_GOODS_AUTO_RECORD`, () =>
                            this.load(),
                        );
                    }}>
                    导入平台备案
                </Button>
                {buttons.includes("RETEST-RECORD") && (
                    <Button
                        onClick={() => {
                            this.reTestRecord();
                        }}>
                        重试备案回传
                    </Button>
                )}
            </>
        );
    }

    reTestRecord() {
        const { selectedRows } = this.state;
        const ids = selectedRows.map(item => item.id);
        if (ids.length <= 0) return message.warning("请选择数据");
        lib.request({
            url: "/ccs/goodsRecord/retryAuditMsg",
            data: {
                ids: ids.join(","),
            },
            success: () => {
                message.success("重试成功");
                this.load();
            },
        });
    }

    batchOperation(row, enable) {
        lib.request({
            url: "/ccs/goodsRecord/updateEnable",
            method: "POST",
            data: {
                id: row.id,
                enable,
            },
            needMask: true,
            success: res => {
                if (res) {
                    this.load(true);
                }
            },
        });
    }

    enableFunc(row) {
        Modal.confirm({
            cancelText: "取消",
            okText: "确定",
            title: "提示",
            content: "是否禁用?",
            onOk: () => {
                this.batchOperation(row, 0);
                this.load(true);
            },
        });
    }

    editFunc(e, row) {
        lib.request({
            url: this.state.detailUrl,
            data: {
                id: row.id,
            },
            useCache: true,
            success: res => {
                console.log(res);
                this.setState({
                    visible: true,
                    editRow: res,
                    flag: true,
                    row: row,
                    modalTitle: "商品备案详情",
                });
            },
        });
    }

    deleteFunc(e, row) {
        e.stopPropagation();
        Modal.confirm({
            cancelText: "取消",
            okText: "确定",
            title: "提示",
            content: "是否删除?",
            onOk: () => {
                this.deleteRow(row);
            },
        });
    }

    enableStatus(row) {
        return (
            <React.Fragment>
                {row.recordStatusDesc === "备案完成" && row.enable === 1 && (
                    <Switch checked={row.enable} onChange={() => this.enableFunc(row)}></Switch>
                )}
                {row.recordStatusDesc === "备案完成" && row.enable === 0 && (
                    <Switch checked={row.enable} onChange={() => this.batchOperation(row, 1)}></Switch>
                )}
            </React.Fragment>
        );
    }

    myOperation(row) {
        //recordStatus 1, "待审核"),(2, "备案完成"),(4, "审核驳回");
        return (
            <React.Fragment>
                <Space style={{ flexWrap: "wrap" }}>
                    <a
                        className="link"
                        onClick={() => {
                            lib.openPage(
                                `/goods-record-details?pageTitle=商品备案详情&id=${row.id}&disableEdit=true&type=auth`,
                                () => {
                                    this.load(true);
                                    this.statusTabRef.current?.getDetail(this.state.search);
                                },
                            );
                        }}>
                        {row.recordStatus == 1 ? "审核" : "详情"}
                    </a>
                    {row.recordStatus === 1 && (
                        <a
                            className="link"
                            onClick={() => {
                                lib.openPage(
                                    `/goods-record-details?pageTitle=商品备案详情&id=${row.id}&disableEdit=true&type=edit`,
                                    () => {
                                        this.load(true);
                                        this.statusTabRef.current?.getDetail(this.state.search);
                                    },
                                );
                            }}>
                            编辑
                        </a>
                    )}
                    {row.recordStatus === 2 && (
                        <a
                            className="link"
                            onClick={() => {
                                lib.openPage(
                                    `/goods-record-details?pageTitle=商品备案详情&id=${row.id}&disableEdit=true&update=edit`,
                                    () => {
                                        this.load(true);
                                        this.statusTabRef.current?.getDetail(this.state.search);
                                    },
                                );
                            }}>
                            更新备案
                        </a>
                    )}
                    {this.state.buttons.includes("GOODS-RECORD-DELETE") && (
                        <a className="link" onClick={e => this.deleteFunc(e, row)}>
                            删除
                        </a>
                    )}
                </Space>
            </React.Fragment>
        );
    }
    onSearch(search) {
        if (!search.recordStatus) {
            search.recordStatus = 0;
        }
        this.statusTabRef.current?.getDetail(search);
    }
    deleteRow(row) {
        lib.request({
            url: `/ccs/goodsRecord/deleteById`,
            data: { id: row.id },
            needMask: true,
            success: res => {
                this.statusTabRef.current?.getDetail(this.state.search);
                this.load(true);
                message.success("删除成功");
            },
        });
    }

    changeDetail(values) {
        for (let i in values) {
            if (!values[i]) {
                delete values[i];
            }
        }
        if (this.state.editRow) {
            values.id = this.state.id;
        }
        values.declarePrice = values.declarePrice.toFixed(4);
        if (values.declarePrice === 0) {
            message.warning("申报单价最小为1");
            return;
        }
        values.grossWeight = parseFloat(values.grossWeight.toFixed(2));
        values.netWeight = parseFloat(values.netWeight.toFixed(2));
        values.width = parseInt(values.width);
        values.height = parseInt(values.height);
        values.length = parseInt(values.length);
        values.firstUnitAmount = parseInt(values.firstUnitAmount);
        values.secondUnitAmount = parseInt(values.secondUnitAmount);
        lib.request({
            url: this.state.upUrl,
            data: values,
            method: "POST",
            needMask: true,
            success: res => {
                if (res) {
                    this.setState({
                        visible: false,
                    });
                    if (this.state.editRow) {
                        message.success("修改成功");
                    } else {
                        message.success("创建成功");
                    }
                    this.clearBottomTabInfo();
                    this.load(true);
                    form.resetFields();
                }
            },
        });
    }

    renderRightOperation() {
        let { config, selectedRows, buttons } = this.state;
        const that = this;
        let page = config.page;
        return (
            <>
                <Button
                    className="btn"
                    disabled={page.exportDisabled || false}
                    onClick={() => {
                        let { pagination, search, importConfig } = this.state;
                        if (!search.customDistrict) {
                            lib.request({
                                url: importConfig[0].from,
                                data: {},
                                needMask: true,
                                success: res => {
                                    importConfig[0].list = res;
                                    this.setState({
                                        importConfig: importConfig,
                                        importOpen: true,
                                    });
                                },
                            });
                            return;
                        }
                        lib.request({
                            url: "/ccs/goodsRecord/exportExcel",
                            needMask: true,
                            data: { ...pagination, ...search },
                            success: json => {
                                lib.openPage("/excel/download-center?page_title=下载中心");
                            },
                        });
                    }}>
                    导出 &#xe638;
                </Button>
                {buttons.includes("REJECT-REASON-SET") && (
                    <Button
                        onClick={() => {
                            this.openRejectReasonSetOpen();
                        }}>
                        驳回原因配置
                    </Button>
                )}
                {buttons.includes("IMPORT_GOODS_RECORD_BATCH_UPDATE_AUDIT") && (
                    <Button
                        onClick={() => {
                            // IMPORT_GOODS_RECORD_BATCH_UPDATE_AUDIT
                            lib.openPage(
                                `/excel/import-data?page_title=批量更新审核结果导入&code=IMPORT_GOODS_RECORD_BATCH_UPDATE_AUDIT`,
                                () => this.load(),
                            );
                        }}>
                        批量更新审核结果
                    </Button>
                )}
                {/* GOODS_RECORD_BATCH_UPDATE */}
                {buttons.includes("GOODS_RECORD_BATCH_UPDATE") && (
                    <Button
                        onClick={() => {
                            // IMPORT_GOODS_RECORD_BATCH_UPDATE_AUDIT
                            lib.openPage(
                                `/excel/import-data?page_title=批量更新备案导入&code=GOODS_RECORD_BATCH_UPDATE`,
                                () => this.load(),
                            );
                        }}>
                        批量更新备案
                    </Button>
                )}
            </>
        );
    }

    openRejectReasonSetOpen() {
        this.setState({
            rejectOpen: true,
        });
    }

    renderModal = () => {
        return (
            <>
                <NewModal
                    visible={this.state.importOpen}
                    onOk={data => {
                        let { pagination, search } = this.state;
                        lib.request({
                            url: "/ccs/goodsRecord/exportExcel",
                            needMask: true,
                            data: { ...pagination, ...search, ...data },
                            success: json => {
                                this.setState({
                                    importOpen: false,
                                });
                                lib.openPage("/excel/download-center?page_title=下载中心");
                            },
                        });
                    }}
                    title={"商品备案导出"}
                    configList={this.state.importConfig}
                    onCancel={() => {
                        this.setState({
                            importOpen: false,
                        });
                    }}
                />
                <RejectReasonSetModal
                    visible={this.state.rejectOpen}
                    closeVisible={() => {
                        this.setState({ rejectOpen: false });
                    }}
                />
            </>
        );
    };
}

export default App;
