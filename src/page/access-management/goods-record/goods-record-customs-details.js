import React, { useState, useEffect, createContext } from "react";
import "../../shop-good.less";
import { Tabs } from "antd";
import { lib } from "react-single-app";
// import GoodsRecordCheckDetail from "./goods-record-check-detail";
import GoodsRecordCheckDetail from "./goods-record-check-new-detail/index";
export default function GoodsRecordCustomsDetails() {
    const [tabList, setTabList] = useState([]);
    const [activeKey, setActiveKey] = useState("0");
    function getTabList(customsCode) {
        const id = lib.getParam("id");
        lib.request({
            url: "/ccs/goodsRecord/recordCustomsList",
            data: { id },
            success: res => {
                setTabList(res);
                if (customsCode) {
                    let activeKey = res.findIndex(ite => {
                        return ite.customsCode === customsCode;
                    });
                    if (activeKey !== -1) {
                        setActiveKey(`${activeKey}`);
                    } else {
                        setActiveKey("0");
                    }
                }
            },
        });
    }

    useEffect(() => {
        getTabList();
    }, []);
    return (
        <div className={"customs-tab"}>
            <Tabs
                activeKey={activeKey}
                defaultActiveKey={"0"}
                destroyInactiveTabPane={true}
                style={{ height: "100%" }}
                onChange={e => {
                    if (e != activeKey) {
                        setActiveKey(e);
                    }
                }}>
                {tabList &&
                    tabList.map((item, index) => {
                        let tabTitle = `${item.customs} - ${item.statusDesc}`;
                        return (
                            <Tabs.TabPane tab={tabTitle} key={`${index}`} style={{ height: "100%" }}>
                                <GoodsRecordCheckDetail customs={item} getTabList={getTabList} />
                            </Tabs.TabPane>
                        );
                    })}
            </Tabs>
        </div>
    );
}
