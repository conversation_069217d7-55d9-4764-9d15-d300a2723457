import { useEffect, useState } from "react";
import { ConfigFormCenter, event, getConfigDataUtils, lib, SearchList, HOC } from "react-single-app";
import { Modal, Table } from "antd";
export default ({ open, closeFn, row }) => {
    const columns = [
        {
            title: "清关单号",
            dataIndex: "inventoryOrderSn",
            key: "inventoryOrderSn",
        },
        {
            title: "锁定数量",
            dataIndex: "lockStockNum",
            key: "lockStockNum",
        },
        {
            title: "清关单状态",
            dataIndex: "inventoryOrderStatus",
            key: "inventoryOrderStatus",
        },
        {
            title: "业务类型",
            dataIndex: "inventoryOrderBizType",
            key: "inventoryOrderBizType",
        },
        {
            title: "单据来源",
            dataIndex: "inventoryOrderChannel",
            key: "inventoryOrderChannel",
        },
    ];
    const [detailTableData, setDetailTableData] = useState([]);
    // const [modalPagination, setModalPagination] = useState({})
    const getData = () => {
        lib.request({
            url: "/ccs/customsBookItem/listLockStock",
            data: {
                id: row.id,
            },
            success: data => {
                setDetailTableData(data);
            },
        });
    };

    useEffect(() => {
        if (open) {
            getData();
        }
    }, [open]);

    return (
        <>
            <Modal
                title={"锁定库存明细"}
                open={open}
                footer={null}
                width={800}
                onCancel={() => {
                    closeFn();
                }}>
                <Table columns={columns} dataSource={detailTableData} pagination={false} />
            </Modal>
        </>
    );
};
