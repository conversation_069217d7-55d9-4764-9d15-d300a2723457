import React, { useState, useEffect } from "react";
import { ConfigFormCenter, lib } from "react-single-app";
import { BooksInventoryDetailConfig } from "./view-config";
import "./index.less";
import { Typography, Divider } from "antd";
import LogTable from "../../../components/LogTable";

const { Title } = Typography;

export default function BooksInventoryDetail() {
    let [detailData, setDetailData] = useState();
    const configData = BooksInventoryDetailConfig();
    const ref = React.useRef();
    const code = lib.getParam("id");

    function getDetail() {
        ref.current.getDetail({ url: "/ccs/customsBookItem/detail", data: { id: code } });
    }

    useEffect(() => {
        getDetail();
    }, []);

    const beforeSetDetail = data => {
        setDetailData(data);
        return data;
    };

    return (
        <div style={{ height: "100%" }}>
            <div className="books-inventory-detail">
                <ConfigFormCenter ref={ref} code={code} confData={configData} beforeSetDetail={beforeSetDetail} />
                <Divider />
                <Title level={5}>账册日志</Title>
                {detailData && (
                    <LogTable
                        api={"/ccs/customsBookItem/pagingTrackLog"}
                        param={{ customsBookItemId: detailData.id }}
                    />
                )}
            </div>
        </div>
    );
}
