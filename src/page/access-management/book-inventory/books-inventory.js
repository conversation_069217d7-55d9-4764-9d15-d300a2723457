import React, { useEffect, useState } from "react";
import { ConfigFormCenter, event, getConfigDataUtils, lib, SearchList, HOC } from "react-single-app";
import { Alert, Button, Drawer, message, Modal, Space, Switch, Table, Tabs, Dropdown, Menu } from "antd";
import { SwapOutlined, DownOutlined } from "@ant-design/icons";
import axios from "axios";
import { BooksInventoryEditConfig } from "./view-config";
import { useAntdTable } from "ahooks";
import AppendModal from "../../../components/AppendModal";
import LockedModal from "./locked-modal";

export function AccountNumDetail({ url, param, onClose, visible }) {
    const defaultPageSize = 10;
    const ref = React.useRef();
    const timeOutIdRef = React.useRef();
    const [tableHeight, setTableHeight] = useState();
    useEffect(() => {
        if (visible) {
            timeOutIdRef.current = setTimeout(() => {
                setTableHeight(ref.current.parentElement.offsetHeight - 160);
            }, 100);
            run({ current: 1, pageSize: defaultPageSize });
        }
        return () => {
            clearTimeout(timeOutIdRef.current);
        };
    }, [visible]);
    const getTableData = ({ current, pageSize }) => {
        return new Promise((resolve, reject) => {
            lib.request({
                url: url,
                data: { ...param, currentPage: current, pageSize },
                success: res => {
                    resolve({
                        total: res.page.totalCount,
                        list: res.dataList,
                    });
                },
                fail: e => {
                    resolve({
                        list: [],
                    });
                },
            });
        });
    };
    const { tableProps, run } = useAntdTable(getTableData, {
        defaultPageSize: 20,
        manual: true,
        showSizeChanger: true,
        showQuickJumper: true,
        refreshDeps: [param],
    });
    const columns = [
        {
            title: "申报单号",
            dataIndex: "declareOrderNo",
            width: 210,
        },
        {
            title: "清单编号",
            dataIndex: "inventoryNo",
            width: 230,
        },
        {
            title: "数量",
            dataIndex: "count",
            width: 100,
        },
        {
            title: "清单状态",
            dataIndex: "inventoryStatus",
            width: 100,
        },
        {
            title: "售后状态",
            dataIndex: "afterSaleStatus",
            width: 100,
        },
        {
            title: "核注状态",
            dataIndex: "endorsementStatus",
            width: 100,
        },
        {
            title: "核注单号",
            dataIndex: "endorsementSn",
            width: 210,
        },
        {
            title: "核放单号",
            dataIndex: "checklistSn",
            width: 210,
        },
    ];
    return (
        <Drawer title={"占用订单明细"} onClose={onClose} width={1200} open={visible} bodyStyle={{ overflow: "hidden" }}>
            <div rootClassName="table-panel" ref={ref}>
                {visible && (
                    <Table
                        columns={columns}
                        scroll={{ y: tableHeight }}
                        rowKey={"id"}
                        size="small"
                        dataSource={tableProps?.dataSource}
                        onChange={tableProps?.onChange}
                        loading={tableProps?.loading}
                        pagination={{
                            size: "default",
                            ...tableProps?.pagination,
                            showSizeChanger: true,
                            showQuickJumper: true,
                            showTotal: total => `总共 ${total} 条`,
                        }}
                    />
                )}
            </div>
        </Drawer>
    );
}

// enable
@HOC.mapAuthButtonsToState({ buttonCodeArr: [] })
class App extends SearchList {
    constructor(props) {
        super(props);
        this.state.modalTitle = "新增账册";
        // 编辑/新建url
        this.state.upUrl = "/ccs/customsBookItem/upset";
        this.state.unitList = [];
        this.state.hsList = [];
        this.state.activeKey = "All";
        this.goodsSeqNoCondition = null;
        this.onSearchReset = this.onSearchReset.bind(this);
        this.state.modalPagination = {
            currentPage: 1,
            pageSize: 10,
            totalCount: 0,
        };
        this.state.typeList = [];
    }

    getConfig() {
        return axios.get(getConfigDataUtils.getDataUrlByDataId(410)).then(res => res.data.data);
    }

    componentDidMount() {
        this.getTypeList();
        event.on("onSearchReset", this.onSearchReset);
    }

    componentWillUnmount() {
        event.off("onSearchReset", this.onSearchReset);
    }

    getTypeList() {
        lib.request({
            url: "/ccs/customsBookItem/listAdjustType",
            success: data => {
                this.setState({
                    typeList: data,
                });
            },
        });
    }

    renderOperationTopView() {
        return (
            <Tabs
                defaultActiveKey={"All"}
                activeKey={this.state.activeKey}
                onChange={e => {
                    if (e !== this.state.activeKey) {
                        this.setState({ activeKey: e });
                    }
                    this.changeImmutable({ exceptionType: e });
                }}>
                {[
                    { id: "All", name: "全部" },
                    { id: "STOCK", name: "库存异常" },
                ].map(item => (
                    <Tabs.TabPane tab={item.name} key={item.id} />
                ))}
            </Tabs>
        );
    }

    onSearchReset() {
        this.goodsSeqNoCondition = null;
        this.setState({ activeKey: "All" });
        this.changeImmutable({ exceptionType: "All" });
    }

    renderProductId(row) {
        return (
            <span
                className="link"
                onClick={() => {
                    lib.openPage(`/books-inventory-detail?page_title=账册库存详情&id=${row.id}`);
                }}>
                {row.productId}
            </span>
        );
    }

    renderOccupiedNum(row) {
        return (
            <span className="link" onClick={() => this.showOccupiedNum(row)}>
                {row.occupiedNum}
            </span>
        );
    }

    showOccupiedNum(row) {
        this.setState({
            customsBookItemId: row.id,
            showAccountNumDetail: true,
        });
    }

    configLoadDefaultParams() {
        return {
            goodsSeqNoCondition: this.goodsSeqNoCondition || "Default",
        };
    }

    usedNumFn(row) {
        return (
            <>
                <a
                    onClick={() => {
                        this.setState({
                            lockedOpen: true,
                            editRow: row,
                        });
                    }}>
                    {row.lockedNum}
                </a>
            </>
        );
    }

    renderRightOperation() {
        return (
            <Space>
                <Button
                    onClick={() =>
                        lib.openPage(`/excel/import-data?page_title=调整库存导入&code=IMPORT_STOCK_ADJUST`, () =>
                            this.load(),
                        )
                    }>
                    调整库存导入
                </Button>
                <Button
                    onClick={() =>
                        lib.openPage(`/excel/import-data?page_title=导入统一料号&code=IMPORT_PRODUCT_STOCK`, () =>
                            this.load(),
                        )
                    }>
                    导入统一料号
                </Button>
            </Space>
        );
    }

    // 从换行字符串转化成用，连接的字符串
    transValue(value) {
        if (typeof value !== "string") return value;
        if (!value) return "";
        if (value.indexOf("\n") === -1) return value.trim().replace(/ /g, "\t");
        return value
            .split("\n")
            .map(item => item.trim().replace(/ /g, "\t"))
            .join(",");
    }

    renderLeftOperation() {
        const { buttons } = this.state;
        return (
            <Space>
                {buttons.includes("enable") ? (
                    <>
                        <Button onClick={() => this.batchOperation(1)}>批量启用</Button>
                        <Button onClick={() => this.batchOperation(0)}>批量禁用</Button>
                    </>
                ) : null}

                {/* <Button type='primary' onClick={() => this.exportFunc()}>导出数据</Button> */}
                <Button
                    onClick={() => {
                        this.goodsSeqNoCondition = this.state.goodsSeqNoCondition === "Desc" ? "Asc" : "Desc";
                        this.setState(
                            {
                                goodsSeqNoCondition: this.state.goodsSeqNoCondition === "Desc" ? "Asc" : "Desc",
                            },
                            () => {
                                this.load();
                            },
                        );
                    }}>
                    金二序号排序
                    {this.state.goodsSeqNoCondition && <SwapOutlined rotate={90} />}
                </Button>
            </Space>
        );
    }

    // 导出数据
    exportFunc() {
        var data = {};
        data.currentPage = this.state.pagination.currentPage;
        data.pageSize = this.state.pagination.pageSize;
        var page = this.state.page;
        for (var key in this.state.page) {
            if (page[key].value !== "") {
                data[key] = page[key].value;
            }
        }
        lib.request({
            url: "/ccs/customsBookItem/exportExcelByDownLoadCenter",
            data,
            needMask: true,
            success: res => {
                if (res) {
                    let url = `/download-center/${new Date().getTime()}?page_title=下载中心&config_id=1593580312171226&refresh_event=${new Date().getTime()}`;
                    window.indexProps.history.push(url);
                    event.emit("add-page", {
                        url,
                    });
                }
            },
        });
    }

    // 禁用启用
    batchOperation(enable) {
        let { selectedRows } = this.state;
        let idList = selectedRows.reduce((prev, curr) => [...prev, curr.id], []);
        if (idList.length === 0) {
            message.warning("请选择数据");
            return;
        }
        lib.request({
            url: "/ccs/customsBookItem/batchEnable",
            method: "POST",
            data: {
                ids: idList,
                enable,
            },
            needMask: true,
            success: res => {
                if (!res.errorMessage) {
                    message.success("修改成功");
                    this.load(true);
                } else {
                    message.error(res.errorMessage);
                }
            },
        });
    }

    spanClick(e, row) {
        let modal = Modal.confirm({
            cancelText: "取消",
            okText: "确定",
            content: `确认${row.enable === 1 ? "禁用" : "启用"}吗？`,
            onOk: res => {
                lib.request({
                    url: "/ccs/customsBookItem/batchEnable",
                    method: "POST",
                    data: {
                        ids: [row.id],
                        enable: row.enable ? 0 : 1,
                    },
                    needMask: true,
                    success: res => {
                        if (res.errorMessage) {
                            message.error(res.errorMessage);
                        } else {
                            message.success("修改成功");
                            this.load(true);
                            modal.destroy();
                        }
                    },
                });
            },
            onCancel() {
                modal.destroy();
            },
        });
    }

    // showDetail(row) {
    //     lib.openPage(`/books-inventory-detail?page_title=账册库存详情&id=${row.id}`);
    // }

    enableStatus(row) {
        const buttons = this.state.buttons;
        return (
            <React.Fragment>
                <Switch
                    disabled={!buttons.includes("enable")}
                    checked={row.enable}
                    onChange={e => this.spanClick(e, row)}></Switch>
            </React.Fragment>
        );
    }

    myOperation(row) {
        let { unitList, hsList } = this.state;
        let { customsDeclarationManageDeclareInvalid, buttons } = this.state;
        const btns = [
            {
                btnName: "出区明细",
                auth: true,
                onClick: () => {
                    lib.openPage(
                        `/outbound-detail-manage?pageTitle=出区明细-${row.goodsName}&bookItemId=${row.id}&config_id=1602300988838706`,
                    );
                },
                showInMore: true,
            },
            {
                btnName: "查看明细",
                auth: true,
                onClick: () => {
                    let param = {
                        id: row.id,
                        productId: row.productId,
                        goodsSeqNo: row.goodsSeqNo,
                        customsBookNo: row.customsBookNo,
                        goodsName: encodeURIComponent(row.goodsName),
                        hsCode: row.hsCode,
                        customsDistrictName: encodeURIComponent(row.customsDistrictName),
                    };
                    let base64 = window.btoa(JSON.stringify(param));
                    lib.openPage(`/check-detail-manage?pageTitle=查看明细&id=${row.id}&detail=${base64}`);
                },
                showInMore: false,
            },
            {
                type: "custom",
                auth: row.enable === 0,
                render: () => (
                    <ChangeModal row={row} callback={() => this.load()} unitList={unitList} hsList={hsList} />
                ),
                showInMore: true,
            },
            {
                btnName: "调整日志",
                auth: true,
                onClick: () => {
                    this.setState({
                        tableModal: true,
                        editRow: row,
                    });
                },
                showInMore: true,
            },
        ];
        const innerOperation = (
            <Dropdown
                overlay={
                    <Menu>
                        {btns
                            .filter(item => item.auth && item.showInMore)
                            .map(item => (
                                <Menu.Item
                                    onClick={() => {
                                        item.onClick && item.onClick();
                                    }}>
                                    {item.type && item.type === "custom" ? item.render() : item.btnName}
                                </Menu.Item>
                            ))}
                    </Menu>
                }>
                <span className="link" style={{ fontFamily: "iconfont", padding: "10px 10px" }}>
                    更多
                    <DownOutlined />
                </span>
            </Dropdown>
        );
        // console.log(btns.filter(item => (item.auth && !item.showInMore)))
        return (
            <Space>
                {btns
                    .filter(item => item.auth && !item.showInMore)
                    .map(item => {
                        return (
                            <a type="link" onClick={item.onClick}>
                                {item.btnName}
                            </a>
                        );
                    })}
                {innerOperation}
            </Space>
        );
    }

    totalInQty(row) {
        return (
            <a
                onClick={() => {
                    this.showDetail(
                        row.customsBookId,
                        "总入",
                        row.goodsName,
                        row.goodsSeqNo,
                        row.customsRecordProductId,
                    );
                }}>
                {row.totalInQty}
            </a>
        );
    }

    totalOutQty(row) {
        return (
            <a
                onClick={() => {
                    this.showDetail(
                        row.customsBookId,
                        "总出",
                        row.goodsName,
                        row.goodsSeqNo,
                        row.customsRecordProductId,
                    );
                }}>
                {row.totalOutQty}
            </a>
        );
    }

    showDetail(bookId, type, goodsName, goodsSeqNo, customsRecordProductId, page, pageSize) {
        const { modalTitle, modalPagination, search, dataType } = this.state;
        let url = "/ccs/itemstocksummary/findindetails";
        if (type === "总出") url = "/ccs/itemstocksummary/findoutdetails";
        this.cacheRow = {
            bookId,
            type,
            goodsName,
            goodsSeqNo,
            customsRecordProductId,
        };
        this.setState({ isModalOpen: true, modalTitle: type });
        const params = {
            dataType: "1",
            ...modalPagination,
            bookId,
            goodsName,
            goodsSeqNo,
            customsRecordProductId,
        };
        if (page) {
            params.currentPage = page;
        }
        if (pageSize) {
            params.pageSize = pageSize;
        }
        lib.request({
            url: url,
            data: params,
            success: res => {
                this.setState({
                    detailTableData: res.dataList.map(item => {
                        return { sn: item.sn, realOrderNo: item.realOrderNo, eventQty: item.eventQty };
                    }),
                    modalPagination: { ...res.page },
                });
            },
        });
    }

    renderModal() {
        const { isModalOpen, modalTitle, modalPagination } = this.state;
        // console.log(modalPagination)
        const columns = [
            {
                title: "企业内部编号",
                dataIndex: "sn",
                key: "sn",
            },
            {
                title: "核注清单编号",
                dataIndex: "realOrderNo",
                key: "realOrderNo",
            },
            {
                title: "数量",
                dataIndex: "eventQty",
                key: "eventQty",
            },
        ];
        const { detailTableData } = this.state;
        return (
            <>
                {this.state.showAccountNumDetail && (
                    <AccountNumDetail
                        param={{ customsBookItemId: this.state.customsBookItemId }}
                        url={"/ccs/customsBookItemStock/getCustomsBookItemOccupiedDetail"}
                        visible={this.state.showAccountNumDetail}
                        onClose={() => {
                            this.setState({
                                showAccountNumDetail: false,
                            });
                        }}
                    />
                )}

                <Modal
                    title={modalTitle}
                    visible={isModalOpen}
                    footer={null}
                    onCancel={() => {
                        this.setState({
                            isModalOpen: false,
                            modalPagination: {
                                currentPage: 1,
                                pageSize: 20,
                                totalCount: 0,
                            },
                        });
                    }}>
                    <Table
                        columns={columns}
                        dataSource={detailTableData}
                        pagination={{
                            current: modalPagination.currentPage,
                            pageSize: modalPagination.pageSize,
                            total: modalPagination.totalCount,
                            showTotal: total => `总共 ${total} 条`,
                            showSizeChanger: true,
                            pageSizeOptions: ["10", "20", "30", "40", "50", "100", "200"],
                            onChange: (page, pageSize) => {
                                let { bookId, type, goodsName, goodsSeqNo, customsRecordProductId } = this.cacheRow;
                                this.showDetail(
                                    bookId,
                                    type,
                                    goodsName,
                                    goodsSeqNo,
                                    customsRecordProductId,
                                    page,
                                    pageSize,
                                );
                            },
                        }}
                    />
                </Modal>

                <LockedModal
                    open={this.state.lockedOpen}
                    row={this.state.editRow}
                    closeFn={load => {
                        if (load) {
                            this.load();
                        }
                        this.setState({
                            lockedOpen: false,
                        });
                    }}
                />

                <AppendModal
                    title={"调整日志"}
                    modalWidth={1400}
                    visible={this.state.tableModal}
                    onOk={() => {
                        this.setState({
                            tableModal: false,
                        });
                    }}
                    onCancel={() => {
                        this.setState({
                            tableModal: false,
                        });
                    }}
                    url={"/ccs/customsBookItem/pagingAdjustLog"}
                    props={{ customsBookItemId: this.state?.editRow?.id }}
                    searchList={[
                        {
                            type: "SELECT",
                            labelName: "调整类型",
                            labelKey: "adjustType",
                            list: this.state.typeList,
                        },
                        {
                            type: "INPUT",
                            labelName: "业务单号",
                            labelKey: "businessNo",
                        },
                    ]}
                    scroll={{
                        x: 1400,
                    }}
                    columns={[
                        { title: "调整类型", dataIndex: "adjustTypeDesc", key: "adjustTypeDesc", width: 150 },
                        { title: "业务单号", dataIndex: "businessNo", key: "businessNo", width: 200 },
                        { title: "描述", dataIndex: "description", key: "description", width: 300 },
                        { title: "调整前", dataIndex: "adjustBefore", key: "adjustBefore", width: 120 },
                        { title: "调整后", dataIndex: "adjustAfter", key: "adjustAfter", width: 120 },
                        { title: "调整值", dataIndex: "adjustNum", key: "adjustNum", width: 100 },
                        { title: "操作人", dataIndex: "operator", key: "operator", width: 100 },
                        { title: "操作时间", dataIndex: "operateTime", key: "operateTime", width: 200 },
                    ]}
                />
            </>
        );
    }
}

export default App;

function ChangeModal({ row, callback }) {
    const [visible, setVisible] = useState(false);
    const ref = React.useRef();
    const configData = BooksInventoryEditConfig();

    useEffect(() => {
        if (visible) {
            ref.current.setMergeDetail(row);
        } else {
            ref.current?.resetChangConfigCount();
        }
    }, [visible]);

    const onSinglesSelectChange = desc => {
        switch (desc.name) {
            case "secondUnit":
                setSecondUnitRules("secondUnit", "secondUnitAmount", "请输入第二法定数量！");
                break;
            case "secondUnitAmount":
                setSecondUnitRules("secondUnitAmount", "secondUnit", "请选择法定第二计量单位！");
                break;
            default:
                break;
        }
    };

    function setSecondUnitRules(filedKey, ruleKey, message) {
        let config = ref.current.config;
        let formFieldsValue = ref.current?.getFormFiled(filedKey);
        let rule = [
            {
                required: Boolean(formFieldsValue),
                message: message,
            },
        ];
        config.baseInfo.children.map(item => {
            if (item.name === ruleKey) {
                item.rules = rule;
            }
        });
        ref.current.changeConfig(config);
    }

    function onOk() {
        ref.current.submitForm();
    }

    const beforeSetDetail = data => {
        return data;
    };
    const onConfigLoadSuccess = config => {
        config.baseInfo.children.map(item => {
            if (item.type === "single-select" && !item.fromParam) {
                ref.current.initSelect(item);
            }
        });
    };
    const onSubmitSuccess = () => {
        setVisible(false);
        callback();
        message.success("提交成功！");
    };
    const beforeSubmit = values => {
        if (values.secondUnitAmount == "") {
            delete values.secondUnitAmount;
        }
        if (values.secondUnit == null) {
            values.secondUnit = "";
        }
        values.id = row.id;
        values.customsBookId = row.customsBookId;
        return values;
    };
    const onSetDetailSuccess = (fieldsValue, changConfigCount) => {
        if (changConfigCount == 1) {
            let config = ref.current.config;
            let secondUnit = ref.current?.getFormFiled("secondUnit");
            let secondUnitAmount = ref.current?.getFormFiled("secondUnitAmount");
            config.baseInfo.children.map(item => {
                if (item.name === "secondUnit") {
                    item.rules = [
                        {
                            required: Boolean(secondUnitAmount),
                            message: "请选择法定第二计量单位",
                        },
                    ];
                } else if (item.name === "secondUnitAmount") {
                    item.rules = [
                        {
                            required: Boolean(secondUnit),
                            message: "请输入第二法定数量",
                        },
                    ];
                }
            });
            ref.current.changeConfig(config);
        }
    };
    return (
        <>
            <Modal
                open={visible}
                title={`修改（料号：${row.productId}）`}
                onOk={onOk}
                onCancel={() => setVisible(false)}
                width={800}>
                <Alert type="error" message="请谨慎修改账册信息，修改时需确认与海关保持一致" />
                <ConfigFormCenter
                    ref={ref}
                    confData={configData}
                    onSinglesSelectChange={onSinglesSelectChange}
                    beforeSetDetail={beforeSetDetail}
                    beforeSubmit={beforeSubmit}
                    onSubmitSuccess={onSubmitSuccess}
                    onSetDetailSuccess={onSetDetailSuccess}
                    submitUrl={"/ccs/customsBookItem/updBook"}
                    onConfigLoadSuccess={onConfigLoadSuccess}
                />
            </Modal>
            <span className="link" onClick={() => setVisible(true)}>
                修改
            </span>
        </>
    );
}
