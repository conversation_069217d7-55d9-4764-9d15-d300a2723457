export const BooksInventoryDetailConfig = () => {
    return {
        baseInfo: {
            children: [
                {
                    label: "金二序号",
                    editEnable: true,
                    name: "goodsSeqNo",
                    type: "text",
                },
                {
                    label: "商品料号",
                    editEnable: false,
                    name: "productId",
                    type: "text",
                },
                {
                    label: "商品名称",
                    editEnable: true,
                    name: "goodsName",
                    type: "text",
                },
                {
                    label: "HS编码",
                    editEnable: true,
                    name: "hsCode",
                    type: "text",
                },
                {
                    label: "商品规格型号",
                    editEnable: false,
                    name: "goodsModel",
                    type: "text",
                },
                {
                    label: "账册编号",
                    editEnable: false,
                    name: "customsBookNo",
                    type: "text",
                },
                {
                    label: "法一数量",
                    editEnable: false,
                    name: "firstUnitAmount",
                    type: "text",
                },
                {
                    label: "法二数量",
                    name: "secondUnitAmount",
                    editEnable: true,
                    type: "text",
                },
                {
                    label: "申报单位",
                    name: "goodsUnitDesc",
                    editEnable: false,
                    type: "text",
                },
                {
                    label: "法一单位",
                    name: "firstUnitDesc",
                    editEnable: false,
                    type: "text",
                },
                {
                    label: "法二单位",
                    name: "secondUnitDesc",
                    editEnable: false,
                    type: "text",
                },
                {
                    label: "申报单价金额",
                    name: "declarePrice",
                    editEnable: false,
                    type: "text",
                },
                {
                    label: "申报币制",
                    name: "currCodeDesc",
                    editEnable: false,
                    type: "text",
                },
                {
                    label: "国别",
                    name: "originCountryDesc",
                    editEnable: false,
                    type: "text",
                },
                {
                    label: "记账清单编号",
                    name: "invtNo",
                    editEnable: false,
                    type: "text",
                },
                {
                    label: "记账清单商品序号",
                    name: "invtGoodsNo",
                    editEnable: false,
                    type: "text",
                },
                {
                    label: "最近入仓（核增日期）",
                    name: "inDate",
                    editEnable: false,
                    type: "text",
                },
                {
                    label: "库存美元总价",
                    name: "totalAmt",
                    editEnable: false,
                    type: "text",
                },
                {
                    label: "入仓数量",
                    name: "inQty",
                    editEnable: false,
                    type: "text",
                },
                {
                    label: "入仓法定数量",
                    name: "inLegalQty",
                    editEnable: false,
                    type: "text",
                },
                {
                    label: "第二入仓法定数量",
                    name: "inSecondLegalQty",
                    editEnable: false,
                    type: "text",
                },
                {
                    label: "来源标识",
                    name: "goodsSourceDesc",
                    editEnable: false,
                    type: "text",
                },
                {
                    label: "备注",
                    name: "remark",
                    editEnable: false,
                    type: "text",
                },
            ],
            label: "账册信息表体",
            name: "baseInfo",
            isGroup: true,
            className: "baseInfo",
        },
    };
};
export const BooksInventoryEditConfig = () => {
    return {
        baseInfo: {
            children: [
                {
                    label: "金二序号",
                    editEnable: true,
                    name: "goodsSeqNo",
                    type: "textInput",
                    rules: [
                        {
                            required: true,
                            message: "请输入金二序号!",
                        },
                    ],
                },
                {
                    label: "商品名称",
                    editEnable: true,
                    name: "goodsName",
                    type: "textInput",
                    rules: [
                        {
                            required: true,
                            message: "请输入商品名称!",
                        },
                    ],
                },
                {
                    label: "HS编码",
                    editEnable: true,
                    name: "hsCode",
                    type: "single-select",
                    from: "/ccs/customs/listHs",
                    wrapperCol: { span: 16 },
                    customConfig: { together: true, showSearch: true },
                    rules: [
                        {
                            required: true,
                            message: "请选择HS编码!",
                        },
                    ],
                },
                {
                    label: "规格",
                    editEnable: true,
                    name: "goodsModel",
                    type: "textInput",
                    rules: [
                        {
                            required: true,
                            message: "请输入规格!",
                        },
                    ],
                },
                {
                    label: "原产国",
                    editEnable: true,
                    name: "originCountry",
                    type: "single-select",
                    customConfig: { showSearch: true },
                    wrapperCol: { span: 16 },
                    from: "/ccs/customs/listCountry",
                    rules: [
                        {
                            required: true,
                            message: "请选择原产国!",
                        },
                    ],
                },
                {
                    label: "申报单位",
                    editEnable: false,
                    name: "goodsUnit",
                    type: "single-select",
                    from: "/ccs/customs/listUom",
                    customConfig: { together: true, showSearch: true },
                    wrapperCol: { span: 16 },
                    rules: [
                        {
                            required: true,
                            message: "请选择申报单位!",
                        },
                    ],
                },
                {
                    label: "法一数量",
                    name: "firstUnitAmount",
                    editEnable: true,
                    type: "textInput",
                    rules: [
                        {
                            required: true,
                            message: "请输入法一数量!",
                        },
                    ],
                },
                {
                    label: "法定计量单位",
                    name: "firstUnit",
                    editEnable: false,
                    type: "single-select",
                    from: "/ccs/customs/listUom",
                    customConfig: { together: true, showSearch: true },
                    wrapperCol: { span: 16 },
                    rules: [
                        {
                            required: true,
                            message: "请选择清关企业!",
                        },
                    ],
                },
                {
                    label: "法二数量",
                    name: "secondUnitAmount",
                    editEnable: false,
                    type: "textInput",
                },
                {
                    label: "第二计量单位",
                    name: "secondUnit",
                    editEnable: false,
                    type: "single-select",
                    wrapperCol: { span: 16 },
                    from: "/ccs/customs/listUom",
                    customConfig: { together: true, allowClear: true, showSearch: true },
                },
                {
                    label: "申报单价金额",
                    name: "declarePrice",
                    editEnable: false,
                    type: "textInput",
                    rules: [
                        {
                            required: true,
                            message: "请输入申报单价金额!",
                        },
                    ],
                },
                {
                    label: "币制",
                    name: "currCode",
                    editEnable: false,
                    type: "single-select",
                    from: "/ccs/customs/listCurrency",
                    wrapperCol: { span: 16 },
                    customConfig: { together: true, showSearch: true },
                    rules: [
                        {
                            required: true,
                            message: "请选择币制!",
                        },
                    ],
                },
                {
                    label: "来源标识",
                    name: "goodsSource",
                    editEnable: false,
                    type: "single-select",
                    from: "/ccs/goodsSource/list",
                    wrapperCol: { span: 16 },
                    customConfig: { together: true, showSearch: true },
                    rules: [
                        {
                            required: true,
                            message: "请选择清关企业!",
                        },
                    ],
                },
            ],
            label: "",
            name: "baseInfo",
            isGroup: true,
            className: "baseInfo",
        },
    };
};
