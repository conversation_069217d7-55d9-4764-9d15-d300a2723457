import React, { useEffect, useState } from "react";
import { Modal } from "antd";
import { ConfigFormCenter } from "react-single-app";

export default function EditEntityWarehouseModal({ detail, visible, close, type }) {
    const ref = React.useRef();
    /**
     * 额外提交参数   {erpWarehouseName ，customsBookNo,customs}
     */
    const [extraParam, setExtraParam] = useState({});
    const handleOk = () => {
        if (type === "detail") {
            close();
        } else {
            ref.current.submitForm();
        }
    };
    const onConfigLoadSuccess = config => {
        config.baseInfo.children.map(item => {
            if (item.type === "single-select" && !item.fromParam) {
                ref.current.initSelect(item);
            }
        });
        if (detail) {
            ref.current.setMergeDetail(detail);
        }
    };
    const onSinglesSelectChange = desc => {
        switch (desc.name) {
            case "erpWarehouseName":
                let { entityWarehouseCode, warehouseCode, customs, port, name, tradeType } = desc.value.data;
                extraParam.erpWarehouseName = name;
                extraParam.customsCode = port;
                delete extraParam.customsBookNo;
                setExtraParam(extraParam);
                ref.current.setMergeDetail({
                    erpWarehouseCode: entityWarehouseCode,
                    wmsWarehouseCode: warehouseCode,
                    customs: customs,
                    customsBookId: null,
                    tradeType: tradeType,
                });
                ref.current.config.baseInfo.children.map(item => {
                    if (item.name === "customsBookId") {
                        ref.current.initSelect(item, { customsDistrictCode: port, tradeType });
                    }
                });
                break;
            case "customsBookId":
                extraParam.customsBookNo = desc.value.data.name;
                extraParam.customs = desc.value.data.customsDistrictName;
                extraParam.customsCode = desc.value.data.customsDistrictCode;
                setExtraParam(extraParam);
                ref.current.setMergeDetail({
                    customs: extraParam.customs,
                });
                break;
            default:
                break;
        }
    };
    const beforeSetDetail = data => {
        return data;
    };
    const beforeSubmit = values => {
        if (type === "addNew") {
            return Object.assign(values, extraParam);
        } else if (type === "edit") {
            return {
                id: detail.id,
                erpWarehouseCode: values.erpWarehouseCode,
                customsBookId: values.customsBookId,
                customsBookNo: extraParam.customsBookNo,
            };
        } else {
            return {};
        }
    };
    const onSubmitSuccess = () => {
        close(true);
    };
    const onSetDetailSuccess = (fieldsValue, changConfigCount) => {
        if (detail) {
            ref.current.config.baseInfo.children.map(item => {
                if (item.name === "customsBookId") {
                    ref.current.initSelect(item, {
                        customsDistrictCode: detail.customsCode,
                        tradeType: detail.tradeType,
                    });
                }
            });
        }
    };

    function handleClose(success) {
        close(success);
    }

    const handleCancel = () => {
        handleClose();
    };
    const renderTitle = () => {
        switch (type) {
            case "edit":
                return "编辑实体仓配置";
            case "addNew":
                return "新增实体仓配置";
            case "detail":
                return "查看实体仓配置";
        }
    };
    const confData = () => {
        data.baseInfo.children.map(item => {
            if (item.name === "customsBookId") {
                item.editEnable = type !== "detail";
            }
        });
        return data;
    };
    return (
        <Modal
            cancelText={"取消"}
            okText={"确定"}
            title={renderTitle()}
            destroyOnClose={true}
            open={visible}
            onOk={handleOk}
            onCancel={handleCancel}>
            <ConfigFormCenter
                ref={ref}
                disableEdit={type !== "addNew"}
                confData={confData()}
                beforeSetDetail={beforeSetDetail}
                onSinglesSelectChange={onSinglesSelectChange}
                onConfigLoadSuccess={onConfigLoadSuccess}
                beforeSubmit={beforeSubmit}
                submitUrl={"/ccs/entityWarehouse/upset"}
                onSubmitSuccess={onSubmitSuccess}
                onSetDetailSuccess={onSetDetailSuccess}
            />
        </Modal>
    );
}

const data = {
    baseInfo: {
        children: [
            {
                label: "实体仓名称",
                editEnable: false,
                name: "erpWarehouseName",
                wrapperCol: { span: 16 },
                type: "single-select",
                from: "/ccs/entityWarehouse/entityWarehouse",
                customConfig: { allowClear: true, showSearch: true },
                rules: [
                    {
                        required: true,
                        message: "请选择实体仓!",
                    },
                ],
            },
            {
                label: "实体仓编码",
                editEnable: false,
                disabled: true,
                name: "erpWarehouseCode",
                wrapperCol: { span: 16 },
                placeholder: " ",
                type: "textInput",
            },
            {
                label: "WMS编码",
                disabled: true,
                name: "wmsWarehouseCode",
                wrapperCol: { span: 16 },
                placeholder: " ",
                type: "textInput",
            },
            {
                label: "口岸",
                editEnable: false,
                disabled: true,
                name: "customs",
                wrapperCol: { span: 16 },
                placeholder: " ",
                type: "textInput",
            },
            {
                label: "账册名称",
                editEnable: true,
                name: "customsBookId",
                wrapperCol: { span: 16 },
                type: "single-select",
                from: "/ccs/customsBook/findBookListV3",
                fromParam: [{ name: "customs", name: "tradeType" }],
                customConfig: { allowClear: true, showSearch: true },
                rules: [
                    {
                        required: true,
                        message: "请选择实体仓!",
                    },
                ],
            },
        ],
        label: "",
        name: "baseInfo",
        isGroup: true,
        className: "baseInfo-proxyCode",
    },
};
