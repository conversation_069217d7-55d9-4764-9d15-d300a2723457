import React, { useState } from "react";
import { message, Modal, Space, Switch, Button, Select } from "antd";
import { lib, SearchList, getConfigDataUtils, HOC } from "react-single-app";
import axios from "axios";
import EditEntityWarehouseModal from "./edit-modal";
@HOC.mapAuthButtonsToState({ pagePath: "/entity-warehouse" })
class App extends SearchList {
    constructor(props) {
        super(props);
        this.state.warehouseTag = [];
        this.state.buttons = [];
    }

    getConfig() {
        return axios.get(getConfigDataUtils.getDataUrlByDataId(458)).then(res => res.data.data);
    }
    componentDidMount() {
        lib.request({
            url: `/ccs/entityWarehouse/listTag`,
            needMask: true,
            success: res => {
                console.log(res);
                this.setState({
                    warehouseTag: res,
                });
            },
        });
    }

    renderLeftOperation() {
        const { buttons } = this.state;
        return (
            <Space>
                {buttons.includes("add") && (
                    <Button onClick={() => this.addNew()} type="primary">
                        新增
                    </Button>
                )}
            </Space>
        );
    }

    upset(row, data) {
        lib.request({
            url: "/ccs/entityWarehouse/upset",
            method: "POST",
            data: {
                id: row.id,
                erpWarehouseCode: row.erpWarehouseCode,
                ...data,
            },
            needMask: true,
            success: () => {
                this.load(true);
                message.success("操作成功");
            },
        });
    }

    enableFunc(row) {
        let content = row.enable === 1 ? "是否禁用?" : "是否启用";
        Modal.confirm({
            cancelText: "取消",
            okText: "确定",
            title: "提示",
            content: content,
            onOk: () => {
                let enable;
                if (row.enable === 2) {
                    enable = 1;
                } else if (row.enable === 1) {
                    enable = 2;
                }
                this.upset(row, { enable: enable });
            },
        });
    }

    enableStatus(row) {
        return (
            <React.Fragment>
                <Switch checked={row.enable === 1} onChange={() => this.enableFunc(row)} />
            </React.Fragment>
        );
    }

    deleteFunc(e, row) {
        Modal.confirm({
            cancelText: "取消",
            okText: "确定",
            title: "提示",
            content: "是否删除?",
            onOk: () => {
                this.deleteRow(row);
            },
        });
    }

    deleteRow(row) {
        lib.request({
            url: `/ccs/entityWarehouse/del`,
            data: { id: row.id },
            needMask: true,
            success: res => {
                if (res) {
                    this.load(true);
                }
            },
        });
    }
    syncRecordWarehouseCustomsBook(row) {
        lib.request({
            url: `/ccs/entityWarehouse/syncRecordWarehouseCustomsBook`,
            data: { id: row.id },
            needMask: true,
            success: res => {
                message.success("同步成功");
                this.load(true);
            },
        });
    }
    myOperation(row) {
        const { buttons } = this.state;
        return (
            <React.Fragment>
                <Space style={{ flexWrap: "wrap" }}>
                    {buttons.includes("look") && (
                        <a
                            className="link"
                            onClick={() => {
                                this.setState({
                                    editRow: row,
                                    visible: true,
                                    type: "detail",
                                });
                            }}>
                            查看
                        </a>
                    )}
                    {buttons.includes("synchronous-record") && (
                        <a
                            className="link"
                            onClick={e => {
                                this.syncRecordWarehouseCustomsBook(row);
                            }}>
                            同步备案
                        </a>
                    )}
                    {buttons.includes("delete") && (
                        <a
                            className="link"
                            onClick={e => {
                                this.deleteFunc(e, row);
                            }}>
                            删除
                        </a>
                    )}
                </Space>
            </React.Fragment>
        );
    }

    renderModal() {
        return (
            <React.Fragment>
                <EditEntityWarehouseModal
                    detail={this.state.editRow}
                    visible={this.state.visible}
                    close={success => {
                        if (success) {
                            this.load(true);
                        }
                        this.setState({
                            editRow: null,
                            visible: false,
                        });
                    }}
                    type={this.state.type}
                />
            </React.Fragment>
        );
    }

    addNew() {
        this.setState({
            editRow: null,
            visible: true,
            type: "addNew",
        });
    }

    warehouseTag(row) {
        return <WarehouseTagSelect row={row} warehouseTag={this.state.warehouseTag || []} load={this.load} />;
    }
}

const WarehouseTagSelect = ({ row, warehouseTag, load }) => {
    const [value, setValue] = useState();
    const warehouseTagChange = (row, value) => {
        setValue(value);
        lib.request({
            url: `/ccs/entityWarehouse/updateTag`,
            data: { id: row.id, tagList: value },
            needMask: true,
            success: res => {
                message.success("标签配置成功");
                load();
            },
            fail: err => {
                setValue(row.warehouseTagList);
            },
        });
    };
    return (
        <Select
            mode="tags"
            allowClear
            style={{ width: "100%" }}
            value={value}
            onChange={value => warehouseTagChange(row, value)}
            fieldNames={{ label: "name", value: "id" }}
            defaultValue={row.warehouseTagList}
            options={warehouseTag}></Select>
    );
};

export default App;
