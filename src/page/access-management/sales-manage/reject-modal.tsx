import React, { useEffect, useRef } from "react";
import { Modal } from "antd";
import { DTEditForm, DTEditFormRefs } from "@dt/components";
export default ({ open, onClose }) => {
    const ref = useRef<DTEditFormRefs>();
    useEffect(() => {
        ref.current?.form?.resetFields();
    }, [open]);
    return (
        <>
            <Modal
                title={"审核不通过"}
                open={open}
                onCancel={() => {
                    onClose();
                }}
                onOk={() => {
                    ref.current.form.validateFields().then(res => {
                        console.log("res:", res);
                        onClose(res);
                    });
                }}>
                <DTEditForm
                    ref={ref}
                    configs={[
                        {
                            type: "TEXTAREA",
                            fProps: {
                                label: "原因",
                                name: "reason",
                                rules: [{ max: 255 }],
                            },
                        },
                    ]}
                    layout={{
                        mode: "appoint",
                        colNum: 1,
                    }}
                />
            </Modal>
        </>
    );
};
