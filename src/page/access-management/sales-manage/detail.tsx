import { DTEditForm, DTEditFormRefs } from "@dt/components";
import React, { useRef } from "react";
import detailConfig, { giftColumn, logColumns, mainColumn } from "./configs";
import { Table, Space } from "antd";
import { lib } from "react-single-app";
import { FieldTimeOutlined, CheckCircleFilled, CloseCircleFilled } from "@ant-design/icons";
import { detailStatus } from "./type";

export default () => {
    const ref = useRef<DTEditFormRefs>();
    return (
        <DTEditForm
            ref={ref}
            configs={detailConfig}
            detailUrl="/ccs/promotionOrder/detail"
            layout={{
                mode: "appoint",
                colNum: 3,
            }}
            detailParams={{
                id: lib.getParam("id"),
            }}>
            {({ moduleType, detail }) => (
                <>
                    {moduleType === "statusInfo" && (
                        <>
                            {[detailStatus.PENDING, detailStatus.LESS_THAN_2DAYS].includes(detail?.status) && (
                                <Space style={{ padding: "20px" }}>
                                    <FieldTimeOutlined style={{ color: "yellow", fontSize: "36px" }} />
                                    待报备
                                </Space>
                            )}
                            {[detailStatus.PASS].includes(detail?.status) && (
                                <Space style={{ padding: "20px" }}>
                                    <CheckCircleFilled style={{ color: "green", fontSize: "36px" }} />
                                    海关审核通过
                                </Space>
                            )}
                            {[detailStatus.REJECT].includes(detail?.status) && (
                                <Space style={{ padding: "20px" }}>
                                    <CloseCircleFilled style={{ color: "red", fontSize: "36px" }} />
                                    海关审核不通过
                                </Space>
                            )}
                        </>
                    )}
                    {moduleType === "mainInfo" && (
                        <Table
                            columns={mainColumn}
                            dataSource={detail?.mainItemVOList || []}
                            scroll={{ x: "1500px", y: "400px" }}
                        />
                    )}
                    {moduleType === "giftInfo" && (
                        <Table
                            columns={giftColumn}
                            dataSource={detail?.presentItemVOList || []}
                            scroll={{ x: "1500px", y: "400px" }}
                        />
                    )}
                    {moduleType === "logInfo" && (
                        <Table
                            columns={logColumns}
                            dataSource={detail?.trackLogVOList || []}
                            scroll={{ x: "1500px", y: "400px" }}
                        />
                    )}
                </>
            )}
        </DTEditForm>
    );
};
