import React, { useRef, useState } from "react";
import { SearchList } from "@dt/components";
import axios from "axios";
import { Button, message, Modal, Tabs } from "antd";
//@ts-ignore
import { hooks, getConfigDataUtils, lib } from "react-single-app";
import RejectModal from "./reject-modal";
import { SearchListRef } from "@dt/components/esm/business/SearchList/type";
const { useGetAuthButtons } = hooks;
export default () => {
    const ref = useRef<SearchListRef>();
    const [selected, setSelected] = useState([]);
    const [auditOpen, setAuditOpen] = useState(false);
    const [buttons] = useGetAuthButtons({ systemCode: "CCS_ADMIN", pagePath: "/sales-manage" });

    const getConfig = () => {
        return axios.get(getConfigDataUtils.getDataUrlByDataId(842)).then(res => res.data.data);
    };

    const audit = () => {
        if (selected.length === 0) return message.error("请选择至少一条数据");
        Modal.confirm({
            title: "审核通过",
            content: "确定通过吗？",
            onOk: () => {
                lib.request({
                    url: "/ccs/promotionOrder/audit",
                    data: {
                        ids: selected.join(","),
                        opinion: "1",
                    },
                    success(data) {
                        message.success("操作成功");
                        //@ts-ignore
                        ref.current?.load();
                        setAuditOpen(false);
                        //@ts-ignore
                        ref.current?.resetSelected();
                    },
                });
            },
        });
    };

    const auditReject = () => {
        if (selected.length === 0) return message.error("请选择至少一条数据");
        setAuditOpen(true);
    };

    return (
        <SearchList
            //@ts-ignore
            ref={ref}
            getConfig={getConfig}
            onTableSelected={selected => {
                setSelected(selected);
            }}
            renderOperationTopView={() => (
                <>
                    <Tabs
                        items={[
                            {
                                label: "全部",
                                key: null,
                            },
                            {
                                label: "待报备",
                                key: "100",
                            },
                            {
                                label: "海关审核通过",
                                key: "200",
                            },
                            {
                                label: "海关审核不通过",
                                key: "300",
                            },

                            {
                                label: "报备截止低于48h",
                                key: "500",
                            },
                            {
                                label: "报备撤回",
                                key: "400",
                            },
                        ]}
                        onChange={e => {
                            console.log(e);
                            //@ts-ignore
                            ref.current?.changeImmutable({ status: e });
                        }}
                    />
                </>
            )}
            renderLeftOperation={() => (
                <>
                    {buttons.includes("sales-audit") && (
                        <Button
                            onClick={() => {
                                audit();
                            }}>
                            审核通过
                        </Button>
                    )}
                    {buttons.includes("sales-reject") && (
                        <Button
                            onClick={() => {
                                auditReject();
                            }}>
                            审核不通过
                        </Button>
                    )}
                </>
            )}
            tableCustomFun={{
                orderCodeFn: (row, index) => {
                    return (
                        <a
                            onClick={() => {
                                lib.openPage("/sales-detail?page_title=促销单详情&id=" + row.id);
                            }}>
                            {row.orderCode}
                        </a>
                    );
                },
            }}
            renderModal={() => (
                <>
                    <RejectModal
                        open={auditOpen}
                        onClose={values => {
                            if (values) {
                                lib.request({
                                    url: "/ccs/promotionOrder/audit",
                                    data: {
                                        ids: selected.join(","),
                                        opinion: "0",
                                        ...values,
                                    },
                                    success(data) {
                                        message.success("操作成功");
                                        //@ts-ignore
                                        ref.current?.load();
                                        setAuditOpen(false);
                                        //@ts-ignore
                                        ref.current?.resetSelected();
                                    },
                                });
                            } else {
                                setAuditOpen(false);
                            }
                        }}
                    />
                </>
            )}
        />
    );
};
