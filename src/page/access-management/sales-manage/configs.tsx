import { DTEditFormConfigs } from "@dt/components";
import React from "react";

const detailConfig: DTEditFormConfigs = {
    statusInfo: {
        title: "",
        configs: [],
        useChildren: true,
    },
    activityInfo: {
        title: "活动报备详情",
        configs: [
            {
                type: "TEXT",
                fProps: {
                    label: "报备单号",
                    name: "orderCode",
                },
            },
            {
                type: "TEXT",
                fProps: {
                    label: "货主",
                    name: "ownerName",
                },
            },
            {
                type: "TEXT",
                fProps: {
                    label: "实体仓",
                    name: "warehouseName",
                },
            },
            {
                type: "TEXT",
                fProps: {
                    label: "活动类型",
                    name: "activityType",
                },
            },
            {
                type: "TEXT",
                fProps: {
                    label: "促销类型",
                    name: "marketingType",
                },
            },
            {
                type: "TEXT",
                fProps: {
                    label: "活动名称",
                    name: "activityName",
                },
            },
            {
                type: "TEXT",
                fProps: {
                    label: "卖家昵称",
                    name: "sellerNick",
                },
            },
            {
                type: "TEXT",
                fProps: {
                    label: "主营行业",
                    name: "categoryName",
                },
            },
            {
                type: "TEXT",
                fProps: {
                    label: "预估订单金额（元）",
                    name: "predictAmount",
                },
            },
            {
                type: "TEXT",
                fProps: {
                    label: "活动开始时间",
                    name: "activityStartDate",
                },
            },
            {
                type: "TEXT",
                fProps: {
                    label: "活动结束时间",
                    name: "activityEndDate",
                },
            },
            {
                type: "TEXT",
                fProps: {
                    label: "单据来源",
                    name: "orderSource",
                },
            },
            {
                type: "TEXT",
                fProps: {
                    label: "商家姓名",
                    name: "sellerContactName",
                },
            },
            {
                type: "TEXT",
                fProps: {
                    label: "商家电话",
                    name: "sellerContactPhone",
                },
            },
            {
                type: "TEXT",
                fProps: {
                    label: "创建时间",
                    name: "createTime",
                },
            },
            {
                type: "TEXT",
                fProps: {
                    label: "备注",
                    name: "remark",
                },
            },
        ],
    },
    mainInfo: {
        title: "主品信息",

        useChildren: true,
        configs: [
            // {
            //     type: "TEXT",
            //     fProps: {
            //         label: "海关备案序号",
            //         name: ["mainItemVO", "recordNumber"],
            //     },
            // },
            // {
            //     type: "TEXT",
            //     fProps: {
            //         label: "商家名称",
            //         name: ["mainItemVO", "itemName"],
            //     },
            // },
            // {
            //     type: "TEXT",
            //     fProps: {
            //         label: "商品编码",
            //         name: ["mainItemVO", "itemCode"],
            //     },
            // },
            // {
            //     type: "TEXT",
            //     fProps: {
            //         label: "促销价",
            //         name: ["mainItemVO", "activityPrice"],
            //     },
            // },
            // {
            //     type: "TEXT",
            //     fProps: {
            //         label: "促销数量",
            //         name: ["mainItemVO", "activityQuantity"],
            //     },
            // },
            // {
            //     type: "TEXT",
            //     fProps: {
            //         label: "原价（元）",
            //         name: ["mainItemVO", "originalPrice"],
            //     },
            // },
            // {
            //     type: "TEXT",
            //     fProps: {
            //         label: "入区价（元）",
            //         name: ["mainItemVO", "registerPrice"],
            //     },
            // },
            // {
            //     type: "TEXT",
            //     fProps: {
            //         label: "商品宝贝链接",
            //         name: ["mainItemVO", "itemUrl"],
            //     },
            //     isCopy: true,
            // },
            // {
            //     type: "TEXT",
            //     fProps: {
            //         label: "条码",
            //         name: ["mainItemVO", "barCode"],
            //     },
            // },
        ],
    },
    giftInfo: {
        title: "赠品信息",
        configs: [],
        useChildren: true,
    },
    logInfo: {
        title: "操作日志",
        configs: [],
        useChildren: true,
    },
};

export const mainColumn = [
    {
        title: "行号",
        width: 50,
        render: (_, record, index) => index + 1,
    },
    {
        title: "海关备案序号",
        dataIndex: "recordNumber",
        key: "recordNumber",
        width: 150,
    },
    {
        title: "商品名称",
        dataIndex: "itemName",
        key: "itemName",
        width: 150,
    },
    {
        title: "商品编码",
        dataIndex: "itemCode",
        key: "itemCode",
        width: 150,
    },
    {
        title: "促销价",
        dataIndex: "activityPrice",
        key: "activityPrice",
        width: 100,
    },
    {
        title: "促销数量",
        dataIndex: "activityQuantity",
        key: "activityQuantity",
        width: 100,
    },
    {
        title: "原价（元）",
        dataIndex: "originalPrice",
        key: "originalPrice",
        width: 100,
    },
    {
        title: "入区价（元）",
        dataIndex: "registerPrice",
        key: "registerPrice",
        width: 100,
    },
    {
        title: "商品宝贝链接",
        dataIndex: "itemUrl",
        width: 250,
        key: "itemUrl",
        render: (text, record, index) => (
            <a target="_blank" href={record.itemUrl}>
                {record.itemUrl}
            </a>
        ),
    },
    {
        title: "条码",
        dataIndex: "barCode",
        key: "barCode",
        width: 150,
    },
];

export const giftColumn = [
    {
        title: "行号",
        width: 50,
        render: (_, record, index) => index + 1,
    },
    {
        title: "赠品海关备案序号",
        dataIndex: "recordNumber",
        key: "recordNumber",
        width: 150,
    },
    {
        title: "商品名称",
        dataIndex: "itemName",
        key: "itemName",
        width: 150,
    },
    {
        title: "商品编码",
        dataIndex: "itemCode",
        key: "itemCode",
        width: 150,
    },
    {
        title: "赠品日销售价（元）",
        dataIndex: "activityPrice",
        key: "activityPrice",
        width: 100,
    },
    {
        title: "赠品数量",
        dataIndex: "activityQuantity",
        key: "activityQuantity",
        width: 100,
    },
    {
        title: "赠品价值（元）",
        dataIndex: "originalPrice",
        key: "originalPrice",
        width: 100,
    },
    {
        title: "赠品入区价（元）",
        dataIndex: "registerPrice",
        key: "registerPrice",
        width: 100,
    },
    {
        title: "商品宝贝链接",
        dataIndex: "itemUrl",
        key: "itemUrl",
        width: 250,
        render: (text, record, index) => (
            <a target="_blank" href={record.itemUrl}>
                {record.itemUrl}
            </a>
        ),
    },
    {
        title: "条码",
        dataIndex: "barCode",
        key: "barCode",
        width: 100,
    },
];

export const logColumns = [
    {
        title: "操作时间",
        dataIndex: "creatTime",
        key: "creatTime",
    },
    {
        title: "操作类型",
        dataIndex: "operateType",
        key: "operateType",
    },
    {
        title: "备注",
        dataIndex: "logInfo",
        key: "logInfo",
    },
    {
        title: "操作人",
        dataIndex: "operator",
        key: "operator",
    },
];

export default detailConfig;
