import React, { useState, useEffect, useImperative<PERSON><PERSON><PERSON>, forwardRef } from "react";
import { lib, SearchList, getConfigDataUtils, HOC, event, DDYObject } from "react-single-app";
import axios from "axios";
import { Button, Space, Badge, Tag, Modal, message, Radio, Tooltip, Switch } from "antd";
import NewModal from "@/components/NewModal";
//@ts-ignore
@HOC.mapAuthButtonsToState({
    buttonCodeArr: ["add", "edit"],
})
class CarryoverBookManage extends SearchList<any, any> {
    newModalRef: any;

    constructor(props: any) {
        super(props);
        this.newModalRef = React.createRef();
    }

    getConfig() {
        let url = getConfigDataUtils.getDataUrlByDataId(758);
        return axios.get(url).then(res => res.data.data);
    }
    componentDidMount() {
        event.on("onSearchReset", this.onSearchReset);
    }

    componentWillUnmount() {
        event.off("onSearchReset", this.onSearchReset);
    }

    onSearchReset() {}

    operateFn(row) {
        return (
            <Button
                type="link"
                onClick={() => {
                    this.setState({
                        status: "edit",
                        imporeOpen: true,
                        editRow: row,
                    });
                }}>
                编辑
            </Button>
        );
    }
    addNew() {
        this.setState({
            status: "add",
            imporeOpen: true,
            editRow: null,
        });
    }
    renderLeftOperation() {
        return (
            <Space>
                <Button onClick={() => this.addNew()} type="primary">
                    新增
                </Button>
            </Space>
        );
    }

    carryoverConfig = [
        {
            type: "INPUT",
            labelName: "账册编号",
            labelKey: "bookNo",
            maxLength: 32,
            required: true,
        },
        {
            type: "INPUT",
            labelName: "所属平台",
            labelKey: "platformName",
            maxLength: 100,
            required: true,
        },
        {
            type: "TEXTAREA",
            labelName: "备注",
            labelKey: "remark",
            maxLength: 250,
            required: false,
        },
    ];

    // @ts-ignore
    renderModal() {
        return (
            <>
                <NewModal
                    ref={this.newModalRef}
                    visible={this.state.imporeOpen}
                    configList={this.carryoverConfig}
                    editRow={this.state.editRow}
                    title={this.state.status === "add" ? "新增" : "编辑"}
                    onOk={data => {
                        const obj = this.newModalRef.current.form.getFieldsValue();
                        for (let i in obj) {
                            obj[i] = obj[i]?.replace(/(^\s+)|(\s+$)/g, "");
                        }
                        this.newModalRef.current.form.setFieldsValue(obj);
                        this.newModalRef.current.form.validateFields().then(res => {
                            lib.request({
                                url: "/ccs/carryforwardcustomsbook/insertOrUpdate",
                                data: { ...res, id: this.state.editRow?.id },
                                success: data => {
                                    message.success(this.state.status === "add" ? "新增成功" : "编辑成功");
                                    this.load();
                                    this.setState({ imporeOpen: false, editRow: {} });
                                },
                            });
                        });
                    }}
                    onCancel={() => {
                        this.setState({ imporeOpen: false, editRow: {} });
                    }}
                />
            </>
        );
    }
}

export default CarryoverBookManage;
