import React, { useEffect, useRef } from "react";
import { message, Modal } from "antd";
import { Space, Button, Input, Select, Table, Form } from "antd";
import { DTEditForm, DTEditFormRefs, DTEditFormConfigs } from "@dt/components";

import { EditOutlined, SearchOutlined, RedoOutlined } from "@ant-design/icons";
import { DetailProductConfigs } from "../configs";
import { lib } from "react-single-app";
export default ({ open, closeFn, row, mode, updateBody = ({ values, type, index }) => {}, typeBol }) => {
    const ref = useRef<DTEditFormRefs>({} as DTEditFormRefs);

    const configs: DTEditFormConfigs = DetailProductConfigs;

    const ok = () => {
        ref.current.form.validateFields().then(res => {
            // if (mode === "edit") {
            //     // res.id = row.id;
            //     res = { ...row, ...res };
            // }
            res = { ...row, ...res };
            // updateBody({ values: res, type: mode, index: row.index });
            lib.request({
                url: `/ccs/processTradeBook/item/createOrEdit`,
                data: {
                    id: mode === "edit" ? row.id : undefined,
                    ...res,
                    bookId: row.bookId,
                    goodsType: row.goodsType,
                },
                success: () => {
                    message.success(`${mode === "edit" ? "编辑" : "新增"}成功`);
                    ref.current.form.resetFields();
                    closeFn(true);
                },
            });
        });
    };

    useEffect(() => {
        if (open) {
            ref.current.mergeDetail({ ...row });
        }
    }, [open, row]);

    useEffect(() => {
        if (open) {
            // 必填
            ref.current.setConfigFormItems(["goodsSeqNo"], {
                fProps: {
                    rules: typeBol ? [{ required: true, message: "请输入底账商品序号" }] : [],
                },
            });

            //是否可编辑
            ref.current.setConfigFormItems(
                ["goodsCode", "goodsName", "goodsModel", "currency", "country", "currency", "declareUnit"],
                {
                    cProps: {
                        disabled: typeBol,
                    },
                },
            );
            ref.current.setConfigFormItem("goodsCode", { listItemLabelKey: "name" });
        }
    }, [open, typeBol]);

    return (
        <>
            <Modal
                title={mode === "edit" ? "编辑" : "新增"}
                open={open}
                width={1300}
                onCancel={() => {
                    closeFn(false);
                }}
                onOk={() => {
                    ok();
                }}>
                <DTEditForm
                    ref={ref}
                    configs={configs}
                    layout={{
                        mode: "appoint",
                        colNum: 3,
                    }}
                    onChange={name => {
                        if (name === "price" || name === "qty") {
                            const price = ref.current.form.getFieldValue("price");
                            const qty = ref.current.form.getFieldValue("qty");
                            ref.current.form.setFieldValue(
                                "totalPrice",
                                (parseFloat(price) * parseFloat(qty)).toFixed(4) || 0,
                            );
                        }
                    }}
                    onConfigChange={(name, value) => {}}
                />
            </Modal>
        </>
    );
};
