import React, { useEffect, useRef } from "react";
import { DTEditForm, DTEditFormRefs, DTEditFormConfigs } from "@dt/components";
import { Modal, message } from "antd";
// import { depFn } from "../../warehouse-address-manage/component/add-modal";
import { lib } from "react-single-app";
import { Button } from "antd";
import moment from "moment";
// import { DeclareFormStatus } from "../configs";
export default ({ detail, load }) => {
    const ref = useRef<DTEditFormRefs>();
    const configs: DTEditFormConfigs = {
        head: {
            moduleMode: "read",
            title: " ",
            renderHeadRight: ({ totalModal, configItem, detail }) => {
                // if (
                //     [
                //         DeclareFormStatus.FINISHED,
                //         DeclareFormStatus.DISCARD,
                //         DeclareFormStatus.FINISHING,
                //         DeclareFormStatus.RECORDING,
                //     ].includes(detail?.status)
                // ) {
                //     return null;
                // }
                return (
                    <div style={{ display: "flex", justifyContent: "flex-end" }}>
                        {configItem.moduleMode === "read" ? (
                            <Button
                                onClick={() => {
                                    configItem.moduleMode = "edit";
                                    ref.current.setConfigModuleItem("head", configItem);
                                }}>
                                编辑
                            </Button>
                        ) : (
                            <Button
                                onClick={() => {
                                    ref.current.form.validateFields().then(values => {
                                        configItem.moduleMode = "read";
                                        submit(() => {
                                            ref.current.setConfigModuleItem("head", configItem);
                                        });
                                        // ref.current.setConfigModuleItem("head", configItem);
                                    });
                                }}>
                                保存
                            </Button>
                        )}
                    </div>
                );
            },
            configs: [
                {
                    type: "INPUT",
                    fProps: { label: "预录入统一编号", name: "preNo" },
                },
                {
                    type: "INPUT",
                    fProps: { label: "加工贸易账册编号", name: "processTradeBookNo" },
                },
                {
                    type: "INPUT",
                    fProps: { label: "企业内部编号", name: "sn" },
                },
                {
                    type: "INPUT",
                    fProps: { label: "主管海关", name: "masterCustomsDesc" },
                },
                {
                    type: "INPUT",
                    fProps: { label: "创建时间", name: "createTime" },
                },
                {
                    type: "INPUT",
                    fProps: { label: "更新时间", name: "updateTime" },
                },
                {
                    type: "INPUT",
                    fProps: { label: "经营单位编码", name: "operateCompanyCode" },
                },
                {
                    type: "INPUT",
                    fProps: { label: "经营单位社会信用代码", name: "operateCompanyUSCC" },
                },
                {
                    type: "INPUT",
                    fProps: { label: "经营单位名称", name: "operateCompanyName" },
                },
                {
                    type: "INPUT",
                    fProps: { label: "加工单位编码", name: "processCompanyCode" },
                },
                {
                    type: "INPUT",
                    fProps: { label: "加工单位社会信用代码", name: "processCompanyUSCC" },
                },
                {
                    type: "INPUT",
                    fProps: { label: "加工单位名称", name: "processCompanyName" },
                },
                {
                    type: "INPUT",
                    fProps: { label: "申报单位编码", name: "declareCompanyCode" },
                },
                {
                    type: "INPUT",
                    fProps: { label: "申报单位社会信用代码", name: "declareCompanyUSCC" },
                },
                {
                    type: "INPUT",
                    fProps: { label: "申报单位名称", name: "declareCompanyName" },
                },
                {
                    type: "INPUT",
                    fProps: { label: "录入单位编码", name: "inputCompanyCode" },
                },
                {
                    type: "INPUT",
                    fProps: { label: "录入单位社会信用代码", name: "inputCompanyUSCC" },
                },
                {
                    type: "INPUT",
                    fProps: { label: "录入单位名称", name: "inputCompanyName" },
                },
                {
                    type: "SELECT",
                    fProps: { label: "申报单位类型", name: "declareCompanyType" },
                    list: [],
                    dataUrl: "/ccs/processTradeBook/listDeclareCompanyType",
                    editable: true,
                    selectShowMode: "together",
                },
                {
                    type: "INPUT",
                    fProps: { label: "申报类型", name: "declareTypeDesc" },
                    // list: [],
                    // dataUrl: "/ccs/declareType/list",
                },
                {
                    type: "INPUT",
                    fProps: { label: "账册类型", name: "bookTypeDesc" },
                },
                {
                    type: "INPUT",
                    fProps: {
                        label: "批准证编号",
                        name: "approvalCertNo",
                        rules: [{ message: "不可超过32位", max: 32, type: "string" }],
                    },
                    editable: true,
                },
                {
                    type: "INPUT",
                    fProps: {
                        label: "企业档案库编号",
                        name: "enterpriseArchiveNo",
                        rules: [{ message: "不可超过32位", max: 32, type: "string" }],
                    },
                    editable: true,
                },
                {
                    type: "INPUT",
                    fProps: { label: "实际进口总金额", name: "actualImportTotalAmount" },
                },
                {
                    type: "INPUT",
                    fProps: { label: "实际出口总金额", name: "actualExportTotalAmount" },
                },

                {
                    type: "INPUT",
                    fProps: { label: "料件项数", name: "mtpckCount" },
                },

                {
                    type: "INPUT",
                    fProps: { label: "成品项数", name: "endPrdCount" },
                },
                {
                    type: "INPUT",
                    fProps: {
                        label: "最大周转金额（万美元）",
                        name: "maxTurnoverAmount",
                        rules: [
                            {
                                message: "整数部分最大14位，小数最大5位",
                                pattern: /^\d{1,14}(\.\d{1,5})?$/,
                                type: "string",
                            },
                        ],
                    },
                    editable: true,
                },
                // {
                //     type: 'INPUT',
                //     fProps: {
                //         label: '备案批准日期',
                //         name: 'sss'
                //     }
                // },
                {
                    type: "INPUT",
                    fProps: { label: "备案批准日期", name: "recordApprovalDate" },
                },
                {
                    type: "INPUT",
                    fProps: { label: "变更批准日期", name: "changeApprovalDate" },
                },
                {
                    type: "INPUT",
                    fProps: {
                        label: "最近核销日期",
                        name: "lastVoucherClearanceDate",
                    },
                },
                {
                    type: "SELECT",
                    fProps: {
                        label: "单耗申报环节",
                        name: "consumptionDeclarationLink",
                        rules: [{ required: true, message: "请选择单耗申报环节" }],
                    },
                    list: [],
                    dataUrl: "/ccs/processTradeBook/listConsumptionDeclarationLink",
                    editable: true,
                    selectShowMode: "together",
                },
                {
                    type: "INPUT",
                    fProps: {
                        label: "单耗版本号控制标志",
                        name: "consumptionVersionControlFlag",
                    },
                },
                {
                    type: "INPUT",
                    fProps: {
                        label: "最大进口金额（美元）",
                        name: "maxImportAmount",
                        rules: [
                            {
                                message: "整数部分最大14位，小数最大5位",
                                pattern: /^\d{1,14}(\.\d{1,5})?$/,
                                type: "string",
                            },
                        ],
                    },
                    editable: true,
                },

                {
                    type: "INPUT",
                    fProps: {
                        label: "核销周期",
                        name: "voucherClearanceCycle",
                        rules: [
                            {
                                pattern: /^(0|[1-9]\d{0,3})$/,
                                message: "整数不超过4位",
                            },
                        ],
                    },
                    editable: true,
                },
                {
                    type: "INPUT",
                    fProps: { label: "核销类型", name: "voucherClearanceTypeDesc" },
                },
                {
                    type: "INPUT",
                    fProps: { label: "账册变更次数", name: "bookChangeCount" },
                },
                {
                    type: "DATE",
                    fProps: {
                        label: "账册结束有效期",
                        name: "bookEndValidity",
                        rules: [{ required: true, message: "请选择账册结末有效期" }],
                    },
                    editable: true,
                },
                {
                    type: "INPUT",
                    fProps: { label: "账册执行标志", name: "bookExecutionFlagDesc" },
                },
                {
                    type: "SELECT",
                    fProps: { label: "账册用途", name: "bookUsage" },
                    list: [],
                    dataUrl: "/ccs/processTradeBook/listBookUsage",
                    editable: true,
                    selectShowMode: "together",
                },
                {
                    type: "SELECT",
                    fProps: { label: "核销方式", name: "voucherClearanceMethod" },
                    list: [],
                    dataUrl: "/ccs/processTradeBook/listVoucherClearanceMethod",
                    editable: true,
                    selectShowMode: "together",
                },

                {
                    type: "INPUT",
                    fProps: {
                        label: "备注",
                        name: "remark",
                        rules: [{ message: "最长限制255位", max: 255, type: "string" }],
                    },
                    editable: true,
                },
            ],
        },
    };

    const submit = fn => {
        ref.current.form.validateFields().then(values => {
            values.bookEndValidity && (values.bookEndValidity = values.bookEndValidity.valueOf());
            // values.licenceValidityDate && (values.licenceValidityDate = values.licenceValidityDate.valueOf());
            values.id = lib.getParam("id");
            lib.request({
                url: "/ccs/processTradeBook/editDetail",
                data: values,
                success(data) {
                    message.success("提交成功");
                    fn();
                    load();
                    // closeFn(true);
                },
            });
        });
    };

    useEffect(() => {
        if (detail) {
            detail.bookEndValidity && (detail.bookEndValidity = moment(detail.bookEndValidity));
            ref.current.mergeDetail(detail || {});
        }
    }, [detail]);

    return (
        <DTEditForm
            configs={configs}
            layout={{
                mode: "appoint",
                colNum: 3,
            }}
            ref={ref}
            totalMode="edit"
        />
    );
};
