import React, { useEffect, useState } from "react";
import { lib } from "react-single-app";
import { Table } from "antd";
export default ({ detail, load }) => {
    const [data, setData] = useState([]);
    const columns = [
        {
            title: "申报表状态",
            dataIndex: "statusDesc",
        },
        {
            title: "日志描述",
            dataIndex: "logInfo",
        },
        {
            title: "操作时间",
            dataIndex: "operatorTime",
        },
        {
            title: "操作人",
            dataIndex: "operator",
        },
        {
            title: "操作",
            dataIndex: "requestMessage",
            render: (val, row, index) => {
                if (!row.callbackDetail) return null;
                // return <BaowenBtn content={row.callbackDetail} />;
            },
        },
    ];
    const getData = () => {
        lib.request({
            url: "/ccs/bizDeclareForm/listTrackLogById",
            data: {
                id: lib.getParam("id"),
            },
            success(data) {
                setData(data);
            },
        });
    };
    useEffect(() => {
        getData();
    }, []);
    return <Table dataSource={data} columns={columns} />;
};
