import React, { useEffect, useRef, useState } from "react";
import { Space, Button, Input, Select, Table, Form, message, Modal, Row, Col } from "antd";
import { DTEditForm, DTEditFormRefs } from "@dt/components";

import { EditOutlined, SearchOutlined, RedoOutlined } from "@ant-design/icons";
import { DDYObject, lib } from "react-single-app";
import EditModal from "./edit-modal";
import { ColumnsType } from "antd/es/table/interface";
import EditProductModal from "./edit-product-modal";
let timeout: ReturnType<typeof setTimeout> | null;
export default ({ detail, showItem }) => {
    const [form] = Form.useForm();
    const ref = useRef<DTEditFormRefs>();
    const [dataSource, setDataSource] = useState([]);
    const dataCache = useRef([]);
    const [editBol, setEditBol] = useState(false);
    const [row, setRow] = useState<DDYObject>({});
    const [mode, setMode] = useState("add");
    const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
    const [selectedRows, setSelectedRows] = useState<DDYObject[]>([]);

    const [pagination, setPagination] = useState({
        current: 1,
        pageSize: 20,
        total: 0,
    });

    const onSelectChange = (newSelectedRowKeys: React.Key[], rows) => {
        setSelectedRowKeys(newSelectedRowKeys);
        setSelectedRows(rows);
        if (rows.length === 1) {
            showItem(rows[0]);
        } else {
            showItem();
        }
    };
    const rowSelection = {
        selectedRowKeys,
        onChange: onSelectChange,
        // onChange: (row, selectedRows) => {
        //     setSelectedRowKeys(row);
        //     if (selectedRows.length === 1) {
        //         setCurrent(selectedRows[0]);
        //         currentRef.current = { ...selectedRows[0] };
        //     } else {
        //         setCurrent({});
        //         currentRef.current = {};
        //     }
        // },
    };
    const fileldData = useRef<DDYObject>(null);
    const add = () => {
        if (fileldData.current) {
            setEditBol(true);
            setMode("add");
            fileldData.current.seqNo = dataSource.length + 1;
            console.log(form.getFieldValue("productId"));
            //@ts-ignore
            setRow({ ...fileldData.current });
        } else {
            message.error("请先选择料号");
        }
    };
    // const [queryParams, setQueryParams] = useState({
    //     mtpckEndprdMarkCd: "",
    //     productId: "",
    //     goodsName: "",
    //     goodsCode: "",
    // });

    const handleSearch = () => {
        // console.log("查询参数:", queryParams);
        // const queryParams = form.getFieldsValue();
        getData(1, 20);
    };

    const handleReset = () => {
        form.resetFields();
        console.log("重置查询条件");
        // setDataSource([...dataCache.current]);
        getData(1, 20);
    };

    const columns = [
        {
            title: "序号",
            dataIndex: "seqNo",
            key: "seqNo",
            width: 50,
        },
        {
            title: "料号",
            dataIndex: "productId",
            key: "productId",
            width: 150,
        },
        {
            title: "商品编码",
            dataIndex: "hsCode",
            key: "hsCode",
            width: 80,
        },
        {
            title: "商品名称",
            dataIndex: "goodsName",
            key: "goodsName",
            width: 180,
        },
        {
            title: "规格型号",
            dataIndex: "goodsModel",
            key: "goodsModel",
            width: 180,
        },
        {
            title: "申报计量单位",
            dataIndex: "declareUnitDesc",
            key: "declareUnitDesc",
            width: 80,
        },
        {
            title: "法定计量单位",
            dataIndex: "legalUnitDesc",
            key: "legalUnitDesc",
            width: 100,
        },
        {
            title: "法定第二计量单位",
            dataIndex: "legalSecondUnitDesc",
            key: "legalSecondUnitDesc",
            width: 100,
        },

        {
            title: "申报单价",
            dataIndex: "declareUnitPrice",
            key: "declareUnitPrice",
            width: 100,
        },

        {
            title: "币制",
            dataIndex: "currencyDesc",
            width: 80,
            key: "currencyDesc",
        },
        {
            title: "申报数量",
            dataIndex: "declareQty",
            key: "declareQty",
            width: 100,
        },
        {
            title: "征免方式",
            dataIndex: "dutyExemptionMethodDesc",
            key: "dutyExemptionMethodDesc",
            width: 100,
        },
        {
            title: "数量控制标志",
            dataIndex: "qtyControlFlagDesc",
            key: "qtyControlFlagDesc",
            width: 100,
        },
        {
            title: "企业执行标志",
            dataIndex: "companyExecutionFlagDesc",
            key: "companyExecutionFlagDesc",
            width: 100,
        },
        {
            title: "修改标志",
            dataIndex: "modifyFlagDesc",
            key: "modifyFlagDesc",
            width: 100,
        },
        {
            title: "海关执行标志",
            dataIndex: "customsExecutionFlagDesc",
            key: "customsExecutionFlagDesc",
            width: 100,
        },
        {
            title: "单损质疑标志",
            dataIndex: "consumptionQuestionFlagDesc",
            key: "consumptionQuestionFlagDesc",
            width: 100,
        },
        {
            title: "磋商标志",
            dataIndex: "negotiationFlagDesc",
            key: "negotiationFlagDesc",
            width: 100,
        },

        {
            title: "国别（地区）",
            dataIndex: "countryRegionDesc",
            key: "countryRegionDesc",
            width: 100,
        },
        {
            title: "操作",
            key: "action",
            width: 100,
            fixed: "right",
            dataIndex: "action",
            render: (_, row, index) => (
                <Button
                    type="link"
                    icon={<EditOutlined />}
                    onClick={() => {
                        setEditBol(true);
                        setMode("edit");
                        //@ts-ignore
                        setRow({ ...row, index: index });
                    }}>
                    编辑
                </Button>
            ),
        },
    ];

    const getData = (page = 1, pageSize = 20) => {
        const queryParams = form.getFieldsValue();
        lib.request({
            url: "/ccs/processTradeBook/item/paging",
            data: {
                refBookId: lib.getParam("id"),
                goodsType: 2,
                current: page,
                pageSize: pageSize,
                ...queryParams,
            },
            success(data) {
                console.log("data:", data.dataList);
                setDataSource(data.dataList);
                dataCache.current = [...data.dataList];
                setPagination({
                    current: data.page.current,
                    pageSize: data.page.pageSize,
                    total: data.page.total,
                });
                setSelectedRows([]);
            },
        });
    };

    const del = () => {
        // /bizDeclareForm/batchDeleteItems
        if (selectedRowKeys.length === 0) {
            return message.error("请勾选数据");
        }
        const idList = selectedRows.map(item => item.id);
        Modal.confirm({
            title: "确认",
            content: "确定删除成品吗？删除成品会自动删除已存在的单耗关系！",
            onOk: () => {
                lib.request({
                    url: "/ccs/processTradeBook/item/delete",
                    data: {
                        // bizDeclareFormId: lib.getParam("id"),
                        idList: idList,
                    },
                    success(data) {
                        message.success("删除成功");
                        getData();
                        // handleReset();
                    },
                });
            },
        });
    };

    const importData = () => {
        let arr = [];
        const params = {
            bookId: lib.getParam("id"),
            goodsType: 2,
        };
        for (let i in params) {
            arr.push(i + "=" + params[i]);
        }
        const str = arr.join("&");
        let importExtendParamBase64 = window.btoa(str + "");
        lib.openPage(
            `/excel/import-data?page_title=保税加工贸易成品导入&code=IMPORT_PROCESS_TRADE_BOOK_ITEM&importExtendParam=${encodeURIComponent(
                importExtendParamBase64,
            )}`,
            () => {
                getData();
            },
        );
    };

    useEffect(() => {
        getData();
    }, []);

    return (
        <div>
            <Row>
                <Col span={8} style={{}}>
                    <DTEditForm
                        configs={[
                            {
                                type: "SELECT",
                                fProps: {
                                    label: "料号",
                                    name: "productId",
                                    rules: [{ required: true, message: "料号是必选的" }],
                                },
                                cProps: {
                                    filterOption: false,
                                    onSearch: e => {
                                        const data = ref.current.form.getFieldsValue();
                                        console.log("data:", data);
                                        if (!e) {
                                            return message.error("请输入料号查询数据");
                                        }
                                        // if(timeout)
                                        if (timeout) {
                                            clearTimeout(timeout);
                                            timeout = null;
                                        }
                                        timeout = setTimeout(() => {
                                            lib.request({
                                                data: {
                                                    productId: e,
                                                    // queryInfo: e,
                                                    goodsType: 2,
                                                    bookId: detail.logisticsBookId,
                                                    id: lib.getParam("id"),
                                                },
                                                needMask: true,
                                                url: "/ccs/processTradeBook/matchItem",
                                                success(res) {
                                                    console.log("res:", res);
                                                    res.map(item => {
                                                        // {`${item.productId}(${item.skuId})`}-
                                                        // {item.recordProductName}
                                                        item.name = `${item.seqNo}-${item.productId}-${item.goodsName}`;
                                                        item.query_data = `${item.seqNo}-${item.productId}`;
                                                    });
                                                    // res.
                                                    ref.current.setConfigFormItem("productId", {
                                                        list: res || [],
                                                    });
                                                    clearTimeout(timeout);
                                                    timeout = null;
                                                },
                                            });
                                        }, 500);
                                    },
                                },
                                list: [],
                                listItemLabelKey: "query_data",
                                listItemValueKey: "productId",
                                // dataUrl: "/ccs/processTradeBook/matchItem",
                            },
                        ]}
                        layout={{
                            mode: "appoint",
                            colNum: 1,
                        }}
                        ref={ref}
                        onChange={(name, value, arr) => {
                            if (name === "productId") {
                                // queryInfo
                                fileldData.current = arr[0];
                            }
                        }}
                    />
                </Col>
                <Space style={{ marginBottom: "16px", marginLeft: "20px" }} direction="horizontal">
                    <Button
                        type="primary"
                        onClick={() => {
                            ref.current.form.validateFields().then(res => {
                                if (fileldData.current) {
                                    add();
                                } else {
                                    message.error("请先选择料号");
                                }
                            });
                        }}>
                        新增
                    </Button>
                    <Button
                        onClick={() => {
                            importData();
                        }}>
                        导入
                    </Button>
                    <Button
                        onClick={() => {
                            del();
                        }}>
                        批量删除
                    </Button>
                </Space>
            </Row>

            <Form form={form} layout="horizontal" style={{ marginTop: "20px" }}>
                <Space style={{ marginBottom: "16px" }}>
                    <Form.Item name="productId" label="料号">
                        <Input placeholder="料号" style={{ width: "150px" }} />
                    </Form.Item>
                    <Form.Item name="hsCode" label="商品编码">
                        <Input placeholder="商品编码" style={{ width: "150px" }} />
                    </Form.Item>
                    <Form.Item name="goodsName" label="商品名称">
                        <Input placeholder="商品名称" style={{ width: "150px" }} />
                    </Form.Item>
                    <Form.Item name="seqNo" label="序号">
                        <Input placeholder="序号" style={{ width: "150px" }} />
                    </Form.Item>
                    <Form.Item>
                        <Button type="primary" icon={<SearchOutlined />} onClick={handleSearch}>
                            查询
                        </Button>
                    </Form.Item>
                    <Form.Item>
                        <Button icon={<RedoOutlined />} onClick={handleReset}>
                            重置
                        </Button>
                    </Form.Item>
                </Space>
            </Form>

            <Table
                dataSource={dataSource}
                columns={columns as ColumnsType<any>}
                pagination={{
                    showSizeChanger: true,
                    ...pagination,
                    onChange: (page, pageSize) => {
                        getData(page, pageSize);
                    },
                }}
                rowSelection={rowSelection}
                rowKey={"id"}
                scroll={{ x: 2400, y: 500 }}
                onRow={record => {
                    return {
                        onClick: event => {
                            showItem(record);
                        },
                    };
                }}
            />
            <EditProductModal
                row={row}
                mode={mode}
                open={editBol}
                typeBol={row ? row?.mtpckEndprdMarkCd === "I" : false}
                closeFn={load => {
                    setEditBol(false);
                    getData();
                }}
                updateBody={({ values, type, index }) => {
                    if (type === "add") {
                        console.log(values);
                        dataSource.unshift(values);
                        setDataSource([...dataSource]);
                        dataCache.current.unshift(values);
                    } else {
                        console.log(values);
                        dataSource[index] = values;
                        setDataSource([...dataSource]);
                        // const
                        const i = dataCache.current.findIndex(item => item.id === values.id);
                        dataCache.current[i] = values;
                    }
                }}
            />
        </div>
    );
};
