import React, { useEffect, useRef, useState } from "react";
import { DTEditForm, DTEditFormRefs, DTEditFormListConfigs, DTEditFormItemProps } from "@dt/components";
import { DetailUnitConsumptionConfigs } from "../configs";
// import DetailBodyForm from "./detail-body-form";
import moment from "moment";
// import DetailBodyForm from "./detail-body-form";
import DetailProductForm from "./detail-product-form";
import DetailUnitConsumptionForm from "./detail-unit-consumption-form";
import { lib } from "react-single-app";
import { Button } from "antd";

export default ({ detail, load }) => {
    const ref = useRef<DTEditFormRefs>();
    const [mode, setMode] = useState<"read" | "edit">("read");
    const configs = DetailUnitConsumptionConfigs;

    useEffect(() => {}, []);

    return (
        <div style={{ minHeight: "600px" }}>
            <DTEditForm
                configs={configs}
                layout={{
                    mode: "appoint",
                    colNum: 3,
                }}
                ref={ref}
                totalMode={mode}
            />
            <div>
                <DetailUnitConsumptionForm
                    detail={detail}
                    showItem={item => {
                        console.log("item:", item);
                        if (item) {
                            if (item.endPrdProductId === ref.current.form.getFieldValue("endPrdProductId")) {
                                ref.current.form.resetFields();
                            } else {
                                ref.current.form.setFieldsValue(item);
                            }
                        } else {
                            // ref.current.setDetail({})
                            ref.current.form.resetFields();
                        }
                    }}
                />
            </div>
        </div>
    );
};
