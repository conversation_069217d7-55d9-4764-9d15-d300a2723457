import React, { useEffect, useRef, useState } from "react";
import { DTEditForm, DTEditFormRefs, DTEditFormListConfigs, DTEditFormItemProps } from "@dt/components";
import { DetailProductConfigs } from "../configs";
// import DetailBodyForm from "./detail-body-form";
import moment from "moment";
// import DetailBodyForm from "./detail-body-form";
import DetailProductForm from "./detail-product-form";
import { lib } from "react-single-app";
import { Button } from "antd";

export default ({ detail, load }) => {
    const ref = useRef<DTEditFormRefs>();
    const [mode, setMode] = useState<"read" | "edit">("read");
    const configs = DetailProductConfigs;

    useEffect(() => {
        ref.current.setConfigFormItem("goodsCode", { listItemLabelKey: "id" });
    }, []);

    return (
        <div style={{ minHeight: "600px" }}>
            <div style={{ display: "flex", alignItems: "center", justifyContent: "flex-end", flexDirection: "row" }}>
                <Button
                    onClick={() => {
                        lib.request({
                            url: "/ccs/processTradeBook/item/export",
                            data: {
                                refBookId: +lib.getParam("id"),
                                goodsType: 2, // 1: 料件，2: 成品
                            },
                            success(data) {
                                console.log("导出成功", data);
                                lib.openPage("/download-center?page_title=下载中心");
                            },
                        });
                    }}>
                    导出
                </Button>
            </div>
            <DTEditForm
                configs={configs}
                layout={{
                    mode: "appoint",
                    colNum: 3,
                }}
                ref={ref}
                totalMode={mode}
            />
            <div>
                <DetailProductForm
                    detail={detail}
                    showItem={item => {
                        console.log("item:", item);
                        if (item) {
                            if (item.productId === ref.current.form.getFieldValue("productId")) {
                                ref.current.form.resetFields();
                            } else {
                                if (item.licenceValidityDate) {
                                    item.licenceValidityDate = moment(item.licenceValidityDate);
                                }
                                ref.current.form.setFieldsValue(item);
                            }
                        } else {
                            // ref.current.setDetail({})
                            ref.current.form.resetFields();
                        }
                    }}
                />
            </div>
        </div>
    );
};
