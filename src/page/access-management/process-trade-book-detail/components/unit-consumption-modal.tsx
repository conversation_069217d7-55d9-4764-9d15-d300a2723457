import React, { useRef, useEffect } from "react";
import { Mo<PERSON>, But<PERSON>, message } from "antd";
import { DTEditForm, DTEditFormConfigs, DTEditFormRefs } from "@dt/components";
import { lib } from "react-single-app";

export default function UnitConsumptionModal({
    visible,
    mode = "add", // "add" | "edit"
    initialValues = {},
    onOk,
    onCancel,
}) {
    const formRef = useRef<DTEditFormRefs>(null);
    const unitConsumptionFormConfigs: DTEditFormConfigs = [
        {
            type: "SELECT",
            fProps: {
                label: "成品申报序号",
                name: "endPrdId",
                rules: [{ required: true, message: "请选择成品申报序号" }],
            },
            cProps: {
                disabled: mode === "edit", // 编辑模式下禁用
            },
            list: [], // 需传入下拉数据
            dataUrl: "/ccs/processTradeBook/listEndprdSeqById",
            defaultParams: {
                id: +lib.getParam("id"), // 获取当前书籍ID
            },
            listItemLabelKey: "goodsName",
        },
        {
            type: "SELECT",
            fProps: {
                label: "料件申报序号",
                name: "mtpckId",
                rules: [{ required: true, message: "请选择料件申报序号" }],
            },
            cProps: {
                disabled: mode === "edit", // 编辑模式下禁用
            },
            list: [],
            dataUrl: "/ccs/processTradeBook/listMtpckSeqById",
            defaultParams: {
                id: +lib.getParam("id"), // 获取当前书籍ID
            },
            listItemLabelKey: "goodsName",
        },
        {
            type: "INPUT",
            fProps: {
                label: "净耗",
                name: "netConsumption",
                rules: [
                    { required: true, message: "请输入净耗" },
                    {
                        pattern: /^\d{1,6}$/,
                        message: "净耗最大6位整数",
                    },
                ],
            },
        },
        {
            type: "SELECT",
            fProps: {
                label: "单耗申报状态",
                name: "declareStatus",
                rules: [{ required: true, message: "请选择单耗申报状态" }],
            },
            list: [],
            dataUrl: "/ccs/processTradeBook/listDeclareStatus",
            selectShowMode: "together",
        },
    ];
    useEffect(() => {
        if (visible && mode === "edit" && initialValues) {
            setTimeout(() => {
                // 确保formRef.current?.form存在
                formRef.current?.form.setFieldsValue(initialValues);
            }, 200);
        } else if (visible && mode === "add") {
            formRef.current?.form.resetFields();
        }
    }, [visible, mode, initialValues]);

    const handleOk = async () => {
        try {
            const values = await formRef.current?.form.validateFields();
            // onOk && onOk(values);
            lib.request({
                url: `/ccs/processTradeBook/consumption/createOrEdit`,
                methods: "POST",
                data: {
                    ...values,
                    refBookId: lib.getParam("id"),
                    //@ts-ignore
                    id: mode === "edit" ? initialValues?.id : undefined, // 如果是编辑模式，传入ID
                },
                success: () => {
                    message.success(`${mode === "edit" ? "编辑" : "新增"}单损耗成功`);
                    formRef.current?.form.resetFields();
                    onOk && onOk(values);
                },
            });
        } catch (e) {
            // 校验失败
        }
    };

    return (
        <Modal
            title={mode === "add" ? "新增单损耗" : "编辑单损耗"}
            open={visible}
            onOk={handleOk}
            onCancel={onCancel}
            destroyOnClose
            okText="确定"
            cancelText="取消">
            <DTEditForm
                ref={formRef}
                configs={unitConsumptionFormConfigs}
                layout={{ colNum: 1, mode: "appoint" }}
                // totalMode={mode === "add" ? "edit" : "edit"}
            />
        </Modal>
    );
}
