import React, { useEffect, useRef, useState } from "react";
import { Space, Button, Input, Select, Table, Form, message, Modal, Row, Col } from "antd";
import { DTEditForm, DTEditFormRefs } from "@dt/components";

import { EditOutlined, SearchOutlined, RedoOutlined } from "@ant-design/icons";
import { DDYObject, lib } from "react-single-app";
import EditModal from "./edit-modal";
import { ColumnsType } from "antd/es/table/interface";
import EditProductModal from "./edit-product-modal";
import UnitConsumptionModal from "./unit-consumption-modal";
let timeout: ReturnType<typeof setTimeout> | null;
export default ({ detail, showItem }) => {
    const [form] = Form.useForm();
    const ref = useRef<DTEditFormRefs>();
    const [dataSource, setDataSource] = useState([]);
    const dataCache = useRef([]);
    const [editBol, setEditBol] = useState(false);
    const [row, setRow] = useState<DDYObject>({});
    const [mode, setMode] = useState("add");
    const [statusList, setStatusList] = useState([]);
    const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
    const [selectedRows, setSelectedRows] = useState<DDYObject[]>([]);

    const [pagination, setPagination] = useState({
        current: 1,
        pageSize: 20,
        total: 0,
    });

    const onSelectChange = (newSelectedRowKeys: React.Key[], rows) => {
        setSelectedRowKeys(newSelectedRowKeys);
        setSelectedRows(rows);
        if (rows.length === 1) {
            showItem(rows[0]);
        } else {
            showItem();
        }
    };

    const getStatusList = () => {
        lib.request({
            url: "/ccs/processTradeBook/listDeclareStatus",
            success(data) {
                setStatusList(data);
            },
        });
    };
    const rowSelection = {
        selectedRowKeys,
        onChange: onSelectChange,
        // onChange: (row, selectedRows) => {
        //     setSelectedRowKeys(row);
        //     if (selectedRows.length === 1) {
        //         setCurrent(selectedRows[0]);
        //         currentRef.current = { ...selectedRows[0] };
        //     } else {
        //         setCurrent({});
        //         currentRef.current = {};
        //     }
        // },
    };
    const fileldData = useRef<DDYObject>(null);
    const add = () => {
        if (fileldData.current) {
            setEditBol(true);
            setMode("add");
            fileldData.current.seqNo = dataSource.length + 1;
            console.log(form.getFieldValue("productId"));
            //@ts-ignore
            setRow({ ...fileldData.current });
        } else {
            message.error("请先选择料号");
        }
    };
    // const [queryParams, setQueryParams] = useState({
    //     mtpckEndprdMarkCd: "",
    //     productId: "",
    //     goodsName: "",
    //     goodsCode: "",
    // });

    const handleSearch = () => {
        // console.log("查询参数:", queryParams);
        // const queryParams = form.getFieldsValue();
        getData(1, 20);
    };

    const handleReset = () => {
        form.resetFields();
        console.log("重置查询条件");
        // setDataSource([...dataCache.current]);
        getData(1, 20);
    };

    const columns = [
        {
            title: "序号",
            dataIndex: "seqNo",
            key: "seqNo",
            width: 50,
            render: (_, record, index) => index + 1,
        },
        {
            title: "成品序号",
            dataIndex: "endPrdSeqNo",
            key: "endPrdSeqNo",
            width: 50,
        },
        {
            title: "成品料号",
            dataIndex: "endPrdProductId",
            key: "endPrdProductId",
            width: 150,
        },
        {
            title: "成品商品编码",
            dataIndex: "endPrdHsCode",
            key: "endPrdHsCode",
            width: 80,
        },
        {
            title: "成品商品名称",
            dataIndex: "endPrdGoodsName",
            key: "endPrdGoodsName",
            width: 180,
        },
        {
            title: "料件序号",
            dataIndex: "mtpckSeqNo",
            key: "mtpckSeqNo",
            width: 180,
        },
        {
            title: "料件料号",
            dataIndex: "mtpckProductId",
            key: "mtpckProductId",
            width: 180,
        },
        {
            title: "料件商品编码",
            dataIndex: "mtpckHsCode",
            key: "mtpckHsCode",
            width: 180,
        },
        {
            title: "料件商品名称",
            dataIndex: "mtpckGoodsName",
            key: "mtpckGoodsName",
            width: 180,
        },
        {
            title: "单耗版本号",
            dataIndex: "consumptionVersionNo",
            key: "consumptionVersionNo",
            width: 180,
        },
        {
            title: "净耗",
            dataIndex: "netConsumption",
            key: "netConsumption",
            width: 100,
        },
        {
            title: "有形损耗率（%）",
            dataIndex: "tangibleLossRate",
            key: "tangibleLossRate",
            width: 100,
        },
        {
            title: "无形损耗率（%）",
            dataIndex: "intangibleLossRate",
            key: "intangibleLossRate",
            width: 100,
        },
        {
            title: "单耗申报状态",
            dataIndex: "declareStatusDesc",
            key: "declareStatusDesc",
            width: 100,
        },
        {
            title: "保税料件比例（%）",
            dataIndex: "bondedMaterialRatio",
            key: "bondedMaterialRatio",
            width: 180,
        },
        {
            title: "修改标志",
            dataIndex: "modifyFlagDesc",
            key: "modifyFlagDesc",
            width: 80,
        },
        {
            title: "单耗有效期",
            dataIndex: "consumptionValidityStr",
            key: "consumptionValidityStr",
            width: 100,
        },

        {
            title: "操作",
            key: "action",
            width: 100,
            fixed: "right",
            dataIndex: "action",
            render: (_, row, index) => (
                <Button
                    type="link"
                    icon={<EditOutlined />}
                    onClick={() => {
                        setEditBol(true);
                        setMode("edit");
                        //@ts-ignore
                        setRow({ ...row, index: index });
                    }}>
                    编辑
                </Button>
            ),
        },
    ];

    const getData = (page = 1, pageSize = 20) => {
        const queryParams = form.getFieldsValue();
        lib.request({
            url: "/ccs/processTradeBook/consumption/paging",
            data: {
                refBookId: lib.getParam("id"),
                // goodsType: 2,
                current: page,
                pageSize: pageSize,
                ...queryParams,
            },
            success(data) {
                console.log("data:", data.dataList);
                setDataSource(data.dataList);
                dataCache.current = [...data.dataList];
                setPagination({
                    current: data.page.current,
                    pageSize: data.page.pageSize,
                    total: data.page.total,
                });
            },
        });
    };

    const del = () => {
        // /bizDeclareForm/batchDeleteItems
        if (selectedRowKeys.length === 0) {
            return message.error("请勾选数据");
        }
        const idList = selectedRows.map(item => item.id);
        Modal.confirm({
            title: "确认",
            content: "确定删除单损耗吗？",
            onOk: () => {
                lib.request({
                    url: "/ccs/processTradeBook/consumption/delete",
                    data: {
                        // bizDeclareFormId: lib.getParam("id"),
                        idList: idList,
                    },
                    success(data) {
                        message.success("删除成功");
                        getData();
                        // handleReset();
                    },
                });
            },
        });
    };

    const importData = () => {
        let arr = [];
        const params = {
            refBookId: lib.getParam("id"),
        };
        for (let i in params) {
            arr.push(i + "=" + params[i]);
        }
        const str = arr.join("&");
        let importExtendParamBase64 = window.btoa(str + "");
        lib.openPage(
            `/excel/import-data?page_title=保税加工贸易单损耗导入&code=IMPORT_PROCESS_TRADE_BOOK_CONSUMPTION&importExtendParam=${encodeURIComponent(
                importExtendParamBase64,
            )}`,
            () => {
                getData();
            },
        );
    };

    useEffect(() => {
        getData();
        getStatusList();
    }, []);

    return (
        <div>
            <Row>
                <Space style={{ marginBottom: "16px", marginLeft: "20px" }} direction="horizontal">
                    <Button
                        type="primary"
                        onClick={() => {
                            setMode("add");
                            setEditBol(true);
                        }}>
                        新增
                    </Button>
                    <Button
                        onClick={() => {
                            importData();
                        }}>
                        导入
                    </Button>
                    <Button
                        onClick={() => {
                            del();
                        }}>
                        批量删除
                    </Button>
                </Space>
            </Row>

            <Form form={form} style={{ marginTop: "20px" }}>
                <div>
                    <Space style={{ marginBottom: "16px" }}>
                        <Form.Item name="endPrdSeqNo" label="成品申报序号">
                            <Input placeholder="成品申报序号" style={{ width: "150px" }} />
                        </Form.Item>
                        <Form.Item name="mtpckSeqNo" label="料件申报序号">
                            <Input placeholder="料件申报序号" style={{ width: "150px" }} />
                        </Form.Item>
                        <Form.Item name="declareStatus" label="单耗申报状态">
                            {/* <Input placeholder="单耗申报状态" style={{ width: "150px" }} /> */}
                            <Select style={{ width: "150px" }}>
                                {statusList.map(item => (
                                    <Select.Option key={item.id} value={item.id}>
                                        {item.name}
                                    </Select.Option>
                                ))}
                            </Select>
                        </Form.Item>
                    </Space>
                </div>
                <div>
                    <Space>
                        <Form.Item name="endPrdName" label="成品商品名称">
                            <Input placeholder="成品商品名称" style={{ width: "150px" }} />
                        </Form.Item>
                        <Form.Item name="mtpckName" label="料件商品名称">
                            <Input placeholder="料件商品名称" style={{ width: "150px" }} />
                        </Form.Item>
                        <Form.Item>
                            <Button type="primary" icon={<SearchOutlined />} onClick={handleSearch}>
                                查询
                            </Button>
                        </Form.Item>
                        <Form.Item>
                            <Button icon={<RedoOutlined />} onClick={handleReset}>
                                重置
                            </Button>
                        </Form.Item>
                    </Space>
                </div>
                <br />
            </Form>

            <Table
                dataSource={dataSource}
                columns={columns as ColumnsType<any>}
                pagination={{
                    showSizeChanger: true,
                    ...pagination,
                    onChange: (page, pageSize) => {
                        getData(page, pageSize);
                    },
                }}
                rowSelection={rowSelection}
                rowKey={"id"}
                scroll={{ x: 2400, y: 500 }}
                onRow={record => {
                    return {
                        onClick: event => {
                            showItem(record);
                            if (selectedRowKeys.includes(record.id)) {
                                const arr = selectedRowKeys.filter(item => item !== record.id);
                                setSelectedRowKeys([...arr]);
                            } else {
                                selectedRowKeys.push(record.id);
                                setSelectedRowKeys([...selectedRowKeys]);
                            }
                        },
                    };
                }}
            />
            <UnitConsumptionModal
                visible={editBol}
                onOk={bol => {
                    setEditBol(false);
                    if (bol) {
                        getData();
                    }
                }}
                onCancel={() => {
                    setEditBol(false);
                }}
                initialValues={row}
                mode={mode}
            />
        </div>
    );
};
