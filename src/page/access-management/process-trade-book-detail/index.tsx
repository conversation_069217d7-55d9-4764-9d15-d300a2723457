import { Tabs } from "antd";
import React, { useEffect, useState } from "react";
import { DDYObject, lib } from "react-single-app";
import DetailHead from "./components/detail-head";
import DetailBody from "./components/detail-body";
import DetailProduct from "./components/detail-product";
import DetailUnitConsumption from "./components/detail-unit-consumption";
import DetailLog from "./components/detail-log";
export default () => {
    const [detail, setDetail] = useState<DDYObject>({});
    const getDetail = () => {
        lib.request({
            url: "/ccs/processTradeBook/detail",
            data: {
                id: lib.getParam("id"),
            },
            success(data) {
                setDetail(data);
            },
        });
    };

    // const
    const coms = {
        0: <DetailHead detail={detail} load={getDetail} />,
        1: <DetailBody detail={detail} load={getDetail} />,
        2: <DetailProduct detail={detail} load={getDetail} />,
        3: <DetailUnitConsumption detail={detail} load={getDetail} />,
        // 4: <DetailLog detail={detail} load={getDetail} />,
    };

    useEffect(() => {
        getDetail();
    }, []);
    return (
        <div style={{ padding: "24px" }}>
            <h2>{detail?.statusDesc}</h2>

            <Tabs
                defaultActiveKey="0"
                destroyInactiveTabPane={true}
                items={["表头", "料件", "成品", "单损耗", "操作日志"].map((text, i) => {
                    const id = String(i + 1);
                    return {
                        key: id,
                        label: `${text}`,
                        children: coms[i],
                        // icon: <Icon />,
                    };
                })}
            />
        </div>
    );
};
