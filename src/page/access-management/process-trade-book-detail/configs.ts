import { DTEditForm, DTEditFormRefs, DTEditFormListConfigs, DTEditFormItemProps } from "@dt/components";
export const DetailBodyHeadConfigs: DTEditFormListConfigs = [
    {
        type: "INPUT",
        fProps: { label: "序号", name: "seqNo" },
        cProps: {
            disabled: true,
        },
    },
    {
        type: "INPUT",
        fProps: { label: "料号", name: "productId", rules: [{ required: true, message: "请输入料号" }] },
    },
    {
        type: "SELECT",
        fProps: {
            label: "商品编码",
            name: "hsCode",
            rules: [{ required: true, message: "请选择商品编码" }],
        },
        list: [],
        dataUrl: "/ccs/customs/listHsV2",
        selectShowMode: "together",
    },
    {
        type: "INPUT",
        fProps: {
            label: "商品名称",
            name: "goodsName",
            rules: [
                { required: true, message: "请输入商品名称" },
                {
                    pattern:
                        /^[\u4e00-\u9fa5a-zA-Z0-9`~!@#$%^&*()_\-+=\[\]{}|\\;:'",.<>/?·！￥…（）—【】、；：‘’“”，。《》？]{1,64}$/,
                    message: "仅限中英文、数字、符号，不能有空格，且不超过64字符",
                },
            ],
        },
    },
    {
        type: "INPUT",
        fProps: {
            label: "规格型号",
            name: "goodsModel",
            rules: [
                { required: true, message: "请输入商品规格型号" },
                {
                    pattern:
                        /^[\u4e00-\u9fa5a-zA-Z0-9`~!@#$%^&*()_\-+=\[\]{}|\\;:'",.<>/?·！￥…（）—【】、；：‘’“”，。《》？]{1,64}$/,
                    message: "仅限中英文、数字、符号，不能有空格，且不超过64字符",
                },
            ],
        },
    },
    {
        type: "SELECT",
        fProps: {
            label: "申报计量单位",
            name: "declareUnit",
            rules: [{ required: true, message: "请选择申报计量单位" }],
        },
        list: [],
        dataUrl: "/ccs/customs/listUom",
        selectShowMode: "together",
    },
    {
        type: "SELECT",
        fProps: {
            label: "法定计量单位",
            name: "legalUnit",
            rules: [{ required: true, message: "请选择法定计量单位" }],
        },
        list: [],
        dataUrl: "/ccs/customs/listUom",
        selectShowMode: "together",
        cProps: {
            disabled: true,
        },
    },
    {
        type: "SELECT",
        fProps: { label: "法定第二计量单位", name: "legalSecondUnit" },
        list: [],
        dataUrl: "/ccs/customs/listUom",
        selectShowMode: "together",
        cProps: {
            disabled: true,
        },
    },
    {
        type: "INPUT",
        fProps: {
            label: "申报单价",
            name: "declareUnitPrice",
            rules: [
                {
                    pattern: /^\d{1,14}(.\d{1,5})?$/,
                    message: "请输入正确的申报单价，最多14位整数和5位小数",
                },
            ],
        },
    },
    {
        type: "SELECT",
        fProps: { label: "币制", name: "currency" },
        list: [],
        dataUrl: "/ccs/customs/listCurrency",
    },
    {
        type: "INPUT",
        fProps: {
            label: "申报数量",
            name: "declareQty",
            rules: [
                {
                    pattern: /^\d{1,16}$/,
                    message: "请输入最多16位整数",
                },
            ],
        },
    },
    {
        type: "SELECT",
        fProps: { label: "征免方式", name: "dutyExemptionMethod" },
        list: [],
        cProps: {
            disabled: true,
        },
        dataUrl: "/ccs/processTradeBook/listDutyExemptionMethod",
    },
    {
        type: "SELECT",
        fProps: {
            label: "企业执行标志",
            name: "companyExecutionFlag",
            rules: [{ required: true, message: "请选择企业执行标志" }],
        },
        list: [],
        dataUrl: "/ccs/processTradeBook/listCompanyExecutionFlag",
        cProps: {
            disabled: true,
        },
    },
    {
        type: "SELECT",
        fProps: {
            label: "重点商品标识",
            name: "focusMark",
            rules: [{ required: true, message: "请选择重点商品标识" }],
        },
        list: [],
        selectShowMode: "together",
        dataUrl: "/ccs/processTradeBook/listFocusMark",
        cProps: {
            disabled: true,
        },
    },
    {
        type: "SELECT",
        fProps: { label: "国别(地区)", name: "countryRegion" },
        list: [],
        dataUrl: "/ccs/customs/listCountry",
        selectShowMode: "together",
        cProps: {
            disabled: true,
        },
    },
    {
        type: "SELECT",
        fProps: { label: "修改标志", name: "modifyFlag", rules: [{ required: true, message: "请选择修改标志" }] },
        list: [],
        dataUrl: "/ccs/bizDeclareForm/listItemEditMark",
        cProps: {
            disabled: true,
        },
    },
    {
        type: "SELECT",
        fProps: { label: "海关执行标志", name: "customsExecutionFlag" },
        list: [],
        dataUrl: "/ccs/processTradeBook/listCustomsExecutionFlag",
        cProps: {
            disabled: true,
        },
    },
    {
        type: "INPUT",
        fProps: { label: "期初数量", name: "initialQty" },
        cProps: {
            disabled: true,
        },
    },
    {
        type: "SELECT",
        fProps: { label: "数量控制标志", name: "qtyControlFlag" },
        list: [],
        dataUrl: "/ccs/processTradeBook/listQtyControlFlag",
        cProps: {
            disabled: true,
        },
    },
    {
        type: "INPUT",
        fProps: { label: "批准最大余数量", name: "maxRemainQty" },
        cProps: {
            disabled: true,
        },
    },
    {
        type: "INPUT",
        fProps: { label: "备注", name: "remark", rules: [{ type: "string", max: 255 }] },
    },
];

export const DetailProductConfigs: DTEditFormListConfigs = [
    {
        type: "INPUT",
        fProps: { label: "序号", name: "seqNo" },
        cProps: {
            disabled: true,
        },
    },
    {
        type: "INPUT",
        fProps: { label: "料号", name: "productId", rules: [{ required: true, message: "请输入料号" }] },
    },
    {
        type: "SELECT",
        fProps: {
            label: "商品编码",
            name: "hsCode",
            rules: [{ required: true, message: "请选择商品编码" }],
        },
        list: [],
        dataUrl: "/ccs/customs/listHsV2",
        selectShowMode: "together",
    },
    {
        type: "INPUT",
        fProps: {
            label: "商品名称",
            name: "goodsName",
            rules: [
                { required: true, message: "请输入商品名称" },
                {
                    pattern:
                        /^[\u4e00-\u9fa5a-zA-Z0-9`~!@#$%^&*()_\-+=\[\]{}|\\;:'",.<>/?·！￥…（）—【】、；：‘’“”，。《》？]{1,64}$/,
                    message: "仅限中英文、数字、符号，不能有空格，且不超过64字符",
                },
            ],
        },
    },
    {
        type: "INPUT",
        fProps: {
            label: "规格型号",
            name: "goodsModel",
            rules: [
                { required: true, message: "请输入商品规格型号" },
                {
                    pattern:
                        /^[\u4e00-\u9fa5a-zA-Z0-9`~!@#$%^&*()_\-+=\[\]{}|\\;:'",.<>/?·！￥…（）—【】、；：‘’“”，。《》？]{1,64}$/,
                    message: "仅限中英文、数字、符号，不能有空格，且不超过64字符",
                },
            ],
        },
    },
    {
        type: "SELECT",
        fProps: {
            label: "申报计量单位",
            name: "declareUnit",
            rules: [{ required: true, message: "请选择申报计量单位" }],
        },
        list: [],
        dataUrl: "/ccs/customs/listUom",
        selectShowMode: "together",
    },
    {
        type: "SELECT",
        fProps: {
            label: "法定计量单位",
            name: "legalUnit",
            rules: [{ required: true, message: "请选择法定计量单位" }],
        },
        list: [],
        dataUrl: "/ccs/customs/listUom",
        selectShowMode: "together",
        cProps: {
            disabled: true,
        },
    },
    {
        type: "SELECT",
        fProps: { label: "法定第二计量单位", name: "legalSecondUnit" },
        list: [],
        dataUrl: "/ccs/customs/listUom",
        selectShowMode: "together",
        cProps: {
            disabled: true,
        },
    },
    {
        type: "INPUT",
        fProps: {
            label: "申报单价",
            name: "declareUnitPrice",
            rules: [
                {
                    pattern: /^\d{1,14}(.\d{1,5})?$/,
                    message: "请输入正确的申报单价，最多14位整数和5位小数",
                },
            ],
        },
    },
    {
        type: "SELECT",
        fProps: { label: "币制", name: "currency" },
        list: [],
        dataUrl: "/ccs/customs/listCurrency",
    },
    {
        type: "INPUT",
        fProps: {
            label: "申报数量",
            name: "declareQty",
            rules: [
                {
                    pattern: /^\d{1,16}$/,
                    message: "请输入最多16位整数",
                },
            ],
        },
    },
    {
        type: "SELECT",
        fProps: { label: "征免方式", name: "dutyExemptionMethod" },
        list: [],
        cProps: {
            disabled: true,
        },
        dataUrl: "/ccs/processTradeBook/listDutyExemptionMethod",
    },
    {
        type: "SELECT",
        fProps: {
            label: "企业执行标志",
            name: "companyExecutionFlag",
            rules: [{ required: true, message: "请选择企业执行标志" }],
        },
        list: [],
        dataUrl: "/ccs/processTradeBook/listCompanyExecutionFlag",
        cProps: {
            disabled: true,
        },
    },
    {
        type: "SELECT",
        fProps: {
            label: "重点商品标识",
            name: "focusMark",
            rules: [{ required: true, message: "请选择重点商品标识" }],
        },
        list: [],
        selectShowMode: "together",
        dataUrl: "/ccs/processTradeBook/listFocusMark",
        cProps: {
            disabled: true,
        },
    },
    {
        type: "SELECT",
        fProps: { label: "国别(地区)", name: "countryRegion" },
        list: [],
        dataUrl: "/ccs/customs/listCountry",
        selectShowMode: "together",
        cProps: {
            disabled: true,
        },
    },
    {
        type: "SELECT",
        fProps: { label: "修改标志", name: "modifyFlag", rules: [{ required: true, message: "请选择修改标志" }] },
        list: [],
        dataUrl: "/ccs/bizDeclareForm/listItemEditMark",
        cProps: {
            disabled: true,
        },
    },
    {
        type: "SELECT",
        fProps: { label: "数量控制标志", name: "qtyControlFlag" },
        list: [],
        dataUrl: "/ccs/processTradeBook/listQtyControlFlag",
        cProps: {
            disabled: true,
        },
    },
    {
        type: "SELECT",
        fProps: { label: "海关执行标志", name: "customsExecutionFlag" },
        list: [],
        dataUrl: "/ccs/processTradeBook/listCustomsExecutionFlag",
        cProps: {
            disabled: true,
        },
    },
    {
        type: "SELECT",
        fProps: { label: "单损质疑标志", name: "consumptionQuestionFlag" },
        list: [],
        dataUrl: "/ccs/processTradeBook/listConsumptionQuestionFlag",
        cProps: {
            disabled: true,
        },
    },
    {
        type: "SELECT",
        fProps: { label: "磋商标志", name: "negotiationFlag" },
        cProps: {
            disabled: true,
        },
        list: [],
        dataUrl: "/ccs/processTradeBook/listNegotiationFlag",
    },
    {
        type: "INPUT",
        fProps: { label: "备注", name: "remark", rules: [{ type: "string", max: 255 }] },
    },
];

export const DetailUnitConsumptionConfigs: DTEditFormListConfigs = [
    { type: "INPUT", fProps: { label: "序号", name: "seqNo" } },
    { type: "INPUT", fProps: { label: "成品序号", name: "endPrdSeqNo" } },
    { type: "INPUT", fProps: { label: "成品料号", name: "endPrdProductId" } },
    { type: "INPUT", fProps: { label: "成品商品编码", name: "endPrdHsCode" } },
    { type: "INPUT", fProps: { label: "成品商品名称", name: "endPrdGoodsName" } },
    { type: "INPUT", fProps: { label: "料件序号", name: "mtpckSeqNo" } },
    { type: "INPUT", fProps: { label: "料件料号", name: "mtpckProductId" } },
    { type: "INPUT", fProps: { label: "料件商品编码", name: "mtpckHsCode" } },
    { type: "INPUT", fProps: { label: "料件商品名称", name: "mtpckGoodsName" } },
    { type: "INPUT", fProps: { label: "单耗版本号", name: "consumptionVersionNo" } },
    { type: "INPUT", fProps: { label: "单耗有效期", name: "consumptionValidity" } },
    { type: "INPUT", fProps: { label: "净耗", name: "netConsumption" } },
    { type: "INPUT", fProps: { label: "有形损耗率(%)", name: "tangibleLossRate" } },
    { type: "INPUT", fProps: { label: "无形损耗率(%)", name: "intangibleLossRate" } },
    { type: "INPUT", fProps: { label: "保税料件比例(%)", name: "bondedMaterialRatio" } },
    { type: "INPUT", fProps: { label: "单耗申报状态", name: "declareStatus" } },
    { type: "INPUT", fProps: { label: "修改标志", name: "modifyFlag" } },
    { type: "INPUT", fProps: { label: "备注", name: "remark" } },
];
