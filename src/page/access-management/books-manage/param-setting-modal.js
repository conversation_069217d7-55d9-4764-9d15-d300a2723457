import React, { useState, useEffect } from "react";

import { ConfigFormCenter } from "react-single-app";
import { Modal } from "antd";

export default function ParamSettingModal({ detail, visible, onClose }) {
    const ref = React.useRef();
    const handleCancel = () => {
        onClose();
    };
    const handleOk = () => {
        ref.current.submitForm();
    };

    const onConfigLoadSuccess = config => {
        config.baseInfo.children.map(item => {
            if (item.type === "single-select" && !item.fromParam) {
                ref.current.initSelect(item);
            }
        });
        if (detail) {
            ref.current.getDetail({ url: "/ccs/customsBook/getConfigParam", data: { id: detail.id } });
        }
    };
    const beforeSubmit = values => {
        values.id = detail.id;
        return values;
    };
    const onSubmitSuccess = () => {
        onClose(true);
    };

    return (
        <Modal title={"参数配置"} open={visible} destroyOnClose={true} onOk={handleOk} onCancel={handleCancel}>
            <ConfigFormCenter
                ref={ref}
                confData={confData}
                onConfigLoadSuccess={onConfigLoadSuccess}
                submitUrl={"/ccs/customsBook/updConfigParam"}
                onSubmitSuccess={onSubmitSuccess}
                beforeSubmit={beforeSubmit}
            />
        </Modal>
    );
}

const confData = {
    baseInfo: {
        children: [
            {
                label: "仓储性质",
                editEnable: true,
                name: "storageAttr",
                type: "single-select",
                allowClear: true,
                from: "/ccs/customsBook/listStorageAttr",
            },
            {
                label: "核注生成流水",
                editEnable: true,
                name: "stockListGenerateEnable",
                type: "single-select",
                from: "/ccs/customsBook/listEnableSwitch",
            },
        ],
        label: "",
        name: "baseInfo",
        isGroup: true,
        className: "baseInfo",
    },
};
