import React, { useState, useImperativeHandle, forwardRef } from "react";
import { lib, SearchList, getConfigDataUtils, event, HOC } from "react-single-app";
import axios from "axios";
import { Tag, Radio, Modal, Button, message } from "antd";
import AddModal from "./component/add-modal";
// import { OvertimeReason, AddTimeoutReason } from "./component/index";
// import "./css/abnormal-statistics.less";
const { mapAuthButtonsToState, widthSearchListRowSpanAble } = HOC;
@mapAuthButtonsToState({})
//@ts-ignore
@widthSearchListRowSpanAble("contactsList", ["name", "phone"])
class WarehouseAddressManage extends SearchList {
    constructor(props) {
        super(props);
        this.onSearchReset = this.onSearchReset.bind(this);
        this.radioCheckedRef = React.createRef();
    }
    getConfig() {
        let url = getConfigDataUtils.getDataUrlByDataId(836);
        return axios.get(url).then(res => res.data.data);
    }
    componentDidMount() {
        event.on("onSearchReset", this.onSearchReset);
    }
    componentWillUnmount() {
        event.off("onSearchReset", this.onSearchReset);
    }
    onSearchReset() {}
    onSearch(search) {}

    // 页面头部右边的菜单
    renderRightOperation() {
        return <></>;
    }
    // 页面头部左边的菜单
    renderLeftOperation(row) {
        return (
            <>
                <AddModal mode={"add"} load={this.load} />
            </>
        );
    }
    operateFn(row) {
        return (
            <>
                <AddModal mode={"edit"} row={row} load={this.load} />
                {/* <Button
                    type="link"
                    onClick={() => {
                        Modal.confirm({
                            title: "确认",
                            content: "确认删除吗？",
                            onOk: () => {
                                lib.request({
                                    url: "/danding-wcms/api/warehouse/delete",
                                    data: {
                                        id: row.id,
                                    },
                                    success: () => {
                                        message.success("删除成功");
                                        this.load();
                                    },
                                });
                            },
                        });
                    }}>
                    删除
                </Button> */}
            </>
        );
    }
    addressFn(row) {
        return <>{row.addressConcat}</>;
    }
}
export default WarehouseAddressManage;
