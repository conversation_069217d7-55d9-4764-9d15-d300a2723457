import React, { useEffect, useRef, useState } from "react";
import { <PERSON><PERSON>, Modal, message } from "antd";
import { DTEditForm, DTEditFormRefs, DTEditFormConfigs } from "@dt/components";
import MultiPerson from "../../ride-hailing-detail/components/multi-person";
import { lib } from "react-single-app";

export const depFn = (arr: any[], fn: (item) => void) => {
    for (let i = 0; i < arr.length; i++) {
        // arr[i].value = arr[i].areaCode;
        // arr[i].name = arr[i].areaName
        fn && fn(arr[i]);
        if (arr[i].children && arr[i].children.length > 0) {
            depFn(arr[i].children, fn);
        }
    }
};

export default ({ mode, row, load }) => {
    const [open, setOpen] = useState(false);
    const ref = useRef<DTEditFormRefs>();
    const dataOnce = useRef(false);
    const dataRef = useRef([]);
    const confgs: DTEditFormConfigs = [
        {
            type: "SELECT",
            fProps: {
                label: "仓库类型",
                name: "type",
                rules: [{ required: true }],
            },
            list: [
                { id: 1, name: "代塔仓" },
                { id: 2, name: "非代塔仓" },
            ],
        },
        {
            type: "SELECT",
            fProps: {
                label: "实体仓名称",
                name: "warehouseCode",
                rules: [{ required: true }],
            },
            list: [],
            dataUrl: "/ccs/entityWarehouse/listEntityByCustomsBookAuth",
        },
        {
            type: "INPUT",
            fProps: {
                label: "实体仓编码",
                name: "warehouseCode",
            },
            cProps: {
                disabled: true,
            },
        },
        {
            type: "INPUT",
            fProps: {
                label: "wms编码",
                name: "wmsWarehouseCode",
            },
            cProps: {
                disabled: true,
            },
        },
        {
            type: "AUTOCOMPLETE",
            fProps: {
                label: "所属企业",
                name: "companyName",
                rules: [
                    {
                        required: true,
                        max: 32,
                        pattern: /^[\u4e00-\u9fa5a-zA-Z]{1,32}$/,
                        message: "请填写中英文，长度不允许超过32位",
                    },
                ],
            },
            list: [],
            // dataUrl: '/danding-wcms/api/warehouse/companyList',
        },
        {
            type: "CASCADER",
            fProps: {
                label: "省市区",
                name: "addressInfo",
                rules: [{ required: true, message: "请选择省市区" }],
            },
            cProps: {
                showSearch: {
                    filter: (inputValue: string, path: any[]) =>
                        path.some(
                            option => (option.label as string).toLowerCase().indexOf(inputValue.toLowerCase()) > -1,
                        ),
                },
            },
            list: [],
        },
        {
            type: "INPUT",
            fProps: {
                label: "地址",
                name: "address",
                rules: [
                    {
                        required: true,
                        max: 128,
                        pattern: /^[\u4e00-\u9fa5a-zA-Z0-9]{1,128}$/,
                        message: "请填写中英文数字，长度不允许超过128位",
                    },
                ],
            },
        },
        {
            type: "CUSTOM",
            fProps: {
                label: "联系人",
                name: "contactsList",
                rules: [
                    { required: true },
                    {
                        validator(rule, value, callback) {
                            if (!value) return Promise.resolve("");
                            for (let i = 0; i < value.length; i++) {
                                if (!value[i].name || !value[i].phone) {
                                    return Promise.reject(`第${i + 1}项请填写完整联系人信息`);
                                } else {
                                    if (!/^[\u4e00-\u9fa5a-zA-Z]{0,10}$/.test(value[i].name)) {
                                        return Promise.reject(`第${i + 1}项姓名请填写中英文，长度不允许超过10位`);
                                    }
                                    if (!/^1[0-9]\d{9}$|^0\d{2,3}-\d{7,8}$/.test(value[i].phone)) {
                                        return Promise.reject(`第${i + 1}项请填写12位以下座机或者11位的手机号码`);
                                    }
                                }
                            }
                            const arr = value.map(item => item.name + item.phone);
                            const arr1 = Array.from(new Set(arr));
                            if (arr.length !== arr1.length) return Promise.reject("存在重复数据");
                            return Promise.resolve("");
                        },
                    },
                ],
                // labelCol: { span: 4 },
            },
            render: MultiPerson,
            // colSpan: 16,
            editable: true,
            transferText(value?: any[]) {
                if (!value) return "";
                return value
                    .map(item => {
                        return item.name + "-" + item.phone;
                    })
                    .join("\n");
            },
            textMaxWidth: 400,
        },
    ];
    useEffect(() => {
        if (open === true) {
            if (row && Object.keys(row).length) {
                ref.current.setDetail({
                    ...row,
                });
                if (row.type === 2) {
                    ref.current.setConfigFormItem("warehouseCode", {
                        type: "INPUT",
                        fProps: {
                            label: "实体仓名称",
                            name: "warehouseCode",
                            rules: [
                                {
                                    required: true,
                                    max: 32,
                                    pattern: /^[\u4e00-\u9fa5a-zA-Z]{1,32}$/,
                                    message: "请填写中英文，长度不允许超过32位",
                                },
                            ],
                        },
                    });
                } else {
                    ref.current.setConfigFormItem("warehouseCode", {
                        type: "SELECT",
                        fProps: {
                            label: "实体仓名称",
                            name: "warehouseCode",
                            rules: [
                                {
                                    required: true,
                                },
                            ],
                        },
                    });
                }
            }

            if (dataOnce.current) return;
            dataOnce.current = true;
            lib.request({
                url: "/tms-portal/api/op/areaGroup",
                success(data) {
                    // console.log("data", data);
                    // data.map((item))
                    depFn(data, item => {
                        item.value = item.areaCode;
                        item.label = item.areaName;
                    });
                    ref.current.setConfigFormItem("addressInfo", {
                        list: data,
                    });
                },
            });
        }
    }, [open]);

    const handOk = () => {
        ref.current.form.validateFields().then(res => {
            // console.log('res', res)
            if (res.addressInfo) {
                res.province = res.addressInfo[0];
                res.city = res.addressInfo[1];
                res.district = res.addressInfo[2];
                delete res.addressInfo;
            }
            const list = ref.current.configs[1].list;
            if (res.warehouseCode && ref.current.configs[1].type === "SELECT") {
                res.warehouseName = list.find(item => item.id === res.warehouseCode).name;
            }
            lib.request({
                url: mode === "add" ? "/danding-wcms/api/warehouse/add" : "/danding-wcms/api/warehouse/update",
                data: {
                    id: row?.id,
                    ...res,
                },
                success(data) {
                    message.success(mode === "add" ? "新增成功" : "编辑成功");
                    load();
                    setOpen(false);
                    ref.current.form.resetFields();
                },
            });
        });
    };

    useEffect(() => {
        if (open) {
            lib.request({
                url: "/ccs/entityWarehouse/listEntityByCustomsBookAuth",
                success(data) {
                    dataRef.current = data || [];
                },
            });
        }
    }, [open]);

    return (
        <>
            <Button
                type={mode === "edit" ? "link" : "primary"}
                onClick={() => {
                    setOpen(!open);
                }}>
                {mode === "add" ? "新增" : "编辑"}
            </Button>
            <Modal
                width={700}
                open={open}
                title={mode === "add" ? "新增" : "编辑"}
                onOk={() => {
                    handOk();
                }}
                onCancel={() => {
                    setOpen(false);
                    ref.current.form.resetFields();
                }}>
                <DTEditForm
                    configs={confgs}
                    layout={{
                        mode: "appoint",
                        colNum: 1,
                    }}
                    // detail={row}
                    beforeMergeForm={fieldValue => {
                        console.log(fieldValue);
                        // fieldValue.
                        if (fieldValue.province && fieldValue.city && fieldValue.district) {
                            fieldValue.addressInfo = [fieldValue.province, fieldValue.city, fieldValue.district];
                        }
                        return { ...fieldValue };
                    }}
                    onChange={(name, value, selects) => {
                        console.log(name, value, selects);
                        if (name === "warehouseCode" && ref.current.configs[1].type === "SELECT") {
                            if (value) {
                                lib.request({
                                    url: "/ccs/entityWarehouse/getWarehouseByErpCode",
                                    data: {
                                        erpWarehouseCode: value,
                                    },
                                    success(data) {
                                        ref.current.form.setFieldValue("wmsWarehouseCode", data);
                                    },
                                });
                            } else {
                                ref.current.form.setFieldValue("wmsWarehouseCode", "");
                            }
                        }
                        if (name === "type") {
                            if (value === 2) {
                                ref.current.setConfigFormItem("warehouseCode", {
                                    type: "INPUT",
                                    fProps: {
                                        label: "实体仓名称",
                                        name: "warehouseCode",
                                        rules: [
                                            {
                                                required: true,
                                                max: 32,
                                                pattern: /^[\u4e00-\u9fa5a-zA-Z0-9]{1,32}$/,
                                                message: "请填写中英文数字，长度不允许超过32位",
                                            },
                                        ],
                                    },
                                });
                                ref.current.form.setFieldValue("wmsWarehouseCode", "");
                                ref.current.form.setFieldValue("warehouseCode", "");
                                ref.current.setConfigFormItem("companyName", {
                                    list: [],
                                });
                                ref.current.form.setFieldValue("companyName", "");
                                lib.request({
                                    url: "/danding-wcms/api/warehouse/companyList",
                                    data: {
                                        type: value,
                                    },
                                    success(data) {
                                        data.map(item => {
                                            item.value = item.id;
                                        });
                                        ref.current.setConfigFormItem("companyName", {
                                            list: data,
                                        });
                                    },
                                });
                            } else {
                                ref.current.setConfigFormItem("warehouseCode", {
                                    type: "SELECT",
                                    fProps: {
                                        label: "实体仓名称",
                                        name: "warehouseCode",
                                        rules: [
                                            {
                                                required: true,
                                            },
                                        ],
                                    },
                                    list: dataRef.current,
                                });
                                // url: "/danding-wcms/api/warehouse/companyList",
                                ref.current.setConfigFormItem("companyName", {
                                    list: [],
                                });
                                lib.request({
                                    url: "/danding-wcms/api/warehouse/companyList",
                                    data: {
                                        type: value,
                                    },
                                    success(data) {
                                        data.map(item => {
                                            item.value = item.id;
                                        });
                                        ref.current.setConfigFormItem("companyName", {
                                            list: data,
                                        });
                                    },
                                });
                                ref.current.form.setFieldValue("companyName", "");
                                ref.current.form.setFieldValue("wmsWarehouseCode", "");
                                ref.current.form.setFieldValue("warehouseCode", "");
                            }
                        }
                    }}
                    ref={ref}
                />
            </Modal>
        </>
    );
};
