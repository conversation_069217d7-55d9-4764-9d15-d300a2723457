import React, { useEffect, useState, useRef } from "react";
import { DTEditForm, DTEditFormConfigs, DTEditFormRefs } from "@dt/components";
import { Button, Modal, message } from "antd";
import { lib } from "react-single-app";

interface GroundModalProps {
    selecteds: number[];
    load: () => void;
}
export default (props: GroundModalProps) => {
    const { selecteds } = props;
    const [open, setOpen] = useState(false);
    const ref = useRef<DTEditFormRefs>({} as DTEditFormRefs);

    const configs: DTEditFormConfigs = [
        {
            type: "RADIO",
            fProps: {
                label: "数据状态",
                name: "status",
                rules: [{ required: true, message: "请选择状态" }],
                labelCol: { span: 6 },
            },
            list: [],
            dataUrl: "/ccs/processTradeBook/listUpdateStatus",
        },
    ];

    const onOk = () => {
        ref.current.form.validateFields().then(values => {
            lib.request({
                url: "/ccs/processTradeBook/updateStatus",
                data: {
                    idList: selecteds.map(item => item.id),
                    ...values,
                },
                success: () => {
                    message.success("修改成功");
                    ref.current.form.resetFields();
                    props.load && props.load();
                    setOpen(false);
                },
            });
        });
    };

    return (
        <>
            <Button
                type="primary"
                onClick={() => {
                    if (selecteds.length === 0) return message.warn("请选择数据");
                    setOpen(true);
                }}>
                修改数据状态
            </Button>
            <Modal
                open={open}
                title={"修改数据状态"}
                onOk={onOk}
                onCancel={() => {
                    setOpen(false);
                }}>
                <DTEditForm
                    ref={ref}
                    configs={configs}
                    layout={{
                        mode: "appoint",
                        colNum: 1,
                    }}
                />
            </Modal>
        </>
    );
};
