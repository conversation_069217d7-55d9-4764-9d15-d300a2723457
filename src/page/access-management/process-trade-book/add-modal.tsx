import React, { useEffect, useState, useRef } from "react";
import { DTEditForm, DTEditFormConfigs, DTEditFormRefs } from "@dt/components";
import { Button, Modal, message } from "antd";
import { lib } from "react-single-app";

interface GroundModalProps {
    // selecteds: number[];
    load: () => void;
}
export default (props: GroundModalProps) => {
    // const { selecteds } = props;
    const [open, setOpen] = useState(false);
    const ref = useRef<DTEditFormRefs>({} as DTEditFormRefs);

    const configs: DTEditFormConfigs = [
        {
            type: "SELECT",
            fProps: {
                label: "加工贸易账册",
                name: "bookId",
                rules: [{ required: true, message: "请选择加工贸易账册" }],
                labelCol: { span: 6 },
            },
            list: [],
            dataUrl: "/ccs/customsBook/listBookWithProcessTrade",
        },
        {
            type: "SELECT",
            fProps: {
                label: "账册类型",
                name: "bookType",
                rules: [{ required: true, message: "请选择账册类型" }],
                labelCol: { span: 6 },
            },
            list: [],
            dataUrl: "/ccs/processTradeBook/listBookType",
            selectShowMode: "together",
        },
    ];

    const onOk = () => {
        ref.current.form.validateFields().then(values => {
            lib.request({
                url: "/ccs/processTradeBook/create",
                data: {
                    ...values,
                },
                success: () => {
                    message.success("新增成功");
                    ref.current.form.resetFields();
                    props.load && props.load();
                    setOpen(false);
                },
            });
        });
    };

    useEffect(() => {
        if (open) {
            ref.current?.resetConfig();
        } else {
            if (ref.current.form) {
                ref.current.form.resetFields();
            }
        }
    }, [open]);

    return (
        <>
            <Button
                type="primary"
                onClick={() => {
                    // if (selecteds.length === 0) return message.error("请选择数据")
                    setOpen(true);
                }}>
                新增
            </Button>
            <Modal
                open={open}
                title={"新增加工贸易"}
                onOk={onOk}
                onCancel={() => {
                    setOpen(false);
                }}>
                <DTEditForm
                    ref={ref}
                    configs={configs}
                    layout={{
                        mode: "appoint",
                        colNum: 1,
                    }}
                />
            </Modal>
        </>
    );
};
