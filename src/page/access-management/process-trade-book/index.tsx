import React, { useState, useRef } from "react";
import { SearchList } from "@dt/components";
import axios from "axios";
//@ts-ignore
import { DDYObject, getConfigDataUtils, hooks, lib } from "react-single-app";
import { Button, Space, message, Modal, Switch } from "antd";
import { SearchListRef } from "@dt/components/esm/business/SearchList/type";
import AddModal from "./add-modal";
import UpdateStatusModal from "./update-status-modal";
// import "./index.less"
export default () => {
    const [selecteds, setSelecteds] = useState<any[]>([]);
    const [detail, setDetail] = useState({});
    const [search, setSearch] = useState<DDYObject>();
    const [updateOpen, setUpdateOpen] = useState(false);
    const [quotationOpen, setQuotationOpen] = useState(false);
    const [buttons] = hooks.useGetAuthButtons({ systemCode: "CCS_ADMIN", pagePath: "/process-trade-book" });
    const tabState = useRef();
    const searchList = useRef<SearchListRef>({} as SearchListRef);
    const getConfig = () => {
        return axios.get(getConfigDataUtils.getDataUrlByDataId(1003)).then(res => res.data.data);
    };

    const batchEnable = (ids, enable) => {
        if (ids.length === 0) {
            return message.warn("请选择数据");
        }
        lib.request({
            url: "/ccs/processTradeBook/enable",
            data: {
                ids: String(ids.map(item => item.id).join(",")),
                enable: enable ? 1 : 0,
            },
            success(data) {
                message.success("操作成功");
                searchList.current.load();
            },
        });
    };

    return (
        <SearchList
            maxLayoutCount={4}
            scrollMode="tableScroll"
            //@ts-ignore
            ref={searchList}
            searchConditionConfig={{
                size: "middle",
            }}
            renderModal={() => <></>}
            tableCustomFun={{
                operateFn: (row, index) => {
                    return <Space></Space>;
                },
                renderSn: (row, index) => {
                    return (
                        <a
                            onClick={() => {
                                lib.openPage(`/process-trade-book-detail?pageTitle=保税加工贸易详情&id=${row.id}`);
                            }}>
                            {row.sn}
                        </a>
                    );
                },
                renderEnable: (row, index) => {
                    return (
                        <a>
                            <Switch
                                checked={row.enable == 1}
                                unCheckedChildren="禁用"
                                checkedChildren="启用"
                                disabled={!buttons.includes("enable-unenable")}
                                onChange={val => {
                                    console.log(val);
                                    lib.request({
                                        url: "/ccs/processTradeBook/enable",
                                        data: {
                                            ids: String(row.id),
                                            enable: val ? 1 : 0,
                                        },
                                        success(data) {
                                            message.success("操作成功");
                                            searchList.current.load();
                                        },
                                    });
                                }}
                            />
                        </a>
                    );
                },
            }}
            renderLeftOperation={() => {
                return (
                    <Space>
                        {buttons.includes("update-data-status") && (
                            <UpdateStatusModal
                                selecteds={selecteds}
                                load={() => {
                                    //@ts-ignore
                                    searchList.current.load();
                                }}
                            />
                        )}
                        {buttons.includes("enable-unenable") && (
                            <>
                                <Button
                                    onClick={() => {
                                        batchEnable(selecteds, false);
                                    }}>
                                    批量禁用
                                </Button>
                                <Button
                                    onClick={() => {
                                        batchEnable(selecteds, true);
                                    }}>
                                    批量启用
                                </Button>
                            </>
                        )}
                    </Space>
                );
            }}
            renderRightOperation={() => {
                return (
                    <Space>
                        <AddModal
                            load={() => {
                                //@ts-ignore
                                searchList.current.load();
                            }}
                        />
                    </Space>
                );
            }}
            onTableSelected={(ids, rows) => {
                setSelecteds([...rows]);
            }}
            tableConfig={{
                // size: 'small',
                rowKey: "id",
            }}
            onSearch={search => {
                setSearch({ ...search });
            }}
            getConfig={getConfig}
        />
    );
};
