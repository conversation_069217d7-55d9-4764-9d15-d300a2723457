import React from "react";
import { lib, SearchList, getConfigDataUtils } from "react-single-app";
import axios from "axios";
import { Button, Space, Tag, Tooltip } from "antd";
import "./css/index.less";
const style = {
    width: "100%",
    whiteSpace: "normal",
    wordBreak: "break-all",
    wordWrap: "break-word",
    maxHeight: "150px",
    overflowY: "auto",
};
class FilingLibrary extends SearchList {
    constructor() {
        super();
        this.state.buttons = [];
    }
    getConfig() {
        let url = getConfigDataUtils.getDataUrlByDataId(572);
        return axios.get(url).then(res => res.data.data);
    }
    // 判断是否是濒危
    getName(row) {
        if (row.isEndangeredFactors === true) {
            return (
                <div style={{ whiteSpace: "pre-wrap" }}>
                    <span style={{ marginRight: 8 }}>{row.goodsRecordName}</span>
                    <Tag color="error">涉濒危</Tag>
                </div>
            );
        } else {
            return <span>{row.goodsRecordName}</span>;
        }
    }
    // 主要成分
    getComposition(row) {
        return (
            <div style={style} className="dome">
                {row.composition}
            </div>
        );
    }
    //备案申报要素
    gethgsbys(row) {
        return (
            <div style={style} className="dome">
                {row.hgsbys}
            </div>
        );
    }
    // 备案口岸
    getCustoms(row) {
        return (
            <div>
                <Tooltip title={row.customs}>
                    <span>{row.customs}</span>
                </Tooltip>
            </div>
        );
    }
    // 涉濒危成分
    getEndangeredFactors(row) {
        return (
            <div>
                <Tooltip title={row.endangeredFactors}>
                    <span>{row.endangeredFactors}</span>
                </Tooltip>
            </div>
        );
    }

    async getButtons(systemCode, pagePath) {
        const data = { systemCode, pagePath };
        const res = await lib.request({
            url: "/ucenter-account/current/lastButtonList",
            data,
        });
        if (res && Array.isArray(res)) {
            const formatBtns = res.map(i => i.code);
            this.setState({
                buttons: formatBtns,
            });
        }
    }

    componentDidMount() {
        this.getButtons("CCS_ADMIN", "/filing-library");
    }

    myOperation(row) {
        return (
            <Space>
                <Button
                    type="link"
                    onClick={() => lib.openPage(`/edit-filing-library?page_title=备案详情&id=${row.id}`)}>
                    查看
                </Button>
                {(this.state.buttons || []).filter(item => item === "GOODS-RECORD-UPDATE").length > 0 ? (
                    <Button
                        type="link"
                        onClick={() => lib.openPage(`/add-filing-library?page_title=备案详情&id=${row.id}`)}>
                        更新
                    </Button>
                ) : null}
            </Space>
        );
    }
}
export default FilingLibrary;
