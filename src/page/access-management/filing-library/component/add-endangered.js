// 新增濒危成分 Modal
import React, { useState, useEffect } from "react";
import { Button, Modal, Form, message, Input, Select } from "antd";
import { lib } from "react-single-app";
function AddEndangered({ load }) {
    const [isModalVisible, setIsModalVisible] = useState(false);
    const [selects, setSelects] = useState([]);
    const [form] = Form.useForm();
    const showModal = () => {
        form.resetFields();
        setIsModalVisible(true);
    };
    const handleOk = async () => {
        const values = await form.validateFields();
        lib.request({
            url: "/ccs/endangeredFactor/save",
            data: values,
            success: res => {
                message.success("新增成功");
                setIsModalVisible(false);
                load();
                form.resetFields();
            },
        });
    };
    const handleCancel = () => {
        setIsModalVisible(false);
    };

    useEffect(() => {
        if (isModalVisible) {
            lib.request({
                url: "/ccs/dictionary/listRecordRuleType",
                success: data => {
                    setSelects(data || []);
                },
            });
        }
    }, [isModalVisible]);
    return (
        <div>
            <Button type="primary" onClick={showModal}>
                新增
            </Button>
            <Modal title="新增" open={isModalVisible} onOk={handleOk} onCancel={handleCancel}>
                <Form
                    form={form}
                    name="basic"
                    labelCol={{
                        span: 6,
                    }}
                    wrapperCol={{
                        span: 16,
                    }}
                    initialValues={{
                        remember: true,
                    }}>
                    <Form.Item label="成分名称" name="endangeredDesc" rules={[{ required: true }]}>
                        <Input placeholder="请输入" />
                    </Form.Item>
                    <Form.Item label="规则类型" name="ruleType" rules={[{ required: true }]}>
                        <Select
                            allowClear={true}
                            // mode={item.mode}
                            style={{ width: "100%" }}
                            showSearch
                            // onSelect={item.onSelect ? e => item.onSelect(e, form) : e => { }}
                            // disabled={item.disabled}
                            optionFilterProp="children">
                            {selects.map((ite, index) => {
                                return (
                                    <Select.Option value={ite.value || ite.id} key={index}>
                                        {ite.name}
                                    </Select.Option>
                                );
                            })}
                        </Select>
                    </Form.Item>
                </Form>
            </Modal>
        </div>
    );
}

export default AddEndangered;
