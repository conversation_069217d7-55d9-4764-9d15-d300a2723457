// 更新备案信息
import React, { useState, useEffect } from "react";
import { message, Form, Input, Button, Typography, Select, Tag } from "antd";

import { CopyOutlined } from "@ant-design/icons";
import "../css/addFilingLibrary.less";
import { lib } from "react-single-app";
const NormalSelect = ({ value, onChange, optionsApi, mode, ...rest }) => {
    const [options, setOptions] = useState([]);
    useEffect(() => {
        lib.request({
            url: optionsApi,
            success: res => {
                const options = res.map(item => {
                    return {
                        value: item.id,
                        label: item.name,
                    };
                });
                setOptions(options);
            },
        });
    }, []);
    return (
        <Select
            optionFilterProp="label"
            mode={mode}
            options={options}
            value={value}
            onChange={onChange}
            {...rest}
            showSearch></Select>
    );
};

function AddFilingLibrary() {
    const [form] = Form.useForm();
    const { TextArea } = Input;
    const type = lib.getParam("type");
    const id = lib.getParam("id");
    const [status, setStatus] = useState();
    useEffect(() => {
        lib.request({
            url: "/ccs/recordBase/detail",
            data: { id: id },
            success: res => {
                setStatus(res.isEndangeredFactors);
                form.setFieldsValue(res);
            },
        });
    }, []);
    // 提交表单
    const handelSubmitClick = async () => {
        const values = await form.validateFields();
        lib.request({
            url: "/ccs/recordBase/edit",
            data: { ...values, id: id },
            success: res => {
                message.success("更新成功");
                lib.closePage();
                lib.openPage(`/filing-library?page_title=备案库`);
            },
        });
    };

    return (
        <div className="addFilingLibrary">
            <Form form={form} scrollToFirstError className="the-goods-record-modals" layout="inline">
                <h2>基础信息</h2>
                <div className="form-library-area">
                    <Form.Item label="备案品名" className="required-icon">
                        <Form.Item
                            name="goodsRecordName"
                            noStyle
                            rules={[{ required: true, message: "请输入备案品名" }]}>
                            <Input maxLength={100} />
                        </Form.Item>
                        {status === true && (
                            <Tag color="error" style={{ marginLeft: "8px" }}>
                                涉濒危
                            </Tag>
                        )}
                    </Form.Item>
                </div>
                <Form.Item label="条码" className="required-icon">
                    <Form.Item name="barCode" noStyle rules={[{ required: true, message: "请输入条码" }]}>
                        <Input disabled={true} />
                    </Form.Item>
                </Form.Item>
                <Form.Item label="品牌">
                    <Form.Item name="brand" noStyle>
                        <Input maxLength={100} />
                    </Form.Item>
                </Form.Item>
                <Form.Item label="涉濒危成分" name="endangeredFactors" labelCol={{ span: 8 }}>
                    <Form.Item name="endangeredFactors" noStyle>
                        <Input disabled={true} />
                    </Form.Item>
                </Form.Item>
                <Form.Item label="备案平台" className="required-icon">
                    <Form.Item
                        name="goodsRecordPlatform"
                        noStyle
                        rules={[{ required: true, message: "请输入备案平台" }]}>
                        <Input maxLength={40} />
                    </Form.Item>
                </Form.Item>
                <Form.Item label="备案口岸" className="required-icon">
                    <Form.Item name="customsList" noStyle rules={[{ required: true, message: "请选择备案口岸" }]}>
                        <NormalSelect optionsApi={"/ccs/customs/listDistrict"} mode="multiple" />
                    </Form.Item>
                </Form.Item>
                <h2>申报信息</h2>
                <Form.Item label="HScode" className="required-icon">
                    <Form.Item name="hsCode" noStyle rules={[{ required: true, message: "请输入HsCode" }]}>
                        {/* <Input maxLength={10} />
                         */}
                        <NormalSelect optionsApi={"/ccs/customs/listHsWithName"} />
                    </Form.Item>
                </Form.Item>
                <Form.Item label="原产国" className="required-icon">
                    <Form.Item name="originCountry" noStyle rules={[{ required: true, message: "请输入原厂国" }]}>
                        <NormalSelect optionsApi={"/ccs/customs/listCountryV2"} />
                    </Form.Item>
                </Form.Item>
                <Form.Item label="规格型号">
                    <Form.Item name="model" noStyle>
                        <Input maxLength={100} />
                    </Form.Item>
                </Form.Item>
                <Form.Item label="第一单位">
                    <Form.Item name="firstUnit" noStyle>
                        <NormalSelect optionsApi={"/ccs/customs/listUomWithName"} />
                    </Form.Item>
                </Form.Item>
                <Form.Item label="第一数量">
                    <Form.Item name="firstUnitAmount" noStyle>
                        <Input maxLength={11} />
                    </Form.Item>
                </Form.Item>
                <Form.Item label="第二单位">
                    <Form.Item name="secondUnit" noStyle>
                        <NormalSelect optionsApi={"/ccs/customs/listUomWithName"} />
                    </Form.Item>
                </Form.Item>
                <Form.Item label="第二数量">
                    <Form.Item name="secondUnitAmount" noStyle>
                        <Input maxLength={11} />
                    </Form.Item>
                </Form.Item>
                <div className="form-library-area">
                    <Form.Item label="主要成分" className="required-icon">
                        <Form.Item name="composition" noStyle rules={[{ required: true, message: "请输入主要成分" }]}>
                            <TextArea
                                rows={4}
                                style={{ width: 600 }}
                                maxLength={1000}
                                autoSize={{
                                    minRows: 3,
                                    maxRows: 5,
                                }}
                            />
                        </Form.Item>
                    </Form.Item>
                </div>
                <div className="form-library-area">
                    <Form.Item label="申报要素" className="required-icon">
                        <Form.Item name="hgsbys" noStyle rules={[{ required: true, message: "请输入申报要素" }]}>
                            <TextArea
                                rows={4}
                                maxLength={1000}
                                style={{ width: 600 }}
                                autoSize={{
                                    minRows: 3,
                                    maxRows: 5,
                                }}
                            />
                        </Form.Item>
                        {type === "edit" && (
                            <Typography.Link>
                                <CopyOutlined onClick={() => copyInput(form.getFieldValue("hgsbys"))} />
                            </Typography.Link>
                        )}
                    </Form.Item>
                </div>
                <div className="form_but_pri">
                    <Button type="primary" onClick={handelSubmitClick} htmlType={"submit"}>
                        提交
                    </Button>
                </div>
            </Form>
        </div>
    );
}

export default AddFilingLibrary;
