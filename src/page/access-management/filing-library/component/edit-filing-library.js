import React, { useState, useEffect } from "react";
import { CopyOutlined } from "@ant-design/icons";
import { Tag, message, Descriptions, Table } from "antd";
import "../css/edit-filing-library.less";
import { lib } from "react-single-app";
import copy from "copy-to-clipboard";
function EditFilingLibrary() {
    const [detail, setDetail] = useState();
    const id = lib.getParam("id");
    useEffect(() => {
        lib.request({
            url: "/ccs/recordBase/detail",
            data: { id: id },
            success: res => {
                setDetail(res);
            },
        });
    }, []);
    // 复制input内容
    const copyInput = value => {
        if (value) {
            copy(value);
            message.success("复制成功");
        } else {
            message.warning("没有可复制内容");
        }
    };
    const columns = [
        {
            title: "操作类型",
            dataIndex: "operateType",
        },
        {
            title: "操作说明",
            dataIndex: "operateContent",
        },
        {
            title: "操作时间",
            dataIndex: "operateTime",
        },
        {
            title: "操作人",
            dataIndex: "operateUser",
        },
    ];
    return (
        <div className="edit-filing-library">
            <Descriptions title="基础信息">
                <Descriptions.Item label="备案品名" span={3}>
                    <div>
                        <span>{detail?.goodsRecordName}</span>
                        {detail?.isEndangeredFactors === true && (
                            <Tag color="error" style={{ marginLeft: 8 }}>
                                涉濒危
                            </Tag>
                        )}
                        <CopyOutlined
                            style={{
                                fontSize: "16px",
                                color: "#1890ff",
                                marginLeft: 8,
                            }}
                            onClick={() => copyInput(detail?.goodsRecordName)}
                        />
                    </div>
                </Descriptions.Item>
                <Descriptions.Item label="条码">
                    <div>
                        <span className="det-top-sapn">{detail?.barCode}</span>
                        <CopyOutlined
                            style={{ fontSize: "16px", color: "#1890ff", marginLeft: 8 }}
                            onClick={() => copyInput(detail?.barCode)}
                        />
                    </div>
                </Descriptions.Item>
                <Descriptions.Item label="品牌">
                    <div>
                        <span className="det-top-sapn">{detail?.brand}</span>
                        <CopyOutlined
                            style={{ fontSize: "16px", color: "#1890ff", marginLeft: 8 }}
                            onClick={() => copyInput(detail?.brand)}
                        />
                    </div>
                </Descriptions.Item>
                <Descriptions.Item label="涉濒危成分">
                    <div>
                        <span className="det-top-sapn">{detail?.endangeredFactors}</span>
                        <CopyOutlined
                            style={{ fontSize: "16px", color: "#1890ff", marginLeft: 8 }}
                            onClick={() => copyInput(detail?.endangeredFactors)}
                        />
                    </div>
                </Descriptions.Item>
                <Descriptions.Item label="备案平台">
                    <div>
                        <span className="det-top-sapn">{detail?.goodsRecordPlatform}</span>
                        <CopyOutlined
                            style={{ fontSize: "16px", color: "#1890ff", marginLeft: 8 }}
                            onClick={() => copyInput(detail?.goodsRecordPlatform)}
                        />
                    </div>
                </Descriptions.Item>
                <Descriptions.Item label="备案口岸">
                    <div>
                        <span className="det-top-sapn">{detail?.customs}</span>
                        <CopyOutlined
                            style={{ fontSize: "16px", color: "#1890ff", marginLeft: 8 }}
                            onClick={() => copyInput(detail?.customs)}
                        />
                    </div>
                </Descriptions.Item>
            </Descriptions>
            <Descriptions title="申报信息">
                <Descriptions.Item label="HScode">
                    <div>
                        <span className="det-top-sapn"> {detail?.hsCodeName}</span>
                        <CopyOutlined
                            style={{ fontSize: "16px", color: "#1890ff", marginLeft: 8 }}
                            onClick={() => copyInput(detail?.hsCodeName)}
                        />
                    </div>
                </Descriptions.Item>
                <Descriptions.Item label="原产国">
                    <div>
                        <span className="det-top-sapn">{detail?.originCountryName}</span>
                        <CopyOutlined
                            style={{ fontSize: "16px", color: "#1890ff", marginLeft: 8 }}
                            onClick={() => copyInput(detail?.originCountryName)}
                        />
                    </div>
                </Descriptions.Item>
                <Descriptions.Item label="规格型号">
                    <div>
                        <span className="det-top-sapn">{detail?.model}</span>
                        <CopyOutlined
                            style={{ fontSize: "16px", color: "#1890ff", marginLeft: 8 }}
                            onClick={() => copyInput(detail?.model)}
                        />
                    </div>
                </Descriptions.Item>
                <Descriptions.Item label="第一单位">
                    <div>
                        <span className="det-top-sapn">{detail?.firstUnitName}</span>
                        <CopyOutlined
                            style={{ fontSize: "16px", color: "#1890ff", marginLeft: 8 }}
                            onClick={() => copyInput(detail?.firstUnitName)}
                        />
                    </div>
                </Descriptions.Item>
                <Descriptions.Item label="第一数量">
                    <div>
                        <span className="det-top-sapn">{detail?.firstUnitAmount}</span>
                        <CopyOutlined
                            style={{ fontSize: "16px", color: "#1890ff", marginLeft: 8 }}
                            onClick={() => copyInput(detail?.firstUnitAmount)}
                        />
                    </div>
                </Descriptions.Item>
                <Descriptions.Item label="第二单位">
                    <div>
                        <span className="det-top-sapn">{detail?.secondUnitName}</span>
                        <CopyOutlined
                            style={{ fontSize: "16px", color: "#1890ff", marginLeft: 8 }}
                            onClick={() => copyInput(detail?.secondUnitName)}
                        />
                    </div>
                </Descriptions.Item>
                <Descriptions.Item label="第二数量" span={3}>
                    <div>
                        <span className="det-top-sapn">{detail?.secondUnitAmount}</span>
                        <CopyOutlined
                            style={{ fontSize: "16px", color: "#1890ff", marginLeft: 8 }}
                            onClick={() => copyInput(detail?.secondUnitAmount)}
                        />
                    </div>
                </Descriptions.Item>
                <Descriptions.Item label="主要成分" span={3}>
                    <div>
                        <span className="det-top-sapn">{detail?.composition}</span>
                        <CopyOutlined
                            style={{ fontSize: "16px", color: "#1890ff", marginLeft: 8 }}
                            onClick={() => copyInput(detail?.composition)}
                        />
                    </div>
                </Descriptions.Item>
                <Descriptions.Item label="申报要素" span={3}>
                    <div>
                        <span className="det-top-sapn">{detail?.hgsbys}</span>
                        <CopyOutlined
                            style={{ fontSize: "16px", color: "#1890ff", marginLeft: 8 }}
                            onClick={() => copyInput(detail?.hgsbys)}
                        />
                    </div>
                </Descriptions.Item>
            </Descriptions>
            <h2>操作日志</h2>
            <div className="det-top-table">
                <Table dataSource={detail?.logList} columns={columns} pagination={false} bordered />
            </div>
        </div>
    );
}

export default EditFilingLibrary;
