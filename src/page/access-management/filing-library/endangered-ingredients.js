//
import React from "react";
import { lib, SearchList, getConfigDataUtils, HOC } from "react-single-app";
import axios from "axios";
import { Button, Space, Switch, Modal, message, Tag } from "antd";
import AddEndangered from "./component/add-endangered";
@HOC.mapAuthButtonsToState({})
class EndangeredIngredients extends SearchList {
    getConfig() {
        let url = getConfigDataUtils.getDataUrlByDataId(573);
        return axios.get(url).then(res => res.data.data);
    }
    // 开关状态
    switchChange(row) {
        console.log(row);
        let modal = Modal.confirm({
            cancelText: "取消",
            okText: "确定",
            content: `确认${row.status === 0 ? "启用" : "禁用"}吗？`,
            onOk: res => {
                lib.request({
                    url: "/ccs/endangeredFactor/enable",
                    method: "POST",
                    needMask: true,
                    data: {
                        id: row.id,
                        status: row.status ? 0 : 1,
                    },
                    success: res => {
                        message.success("修改成功");
                        this.load(true);
                        modal.destroy();
                    },
                });
            },
            onCancel() {
                modal.destroy();
            },
        });
    }
    getStatus(row) {
        return <Switch checked={row.status} onChange={e => this.switchChange(row)} />;
    }
    // 删除
    handelDelete(row) {
        Modal.confirm({
            cancelText: "取消",
            okText: "确定",
            title: "提示",
            content: "是否删除?",
            onOk: () => {
                lib.request({
                    url: `/ccs/endangeredFactor/remove`,
                    data: { id: row.id },
                    needMask: true,
                    success: res => {
                        message.success("删除成功");
                        this.load(true);
                    },
                });
            },
        });
    }
    renderLeftOperation() {
        const { buttons, selectedRows } = this.state;
        let idList = selectedRows.reduce((prev, curr) => [...prev, curr.id], []);
        return (
            <>
                {buttons.includes("batch-open") && (
                    <Button
                        onClick={() => {
                            if (idList.length == 0) {
                                return message.warn("请勾选数据");
                            }
                            Modal.confirm({
                                title: "确认",
                                content: "确定启用吗?",
                                onOk: () => {
                                    lib.request({
                                        url: "/ccs/endangeredFactor/batchEnable",
                                        data: {
                                            ids: idList.join(","),
                                            status: 1,
                                        },
                                        success: () => {
                                            message.success("启用成功");
                                            this.load();
                                        },
                                    });
                                },
                            });
                        }}>
                        批量启用
                    </Button>
                )}
                {buttons.includes("batch-close") && (
                    <Button
                        onClick={() => {
                            if (idList.length == 0) {
                                return message.warn("请勾选数据");
                            }
                            Modal.confirm({
                                title: "确认",
                                content: "确定禁用吗?",
                                onOk: () => {
                                    lib.request({
                                        url: "/ccs/endangeredFactor/batchEnable",
                                        data: {
                                            ids: idList.join(","),
                                            status: 0,
                                        },
                                        success: () => {
                                            message.success("禁用成功");
                                            this.load();
                                        },
                                    });
                                },
                            });
                        }}>
                        批量禁用
                    </Button>
                )}
                {buttons.includes("batch-delete") && (
                    <Button
                        onClick={() => {
                            if (idList.length == 0) {
                                return message.warn("请勾选数据");
                            }
                            Modal.confirm({
                                title: "确认",
                                content: "确定删除吗?",
                                onOk: () => {
                                    lib.request({
                                        url: "/ccs/endangeredFactor/batchRemove",
                                        data: {
                                            ids: idList.join(","),
                                        },
                                        success: () => {
                                            message.success("删除成功");
                                            this.load();
                                        },
                                    });
                                },
                            });
                        }}>
                        批量删除
                    </Button>
                )}
            </>
        );
    }

    renderRightOperation() {
        return (
            <AddEndangered
                load={() => {
                    this.load(true);
                }}
            />
        );
    }
    myOperation(row) {
        return (
            <Space>
                {!row.status && (
                    <Button type="link" onClick={() => this.handelDelete(row)}>
                        删除
                    </Button>
                )}
            </Space>
        );
    }
}
export default EndangeredIngredients;
