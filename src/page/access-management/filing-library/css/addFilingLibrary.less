.addFilingLibrary {
    h2 {
        height: 40px;
        line-height: 40px;
        margin: 16px;
        background: rgba(0, 0, 0, 0.02);
        padding-left: 20px;
        width: calc(100% - 16px);
        font-size: 16px;
    }
    .form_but_pri {
        margin-left: 100px;
    }
    .form-library-area {
        width: 100%;
    }
    .the-goods-record-modals {
        position: relative;
        .ant-modal-body {
            //   height: calc(100vh - 185px);
            overflow: auto;
        }
        .ant-select {
            width: 220px !important;
        }
        .ant-input {
            width: 220px;
        }
        .ant-input-number {
            width: 220px !important;
        }
        .ant-form-item {
            margin-bottom: 25px;
        }
        .required-icon  {
            .ant-form-item-label > label::before  {
                display: inline-block;
                margin-right: 4px;
                color: #ff4d4f;
                font-size: 14px;
                font-family: SimSun, sans-serif;
                line-height: 1;
                content: "*";
            }
        }
        .ant-form-item-label {
            width: 130px;
            text-align: right !important;
            overflow: visible;
        }
    }
}
