import React, { useEffect, useRef, useState } from "react";
import { DTEditForm, DTEditFormConfigs } from "@dt/components";
import { message, Modal } from "antd";
import { DetailStatusCode, detailConfig } from "./configs.tsx";
import Steps from "./components/steps.tsx";
import { DTEditFormConfigsItem, DTEditFormModeType, DTEditFormRefs } from "@dt/components/esm/business/edit-form/type";
import { DDYObject, lib } from "react-single-app";
import moment from "moment";
import CarTable from "./components/car-table.tsx";
import LogTable from "./components/log-table.tsx";
import { depFn } from "../warehouse-address-manage/component/add-modal.tsx";
import ScrollAnchor from "./components/scroll-anchor.tsx";
import SubmitModal from "./components/submit-modal.tsx";

export default () => {
    const ref = useRef<DTEditFormRefs>({} as DTEditFormRefs);
    const [totalMode, setTotalModal] = useState<DTEditFormModeType>("read");
    const [detail, setDetail] = useState<DDYObject>({});

    const [submitOpen, setSubmitOpen] = useState(false);
    const [submitRow, setSubmitRow] = useState<DDYObject>();
    const [submitName, setSubmitName] = useState("");
    const [carTableShow, setCarTableShow] = useState(false);
    const getDetail = () => {
        lib.request({
            url: "/danding-wcms/api/order/detail/ccs",
            data: {
                orderNo: lib.getParam("id"),
            },
            success(data) {
                setDetail(data);
                if (data.state === DetailStatusCode.CREATED) {
                    if (lib.getParam("type") === "edit") {
                        setTotalModal("edit");
                        //@ts-ignore
                        ref.current.configs.addressInfo.moduleMode = "edit";
                        //@ts-ignore
                        ref.current.configs.reciverInfo.moduleMode = "edit";
                        //@ts-ignore
                        ref.current.setConfigModuleItem("addressInfo", ref.current.configs.addressInfo);
                        //@ts-ignore
                        ref.current.setConfigModuleItem("reciverInfo", ref.current.configs.reciverInfo);
                    }
                }
                if (data.entryExitType === 1) {
                    ref.current.setConfigFormItem("originWarehouseType", { editable: true });
                    ref.current.setConfigFormItem("originWarehouseCode", { editable: true });
                    ref.current.setConfigFormItem("originOwnerCode", { editable: true });
                    ref.current.setConfigFormItem("destinationWarehouseType", { editable: false });
                    ref.current.setConfigFormItem("destinationWarehouseCode", { editable: false });
                    ref.current.setConfigFormItem("destinationOwnerCode", { editable: false });
                } else {
                    ref.current.setConfigFormItem("originWarehouseType", { editable: false });
                    ref.current.setConfigFormItem("originWarehouseCode", { editable: false });
                    ref.current.setConfigFormItem("originOwnerCode", { editable: false });
                    ref.current.setConfigFormItem("destinationWarehouseType", { editable: true });
                    ref.current.setConfigFormItem("destinationWarehouseCode", { editable: true });
                    ref.current.setConfigFormItem("destinationOwnerCode", { editable: true });
                }
                // 是否减免
                if (data.quoteWaiver) {
                    ref.current.setConfigFormItem("quoteWaiverExplanation", {
                        fProps: {
                            rules: data.quoteWaiver
                                ? [
                                      {
                                          required: true,
                                          max: 256,
                                          pattern: /^[\u4e00-\u9fa5a-zA-Z0-9]{1,256}$/,
                                          message: "请填写中英文数字，长度不允许超过256位",
                                      },
                                  ]
                                : [
                                      {
                                          max: 256,
                                          pattern: /^[\u4e00-\u9fa5a-zA-Z0-9]{1,256}$/,
                                          message: "请填写中英文数字，长度不允许超过256位",
                                      },
                                  ],
                            label: "减免说明",
                            name: "quoteWaiverExplanation",
                        },
                    });
                }
                if (data.originWarehouseType) {
                    ref.current.setConfigFormItem(
                        "originOwnerCode",
                        data.originWarehouseType === 2
                            ? {
                                  type: "INPUT",
                                  fProps: {
                                      label: "起点仓",
                                      name: "originOwnerCode",
                                      rules: [
                                          {
                                              max: 32,
                                              pattern: /^[\u4e00-\u9fa5a-zA-Z0-9]{1,32}$/,
                                              message: "请填写中英文数字，长度不允许超过32位",
                                          },
                                      ],
                                  },
                              }
                            : {
                                  type: "SELECT",
                                  fProps: {
                                      label: "起点仓货主",
                                      name: "originOwnerCode",
                                  },
                              },
                    );
                    ref.current.setConfigFormItem("originWarehouseCode", {
                        type: data.originWarehouseType === 2 ? "AUTOCOMPLETE" : "SELECT",
                        fProps: {
                            label: "起点仓",
                            name: "originWarehouseCode",
                            rules:
                                data.originWarehouseType === 2
                                    ? [
                                          {
                                              max: 32,
                                              pattern: /^[\u4e00-\u9fa5a-zA-Z0-9]{1,32}$/,
                                              message: "请填写中英文数字，长度不允许超过32位",
                                          },
                                      ]
                                    : [],
                        },
                    });
                    // ref.current.form.setFieldsValue({ originOwnerCode: "" });
                    // ref.current.form.setFieldsValue({ originWarehouseCode: "" });
                }
                if (data.destinationWarehouseType) {
                    ref.current.setConfigFormItem(
                        "destinationOwnerCode",
                        data.destinationWarehouseType === 2
                            ? {
                                  type: "INPUT",
                                  fProps: {
                                      label: "目的仓货主",
                                      name: "destinationOwnerCode",
                                      rules: [
                                          {
                                              max: 32,
                                              pattern: /^[\u4e00-\u9fa5a-zA-Z0-9]{1,32}$/,
                                              message: "请填写中英文数字，长度不允许超过32位",
                                          },
                                      ],
                                  },
                              }
                            : {
                                  type: "SELECT",
                                  fProps: {
                                      label: "目的仓货主",
                                      name: "destinationOwnerCode",
                                  },
                              },
                    );
                    // originWarehouseCode
                    ref.current.setConfigFormItem("destinationWarehouseCode", {
                        type: data.destinationWarehouseType === 2 ? "AUTOCOMPLETE" : "SELECT",
                        fProps: {
                            label: "目的仓",
                            name: "destinationWarehouseCode",
                            rules:
                                data.originWarehouseType === 2
                                    ? [
                                          {
                                              max: 32,
                                              pattern: /^[\u4e00-\u9fa5a-zA-Z0-9]{1,32}$/,
                                              message: "请填写中英文数字，长度不允许超过32位",
                                          },
                                      ]
                                    : [],
                        },
                    });
                    // ref.current.form.setFieldsValue({ destinationOwnerCode: "" });
                    // ref.current.form.setFieldsValue({ originWarehouseCode: "" });
                }
            },
        });
    };
    const configs = detailConfig({
        changeMode: (mode: string, data?: any) => {
            if (mode === "read") {
                setTotalModal("edit");
            }
            if (mode === "edit") {
                update();
            }
            if (mode === "addressInfo") {
                ref.current.form
                    .validateFields(["pickUpCompany", "pickUpRegion", "pickUpAddress", "pickUpPersonList"])
                    .then(res => {
                        setSubmitOpen(true);
                        const originWarehouseCode = ref.current.form.getFieldValue("originWarehouseCode");
                        const originWarehouseType = ref.current.form.getFieldValue("originWarehouseType");
                        //@ts-ignore
                        setSubmitRow({
                            companyName: res.pickUpCompany,
                            address: res.pickUpAddress,
                            region: res.pickUpRegion,
                            name: originWarehouseCode,
                            type: originWarehouseType,
                            contactList: res.pickUpPersonList,
                        });
                        setSubmitName("addressInfo");
                    });
            }

            if (mode === "reciverInfo") {
                ref.current.form
                    .validateFields(["receiverCompany", "receiverRegion", "receiverAddress", "receiverPersonList"])
                    .then(res => {
                        setSubmitOpen(true);
                        setSubmitName("reciverInfo");
                        const destinationWarehouseCode = ref.current.form.getFieldValue("destinationWarehouseCode");
                        const destinationWarehouseType = ref.current.form.getFieldValue("destinationWarehouseType");
                        //@ts-ignore
                        setSubmitRow({
                            companyName: res.receiverCompany,
                            address: res.receiverAddress,
                            region: res.receiverRegion,
                            name: destinationWarehouseCode,
                            type: destinationWarehouseType,
                            contactList: res.receiverPersonList,
                        });
                    });
            }
            if (mode === "submitSubscribe") {
                Modal.confirm({
                    title: "提交约车",
                    content: "确定提交约车吗？",
                    onOk: () => {
                        lib.request({
                            url: "/danding-wcms/api/order/pushToTms/ccs",
                            data: {
                                orderNo: lib.getParam("id"),
                            },
                            success(data) {
                                message.success("提交成功");
                                getDetail();
                            },
                        });
                    },
                });
            }

            if (mode === "carTableShow") {
                setCarTableShow(data);
            }
        },
        ref,
        load: getDetail,
    });

    const update = () => {
        const values = ref.current.form.getFieldsValue();
        ref.current.form.resetFields();
        ref.current.form.setFieldsValue(values);

        ref.current.form
            .validateFields([
                "originWarehouseCode",
                "originWarehouseType",
                "originOwnerCode",
                "destinationWarehouseCode",
                "destinationWarehouseType",
                "destinationOwnerCode",
                "remarks",
                "argVehicleType",
                "argTraySize",
                "argGoodsQuantity",
                "argGoodsVolume",
                "argVehicleRequirements",
                "argInstructions",
                "argUseTime",
                "argPickUpTime",
                "quoted",
                "quoteWaiver",
                "quoteWaiverExplanation",
            ])
            .then(res => {
                setTotalModal(preValue => (preValue === "read" ? "edit" : "read"));
                // console.log("res:", res);
                res.argUseTime = res.argUseTime && moment(res.argUseTime).valueOf();
                res.argPickUpTime = res.argPickUpTime && moment(res.argPickUpTime).valueOf();
                lib.request({
                    url: "/danding-wcms/api/order/submitBasisInfo/ccs",
                    data: {
                        ...res,
                        orderNo: lib.getParam("id"),
                    },
                    success(data) {
                        message.success("保存成功");
                    },
                });
            });
    };

    useEffect(() => {
        // 获取始发地和目的地下拉数据
        if (ref.current) {
            lib.request({
                url: "/tms-portal/api/op/areaGroup",
                success(data) {
                    depFn(data, item => {
                        item.value = item.areaCode;
                        item.label = item.areaName;
                    });
                    ref.current.setConfigFormItem("receiverRegion", {
                        list: data,
                    });
                    ref.current.setConfigFormItem("pickUpRegion", {
                        list: data,
                    });
                },
            });
            // 获取提货和到货公司下拉数据
            lib.request({
                url: "/danding-wcms/api/warehouse/companyList",
                success(data) {
                    data.map(item => {
                        item.value = item.id;
                    });
                    ref.current.setConfigFormItem("pickUpCompany", {
                        list: [...data],
                    });
                    ref.current.setConfigFormItem("receiverCompany", {
                        list: [...data],
                    });
                },
            });
        }

        getDetail();
    }, []);

    const savePosition = (data, name) => {
        const params =
            name === "addressInfo"
                ? {
                      pickUpCompany: data.companyName,
                      pickUpRegion: data.region,
                      pickUpAddress: data.address,
                      pickUpPersonList: data.contactList,
                      warehouseCode: data.name,
                      type: data.type,
                      method: "pickUp",
                  }
                : {
                      receiverCompany: data.companyName,
                      receiverRegion: data.region,
                      receiverAddress: data.address,
                      receiverPersonList: data.contactList,
                      warehouseCode: data.name,
                      type: data.type,
                      method: "receiver",
                  };
        lib.request({
            url: "/danding-wcms/api/warehouse/addOrUpdate",
            data: {
                orderNo: lib.getParam("id"),
                ...params,
            },
            success() {
                message.success("保存成功");
                const moduleConfig = ref.current.configs[name];
                moduleConfig.moduleMode = "read";
                setSubmitOpen(false);
                ref.current.setConfigModuleItem(name, moduleConfig);
            },
        });
    };

    const list = [
        { title: "基本信息", index: 0 },
        { title: "用车信息", index: 3 },
        { title: "提货信息", index: 4 },
        { title: "到货信息", index: 5 },
        { title: "报价信息", index: 6 },
        { title: "车辆信息", index: 7 },
        { title: "轨迹日志", index: 8 },
    ];

    return (
        <>
            <ScrollAnchor list={list} scrollContainerName="ant-form-horizontal" defaultCurrent={0}>
                <DTEditForm
                    ref={ref}
                    totalMode={totalMode}
                    detail={detail}
                    beforeMergeForm={data => {
                        return {
                            ...data,
                            createdTime: data.createdTime && moment(data.createdTime),
                            argUseTime: data.argUseTime && moment(data.argUseTime),
                            argPickUpTime: data.argPickUpTime && moment(data.argPickUpTime),
                        };
                    }}
                    layout={{
                        mode: "appoint",
                        colNum: 3,
                    }}
                    onChange={(name, value, selects, configItem) => {
                        // 是否减免
                        if (name === "quoteWaiver") {
                            ref.current.setConfigFormItem("quoteWaiverExplanation", {
                                fProps: {
                                    rules: value
                                        ? [
                                              {
                                                  required: true,
                                                  max: 256,
                                                  pattern: /^[\u4e00-\u9fa5a-zA-Z0-9]{1,256}$/,
                                                  message: "请填写中英文数字，长度不允许超过256位",
                                              },
                                          ]
                                        : [
                                              {
                                                  max: 256,
                                                  pattern: /^[\u4e00-\u9fa5a-zA-Z0-9]{1,256}$/,
                                                  message: "请填写中英文数字，长度不允许超过256位",
                                              },
                                          ],
                                    label: "减免说明",
                                    name: "quoteWaiverExplanation",
                                },
                            });
                        }
                        if (name === "originWarehouseCode" && configItem.type === "SELECT") {
                            lib.request({
                                url: "/danding-wcms/api/warehouse/detail",
                                data: {
                                    warehouseCode: value,
                                },
                                success(data) {
                                    ref.current.form.setFieldsValue({
                                        pickUpCompany: data.companyName,
                                        pickUpRegion:
                                            data.province && data.city && data.district
                                                ? [data.province, data.city, data.district]
                                                : null,
                                        pickUpAddress: data.address,
                                        pickUpPersonList: data.contactsList,
                                    });
                                },
                            });
                        }
                        if (name === "destinationWarehouseCode" && configItem.type === "SELECT") {
                            lib.request({
                                url: "/danding-wcms/api/warehouse/detail",
                                data: {
                                    warehouseCode: value,
                                },
                                success(data) {
                                    ref.current.form.setFieldsValue({
                                        receiverCompany: data.companyName,
                                        receiverRegion:
                                            data.province && data.city && data.district
                                                ? [data.province, data.city, data.district]
                                                : null,
                                        receiverAddress: data.address,
                                        receiverPersonList: data.contactsList,
                                    });
                                },
                            });
                        }
                        if (name === "originWarehouseType") {
                            ref.current.setConfigFormItem("originOwnerCode", {
                                type: value === 2 ? "INPUT" : "SELECT",
                                fProps: {
                                    label: "起点仓货主",
                                    name: "originOwnerCode",
                                    rules:
                                        value === 2
                                            ? [
                                                  {
                                                      max: 32,
                                                      pattern: /^[\u4e00-\u9fa5a-zA-Z0-9]{1,32}$/,
                                                      message: "请填写中英文数字，长度不允许超过32位",
                                                  },
                                              ]
                                            : [],
                                },
                            });
                            ref.current.setConfigFormItem("originWarehouseCode", {
                                type: value === 2 ? "AUTOCOMPLETE" : "SELECT",
                                fProps: {
                                    label: "起点仓",
                                    name: "originWarehouseCode",
                                    rules:
                                        value === 2
                                            ? [
                                                  {
                                                      max: 32,
                                                      pattern: /^[\u4e00-\u9fa5a-zA-Z0-9]{1,32}$/,
                                                      message: "请填写中英文数字，长度不允许超过32位",
                                                  },
                                              ]
                                            : [],
                                },
                            });
                            ref.current.form.setFieldsValue({ originOwnerCode: "", originWarehouseCode: "" });
                        }
                        if (name === "destinationWarehouseType") {
                            console.log("value:", value);
                            ref.current.setConfigFormItem("destinationOwnerCode", {
                                type: value === 2 ? "INPUT" : "SELECT",
                                fProps: {
                                    label: "目的仓货主",
                                    name: "destinationOwnerCode",
                                    rules:
                                        value === 2
                                            ? [
                                                  {
                                                      max: 32,
                                                      pattern: /^[\u4e00-\u9fa5a-zA-Z0-9]{1,32}$/,
                                                      message: "请填写中英文数字，长度不允许超过32位",
                                                  },
                                              ]
                                            : [],
                                },
                            });
                            // originWarehouseCode
                            ref.current.setConfigFormItem("destinationWarehouseCode", {
                                type: value === 2 ? "AUTOCOMPLETE" : "SELECT",
                                fProps: {
                                    label: "目的仓",
                                    name: "destinationWarehouseCode",
                                    rules:
                                        value === 2
                                            ? [
                                                  {
                                                      max: 32,
                                                      pattern: /^[\u4e00-\u9fa5a-zA-Z0-9]{1,32}$/,
                                                      message: "请填写中英文数字，长度不允许超过32位",
                                                  },
                                              ]
                                            : [],
                                },
                            });
                            ref.current.form.setFieldsValue({ destinationOwnerCode: "", destinationWarehouseCode: "" });
                        }
                    }}
                    configs={configs}>
                    {({ moduleType, colProps, layout, formItemColProps }) => (
                        <>
                            {moduleType === "custom" && <Steps detail={detail} />}
                            {moduleType === "carTableInfo" && (
                                <CarTable data={detail?.truckList || []} load={getDetail} show={carTableShow} />
                            )}
                            {moduleType === "logTableInfo" && <LogTable data={detail?.logList} />}
                        </>
                    )}
                </DTEditForm>
            </ScrollAnchor>
            <SubmitModal
                open={submitOpen}
                row={submitRow}
                closeFn={data => {
                    if (data) {
                        savePosition(data, submitName);
                    }
                    setSubmitRow(null);
                    setSubmitOpen(false);
                }}
            />
        </>
    );
};
