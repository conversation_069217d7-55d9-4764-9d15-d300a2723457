import React from "react";
import { DTEditForm, DTEditFormConfigs } from "@dt/components";
import { copyVal, isEmptyValue } from "@dt/util";
import { Button, Space, Modal, message } from "antd";
import { CopyOutlined, EyeOutlined, EyeInvisibleOutlined } from "@ant-design/icons";
import { lib } from "react-single-app";
import SizeSelect from "./components/size-select";
import MultiPerson from "./components/multi-person";
import NewQuotationModal from "./components/new-quotation-modal";
import ShowBtn from "./components/show-btn";

export enum DetailStatusCode {
    /**
     * 已创建
     */
    CREATED = 10,
    /**
     * 审核中
     */
    AUDITING = 20,
    /**
     * 已报价
     */
    OFFER = 30,
    /**
     * 等待车辆信息
     */
    WAIT = 40,
    /**
     * 约车完成
     */
    FINISHED = 60,
    /**
     * 已取消
     */
    CANCEL = 100,
}

const detailConfig: (props: any) => DTEditFormConfigs = props => {
    const { changeMode, ref, load } = props;
    return {
        mainInfo: {
            title: `YC单号:${lib.getParam("id")}`,
            renderHeadRight: item => (
                <>
                    <CopyOutlined
                        onClick={() => {
                            copyVal(lib.getParam("id"));
                        }}
                        className="margin-small-left"
                        style={{ color: "#585653" }}
                    />
                </>
            ),
            configs: [
                {
                    type: "TEXT",
                    fProps: {
                        label: "用车仓",
                        name: "orderWarehouseName",
                    },
                },
                {
                    type: "TEXT",
                    fProps: {
                        label: "清关单号",
                        name: "ccsInveCustomsSn",
                    },
                    isCopy: true,
                },
                {
                    type: "TEXT",
                    fProps: {
                        label: "仓单号",
                        name: "erpInOutOrderNo",
                    },
                    isCopy: true,
                },
            ],
        },
        custom: {
            title: "",
            configs: [],
            useChildren: true,
        },
        baseInfo: {
            title: "基本信息",
            renderHeadRight: ({ configItem, totalModal, detail }) => (
                <Space>
                    {detail?.state === DetailStatusCode.CREATED && totalModal === "read" && (
                        <Button
                            onClick={() => {
                                props.changeMode("submitSubscribe");
                            }}>
                            提交约车
                        </Button>
                    )}
                    {detail?.state === DetailStatusCode.CREATED && totalModal === "read" && (
                        <Button
                            onClick={() => {
                                props.changeMode(totalModal);
                            }}>
                            编辑
                        </Button>
                    )}
                    {detail?.state === DetailStatusCode.CREATED && totalModal === "edit" && (
                        <Button
                            onClick={() => {
                                props.changeMode(totalModal);
                            }}>
                            保存
                        </Button>
                    )}
                    {detail?.state === DetailStatusCode.OFFER && <NewQuotationModal detail={detail} load={load} />}
                    {[DetailStatusCode.AUDITING, DetailStatusCode.OFFER, DetailStatusCode.WAIT].includes(
                        detail?.state,
                    ) && (
                        <Button
                            onClick={() => {
                                // props.changeMode(totalModal);
                                Modal.confirm({
                                    title: "取消约车",
                                    content: "确定取消约车吗？",
                                    onOk: () => {
                                        lib.request({
                                            url: "/danding-wcms/api/order/cancel/ccs",
                                            data: {
                                                orderNo: lib.getParam("id"),
                                            },
                                            success(data) {
                                                message.success("取消成功");
                                                load();
                                            },
                                        });
                                    },
                                });
                            }}>
                            取消约车
                        </Button>
                    )}
                </Space>
            ),
            configs: [
                {
                    type: "SELECT",
                    fProps: {
                        label: "起点类型",
                        name: "originWarehouseType",
                        rules: [{ required: true, message: "请选择起点类型" }],
                    },
                    list: [
                        { id: 1, name: "代塔仓" },
                        { id: 2, name: "非代塔仓" },
                    ],
                },
                {
                    type: "SELECT",
                    fProps: {
                        label: "起点仓",
                        name: "originWarehouseCode",
                    },
                    list: [],
                    relys: [
                        {
                            from: "originWarehouseType",
                            reqkey: "type",
                        },
                    ],
                    dataUrl: "/danding-wcms/api/warehouse/warehouseCodeList",
                },
                {
                    type: "SELECT",
                    fProps: {
                        label: "起点仓货主",
                        name: "originOwnerCode",
                    },
                    relys: [
                        {
                            from: "originWarehouseCode",
                            reqkey: "entityWarehouseCode",
                        },
                    ],
                    list: [],
                    dataUrl: "/danding-wcms/api/dict/ownerList",
                },
                {
                    type: "SELECT",
                    fProps: {
                        label: "目的类型",
                        name: "destinationWarehouseType",
                        rules: [{ required: true, message: "请选择起点类型" }],
                    },
                    list: [
                        { id: 1, name: "代塔仓" },
                        { id: 2, name: "非代塔仓" },
                    ],
                    // dataUrl: "/danding-wcms/api/dict/tradeTypeList",
                    editable: true,
                },
                {
                    type: "SELECT",
                    fProps: {
                        label: "目的仓",
                        name: "destinationWarehouseCode",
                    },
                    list: [],
                    relys: [
                        {
                            from: "destinationWarehouseType",
                            reqkey: "type",
                        },
                    ],
                    dataUrl: "/danding-wcms/api/warehouse/warehouseCodeList",
                    editable: true,
                },
                {
                    type: "SELECT",
                    fProps: {
                        label: "目的仓货主",
                        name: "destinationOwnerCode",
                    },
                    relys: [
                        {
                            from: "destinationWarehouseCode",
                            reqkey: "entityWarehouseCode",
                        },
                    ],
                    list: [],
                    dataUrl: "/danding-wcms/api/dict/ownerList",
                    editable: true,
                },
                {
                    type: "TEXT",
                    fProps: {
                        label: "业务类型",
                        name: "ccsInveBusinessTypeDesc",
                    },
                },
                {
                    type: "TEXT",
                    fProps: {
                        label: "出入类型",
                        name: "entryExitTypeDesc",
                    },
                },
                {
                    type: "DATE",
                    fProps: {
                        label: "创建时间",
                        name: "createdTime",
                    },
                    showTextFormat: "YYYY-MM-DD HH:mm:ss",
                },
                {
                    type: "TEXT",
                    fProps: {
                        label: "提单人",
                        name: "createdBy",
                    },
                },
                {
                    type: "INPUT",
                    fProps: {
                        label: "备注",
                        name: "remarks",
                        labelCol: { span: 4 },
                        rules: [
                            {
                                max: 256,
                                pattern: /^[\u4e00-\u9fa5a-zA-Z0-9]{1,256}$/,
                                message: "请填写中英文数字，长度不允许超过256位",
                            },
                        ],
                    },
                    editable: true,
                    colSpan: 16,
                },
            ],
        },
        carInfo: {
            title: "用车信息",
            configs: [
                {
                    type: "RADIO",
                    fProps: {
                        label: "用车类型",
                        name: "argVehicleType",
                        rules: [{ required: true, message: "请选择用车类型" }],
                    },
                    list: [],
                    editable: true,
                    dataUrl: "/danding-wcms/api/truck/argVehicleTypeList",
                },
                {
                    type: "CUSTOM",
                    fProps: {
                        label: "托盘尺寸(m)",
                        name: "argTraySize",
                        rules: [
                            { required: true },
                            {
                                validator(rule, value, callback) {
                                    if (!value) {
                                        return Promise.resolve("");
                                    }
                                    const arr = value.split("*");
                                    if (!arr[0]) {
                                        return Promise.reject("请输入托盘长度");
                                    } else if (!/^\d{1,5}(\.\d{0,2})?$/.test(arr[0]) || arr[0] <= 0) {
                                        return Promise.reject("托盘长度最多5位整数，2位小数");
                                    }
                                    if (!arr[1]) {
                                        return Promise.reject("请输入托盘宽度");
                                    } else if (!/^\d{1,5}(\.\d{0,2})?$/.test(arr[1]) || arr[1] <= 0) {
                                        return Promise.reject("托盘宽度最多5位整数，2位小数");
                                    }
                                    if (!arr[2]) {
                                        return Promise.reject("请输入托盘高度");
                                    } else if (!/^\d{1,5}(\.\d{0,2})?$/.test(arr[2]) || arr[2] <= 0) {
                                        return Promise.reject("托盘高度最多5位小数，2位正整数");
                                    }
                                    return Promise.resolve();
                                },
                            },
                        ],
                        required: true,
                    },
                    render: SizeSelect,
                    editable: true,
                },
                {
                    type: "INPUT",
                    fProps: {
                        label: "货品托数",
                        name: "argGoodsQuantity",
                        rules: [
                            { required: true },
                            {
                                pattern: /^\d{1,2}$/,
                                message: "仅支持整数，不超过2位字符",
                            },
                        ],
                    },
                    editable: true,
                },
                {
                    type: "INPUT",
                    fProps: {
                        label: "货品体积(m³)",
                        name: "argGoodsVolume",
                        rules: [
                            { required: true },
                            {
                                validator(rule, value, callback) {
                                    if (!value) return Promise.resolve("");
                                    if (!/^\d{1,5}(\.\d{0,2})?$/.test(value)) {
                                        return Promise.reject("货品体积最多5位整数，2位小数");
                                    }
                                    return Promise.resolve("");
                                },
                            },
                        ],
                        tooltip: "计算规则: 单件或单托长*宽*高*件数或托数",
                    },
                    editable: true,
                },
                {
                    type: "DATE",
                    fProps: {
                        label: "用车时间",
                        name: "argUseTime",
                        rules: [{ required: true }],
                    },
                    editable: true,
                    showTextFormat: "YYYY-MM-DD HH:mm:ss",
                },
                {
                    type: "DATE",
                    fProps: {
                        label: "提货时间",
                        name: "argPickUpTime",
                        rules: [{ required: true }],
                    },
                    editable: true,
                    showTextFormat: "YYYY-MM-DD HH:mm:ss",
                },
                {
                    type: "INPUT",
                    fProps: {
                        label: "车辆要求",
                        name: "argVehicleRequirements",
                        rules: [
                            {
                                required: true,
                                max: 256,
                                pattern: /^[\u4e00-\u9fa5a-zA-Z0-9]{1,256}$/,
                                message: "请填写中英文数字，长度不允许超过256位",
                            },
                        ],
                    },
                    editable: true,
                },
                {
                    type: "INPUT",
                    fProps: {
                        label: "用车说明",
                        name: "argInstructions",
                        rules: [
                            {
                                required: true,
                                max: 256,
                                pattern: /^[\u4e00-\u9fa5a-zA-Z0-9]{1,256}$/,
                                message: "请填写中英文数字，长度不允许超过256位",
                            },
                        ],

                        labelCol: { span: 4 },
                    },
                    editable: true,
                    colSpan: 16,
                    textMaxWidth: 400,
                },
            ],
        },
        addressInfo: {
            title: "提货信息",
            renderHeadRight: ({ configItem, totalModal, detail }) => {
                if (detail?.state !== DetailStatusCode.CREATED) return null;
                return (
                    <>
                        <Button
                            onClick={() => {
                                if (configItem.moduleMode === "edit") {
                                    props.changeMode("addressInfo", configItem);
                                } else {
                                    configItem.moduleMode = "edit";
                                    ref.current.setConfigModuleItem("addressInfo", configItem);
                                }
                            }}>
                            {configItem.moduleMode === "edit" ? "保存仓址" : "编辑"}
                        </Button>
                    </>
                );
            },
            moduleMode: "read",
            configs: [
                {
                    type: "INPUT",
                    fProps: {
                        label: "提货公司",
                        name: "pickUpCompany",
                        rules: [
                            {
                                required: true,
                                max: 128,
                                pattern: /^[\u4e00-\u9fa5a-zA-Z]{1,128}$/,
                                message: "请填写中英文，长度不允许超过128位",
                            },
                        ],
                    },
                    editable: true,
                    list: [],
                },
                {
                    type: "CASCADER",
                    fProps: {
                        label: "始发地",
                        name: "pickUpRegion",
                        rules: [{ required: true, message: "请选择始发地" }],
                    },
                    list: [],
                    editable: true,
                },
                {
                    type: "INPUT",
                    fProps: {
                        label: "提货地址",
                        name: "pickUpAddress",
                        rules: [
                            {
                                required: true,
                                max: 128,
                                pattern: /^[\u4e00-\u9fa5a-zA-Z0-9]{1,128}$/,
                                message: "请填写中英文数字，长度不允许超过128位",
                            },
                        ],
                    },
                    editable: true,
                },
                {
                    type: "CUSTOM",
                    fProps: {
                        label: "提货人",
                        name: "pickUpPersonList",
                        rules: [
                            { required: true },
                            {
                                validator(rule, value, callback) {
                                    if (!value) return Promise.resolve("");
                                    for (let i = 0; i < value.length; i++) {
                                        if (!value[i].name || !value[i].phone) {
                                            return Promise.reject(`第${i + 1}项请填写完整联系人信息`);
                                        } else {
                                            if (!/^[\u4e00-\u9fa5a-zA-Z]{0,10}$/.test(value[i].name)) {
                                                return Promise.reject(
                                                    `第${i + 1}项姓名请填写中英文，长度不允许超过10位`,
                                                );
                                            }
                                            if (!/^1[0-9]\d{9}$|^0\d{2,3}-\d{7,8}$/.test(value[i].phone)) {
                                                return Promise.reject(
                                                    `第${i + 1}项请填写12位以下座机或者11位的手机号码`,
                                                );
                                            }
                                        }
                                    }
                                    const arr = value.map(item => item.name + item.phone);
                                    const arr1 = Array.from(new Set(arr));
                                    if (arr.length !== arr1.length) return Promise.reject("存在重复数据");
                                    return Promise.resolve("");
                                },
                            },
                        ],
                        labelCol: { span: 4 },
                    },
                    render: MultiPerson,
                    colSpan: 16,
                    editable: true,
                    transferText(value?: any[]) {
                        if (!value) return "";
                        return value
                            .map(item => {
                                return item.name + "-" + item.phone;
                            })
                            .join("/");
                    },
                    textMaxWidth: 400,
                },
            ],
        },
        reciverInfo: {
            title: "到货信息",
            renderHeadRight: ({ configItem, totalModal, detail }) => {
                if (detail?.state !== DetailStatusCode.CREATED) return null;
                return (
                    <>
                        <Button
                            onClick={() => {
                                if (configItem.moduleMode === "edit") {
                                    props.changeMode("reciverInfo", configItem);
                                } else {
                                    configItem.moduleMode = "edit";
                                    ref.current.setConfigModuleItem("reciverInfo", configItem);
                                }
                            }}>
                            {configItem.moduleMode === "edit" ? "保存仓址" : "编辑"}
                        </Button>
                    </>
                );
            },
            moduleMode: "read",
            configs: [
                {
                    type: "INPUT",
                    fProps: {
                        label: "到货公司",
                        name: "receiverCompany",
                        rules: [
                            {
                                required: true,
                                max: 128,
                                pattern: /^[\u4e00-\u9fa5a-zA-Z]{1,128}$/,
                                message: "请填写中英文，长度不允许超过128位",
                            },
                        ],
                    },
                    editable: true,
                },
                {
                    type: "CASCADER",
                    fProps: {
                        label: "目的地",
                        name: "receiverRegion",
                        rules: [{ required: true }],
                    },
                    list: [],
                    editable: true,
                },
                {
                    type: "INPUT",
                    fProps: {
                        label: "到货地址",
                        name: "receiverAddress",
                        rules: [
                            {
                                required: true,
                                max: 128,
                                pattern: /^[\u4e00-\u9fa5a-zA-Z0-9]{1,128}$/,
                                message: "请填写中英文数字，长度不允许超过128位",
                            },
                        ],
                    },
                    editable: true,
                },
                {
                    type: "CUSTOM",
                    fProps: {
                        label: "收货人",
                        name: "receiverPersonList",
                        rules: [
                            { required: true },
                            {
                                validator(rule, value, callback) {
                                    if (!value) return Promise.resolve("");
                                    for (let i = 0; i < value.length; i++) {
                                        if (!value[i].name || !value[i].phone) {
                                            return Promise.reject(`第${i + 1}项请填写完整联系人信息`);
                                        } else {
                                            if (!/^[\u4e00-\u9fa5a-zA-Z]{0,10}$/.test(value[i].name)) {
                                                return Promise.reject(
                                                    `第${i + 1}项姓名请填写中英文，长度不允许超过10位`,
                                                );
                                            }
                                            if (!/^1[0-9]\d{9}$|^0\d{2,3}-\d{7,8}$/.test(value[i].phone)) {
                                                return Promise.reject(
                                                    `第${i + 1}项请填写12位以下座机或者11位的手机号码`,
                                                );
                                            }
                                        }
                                    }
                                    const arr = value.map(item => item.name + item.phone);
                                    const arr1 = Array.from(new Set(arr));
                                    if (arr.length !== arr1.length) return Promise.reject("存在重复数据");
                                    return Promise.resolve("");
                                },
                            },
                        ],
                        labelCol: { span: 4 },
                    },
                    render: MultiPerson,
                    colSpan: 16,
                    editable: true,
                    transferText(value?: any[]) {
                        if (!value) return "";
                        return value
                            .map(item => {
                                return item.name + "-" + item.phone;
                            })
                            .join("/");
                    },
                    textMaxWidth: 400,
                },
            ],
        },
        priceInfo: {
            title: "报价信息",
            configs: [
                {
                    type: "SELECT",
                    fProps: {
                        label: "是否报价",
                        name: "quoted",
                        rules: [{ required: true }],
                    },
                    list: [
                        { id: true, name: "是" },
                        { id: false, name: "否" },
                    ],
                    editable: true,
                },
                {
                    type: "SELECT",
                    fProps: {
                        label: "是否减免",
                        name: "quoteWaiver",
                    },
                    list: [
                        { id: true, name: "是" },
                        { id: false, name: "否" },
                    ],
                    editable: true,
                },
                {
                    type: "INPUT",
                    fProps: {
                        label: "减免说明",
                        name: "quoteWaiverExplanation",
                    },
                    editable: true,
                },
                {
                    type: "INPUT",
                    fProps: {
                        label: "车辆来源",
                        name: "quoteTruckSourceDesc",
                    },
                },
                {
                    type: "INPUT",
                    fProps: {
                        label: "车型",
                        name: "quoteTruckModel",
                    },
                },
                {
                    type: "INPUT",
                    fProps: {
                        label: "报价(元)",
                        name: "quoteContractQuote",
                    },
                },
                {
                    type: "INPUT",
                    fProps: {
                        label: "报价备注",
                        name: "quoteRemarks",
                    },
                },
            ],
        },
        carTableInfo: {
            title: "车辆信息",
            useChildren: true,
            configs: [],
            renderHeadRight: ({ detail }) => {
                return (
                    <Space>
                        <ShowBtn
                            tab={(show: boolean) => {
                                props.changeMode("carTableShow", show);
                            }}
                        />
                        <Button
                            onClick={() => {
                                if (detail?.truckList.length === 0) {
                                    return message.error("下载失败，未收到证件");
                                }
                                lib.request({
                                    url: "/danding-wcms/api/order/truck/document/export",
                                    data: {
                                        funcCode: "ORDER_TRUCK_INFO",
                                        param: {
                                            orderNo: lib.getParam("id"),
                                        },
                                    },
                                    success: () => {
                                        //todo
                                        message.success("下载成功");
                                        lib.openPage("/download-center?page_title=下载中心");
                                    },
                                });
                            }}>
                            下载
                        </Button>
                    </Space>
                );
            },
        },
        logTableInfo: {
            title: "轨迹日志",
            useChildren: true,
            configs: [],
        },
    };
};

export { detailConfig };
