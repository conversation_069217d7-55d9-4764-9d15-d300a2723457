import React, { useEffect, useRef } from "react";
import { DTEditForm, DTEditFormRefs, DTEditFormConfigs } from "@dt/components";
import { Modal } from "antd";
import { depFn } from "../../warehouse-address-manage/component/add-modal";
import { lib } from "react-single-app";
export default ({ open, closeFn, row }) => {
    const configs: DTEditFormConfigs = [
        {
            type: "SELECT",
            fProps: {
                label: "仓库类型",
                name: "type",
            },
            list: [
                { id: 1, name: "代塔仓" },
                { id: 2, name: "非代塔仓" },
            ],
        },
        {
            type: "SELECT",
            fProps: {
                label: "仓库名称",
                name: "name",
            },
            list: [],
            relys: [
                {
                    from: "type",
                },
            ],
            dataUrl: "/danding-wcms/api/warehouse/warehouseCodeList",
        },
        {
            type: "INPUT",
            fProps: {
                label: "所属企业",
                name: "companyName",
            },
        },
        {
            type: "CASCADER",
            fProps: {
                label: "省市区",
                name: "region",
            },
            list: [],
        },
        {
            type: "INPUT",
            fProps: {
                label: "地址",
                name: "address",
            },
        },
        {
            type: "CUSTOM",
            fProps: {
                label: "联系人",
                name: "contactList",
            },
            render: () => {
                return null;
            },
            renderRead: ({ value }) => {
                return (
                    <div style={{ marginTop: "5px" }}>
                        {(value || []).map(item => {
                            return (
                                <p>
                                    {item.name}-{item.phone}
                                </p>
                            );
                        })}
                    </div>
                );
            },
        },
    ];
    const ref = useRef<DTEditFormRefs>();
    useEffect(() => {
        if (row) {
            console.log("row:", row);
            // const str = row.contactList.map(item => item.name + "-" + item.phone).join(" ");
            // ref.current.form.setFieldsValue({ ...row, contactList: str })
            ref.current.setDetail({ ...row });
        }
    }, [row]);
    useEffect(() => {
        if (open) {
            lib.request({
                url: "/tms-portal/api/op/areaGroup",
                success(data) {
                    depFn(data, item => {
                        item.value = item.areaCode;
                        item.label = item.areaName;
                    });
                    ref.current.setConfigFormItem("region", {
                        list: data,
                    });
                },
            });
        }
    }, [open]);
    return (
        <Modal
            open={open}
            title={"保存仓址"}
            onCancel={() => {
                ref.current.form.resetFields();
                closeFn();
            }}
            onOk={() => {
                closeFn(row);
            }}>
            <p>请确认以下信息(便于复用)：</p>
            <p>
                仅支持保存非代塔仓收发货人地址，若已存在点击保存后系统自动更新，代塔仓可按照路径准入管理
                {"->"}仓址管理，提前配置
            </p>
            <DTEditForm
                configs={configs}
                layout={{
                    mode: "appoint",
                    colNum: 1,
                }}
                totalMode="read"
                ref={ref}
            />
        </Modal>
    );
};
