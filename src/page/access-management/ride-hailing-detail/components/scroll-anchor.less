.scroll-anchor-container {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: row;
    .sa-left {
        width: 100px;
        min-width: 100px;
        height: 100%;
        .sa-left-item {
            height: 40px;
            font-size: 14px;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            margin-bottom: 4px;
            padding-left: 20px;
            line-height: 40px;
            width: 100%;
            cursor: pointer;
            position: relative;
        }
        .active {
            width: 100%;
            height: 40px;
            background: #f0f6ff;
            color: #0268ff;
            position: relative;
        }
        .active::before {
            content: "";
            position: absolute;
            left: 0;
            width: 4px;
            height: 40px;
            background: #0268ff;
            border-radius: 0px 100px 100px 0px;
        }
    }
    .sa-right {
        // width: calc(100%-100px);
        height: 100%;
        // padding-bottom: 64px;
        overflow: hidden;
        box-sizing: border-box;
        border-left: 2px solid #eee;
    }
    .sa-scroll {
        overflow-y: scroll;
        height: 100%;
    }
}
