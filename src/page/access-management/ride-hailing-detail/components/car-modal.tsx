import React, { useEffect, useRef, useState } from "react";
import { Modal, Space, Select, Input, message } from "antd";
import { DTEditForm, DTEditFormRefs, DTEditFormConfigs } from "@dt/components";
import { PlusOutlined } from "@ant-design/icons";
import { lib } from "react-single-app";
const { Option } = Select;
export default ({ mode, open, onClose, row }) => {
    const ref = useRef<DTEditFormRefs>({} as DTEditFormRefs);
    const confgs = [
        {
            type: "SELECT",
            fProps: {
                label: "车牌号",
                name: "truckLicensePlate",
                required: true,
                rules: [{ required: true }],
            },
            list: [],
            dataUrl: "/danding-wcms/api/truck/licensePlateList",
            editable: true,
        },
        {
            type: "INPUT",
            fProps: {
                label: "空车皮重(kg)",
                name: "truckEmptyWeight",
                rules: [{ required: true }],
            },
            editable: true,
        },
        {
            type: "FILE",
            colSpan: 8,
            fProps: {
                label: "行驶证",
                name: "truckVehicleRegistrationImage",
                labelCol: { span: 12 },
                wrapperCol: { span: 12 },
                extra: "支持png、jpg、jpeg格式，最大支持２M",
                rules: [{ required: true }],
            },
            editable: true,
            cProps: {
                maxCount: 1,
                uploadButton: (
                    <div>
                        <PlusOutlined />
                        <div style={{ marginTop: 8, color: "rgba(0, 0, 0, 0.65)" }}>行驶证正面</div>
                    </div>
                ),
            },
        },
        {
            type: "FILE",
            colSpan: 4,
            fProps: {
                label: "",
                name: "truckVehicleRegistrationImage2",
                labelCol: { span: 0 },
                wrapperCol: { span: 24 },
                rules: [{ required: true, message: "请上传行驶证反面" }],
            },
            editable: true,
            cProps: {
                maxCount: 1,
                uploadButton: (
                    <div>
                        <PlusOutlined />
                        <div style={{ marginTop: 8, color: "rgba(0, 0, 0, 0.65)" }}>行驶证反面</div>
                    </div>
                ),
            },
        },
        {
            type: "FILE",
            colSpan: 12,
            fProps: {
                label: "过磅单",
                name: "truckWeighingListImage",
                extra: "支持png、jpg、jpeg格式，最大支持２M",
                rules: [{ required: true, message: "请上传过磅单" }],
            },
            cProps: {
                listType: "text",
            },
            editable: true,
        },
        {
            type: "CUSTOM",
            colSpan: 24,
            fProps: {
                label: "司机信息",
                name: "driverInfo",
                required: true,
                rules: [{ required: true }],
                labelCol: { span: 4 },
            },
            render: DriversInput,
            editable: true,
            renderRead: ({ value }) => <DriversInput value={value} mode={"read"} />,
        },
        {
            type: "FILE",
            colSpan: 8,
            fProps: {
                label: "驾驶证",
                name: "drivingLicenseImage",
                labelCol: { span: 12 },
                wrapperCol: { span: 12 },
                extra: "支持png、jpg、jpeg格式，最大支持２M",
                rules: [{ required: true }],
            },
            editable: true,
            cProps: {
                maxCount: 1,
                uploadButton: (
                    <div>
                        <PlusOutlined />
                        <div style={{ marginTop: 8, color: "rgba(0, 0, 0, 0.65)" }}>驾驶证正面</div>
                    </div>
                ),
            },
        },
        {
            type: "FILE",
            colSpan: 4,
            fProps: {
                label: "",
                name: "drivingLicenseImage2",
                labelCol: { span: 0 },
                wrapperCol: { span: 24 },
                rules: [{ required: true, message: "请上传驾驶证反面" }],
            },
            editable: true,
            cProps: {
                maxCount: 1,
                uploadButton: (
                    <div>
                        <PlusOutlined />
                        <div style={{ marginTop: 8, color: "rgba(0, 0, 0, 0.65)" }}>驾驶证反面</div>
                    </div>
                ),
            },
        },
        {
            type: "FILE",
            colSpan: 8,
            fProps: {
                label: "身份证",
                name: "driverIdCardFrontImage",
                labelCol: { span: 12 },
                wrapperCol: { span: 12 },
                extra: "支持png、jpg、jpeg格式，最大支持２M",
            },
            editable: true,
            cProps: {
                maxCount: 1,
                uploadButton: (
                    <div>
                        <PlusOutlined />
                        <div style={{ marginTop: 8, color: "rgba(0, 0, 0, 0.65)" }}>身份证正面</div>
                    </div>
                ),
            },
        },
        {
            type: "FILE",
            colSpan: 4,
            fProps: {
                label: "",
                name: "driverIdCardFrontImage2",
                labelCol: { span: 0 },
                wrapperCol: { span: 24 },
            },
            editable: true,
            cProps: {
                maxCount: 1,
                uploadButton: (
                    <div>
                        <PlusOutlined />
                        <div style={{ marginTop: 8, color: "rgba(0, 0, 0, 0.65)" }}>身份证反面</div>
                    </div>
                ),
            },
        },
    ];
    useEffect(() => {}, [row]);
    return (
        <Modal
            title={"车辆信息"}
            open={open}
            onCancel={onClose}
            width={850}
            onOk={() => {
                if (mode === "edit") {
                    ref.current.form.validateFields().then(res => {
                        console.log("res:", res);
                        res.driverId = res.driverInfo?.driverId;
                        res.driverPhone = res.driverInfo?.driverPhone;
                        res.driverIdNumber = res.driverInfo?.driverIdNumber;
                        delete res.driverInfo;
                        res.truckVehicleRegistrationImage = res.truckVehicleRegistrationImage?.[0].url;
                        res.truckVehicleRegistrationImage2 = res.truckVehicleRegistrationImage2?.[0].url;
                        res.truckWeighingListImage = res.truckWeighingListImage?.[0].url;
                        res.drivingLicenseImage = res.drivingLicenseImage?.[0].url;
                        res.drivingLicenseImage2 = res.drivingLicenseImage2?.[0].url;
                        res.driverIdCardFrontImage = res.driverIdCardFrontImage?.[0].url;
                        res.driverIdCardFrontImage2 = res.driverIdCardFrontImage2?.[0].url;
                        lib.request({
                            url: "/danding-wcms/api/order/submitTruckInfo/tms",
                            data: {
                                orderSn: lib.getParam("id"),
                                ...res,
                            },
                            success(data) {
                                message.success("新增成功");
                                onClose(true);
                            },
                        });
                    });
                } else {
                    onClose();
                }
            }}>
            <DTEditForm
                ref={ref}
                totalMode={mode}
                detail={row}
                configs={confgs as DTEditFormConfigs}
                beforeMergeForm={data => {
                    return {
                        ...data,
                        truckVehicleRegistrationImage: data.truckVehicleRegistrationImage
                            ? [{ url: data.truckVehicleRegistrationImage }]
                            : [],
                        truckVehicleRegistrationImage2: data.truckVehicleRegistrationImage2
                            ? [{ url: data.truckVehicleRegistrationImage2 }]
                            : [],
                        truckWeighingListImage: data.truckWeighingListImage
                            ? [{ url: data.truckWeighingListImage, name: data.truckLicensePlate + "_过磅单" }]
                            : [],
                        drivingLicenseImage: data.drivingLicenseImage ? [{ url: data.drivingLicenseImage }] : [],
                        drivingLicenseImage2: data.drivingLicenseImage2 ? [{ url: data.drivingLicenseImage2 }] : [],
                        driverIdCardFrontImage: data.driverIdCardFrontImage
                            ? [{ url: data.driverIdCardFrontImage }]
                            : [],
                        driverIdCardFrontImage2: data.driverIdCardFrontImage2
                            ? [{ url: data.driverIdCardFrontImage2 }]
                            : [],
                        driverInfo: {
                            driverId: data.driverId,
                            driverName: data.driverName,
                            driverPhone: data.driverPhone,
                            driverIdNumber: data.driverIdNumber,
                        },
                    };
                }}
                layout={{
                    mode: "appoint",
                    colNum: 2,
                }}
            />
        </Modal>
    );
};
const DriversInput = ({ value, onChange = value => {}, mode }) => {
    const [list, setList] = useState([]);
    const [driverPhone, setDriverPhone] = useState("");
    const [driverIdNumber, setDriverIdNumber] = useState("");
    useEffect(() => {
        lib.request({
            url: "/danding-wcms/api/driver/driverNameList",
            success(data) {
                setList(data);
            },
        });
    }, []);
    useEffect(() => {
        setDriverIdNumber(value?.driverIdNumber);
        setDriverPhone(value?.driverPhone);
    }, [value]);
    return (
        <Space>
            {mode === "read" ? (
                <>
                    {value?.driverName}-{value?.driverPhone}-{value?.driverIdNumber}
                </>
            ) : (
                <>
                    <Select
                        allowClear={true}
                        style={{ width: 100 }}
                        showSearch={true}
                        value={value?.driverId}
                        onChange={val => {
                            onChange({
                                driverId: val,
                                driverPhone: driverPhone,
                                driverIdNumber: driverIdNumber,
                            });
                        }}
                        filterOption={(input, option) =>
                            (option.children as unknown as string)?.toLowerCase().indexOf(input.toLowerCase()) >= 0
                        }>
                        {list.map((ite, index) => {
                            return (
                                <Option value={ite.id} key={index} data={ite}>
                                    {ite.id + "-" + ite.name}
                                </Option>
                            );
                        })}
                    </Select>
                    -
                    <Input
                        style={{ width: 150 }}
                        value={driverPhone}
                        onChange={e => {
                            setDriverPhone(e.target.value);
                        }}
                        onBlur={() => {
                            onChange({
                                driverId: value?.driverId,
                                driverPhone: driverPhone,
                                driverIdNumber: driverIdNumber,
                            });
                        }}
                    />
                    -
                    <Input
                        style={{ width: 250 }}
                        value={driverIdNumber}
                        onChange={e => {
                            setDriverIdNumber(e.target.value);
                        }}
                        onBlur={() => {
                            onChange({
                                driverId: value?.driverId,
                                driverPhone: driverPhone,
                                driverIdNumber: driverIdNumber,
                            });
                        }}
                    />
                </>
            )}
        </Space>
    );
};
