import React from "react";
import { Input, Space } from "antd";

export default (props: { value?: string; onChange?: (value: any) => void }) => {
    const { value, onChange } = props;
    return (
        <>
            <Space>
                <Input
                    placeholder="长(m)"
                    value={value?.split("*")[0] || ""}
                    onChange={e => {
                        onChange(
                            (e.target.value || "") +
                                "*" +
                                (value?.split("*")[1] || "") +
                                "*" +
                                (value?.split("*")[2] || ""),
                        );
                    }}
                />
                ~
                <Input
                    placeholder="宽(m)"
                    value={value?.split("*")[1] || ""}
                    onChange={e => {
                        onChange(
                            (value?.split("*")[0] || "") +
                                "*" +
                                (e.target.value || "") +
                                "*" +
                                (value?.split("*")[2] || ""),
                        );
                    }}
                />
                ~
                <Input
                    placeholder="高(m)"
                    value={value?.split("*")[2] || ""}
                    onChange={e => {
                        onChange(
                            (value?.split("*")[0] || "") +
                                "*" +
                                (value?.split("*")[1] || "") +
                                "*" +
                                (e.target.value || ""),
                        );
                    }}
                />
            </Space>
        </>
    );
};
