import React, { useEffect, useState } from "react";
import { Space, Button, Input, message } from "antd";
import { MinusCircleOutlined, PlusOutlined } from "@ant-design/icons";
let IDX = 36,
    HEX = "";
while (IDX--) HEX += IDX.toString(36);
export function uid(len) {
    let str = "",
        num = len || 11;
    while (num--) str += HEX[(Math.random() * 36) | 0];
    return str;
}
export default (props: { value?: any; onChange?: (value: any) => void }) => {
    const [list, setList] = useState([{ name: "", phone: "", key: uid(10) }]);

    useEffect(() => {
        if (props.value) {
            setList(props.value);
        }
    }, [props.value]);
    return (
        <div>
            {list.map(
                (
                    item: {
                        key: string;
                        name: any;
                        phone: any;
                    },
                    index: number,
                ) => {
                    return (
                        <div key={item.key} style={{ marginBottom: "10px" }}>
                            <Space>
                                <Input
                                    value={item.name}
                                    style={{ width: "100px" }}
                                    placeholder="姓名"
                                    onChange={e => {
                                        list[index].name = e.target.value;
                                        setList([...list]);
                                    }}
                                    onBlur={() => {
                                        props.onChange([...list]);
                                    }}
                                />
                                ~
                                <Input
                                    value={item.phone}
                                    style={{ width: "200px" }}
                                    placeholder="联系方式"
                                    onChange={e => {
                                        list[index].phone = e.target.value;
                                        setList([...list]);
                                    }}
                                    onBlur={() => {
                                        props.onChange([...list]);
                                    }}
                                />
                                <MinusCircleOutlined
                                    onClick={() => {
                                        if (list.length === 1) {
                                            return message.error("至少保留一位联系人");
                                        }
                                        list.splice(index, 1);
                                        setList([...list]);
                                        props.onChange([...list]);
                                    }}
                                />
                            </Space>
                        </div>
                    );
                },
            )}
            <Button
                icon={<PlusOutlined />}
                style={{ width: "300px", display: "block" }}
                onClick={() => {
                    if (list.length > 15) return message.error("最多添加15位联系人");
                    list.push({ name: "", phone: "", key: uid(10) });
                    props.onChange([...list]);
                }}></Button>
        </div>
    );
};
