import React, { useEffect, useMemo, useState } from "react";
import { Table } from "antd";
import CarModal from "./car-modal";
const replaceCharsWithStar = (str: string) => {
    // 检查字符串是否为空
    if (!str) return "";

    // 获取第一个字符，然后创建相应数量的星号
    const firstChar = str[0];
    const stars = "*".repeat(str.length - 1);

    // 拼接第一个字符和星号字符串
    return firstChar + stars;
};
function hideMiddleFourDigits(phoneNumber) {
    // 检查手机号码长度是否为11位（假设是中国大陆的手机号码）
    if (!phoneNumber) return "";
    // 切片操作：取第一个字符，然后取最后四个字符，中间用四个星号替换
    const maskedPhone = phoneNumber.slice(0, 3) + "****" + phoneNumber.slice(-4);

    return maskedPhone;
}
function hideMiddleFourDigits1(phoneNumber) {
    // 检查手机号码长度是否为11位（假设是中国大陆的手机号码）
    if (!phoneNumber) return "";
    // 切片操作：取第一个字符，然后取最后四个字符，中间用四个星号替换
    const maskedPhone = phoneNumber.slice(0, 4) + "****" + phoneNumber.slice(-4);

    return maskedPhone;
}
export default ({ data = [], load, show }) => {
    const [open, setOpen] = useState(false);
    const [dataSource, setDataSource] = useState(data);
    const [row, setRow] = useState();
    const columns = [
        {
            title: "序号",
            render: (value, _, index) => {
                return index + 1;
            },
        },
        {
            title: "车牌号",
            dataIndex: "truckLicensePlate",
            key: "truckLicensePlate",
        },
        {
            title: "司机姓名",
            dataIndex: "driverName",
            key: "driverName",
        },
        {
            title: "联系电话",
            dataIndex: "driverPhone",
            key: "driverPhone",
        },
        {
            title: "身份证号码",
            dataIndex: "driverIdNumber",
            key: "driverIdNumber",
        },
        {
            title: "空车皮重(kg)",
            dataIndex: "truckEmptyWeight",
            key: "truckEmptyWeight",
        },
        {
            title: "行驶证",
            render: row => {
                return (
                    <>
                        {row.truckVehicleRegistrationImage && (
                            <a target="_blank" href={row.truckVehicleRegistrationImage}>
                                {row.truckLicensePlate}_行驶证1
                            </a>
                        )}
                        <br />
                        {row.truckVehicleRegistrationImage2 && (
                            <a target="_blank" href={row.truckVehicleRegistrationImage2}>
                                {row.truckLicensePlate}_行驶证2
                            </a>
                        )}
                    </>
                );
            },
        },
        {
            title: "驾驶证",
            render: row => {
                return (
                    <>
                        {row.drivingLicenseImage && (
                            <a href={row.drivingLicenseImage}>{row.truckLicensePlate}_驾驶证1</a>
                        )}
                        <br />
                        {row.drivingLicenseImage2 && (
                            <a href={row.drivingLicenseImage2}>{row.truckLicensePlate}_驾驶证2</a>
                        )}
                    </>
                );
            },
        },
        {
            title: "过磅单",
            render: row => {
                return (
                    <>
                        {row.truckWeighingListImage && (
                            <a target="_blank" href={row.truckWeighingListImage}>
                                {row.truckLicensePlate}_过磅单
                            </a>
                        )}
                    </>
                );
            },
        },
        {
            title: "身份证",
            render: row => {
                return (
                    <>
                        {row.driverIdCardFrontImage && (
                            <a target="_blank" href={row.driverIdCardFrontImage}>
                                {row.driverName}_身份证1
                            </a>
                        )}
                        <br />
                        {row.driverIdCardFrontImage2 && (
                            <a target="_blank" href={row.driverIdCardFrontImage2}>
                                {row.driverName}_身份证2
                            </a>
                        )}
                    </>
                );
            },
        },
        {
            title: "操作",
            render: record => {
                return (
                    <a
                        onClick={() => {
                            setOpen(true);
                            setRow(record);
                        }}>
                        查看
                    </a>
                );
            },
        },
    ];

    // console.log('show:', show)
    useEffect(() => {
        if (data) {
            const arr = data.map(item => {
                const obj = { ...item };
                obj.driverName = show ? obj.driverName : replaceCharsWithStar(item.driverName);
                obj.driverPhone = show ? obj.driverPhone : hideMiddleFourDigits(item.driverPhone);
                obj.driverIdNumber = show ? obj.driverIdNumber : hideMiddleFourDigits1(item.driverIdNumber);
                return obj;
            });
            setDataSource(arr);
        }
    }, [show, data]);
    return (
        <>
            <Table columns={columns} dataSource={dataSource} scroll={{ x: 1500, y: 500 }} />
            <CarModal
                open={open}
                onClose={neadLoad => {
                    setOpen(false);
                    if (neadLoad) {
                        load();
                    }
                }}
                row={row}
                mode="read"
            />
        </>
    );
};
