import React, { useEffect, useRef } from "react";
import { ReactChildren, useState } from "react";
import "./scroll-anchor.less";

interface ScrollAnchorProps {
    list: {
        title: string;
        /**
         * 制定对应右边滚动容器的第几项子元素
         * 不传时，默认根据数组下标来做一一对应
         */
        index?: number;
    }[];
    /**
     * 滚动容器名称
     */
    scrollContainerName?: string;
    /**
     * 关联滚动元素的class名称
     */
    scrollItemName?: string;
    /**
     * 默认选中项
     */
    defaultCurrent?: number;
}

export default (props: React.PropsWithChildren<ScrollAnchorProps>) => {
    const [current, setCurrent] = useState(props.defaultCurrent || 0);
    /**
     * 组件容器
     */
    const componentRef = useRef<HTMLDivElement>();
    /**
     * containerRef: 滚动容器
     * */
    const containerRef = useRef<HTMLDivElement>();
    /**
     * 右侧滚动子元素高度记录
     */
    const rightHeightList = useRef([]);
    /**
     * scrollBlock: 滚动锁，避免手动js滚动和原生滚动冲突
     */
    const scrollBlock = useRef<boolean>(false);

    /**
     * scorllTimer: 滚动延迟器
     *
     */
    const scorllTimer = useRef<NodeJS.Timeout>();

    const setSystemSelectByScroll = (scrollTop: number) => {
        const arr = props.list.map((item, index) => {
            return item.index || index;
        });
        for (let i = 0; i < rightHeightList.current.length; i++) {
            if (rightHeightList.current[i] > scrollTop) {
                if (arr.includes(+i)) {
                    setCurrent(i);
                    return;
                }
            }
        }
        // 超出最大阀值处理
        setCurrent(+Object.keys(rightHeightList.current)[0]);
    };

    const scrollFn = e => {
        //@ts-ignore
        const { scrollHeight, scrollTop, clientHeight } = e.target;
        if (scrollBlock.current) return;
        setSystemSelectByScroll(scrollTop);
    };
    useEffect(() => {
        // const
        if (componentRef.current) {
            //@ts-ignore
            containerRef.current = componentRef.current.getElementsByClassName(
                props.scrollContainerName || "sa-right",
            )[0];
            containerRef.current.setAttribute("class", `${props.scrollContainerName || "sa-right"} sa-scroll`);
            containerRef.current.addEventListener("scroll", scrollFn);
        }
        return () => {
            if (containerRef.current) {
                containerRef.current.removeEventListener("scroll", scrollFn);
            }
        };
    }, [componentRef.current]);

    useEffect(() => {
        if (props.children) {
            // 获取子元素高度列表
            if (componentRef.current) {
                const doms = containerRef.current.children;
                const heights = Array.from(doms).map(item => item.clientHeight);
                rightHeightList.current[0] = heights[0];
                heights.reduce((pre, current, index) => {
                    const val = pre ? pre + current : current;
                    rightHeightList.current[index] = val;
                    return val;
                });
            }
        }
    }, [props.children]);

    return (
        <div className="scroll-anchor-container" ref={componentRef}>
            <div className="sa-left">
                {props.list.map((item, index) => {
                    const itemIndex = item.index || index;
                    const isActive = itemIndex === current;
                    return (
                        <div
                            key={index}
                            className={`sa-left-item ${isActive ? "active" : ""}`}
                            onClick={() => {
                                scrollBlock.current = true;
                                if (scorllTimer.current) {
                                    clearTimeout(scorllTimer.current);
                                }
                                setCurrent(itemIndex);
                                containerRef.current.children[itemIndex].scrollIntoView({ behavior: "smooth" });

                                //预防多次点击
                                scorllTimer.current = setTimeout(() => {
                                    scrollBlock.current = false;
                                    clearTimeout(scorllTimer.current);
                                }, 1000);
                            }}>
                            {item.title}
                        </div>
                    );
                })}
            </div>
            <div className="sa-right">{props.children}</div>
        </div>
    );
};
