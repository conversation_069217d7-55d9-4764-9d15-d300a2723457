import { useState } from "react";
import { CopyOutlined, EyeOutlined, EyeInvisibleOutlined } from "@ant-design/icons";
import React from "react";
export default ({ tab }) => {
    const [show, setShow] = useState(false);
    return (
        <>
            {show ? (
                <EyeOutlined
                    onClick={() => {
                        setShow(false);
                        tab && tab(false);
                    }}
                />
            ) : (
                <EyeInvisibleOutlined
                    onClick={() => {
                        setShow(true);
                        tab && tab(true);
                    }}
                />
            )}
        </>
    );
};
