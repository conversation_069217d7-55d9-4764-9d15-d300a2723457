import React from "react";
import { Table } from "antd";

export default ({ data = [] }) => {
    const columns = [
        {
            title: "YC单状态",
            dataIndex: "orderStateDesc",
            key: "orderStateDesc",
            width: 120,
        },
        {
            title: "日志描述",
            dataIndex: "text",
            key: "text",
            width: 400,
        },
        {
            title: "发生时间",
            dataIndex: "createdTime",
            key: "createdTime",
            width: 150,
        },
        {
            title: "操作人",
            dataIndex: "createdBy",
            key: "createdBy",
            width: 100,
        },
    ];
    return (
        <>
            <Table
                columns={columns}
                dataSource={data}
                scroll={{
                    // x: 1500,
                    y: 500,
                }}
            />
        </>
    );
};
