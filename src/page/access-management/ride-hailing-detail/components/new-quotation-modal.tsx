import React, { useState } from "react";
import QuotationModal from "../../ride-hailing-manage/quotation-modal";
import { Button } from "antd";

export default props => {
    const [quotationOpen, setQuotationOpen] = useState(false);
    return (
        <>
            <Button
                onClick={() => {
                    // props.changeMode(totalModal);
                    setQuotationOpen(true);
                }}>
                确认报价
            </Button>
            <QuotationModal
                row={props.detail}
                open={quotationOpen}
                load={bol => {
                    if (bol) {
                        props.load();
                    }
                    setQuotationOpen(false);
                }}
            />
        </>
    );
};
