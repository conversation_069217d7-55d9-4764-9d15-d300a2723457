import React, { useEffect, useState } from "react";
import { DDYObject } from "react-single-app";
import { Popover, Steps, StepsProps } from "antd";
import { DetailStatusCode } from "../configs";
interface StatusProps {
    detail: DDYObject;
}
export default (props: StatusProps) => {
    const { orderLogInfoMap = {}, orderStateProgressBarList = [], state } = props.detail;
    const [current, setCurrent] = useState(0);
    useEffect(() => {
        orderStateProgressBarList.forEach((item, index) => {
            if (item.state === state) {
                setCurrent(index);
            }
        });
    }, [orderLogInfoMap]);
    return (
        <>
            <Steps
                current={current}
                items={orderStateProgressBarList.map(
                    (
                        item: {
                            status: boolean;
                            stateDesc: any;
                            state: any;
                        },
                        index,
                    ) => {
                        if (current === index && item.state === DetailStatusCode.CANCEL) {
                            return {
                                title: item.stateDesc,
                                status: "wait",
                                description: orderLogInfoMap[item.state]?.createdTime,
                            };
                        }
                        return {
                            title: item.stateDesc,
                            description: orderLogInfoMap[item.state]?.createdTime,
                        };
                    },
                )}
            />
        </>
    );
};
