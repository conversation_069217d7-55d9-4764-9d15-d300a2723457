.title-div {
    padding: 14px 0;
    overflow: hidden;
    h3 {
        float: left;
    }
    button {
        float: right;
    }
}
.ant-table-thead > tr > th.textAlignCenter {
    text-align: center;
}
.ant-table-tbody > tr > td.textAlignCenter {
    text-align: center;
}

.footer-div {
    padding: 14px 0;
    display: flex;
    justify-content: center;
}

.main-page {
    i.wait-icon {
        display: inline-block;
        width: 10px;
        height: 10px;
        border-radius: 10px;
        background: #000;
        margin: 0 6px;
    }
    i.ing-icon {
        display: inline-block;
        width: 10px;
        height: 10px;
        border-radius: 10px;
        background: #fa7c14;
        margin: 0 6px;
    }
    i.success-icon {
        display: inline-block;
        width: 10px;
        height: 10px;
        border-radius: 10px;
        background: #52c41a;
        margin: 0 6px;
    }
    i.cancel-icon {
        display: inline-block;
        width: 10px;
        height: 10px;
        border-radius: 10px;
        background: #bbbbbb;
        margin: 0 6px;
    }
    i.fail-icon {
        display: inline-block;
        width: 10px;
        height: 10px;
        border-radius: 10px;
        background: #f5222d;
        margin: 0 6px;
    }
    i.error-icon {
        display: inline-block;
        width: 10px;
        height: 10px;
        border-radius: 10px;
        background: #e26f11;
        margin: 0 6px;
    }
    .ant-tabs-nav .ant-tabs-tab {
        padding: 6px 12px;
    }
}
.ant-modal-root {
    .modal-header {
        display: flex;
        flex-wrap: nowrap;
        justify-content: space-around;
        align-items: center;
    }
    .modal-title {
        font-size: 16px;
        font-weight: bold;
    }
    .modal-center {
        text-align: center;
        padding-bottom: 10px;
    }
}
.num-table {
    width: 100%;
    overflow: auto;
    max-height: 120px;
    padding-bottom: 12px;
}
.num-table .num-td:nth-child(7),
.num-table .num-td:nth-child(14),
.num-table .num-td:nth-child(21),
.num-table .num-td:nth-child(28),
.num-table .num-td:nth-child(35),
.num-table .num-td:nth-child(42),
.num-table .num-td:nth-child(49),
.num-table .num-td:nth-child(56),
.num-table .num-td:nth-child(63),
.num-table .num-td:nth-child(70),
.num-table .num-td:nth-child(77),
.num-table .num-td:nth-child(84),
.num-table .num-td:nth-child(91),
.num-table .num-td:nth-child(98),
.num-table .num-td:nth-child(105),
.num-table .num-td:nth-child(112),
.num-table .num-td:nth-child(119),
.num-table .num-td:nth-child(126),
.num-table .num-td:nth-child(133),
.num-table .num-td:nth-child(140),
.num-table .num-td:nth-child(147),
.num-table .num-td:nth-child(154),
.num-table .num-td:nth-child(161),
.num-table .num-td:nth-child(168),
.num-table .num-td:nth-child(175),
.num-table .num-td:nth-child(182),
.num-table .num-td:nth-child(189),
.num-table .num-td:nth-child(196),
.num-table .num-td:nth-child(203) {
    margin-right: 0;
}
.num-td {
    width: 12%;
    margin-right: 2.6%;
    float: left;
    button {
        width: 100%;
    }
}
.translatey {
    transform: translateY(5px);
}
.num-table-title {
    line-height: 18px;
    text-align: center;
}
.nuclear-note-manage {
    .tbody {
        min-height: calc(100vh - 310px);
    }
}
.base-page .bottom-tab .ant-table-tbody > tr > td {
    // padding: 0;
    line-height: 20px;
}
.bottom-tab .bottom-tab-table {
    width: 100%;
    // height: 50px;
    overflow: auto;
}
// .nuclear-note-manage .bottom-tab{
// bottom: 41px;
// box-shadow: none;
// }
td .link.import {
    position: relative;
}
td .link.import input {
    position: absolute;
    top: 0;
    left: 0;
    opacity: 0;
}

.main-content {
    .main {
        .main-page {
            overflow: auto;
        }
    }
}
.required-icon  {
    .ant-form-item-label > label::before  {
        display: inline-block;
        margin-right: 4px;
        color: #ff4d4f;
        font-size: 14px;
        font-family: SimSun, sans-serif;
        line-height: 1;
        content: "*";
    }
}
.the-goods-record-modal {
    position: relative;
    .ant-modal {
        top: 80px;
        .ant-modal-body {
            height: calc(100vh - 268px);
            overflow: auto;
        }
        .ant-select {
            width: 200px !important;
        }
        .ant-input {
            width: 200px;
        }
        .ant-input-number {
            width: 200px !important;
        }
        .ant-form-item {
            margin-bottom: 16px;
        }
        .ant-form-item-label {
            width: 140px;
            overflow: visible;
        }
    }
}
.the-goods-record-modals {
    position: relative;

    .ant-modal-body {
        // height: calc(100vh - 268px);
        overflow: auto;
    }
    .ant-select {
        width: 230px !important;
    }
    .ant-input {
        width: 230px;
    }
    .ant-input-number {
        width: 230px !important;
    }
    .ant-form-item {
        margin-bottom: 25px !important;
    }

    .ant-form-item-label {
        width: 120px;
        overflow: visible;
    }
}
#body-wait {
    .mask {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.35);
        z-index: 2100;
    }
    img {
        display: block;
        width: 160px;
        height: 160px;
        position: fixed;
        left: 50%;
        top: 50%;
        margin-left: -80px;
        margin-right: -80px;
        z-index: 2301;
    }
}

.goods-record-check-detail-root {
    display: flex;
    flex-direction: row;
    align-items: center;
    // justify-content: ;
    .root-left {
        width: 66.7%;
        height: 100%;
    }
    .root-right {
        width: 33.3%;
        height: 100%;
    }
}
