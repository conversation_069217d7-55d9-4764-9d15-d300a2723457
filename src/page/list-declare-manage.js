import React, { useState, useEffect } from "react";
import "./shop-good.less";
import { ConfigCenter, lib, SearchList, getConfigDataUtils, HOC } from "react-single-app";
import { Checkbox, Button, Modal, Drawer, Input, Select, Form, message, Tooltip, Space, Tabs, Table } from "antd";
const { TextArea } = Input;
import event from "../common/event";
import axios from "axios";
const { TabPane } = Tabs;
import { UploadOutlined } from "@ant-design/icons";
import moment from "moment";
import BottomTag from "../components/BottomTag";
import { area_data } from "../common/areas";
import NewModal from "../../src/components/NewModal";
import "./list-declare-manage.less";
function data2areas(area_data) {
    const theData = [];
    if (!Array.isArray(area_data)) {
        Object.keys(area_data).map(item => {
            theData.push({
                id: item,
                name: item,
                children: data2areas(area_data[item]),
            });
        });
    } else {
        area_data.map(ite => {
            theData.push({
                id: ite,
                name: ite,
            });
        });
    }
    return theData;
}
const AREADATA = data2areas(area_data);
const isType = (obj, type) => {
    if (typeof obj !== "object") return false;
    const typeString = Object.prototype.toString.call(obj);
    let flag;
    switch (type) {
        case "Array":
            flag = typeString === "[object Array]";
            break;
        case "Date":
            flag = typeString === "[object Date]";
            break;
        case "RegExp":
            flag = typeString === "[object RegExp]";
            break;
        default:
            flag = false;
    }
    return flag;
};
/**
 * deep clone
 * @param  {[type]} parent object 需要进行克隆的对象
 * @return {[type]}        深克隆后的对象
 */
const clone = parent => {
    // 维护两个储存循环引用的数组
    const parents = [];
    const children = [];

    const _clone = parent => {
        if (parent === null) return null;
        if (typeof parent !== "object") return parent;

        let child, proto;

        if (isType(parent, "Array")) {
            // 对数组做特殊处理
            child = [];
        } else if (isType(parent, "RegExp")) {
            // 对正则对象做特殊处理
            child = new RegExp(parent.source, getRegExp(parent));
            if (parent.lastIndex) child.lastIndex = parent.lastIndex;
        } else if (isType(parent, "Date")) {
            // 对Date对象做特殊处理
            child = new Date(parent.getTime());
        } else {
            // 处理对象原型
            proto = Object.getPrototypeOf(parent);
            // 利用Object.create切断原型链
            child = Object.create(proto);
        }

        // 处理循环引用
        const index = parents.indexOf(parent);

        if (index != -1) {
            // 如果父数组存在本对象,说明之前已经被引用过,直接返回此对象
            return children[index];
        }
        parents.push(parent);
        children.push(child);

        for (let i in parent) {
            // 递归
            child[i] = _clone(parent[i]);
        }

        return child;
    };
    return _clone(parent);
};
@HOC.mapAuthButtonsToState({ buttonCodeArr: ["exportAuth", "CUSTOMS-DATA-EXPORT"] })
class App extends SearchList {
    constructor(props) {
        super(props);
        this.state.detail = 0;
        this.state.listData = [];
        this.state.area_data = [];
        this.state.isLog = false;
        this.state.provList = AREADATA;
        this.state.cityList = [];
        this.state.zoneList = [];
        this.state.tabsList = [
            {
                tab: "基础信息",
                type: "TABLE",
                columns: [
                    {
                        title: "渠道订单号",
                        dataIndex: ["customsInventory", "outOrderNo"],
                        width: "230",
                        formType: "TEXT",
                    },
                    {
                        title: "出库单号",
                        dataIndex: ["customsInventory", "outboundNo"],
                        width: "230",
                        formType: "TEXT",
                    },
                    {
                        title: "清关企业",
                        dataIndex: ["customsInventory", "agentCompanyName"],
                        width: "230",
                        formType: "TEXT",
                    },
                    {
                        title: "电商平台",
                        dataIndex: ["customsInventory", "ebpName"],
                        width: "230",
                        formType: "TEXT",
                    },
                    {
                        title: "电商企业名称",
                        dataIndex: ["customsInventory", "ebcName"],
                        width: "230",
                        formType: "TEXT",
                    },
                    {
                        title: "申报口岸",
                        dataIndex: ["customsInventory", "customs"],
                        width: "120",
                        formType: "TEXT",
                    },
                    {
                        title: "订单总金额",
                        dataIndex: ["customsInventory", "totalPrice"],
                        width: "120",
                        formType: "TEXT",
                    },
                    {
                        title: "总税金",
                        dataIndex: ["customsInventory", "taxPrice"],
                        width: "120",
                        formType: "TEXT",
                    },
                    {
                        title: "应征关税",
                        dataIndex: ["customsInventory", "customsTax"],
                        width: "120",
                        formType: "TEXT",
                    },
                    {
                        title: "应征增值税",
                        dataIndex: ["customsInventory", "valueAddedTax"],
                        width: "120",
                        formType: "TEXT",
                    },
                    {
                        title: "应征消费税",
                        dataIndex: ["customsInventory", "consumptionTax"],
                        width: "120",
                        formType: "TEXT",
                    },
                    {
                        title: "订购人姓名",
                        dataIndex: ["customsInventory", "buyerName"],
                        width: "120",
                        formType: "INPUT",
                    },
                    {
                        title: "订购人身份证号",
                        dataIndex: ["customsInventory", "buyerIdNumber"],
                        width: "200",
                        formType: "INPUT",
                    },
                    {
                        title: "收货(省)",
                        dataIndex: ["customsInventory", "consigneeProvince"],
                        width: "120",
                        formType: "SELECT",
                        selectData: this.state.provList,
                        onChange: (e, form) => {
                            let { tabsList, cityList } = this.state;
                            this.state.provList.map(item => {
                                if (item.id === e) {
                                    cityList = item.children;
                                }
                            });
                            tabsList[0].columns[14].selectData = cityList;
                            form.current.setFields([
                                { name: ["customsInventory", "consigneeCity"], value: "" },
                                { name: ["customsInventory", "consigneeDistrict"], value: "" },
                            ]);
                            this.setState({ cityList, tabsList });
                        },
                    },
                    {
                        title: "市",
                        dataIndex: ["customsInventory", "consigneeCity"],
                        width: "120",
                        formType: "SELECT",
                        selectData: this.state.cityList,
                        onChange: (e, form) => {
                            let { tabsList, zoneList } = this.state;
                            this.state.cityList.map(item => {
                                if (item.id === e) {
                                    zoneList = item.children;
                                }
                            });
                            tabsList[0].columns[15].selectData = zoneList;
                            form.current.setFields([{ name: ["customsInventory", "consigneeDistrict"], value: "" }]);
                            this.setState({ zoneList, tabsList });
                        },
                    },
                    {
                        title: "区",
                        dataIndex: ["customsInventory", "consigneeDistrict"],
                        width: "120",
                        formType: "SELECT",
                        selectData: this.state.zoneList,
                        onChange: (e, form) => {},
                    },
                    {
                        title: "详细地址",
                        dataIndex: ["customsInventory", "consigneeAddress"],
                        width: "200",
                        formType: "INPUT",
                    },
                    {
                        title: "物流企业",
                        dataIndex: ["customsInventory", "logisticsCompanyName"],
                        width: "200",
                        formType: "TEXT",
                    },
                    {
                        title: "运单编号",
                        dataIndex: ["customsInventory", "logisticsNo"],
                        width: "200",
                        formType: "TEXT",
                    },
                ],
            },
            {
                tab: "商品信息",
                type: "TABLE",
                columns: [
                    {
                        title: "SKU",
                        dataIndex: "itemNo",
                        width: "200",
                        formType: "TEXT",
                    },
                    {
                        title: "料号",
                        dataIndex: "productId",
                        width: "200",
                        formType: "TEXT",
                    },
                    {
                        title: "金二序号",
                        dataIndex: "goodsSeqNo",
                        width: "200",
                        formType: "TEXT",
                    },
                    {
                        title: "备案名称",
                        dataIndex: "goodsName",
                        width: "600",
                        formType: "TEXT",
                    },
                    {
                        title: "条码",
                        dataIndex: "barCode",
                        width: "200",
                        formType: "TEXT",
                    },
                    {
                        title: "HS编码",
                        dataIndex: "hsCode",
                        width: "600",
                        formType: "SELECT",
                        together: true,
                        extra: "/ccs/customs/listHs",
                        // 'disabled': true,
                        onChange: (id, form, name) => {
                            console.log(name);
                            let keyArr = name.slice(0, -1);
                            lib.request({
                                url: "/ccs/customs/hsTaxDetailV2",
                                data: {
                                    id,
                                },
                                needMask: true,
                                success: res => {
                                    let { firstLegalUnit, secondLegalUnit } = res;
                                    form.current.setFields([
                                        { name: [...keyArr.concat("firstUnit")], value: firstLegalUnit },
                                        { name: [...keyArr.concat("secondUnit")], value: secondLegalUnit },
                                    ]);
                                },
                            });
                        },
                    },
                    {
                        title: "净重",
                        dataIndex: "netWeight",
                        width: "200",
                        formType: "TEXT",
                        numberType: "integer",
                    },
                    {
                        title: "毛重",
                        dataIndex: "grossWeight",
                        width: "200",
                        formType: "TEXT",
                        numberType: "integer",
                    },
                    {
                        title: "原产国",
                        dataIndex: "originCountry",
                        width: "200",
                        formType: "SELECT",
                        extra: "/ccs/customs/listCountry",
                        // 'disabled': true
                    },
                    {
                        title: "第一计量单位",
                        dataIndex: "firstUnit",
                        width: "120",
                        formType: "SELECT",
                        extra: "/ccs/customs/listUom",
                        // 'disabled': true
                    },
                    {
                        title: "第一计量单位数量",
                        dataIndex: "firstUnitAmount",
                        width: "200",
                        formType: "INPUTNUMBER",
                        numberType: "decimal",
                    },
                    {
                        title: "第二计量单位",
                        dataIndex: "secondUnit",
                        width: "120",
                        formType: "SELECT",
                        extra: "/ccs/customs/listUom",
                        // 'disabled': true
                    },
                    {
                        title: "第二计量单位数量",
                        dataIndex: "secondUnitAmount",
                        width: "200",
                        formType: "INPUTNUMBER",
                        numberType: "decimal",
                    },
                    {
                        title: "单价",
                        dataIndex: "unitPrice",
                        width: "120",
                        formType: "TEXT",
                        numberType: "decimal",
                    },
                    {
                        title: "数量",
                        dataIndex: "count",
                        width: "120",
                        formType: "TEXT",
                        numberType: "integer",
                    },
                    {
                        title: "总价",
                        dataIndex: "totalPrice",
                        width: "120",
                        formType: "TEXT",
                        numberType: "decimal",
                    },
                    {
                        title: "税金",
                        dataIndex: "taxPrice",
                        width: "120",
                        formType: "TEXT",
                        numberType: "decimal",
                    },
                ],
            },
            {
                tab: "轨迹日志",
                type: "TABLE",
                columns: [
                    // {
                    //     "title":"单据编号",
                    //     "dataIndex":"sn",
                    //     "width":"200",
                    //     "formType":"TEXT",
                    // },
                    // {
                    //     "title": "单据类型",
                    //     "dataIndex": "typeDesc",
                    //     "width": "120",
                    //     "formType": "TEXT",
                    // },
                    {
                        title: "修改前状态",
                        dataIndex: "oldStatusStr",
                        width: "120",
                        formType: "TEXT",
                    },
                    {
                        title: "修改后状态",
                        dataIndex: "newStatusStr",
                        width: "160",
                        formType: "TEXT",
                    },
                    {
                        title: "操作描述",
                        dataIndex: "operateDesStr",
                        width: "200",
                        formType: "TEXT",
                    },
                    {
                        title: "日志描述",
                        dataIndex: "logDes",
                        width: "280",
                        formType: "TEXT",
                        renderType: "func",
                        extra: "renderItem3",
                    },
                    {
                        title: "报文",
                        dataIndex: "",
                        width: "140",
                        formType: "TEXT",
                        renderType: "func",
                        extra: "renderItem",
                    },
                    // {
                    //     "title": "响应报文",
                    //     "dataIndex": "",
                    //     "width": "140",
                    //     "formType": "TEXT",
                    //     "renderType": "func",
                    //     "extra": "renderItem2"
                    // },
                    // {
                    //   "title": "操作人",
                    //   "dataIndex": "oper",
                    //   "width": "140",
                    //   "formType": "TEXT",
                    // },
                    {
                        title: "日志时间",
                        dataIndex: "createTime",
                        width: "160",
                        formType: "TEXT",
                        renderType: "js",
                        extra: "value => new Date(value).format('yyyy-MM-dd hh:mm:ss')",
                    },
                ],
            },
        ];
        this.state.isLog = false;
        this.state.buttonAuth = { exportAuth: false };
        this.state.count = {
            totalFee: Number.NaN,
            calculateTaxFee: Number.NaN,
            totalTax: Number.NaN,
        };
        this.state.exportVisible = false;
        this.state.exportConfigList = [
            {
                type: "DATERANGE",
                labelName: "创建时间",
                labelKey: "createTime",
                showTime: {
                    hideDisabledOptions: true,
                    defaultValue: [moment("00:00:00", "HH:mm:ss"), moment("23:59:59", "HH:mm:ss")],
                },
                required: true,
                disabledDate: (current, values) => {
                    if (!values) {
                        return false;
                    }
                    const tooLate = values[0] && current.diff(values[0], "days") > 90;
                    const tooEarly = values[1] && values[1].diff(current, "days") > 90;
                    return !!tooEarly || !!tooLate;
                },
            },
            {
                type: "SELECT",
                labelName: "账册编号",
                labelKey: "bookId",
                required: true,
                // disabled: true,
                list: [],
                from: "/ccs/jdServProvider/listAuthBook",
            },
            {
                type: "SELECT",
                labelName: "数据类型",
                labelKey: "dataType",
                list: [
                    {
                        id: 1,
                        name: "人审取消",
                    },
                    {
                        id: 2,
                        name: "入区货值",
                    },
                ],
                required: true,
            },
        ];
    }

    getConfig() {
        return axios.get(getConfigDataUtils.getDataUrlByDataId(317)).then(e => e.data.data);
    }

    componentDidMount() {
        let toTime = moment().format("YYYY-MM-DD"),
            fromTime = moment().subtract(1, "months").format("YYYY-MM-DD");
        this.changeImmutable({
            createFrom: moment(fromTime + " 00:00:00.000").format("x"),
            createTo: moment(toTime + " 23:59:59.999").format("x"),
        });
        let { tabsList } = this.state;
        tabsList.map(ite => {
            ite.columns.map(it => {
                if (it.extra && it.formType === "SELECT") {
                    lib.request({
                        url: it.extra,
                        success: res => {
                            if (res) {
                                it.selectData = res || [];
                                this.setState({ tabsList });
                            }
                        },
                    });
                }
            });
        });
        this.setState({ tabsList });
        lib.request({
            url: this.state.exportConfigList[1].from,
            data: {},
            success: data => {
                this.state.exportConfigList[1].list = data;
                this.setState({
                    exportConfigList: this.state.exportConfigList,
                });
            },
        });
    }
    onSearch(search) {
        lib.request({
            url: "/ccs/singleInvtOrder/tax/statistics/ByEs",
            data: search,
            success: res => {
                this.setState({
                    count: res,
                });
            },
        });
    }
    renderSelfButtons() {
        return (
            <React.Fragment>
                <Button onClick={() => this.exportFunc()}>导出</Button>
            </React.Fragment>
        );
    }
    renderDeclareOrderNo(row) {
        return (
            <a
                onClick={() => {
                    lib.openPage(`/declaration-manage-detail?orderId=${row.orderId}&page_title=申报单详情`);
                }}>
                {row.declareOrderNo}
            </a>
        );
    }

    tabSubmit(values) {
        const { listData } = this.state;
        let data = values.customsInventory;
        data.id = this.state.id;
        if (data.itemList?.length) {
            let errorIndex = data.itemList?.findIndex(item => item.firstUnit && !item.firstUnitAmount);
            if (~errorIndex) {
                message.warning(`${listData.itemList[errorIndex].itemName}的第一计量单位数量不能为空`);
                return;
            }
            errorIndex = data.itemList?.findIndex(item => !item.firstUnit && item.firstUnitAmount);
            if (~errorIndex) {
                message.warning(`${listData.itemList[errorIndex].itemName}的第一计量单位不能为空`);
                return;
            }
            errorIndex = data.itemList?.findIndex(item => !item.secondUnit && item.secondUnitAmount);
            if (~errorIndex) {
                message.warning(`${listData.itemList[errorIndex].itemName}的第二计量单位不能为空`);
                return;
            }
            errorIndex = data.itemList?.findIndex(item => item.secondUnit && !item.secondUnitAmount);
            if (~errorIndex) {
                message.warning(`${listData.itemList[errorIndex].itemName}的第二计量单位数量不能为空`);
                return;
            }
        }

        data.itemList?.map(item => {
            item.firstUnit = item.firstUnit || "";
            item.firstUnitAmount = item.firstUnitAmount || null;
            item.secondUnit = item.secondUnit || "";
            item.secondUnitAmount = item.secondUnitAmount || null;
        });
        console.log(listData.itemList, "item");
        // return
        if (listData.itemList) {
            listData.itemList.map((item, index) => {
                data.itemList[index].id = item.id;
                data.itemList[index].goodsName = item.itemName;
                data.itemList[index].productId = item.productId;
                data.itemList[index].barCode = item.barCode;
                // data.itemList[index].hsCode = item.hsCode
                data.itemList[index].grossWeight = item.grossWeight;
                data.itemList[index].netWeight = item.netWeight;
                // data.itemList[index].originCountry = item.originCountry

                data.itemList[index].unitPrice = item.unitPrice;
                data.itemList[index].goodsCount = item.goodsCount;
                data.itemList[index].goodsAmount = item.goodsAmount;
                data.itemList[index].count = item.count;
                // data.itemList[index].firstUnitAmount = item.firstUnitAmount
                data.itemList[index].goodsSeqNo = item.goodsSeqNo;
                // data.itemList[index].itemName = item.itemName
                // data.itemList[index].secondUnitAmount = item.secondUnitAmount
                data.itemList[index].taxPrice = item.taxPrice;
                data.itemList[index].totalPrice = item.totalPrice;
            });
        }
        if (listData.logList) {
            data.logList = listData.logList;
        }
        Object.assign(listData, data);
        lib.request({
            url: "/ccs/singleInvtOrder/save",
            method: "POST",
            data: data,
            success: res => {
                message.success("修改成功");
                this.load(true);
                this.setState({ detail: 0 });
            },
        });
    }
    tabOnClick(value) {
        if (value == 2) {
            this.setState({
                isLog: true,
            });
        } else {
            this.setState({
                isLog: false,
            });
        }
    }

    renderDetail() {
        const { isLog, tabsList } = this.state;
        let props = {
            tabsList,
            showSubmitBtn: !isLog,
            id: this.state.id,
            tabSubmit: this.tabSubmit.bind(this),
            tabOnClick: this.tabOnClick.bind(this),
        };
        return <BottomTag {...props}></BottomTag>;
    }

    renderStatus(row) {
        return <Tooltip title={row.customsDetail}>{row.customsStatusDesc}</Tooltip>;
    }

    getCheckedRows() {
        return this.state.dataList.filter(item => item.checked);
    }

    renderRightOperation = () => {
        if (!this.state.buttons.includes("CUSTOMS-DATA-EXPORT")) return null;
        return (
            <Button
                onClick={() => {
                    this.state.exportConfigList[0].dates = [moment().subtract(90, "days"), moment()];
                    this.setState({
                        exportVisible: true,
                        exportConfigList: this.state.exportConfigList,
                    });
                    this.newModalRef.setFormValue([
                        {
                            name: "bookId",
                            value: 10,
                        },
                        {
                            name: "createTime",
                            value: [moment().subtract(90, "days"), moment()],
                        },
                    ]);
                }}>
                关务数据导出
                <UploadOutlined />
            </Button>
        );
    };

    renderLeftOperation() {
        let { count } = this.state;
        return (
            <React.Fragment>
                <Space>
                    <Button type="primary" onClick={this.pushAgain}>
                        清单重推
                    </Button>
                    <Button onClick={this.errorPush}>清单状态同步</Button>
                    <span style={{ paddingLeft: 20 }}>
                        当前申报总金额 {count.totalFee} , 预扣税金 {count.calculateTaxFee} , 海关税金 {count.totalTax}
                    </span>
                </Space>
            </React.Fragment>
        );
    }

    errorPush = () => {
        let modal = Modal.confirm({
            cancelText: "取消",
            okText: "确定",
            title: "提示",
            content: "确定同步吗？",
            onOk: () => {
                let list = this.state.selectedRows;
                if (list?.length === 0) {
                    message.warning("请选择数据");
                    return;
                } else {
                    let ids = "";
                    list.map(item => {
                        ids += item.orderId + ",";
                    });
                    lib.request({
                        url: "/ccs/order/exceptionOrderDeclare",
                        data: { ids: ids.slice(0, -1) },
                        success: res => {
                            message.success("同步成功");
                            this.setState({
                                selectedRows: [],
                                selectedIdList: [],
                            });
                            this.load(true);
                        },
                    });
                }
            },
            onCancel: res => {
                modal.destroy();
                this.load(true);
            },
        });
    };

    pushAgain = () => {
        let modal = Modal.confirm({
            cancelText: "取消",
            okText: "确定",
            title: "提示",
            content: "确定重推吗？",
            onOk: () => {
                let list = this.state.selectedRows;
                if (list?.length === 0) {
                    message.warning("请选择数据");
                    return;
                } else {
                    let ids = "";
                    list.map(item => {
                        ids += item.orderId + ",";
                    });
                    lib.request({
                        url: "/ccs/singleInvtOrder/reDeclare",
                        data: { ids: ids.slice(0, -1) },
                        success: res => {
                            message.success("重推成功");
                            this.setState({
                                selectedRows: [],
                                selectedIdList: [],
                            });
                            this.load(true);
                        },
                    });
                }
            },
            onCancel: res => {
                modal.destroy();
                this.load(true);
            },
        });
    };

    renderModal() {
        return (
            <>
                <NewModal
                    title={"关务数据导出"}
                    onOk={data => {
                        data.createTimeFrom = data.createTime[0].valueOf();
                        data.createTimeTo = data.createTime[1].valueOf();
                        delete data.createTime;
                        lib.request({
                            url: "/ccs/singleInvtOrder/exportCusOfficerData",
                            data,
                            success: res => {
                                this.setState({
                                    exportVisible: false,
                                });
                                lib.openPage("/excel/download-center?page_title=下载中心");
                            },
                            fail: () => {},
                        });
                    }}
                    onCancel={() => {
                        this.setState({
                            exportVisible: false,
                        });
                    }}
                    configList={this.state.exportConfigList}
                    visible={this.state.exportVisible}
                    formItemLayout={{
                        labelCol: { span: 8 },
                        wrapperCol: { span: 14 },
                    }}
                    ref={ref => {
                        this.newModalRef = ref;
                    }}
                    modalStyle={{ width: "800px" }}
                />
            </>
        );
    }
}

export default App;
