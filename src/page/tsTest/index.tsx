import { MenuOutlined } from "@ant-design/icons";
import { Table } from "antd";
import { arrayMoveImmutable } from "array-move";
import React, { useState } from "react";
import { SortableContainer, SortableElement, SortableHandle } from "react-sortable-hoc";
const DragHandle = SortableHandle(() => (
    <MenuOutlined
        style={{
            cursor: "grab",
            color: "#999",
        }}
    />
));
const columns = [
    {
        title: "Sort",
        dataIndex: "sort",
        width: 30,
        className: "drag-visible",
        render: () => <DragHandle />,
    },
    {
        title: "Name",
        dataIndex: "name",
        className: "drag-visible",
    },
    {
        title: "Age",
        dataIndex: "age",
    },
    {
        title: "Address",
        dataIndex: "address",
    },
];
const data = [
    {
        key: "1",
        name: "<PERSON>",
        age: 32,
        address: "New York No. 1 Lake Park",
        index: 0,
    },
    {
        key: "2",
        name: "<PERSON>",
        age: 42,
        address: "London No. 1 Lake Park",
        index: 1,
    },
    {
        key: "3",
        name: "<PERSON> Black",
        age: 32,
        address: "Sydney No. 1 Lake Park",
        index: 2,
    },
];
const SortableItem = SortableElement(props => <tr {...props} />);
const SortableBody = SortableContainer(props => <tbody {...props} />);
const App = () => {
    const [dataSource, setDataSource] = useState(data);
    const onSortEnd = ({ oldIndex, newIndex }) => {
        if (oldIndex !== newIndex) {
            const newData = arrayMoveImmutable(dataSource.slice(), oldIndex, newIndex).filter(el => !!el);
            console.log("Sorted items: ", newData);
            setDataSource(newData);
        }
    };
    const DraggableContainer = props => (
        <SortableBody useDragHandle disableAutoscroll helperClass="row-dragging" onSortEnd={onSortEnd} {...props} />
    );
    const DraggableBodyRow = ({ className, style, ...restProps }) => {
        // function findIndex base on Table rowKey props and should always be a right array index
        const index = dataSource.findIndex(x => x.index === restProps["data-row-key"]);
        return <SortableItem index={index} {...restProps} />;
    };
    return (
        <Table
            pagination={false}
            dataSource={dataSource}
            columns={columns}
            rowKey="index"
            components={{
                body: {
                    wrapper: DraggableContainer,
                    row: DraggableBodyRow,
                },
            }}
        />
    );
};
export default App;
