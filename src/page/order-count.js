import React, { useEffect, useState, useRef } from "react";
import { Select, Row, Col, Radio, DatePicker } from "antd";
import "./order-count.less";
import { lib } from "react-single-app";
// import { Chart  } from '@antv/g2';
import md5 from "md5";
import moment from "moment";
function SearchCondition({ setList }) {
    let statisticTypeList = [
        { id: "order", name: "订单" },
        { id: "logistics", name: "运单" },
        { id: "payment", name: "支付单" },
        { id: "inventory", name: "清单" },
    ];
    let [declareCompanyList, setDeclareCompanyList] = useState([]);
    let [tenantList, setTenantList] = useState([]);
    let [query, setQuery] = useState({
        dateQueryType: "TODAY",
    });
    let [from, to] = [null, null];

    function search(query) {
        setQuery({ ...query });
        lib.request({
            url: "/ccs/statistic/order",
            data: query,
            neekMask: true,
            success: data => {
                var list = data.statisticNodeVOS.map(item => {
                    item["订单总数"] = item.nodeOrderCount;
                    item["订单总金额"] = item.nodeOrderAmount;
                    return item;
                });
                setList(list);
            },
        });
    }
    useEffect(() => {
        lib.request({
            url: "/ccs/company/listWithSBQYAll",
            success: data => setDeclareCompanyList(data),
        });
        lib.request({
            url: "/ccs/taxesTenant/listAccount",
            success: data => setTenantList(data),
        });
        search(query);
    }, []);
    return (
        <div className="search-conditions">
            <Row className="row">
                <Col span={8} className="col">
                    <label className="label">单据类型</label>
                    <Select
                        className="select"
                        allowClear
                        value={query.statisticType}
                        onChange={value => {
                            query.statisticType = value;
                            search(query);
                        }}>
                        {statisticTypeList.map(item => (
                            <Select.Option key={item.id} value={item.id}>
                                {item.name}
                            </Select.Option>
                        ))}
                    </Select>
                </Col>
                <Col span={8} className="col">
                    <label className="label">清关企业</label>
                    <Select
                        className="select"
                        showSearch
                        allowClear
                        value={query.declareCompanyId}
                        onChange={value => {
                            query.declareCompanyId = value;
                            search(query);
                        }}>
                        {declareCompanyList.map(item => (
                            <Select.Option key={item.id} value={item.id}>
                                {item.name}
                            </Select.Option>
                        ))}
                    </Select>
                </Col>
                <Col span={8} className="col">
                    <label className="label">用户</label>
                    <Select
                        className="select"
                        showSearch
                        allowClear
                        value={query.tenantId}
                        onChange={value => {
                            query.tenantId = value;
                            search(query);
                        }}>
                        {tenantList.map(item => (
                            <Select.Option key={item.id} value={item.id}>
                                {item.name}
                            </Select.Option>
                        ))}
                    </Select>
                </Col>
            </Row>
            <div>
                <label className="label">日期</label>
                <Radio.Group
                    options={[
                        { label: "今日", value: "TODAY" },
                        { label: "近七天", value: "RECENT_SEVEN" },
                        { label: "本周", value: "WEEK" },
                        { label: "本月", value: "MONTH" },
                        { label: "本年", value: "YEAR" },
                        { label: "全部", value: "ALL" },
                        { label: "自定义", value: "CUSTOM" },
                    ]}
                    onChange={e => {
                        query.dateQueryType = e.target.value;
                        if (query.dateQueryType == "CUSTOM") {
                            setQuery({ ...query });
                        } else {
                            delete query.createFrom;
                            delete query.createTo;
                            search(query);
                        }
                    }}
                    value={query.dateQueryType}
                    optionType="button"
                    buttonStyle="solid"
                />
                {query.dateQueryType == "CUSTOM" && (
                    <DatePicker.RangePicker
                        style={{ marginLeft: "10px" }}
                        allowClear
                        onChange={dates => {
                            query.createFrom = new Date(dates[0].startOf("day")).getTime();
                            query.createTo = new Date(dates[1].endOf("day")).getTime();
                            search(query);
                        }}
                        onCalendarChange={dates => {
                            if (!dates) {
                                return (from = to = null);
                            }
                            from = dates[0] ? new Date(dates[0].startOf("day")).getTime() : null;
                            to = dates[1] ? new Date(dates[1].endOf("day")).getTime() : null;
                        }}
                        disabledDate={current => {
                            if (from) {
                                return new Date(current).getTime() > from + 3600 * 24 * 30 * 1000 - 1;
                            }
                            if (to) {
                                return new Date(current).getTime() < to - 3600 * 24 * 30 * 1000;
                            }
                            return false;
                        }}
                    />
                )}
            </div>
        </div>
    );
}
function OrderChart({ list }) {
    let container = useRef();
    let [type, setType] = useState("订单总数");
    let key = md5(JSON.stringify(list)) + type;
    useEffect(() => {
        const chart = new Chart({
            container: container.current,
            autoFit: true,
            padding: [80, 80, 50],
        });
        chart.data(list);
        let totalCount = 0,
            amountCount = 0;
        list.map(item => {
            (totalCount += item.nodeOrderCount), (amountCount += item.nodeOrderAmount);
        });

        chart.annotation().dataMarker({
            position: ["95%", "5%"],
            text: {
                content: `订单总数:${totalCount}\n订单总金额:${amountCount}`,
                style: {
                    textAlign: "right",
                    lineHeight: 18,
                },
            },
            point: null,
            line: null,
        });
        chart.line().position(`nodeTime*${type}`).color("#1890ff");
        chart.render();
    }, [key]);

    return (
        <div ref={container} className="chart" key={key}>
            <div className="type">
                <Radio.Group
                    options={[
                        { label: "订单总数", value: "订单总数" },
                        { label: "订单总金额", value: "订单总金额" },
                    ]}
                    onChange={e => setType(e.target.value)}
                    value={type}
                    optionType="button"
                    buttonStyle="solid"
                />
            </div>
        </div>
    );
}

function OrderCount() {
    let [list, setList] = useState([]);

    return (
        <div className="order-count">
            <SearchCondition setList={setList} />
            <OrderChart list={list} />
        </div>
    );
}

export default OrderCount;
