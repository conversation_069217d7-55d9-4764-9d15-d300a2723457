import React, { useEffect, useRef } from "react";
import { DTEditForm, DTEditFormRefs, DTEditFormConfigs } from "@dt/components";
import { Modal, message } from "antd";
// import { depFn } from "../../warehouse-address-manage/component/add-modal";
import { lib } from "react-single-app";
import moment from "moment";
export default ({ open, closeFn }) => {
    const configs: DTEditFormConfigs = [
        {
            type: "SELECT",
            fProps: {
                label: "导入模版",
                name: "funcCode",
                labelCol: { span: 6 },
                rules: [{ required: true, message: "请选择导入模版" }],
            },
            dataUrl: "/ccs/reconciliation/order/listImportTemplate",
            list: [],
        },
        {
            type: "SELECT",
            fProps: {
                label: "区内企业",
                name: "areaCompanyId",
                labelCol: { span: 6 },
                rules: [{ required: true, message: "请选择区内企业" }],
            },
            dataUrl: "/ccs/company/listWithSBQYAll",
            list: [],
        },
        {
            type: "DATE",
            fProps: {
                label: "对账日期",
                name: "reconciliationTime",
                labelCol: { span: 6 },
                rules: [{ required: true, message: "请选择对账日期" }],
            },
        },
    ];
    const ref = useRef<DTEditFormRefs>();

    const submit = () => {
        ref.current.form.validateFields().then(values => {
            values.reconciliationTime = values.reconciliationTime.valueOf();
            if (values.importUrl && values.importUrl[0]) {
                values.url = values.importUrl[0].url;
                delete values.importUrl;
            }
            let arr = [];
            for (let i in values) {
                if (i !== "funcCode") {
                    arr.push(i + "=" + values[i]);
                }
            }
            const str = arr.join("&");
            let importExtendParamBase64 = window.btoa(str + "");
            const url = `/excel/import-data?page_title=导入&code=${encodeURIComponent(
                values.funcCode,
            )}&importExtendParam=${encodeURIComponent(importExtendParamBase64)}`;
            lib.openPage(url, () => {
                closeFn(true);
            });
            closeFn();
            // lib.request({
            //     url: "/ccs/reconciliation/order/excelImport",
            //     data: values,
            //     success(data) {
            //         message.success("提交成功");
            //         closeFn(true);
            //     },
            // });
        });
    };

    useEffect(() => {
        if (open) {
            ref.current.form.resetFields();
            ref.current.form.setFieldsValue({
                reconciliationTime: moment(),
            });
        }
    }, [open]);

    return (
        <Modal
            open={open}
            title={"导入"}
            onCancel={() => {
                ref.current.form.resetFields();
                closeFn();
            }}
            onOk={() => {
                submit();
            }}>
            <div style={{ marginBottom: "10px" }}>
                请按照平台WMS导出的出库Excel操作导入请勿重复导入同一对账日期的Excel（对账日期代表出库日期）
            </div>
            <DTEditForm
                configs={configs}
                layout={{
                    mode: "appoint",
                    colNum: 1,
                }}
                ref={ref}
            />
        </Modal>
    );
};
