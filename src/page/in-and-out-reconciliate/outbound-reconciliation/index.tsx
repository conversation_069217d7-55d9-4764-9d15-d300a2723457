import React, { useRef, useState, useEffect } from "react";
import { SearchList } from "@dt/components";
import { DDYObject, getConfigDataUtils, lib } from "react-single-app";
import { Button, Modal, message, Popover, Tabs, Tooltip, Space, Tag } from "antd";
import axios from "axios";
import ExportModal from "./export-modal";
import LogModal from "./log-modal";
// import NewModal from "@/components/NewModal";
// import { request } from "@dt/networks";
// import FeedbackModal from "./feedback-modal";
// import LogModal from "./log-modal";

enum orderEnum {
    // :
    /**
     * 解析中
     */
    "ANALYSIS_ING" = "ANALYSIS_ING",
    /**
     * 已作废
     */
    "DISCARD" = "DISCARD",
    /**
     * 解析失败
     */
    "ANALYSIS_FAIL" = "ANALYSIS_FAIL",
    /**
     * 对账异常
     */
    "EXCEPTION" = "EXCEPTION",
    /**
     * 对账完成
     */
    "FINISH" = "FINISH",
}

export default () => {
    const [feedbackOpen, setFeedbackOpen] = useState(false);
    // const [ids, setIds] = useState();
    const searchListRef = useRef(null);
    // const selecteds = useRef([]);
    const [selecteds, setSelecteds] = useState([]);
    const [logOpen, setLogOpen] = useState(false);
    const [tabsData, setTabsData] = useState([]);
    const [exportOpen, setExportOpen] = useState(false);
    const [row, setRow] = useState(null);
    const modalRef = useRef({});
    const getConfig = async () => {
        let url = getConfigDataUtils.getDataUrlByDataId(900);
        const res = await axios.get(url);
        return res.data.data;
    };

    const getTabs = () => {
        // console.log("getTabs", searchListRef.current);
        const data = searchListRef.current?.getSearchData() || {};
        lib.request({
            url: "/ccs/reconciliation/order/statusCount",
            data: {
                ...data,
            },
            success(data) {
                setTabsData(data);
            },
        });
    };
    useEffect(() => {
        // getDatas();
        // getTabs();
        setTimeout(() => {
            getTabs();
        }, 500);
    }, []);

    return (
        <>
            <SearchList
                ref={searchListRef}
                searchConditionConfig={{
                    size: "middle",
                }}
                paginationConfig={{
                    showPosition: "bottom",
                }}
                scrollMode="tableScroll"
                headerMode={"sticky"}
                // preventLoadProcess={() => true}
                getConfig={getConfig}
                renderLeftOperation={() => {
                    return (
                        <>
                            <Button
                                onClick={() => {
                                    if (!selecteds.length) {
                                        return message.error("请勾选数据");
                                    }
                                    Modal.confirm({
                                        title: "确认",
                                        content: "确定标记处理此对账任务吗？",
                                        onOk: () => {
                                            lib.request({
                                                url: "/ccs/reconciliation/order/markDeal",
                                                data: {
                                                    ids: selecteds.map(item => item.id).join(","),
                                                },
                                                success(data) {
                                                    setSelecteds([]);
                                                    searchListRef.current.load();
                                                    searchListRef.current.resetSelected();
                                                    message.success("操作完成");
                                                },
                                            });
                                        },
                                    });
                                }}>
                                标记处理
                            </Button>
                            <Button
                                onClick={() => {
                                    if (!selecteds.length) {
                                        return message.error("请勾选数据");
                                    }
                                    Modal.confirm({
                                        title: "确认",
                                        content: "确定重新对账吗？",
                                        onOk: () => {
                                            lib.request({
                                                url: "/ccs/reconciliation/order/refresh",
                                                data: {
                                                    ids: selecteds.map(item => item.id).join(","),
                                                },
                                                success(data) {
                                                    setSelecteds([]);
                                                    searchListRef.current.load();
                                                    searchListRef.current.resetSelected();
                                                    message.success("操作完成");
                                                },
                                            });
                                        },
                                    });
                                }}>
                                重新对账
                            </Button>
                        </>
                    );
                }}
                renderRightOperation={() => {
                    return (
                        <>
                            <Button
                                onClick={() => {
                                    setExportOpen(true);
                                }}>
                                导入
                            </Button>
                        </>
                    );
                }}
                onSearchReset={() => {
                    getTabs();
                }}
                onSearch={() => {
                    getTabs();
                    searchListRef.current.resetSelected();
                }}
                renderTableTopView={() => {
                    return (
                        <>
                            <Tabs
                                onChange={e => {
                                    searchListRef.current.changeImmutable({
                                        status: e && e !== "null" ? e : null,
                                    });
                                }}>
                                {tabsData.map(item => {
                                    return (
                                        <Tabs.TabPane
                                            tab={
                                                item.status !== orderEnum.DISCARD
                                                    ? `${item.statusDesc}(${item.count || 0})`
                                                    : item.statusDesc
                                            }
                                            key={item.status}></Tabs.TabPane>
                                    );
                                })}
                            </Tabs>
                        </>
                    );
                }}
                tableCustomFun={{
                    operateFn: row => {
                        return (
                            <>
                                {[orderEnum.EXCEPTION, orderEnum.ANALYSIS_ING, orderEnum.ANALYSIS_FAIL].includes(
                                    row.status,
                                ) && (
                                    <Button
                                        type="link"
                                        onClick={() => {
                                            Modal.confirm({
                                                title: "确认",
                                                content: "确定作废当前订单吗?",
                                                onOk: () => {
                                                    lib.request({
                                                        url: "/ccs/reconciliation/order/discard",
                                                        data: {
                                                            id: row.id,
                                                        },
                                                        success(data) {
                                                            message.success("作废成功");
                                                            setSelecteds([]);
                                                            searchListRef.current.resetSelected();
                                                            searchListRef.current.load();
                                                        },
                                                    });
                                                },
                                            });
                                        }}>
                                        作废
                                    </Button>
                                )}

                                <Button
                                    type="link"
                                    onClick={() => {
                                        // setSelecteds([row]);
                                        setRow(row);
                                        setLogOpen(true);
                                    }}>
                                    日志
                                </Button>
                            </>
                        );
                    },
                    reconciliationSnFn: row => {
                        return (
                            <>
                                <span>{row.sn}</span>
                                <br />
                                <Space>
                                    {row.tagDescList.map(item => (
                                        <Tag color="#f50">{item}</Tag>
                                    ))}
                                </Space>
                            </>
                        );
                    },
                    statusDescFn: row => {
                        return (
                            <>
                                {row.statusDesc === "解析失败" ? (
                                    <Tooltip title={row.exceptionReason}>
                                        <span>{row.statusDesc}</span>
                                    </Tooltip>
                                ) : (
                                    row.statusDesc
                                )}
                            </>
                        );
                    },
                    unmatchedCountFn: row => {
                        return (
                            <span
                                style={{ color: "blue" }}
                                onClick={() => {
                                    lib.openPage(
                                        `/ccs/reconciliation-order?page_title=对账订单&reconciliationSn=${row.sn}`,
                                    );
                                }}>
                                {row.unmatchedCount}
                            </span>
                        );
                    },
                    differenceOrderCountFn: row => {
                        return (
                            <span
                                style={{ color: "blue" }}
                                onClick={() => {
                                    lib.openPage(
                                        `/ccs/reconciliation-order?page_title=对账订单&reconciliationSn=${row.sn}`,
                                    );
                                }}>
                                {row.differenceOrderCount}
                            </span>
                        );
                    },
                }}
                renderModal={() => {
                    return (
                        <>
                            <ExportModal
                                open={exportOpen}
                                closeFn={load => {
                                    setExportOpen(false);
                                    if (load) {
                                        searchListRef.current.load();
                                    }
                                }}
                            />
                            <LogModal
                                row={row}
                                open={logOpen}
                                closeFn={load => {
                                    setLogOpen(false);
                                    // setSelecteds([]);
                                    setRow(null);
                                    if (load) {
                                        setSelecteds([]);
                                        searchListRef.current.resetSelected();
                                        searchListRef.current.load();
                                    }
                                }}
                            />
                        </>
                    );
                }}
                onTableSelected={(row, selectedRows) => {
                    // selecteds.current = selectedRows;
                    setSelecteds(selectedRows);
                }}
            />
        </>
    );
};
