import React, { useRef, useState, useEffect } from "react";
import { SearchList } from "@dt/components";
import { DDYObject, getConfigDataUtils, lib } from "react-single-app";
import { Button, Modal, message, Popover, Tabs, Tooltip, Tag, Space } from "antd";
import axios from "axios";
import LogModal from "./log-modal";
import SolveModal from "./solve-modal";
// import NewModal from "@/components/NewModal";
// import { request } from "@dt/networks";
// import FeedbackModal from "./feedback-modal";
// import LogModal from "./log-modal";

enum exceptionTypeEnum {
    /**
     * 订单异常
     */
    ORDER_EXCEPTION = "ORDER_EXCEPTION",
    //     0
    //     :
    //     { name: "订单异常", id: "ORDER_EXCEPTION" }
    // 1
    /**
     * 料号不一致
     */
    DIFFERENT_PRODUCT_ID = "DIFFERENT_PRODUCT_ID",
    /**
     * 数量不一致
     */
    DIFFERENT_COUNT = "DIFFERENT_COUNT",
    /**
     * EMG映射异常
     */
    EMG_MAPPING_EXCEPTION = "EMG_MAPPING_EXCEPTION",
    /**
     * EMG未映射
     */
    EMG_NOT_MAPPING = "EMG_NOT_MAPPING",
}

export default () => {
    const searchListRef = useRef(null);
    const selectds = useRef([]);
    const [logOpen, setLogOpen] = useState(false);
    const [solveOpen, setSolveOpen] = useState(false);
    const [row, setRow] = useState(null);
    const [tabsData, setTabsData] = useState([]);
    const [tabVal, setTabVal] = useState(lib.getParam("reconciliationSn") ? "EXCEPTION" : "");
    const getConfig = async () => {
        let url = getConfigDataUtils.getDataUrlByDataId(902);
        const res = await axios.get(url);
        return res.data.data;
    };

    const getTabs = () => {
        const data = searchListRef.current?.getSearchData() || {};
        lib.request({
            url: "/ccs/reconciliation/item/statusCount",
            data: {
                ...data,
            },
            success(data) {
                setTabsData(data);
            },
        });
    };
    useEffect(() => {
        setTimeout(() => {
            if (lib.getParam("reconciliationSn")) {
                searchListRef.current.changeImmutable({
                    status: "EXCEPTION",
                });
            } else {
                message.error("请填写对账ID后查询数据");
                getTabs();
            }
        }, 500);
    }, []);
    return (
        <>
            <SearchList
                ref={searchListRef}
                searchConditionConfig={{
                    size: "middle",
                    resetParams: {
                        urlSearchParamClearable: true,
                        defaultSearchParamClearable: true,
                    },
                }}
                defaultLoading={false}
                paginationConfig={{
                    showPosition: "bottom",
                }}
                scrollMode="tableScroll"
                headerMode={"sticky"}
                // preventLoadProcess={() => true}
                getConfig={getConfig}
                renderLeftOperation={() => {
                    return (
                        <>
                            <Button
                                onClick={() => {
                                    if (!selectds.current.length) {
                                        return message.error("请勾选数据");
                                    }
                                    setSolveOpen(true);
                                }}>
                                标记解决
                            </Button>
                            <Button
                                onClick={() => {
                                    if (!selectds.current.length) {
                                        return message.error("请勾选数据");
                                    }
                                    Modal.confirm({
                                        title: "确认",
                                        content: "确定作废对账订单吗？",
                                        onOk: () => {
                                            lib.request({
                                                url: "/ccs/reconciliation/item/discard",
                                                data: {
                                                    ids: selectds.current.map(item => item.id).join(","),
                                                },
                                                success(data) {
                                                    selectds.current = [];
                                                    searchListRef.current.resetSelected();
                                                    searchListRef.current.load();
                                                    message.success("操作完成");
                                                },
                                            });
                                        },
                                    });
                                }}>
                                批量作废
                            </Button>
                        </>
                    );
                }}
                onSearchReset={() => {
                    setTabVal("");
                    getTabs();
                }}
                onSearch={() => {
                    getTabs();
                }}
                renderTableTopView={() => {
                    return (
                        <>
                            <Tabs
                                activeKey={tabVal as string}
                                onChange={e => {
                                    setTabVal(e);
                                    searchListRef.current.changeImmutable({
                                        status: e && e !== "null" ? e : null,
                                    });
                                }}>
                                {tabsData.map(item => {
                                    return (
                                        <Tabs.TabPane
                                            tab={
                                                !["DEAL", "DISCARD"].includes(item.status)
                                                    ? `${item.statusDesc}(${item.count || 0})`
                                                    : item.statusDesc
                                            }
                                            key={item.status === null ? "" : item.status}></Tabs.TabPane>
                                    );
                                })}
                            </Tabs>
                        </>
                    );
                }}
                tableCustomFun={{
                    operateFn: row => {
                        return (
                            <>
                                <Button
                                    type="link"
                                    onClick={() => {
                                        setRow(row);
                                        setLogOpen(true);
                                    }}>
                                    日志
                                </Button>
                            </>
                        );
                    },
                    reconciliationSnFn: row => {
                        return (
                            <>
                                <span>{row.reconciliationSn}</span>
                                <Space>
                                    {row.tagDescList.map(item => (
                                        <Tag color="#f50">{item}</Tag>
                                    ))}
                                </Space>
                            </>
                        );
                    },
                    statusDescFn: row => {
                        const color = {
                            异常: "red",
                            已解决: "green",
                            作废: "#333",
                        };
                        return <span style={{ color: color[row.statusDesc] }}>{row.statusDesc}</span>;
                    },
                    noDifferenceOrderCountFn: row => {
                        return <span style={{ color: "blue" }}>{row.noDifferenceOrderCount}</span>;
                    },
                    differenceOrderCountFn: row => {
                        return <span style={{ color: "blue" }}>{row.differenceOrderCount}</span>;
                    },
                    exceptionTypeDescFn: row => {
                        return (
                            <span>
                                {[
                                    exceptionTypeEnum.EMG_MAPPING_EXCEPTION,
                                    exceptionTypeEnum.ORDER_EXCEPTION,
                                    exceptionTypeEnum.EMG_NOT_MAPPING,
                                ].includes(row.exceptionType) ? (
                                    <Tooltip title={"处理完毕，可点击刷新解决异常"}>
                                        <span>{row.exceptionTypeDesc}</span>
                                    </Tooltip>
                                ) : (
                                    row.exceptionTypeDesc
                                )}
                            </span>
                        );
                    },
                }}
                renderModal={() => {
                    return (
                        <>
                            <LogModal
                                row={row}
                                open={logOpen}
                                closeFn={load => {
                                    setLogOpen(false);
                                    setRow(null);
                                    if (load) {
                                        searchListRef.current.load();
                                    }
                                }}
                            />
                            <SolveModal
                                ids={selectds.current.map(item => item.id)}
                                open={solveOpen}
                                closeFn={load => {
                                    setSolveOpen(false);
                                    if (load) {
                                        selectds.current = [];
                                        searchListRef.current.resetSelected();
                                        searchListRef.current.load();
                                    }
                                }}
                            />
                        </>
                    );
                }}
                onTableSelected={(row, selectedRows) => {
                    selectds.current = selectedRows;
                    console.log("onTableSelected:", selectds.current);
                }}
            />
        </>
    );
};
