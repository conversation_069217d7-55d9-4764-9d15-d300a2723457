import React, { useEffect, useState } from "react";
import { Modal, Table } from "antd";
import { lib } from "react-single-app";

export default ({ open, closeFn, row }) => {
    const [dataSource, setDataSource] = useState([]);
    const getData = () => {
        lib.request({
            url: "/ccs/reconciliation/item/listTrackLog",
            data: {
                id: row.id,
            },
            success(data) {
                setDataSource(data);
            },
        });
    };

    useEffect(() => {
        if (open) {
            console.log(row);
            getData();
        }
    }, [open]);

    return (
        <Modal
            width={800}
            title="日志"
            open={open}
            onOk={() => {
                closeFn();
            }}
            onCancel={() => {
                closeFn();
            }}>
            <Table
                columns={[
                    {
                        title: "对账状态",
                        dataIndex: "statusDesc",
                    },
                    {
                        title: "日志描述",
                        dataIndex: "logInfo",
                    },
                    {
                        title: "发生时间",
                        dataIndex: "createTime",
                    },
                    {
                        title: "操作人",
                        dataIndex: "operator",
                    },
                ]}
                dataSource={dataSource}></Table>
        </Modal>
    );
};
