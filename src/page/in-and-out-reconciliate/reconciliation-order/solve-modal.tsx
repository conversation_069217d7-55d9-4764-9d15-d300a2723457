import React, { useEffect, useRef } from "react";
import { DTEditForm, DTEditFormRefs, DTEditFormConfigs } from "@dt/components";
import { Modal, message } from "antd";
// import { depFn } from "../../warehouse-address-manage/component/add-modal";
import { lib } from "react-single-app";
export default ({ open, closeFn, ids }) => {
    const configs: DTEditFormConfigs = [
        {
            type: "TEXTAREA",
            fProps: {
                label: "备注",
                name: "remark",
                labelCol: { span: 6 },
                rules: [{ required: true, message: "请对异常备注" }],
            },
            cProps: {
                showCount: true,
                maxLength: 255,
                placeholder: "请对异常备注",
            },
        },
    ];
    const ref = useRef<DTEditFormRefs>();

    const submit = () => {
        ref.current.form.validateFields().then(values => {
            values.idList = ids;
            lib.request({
                url: "/ccs/reconciliation/item/markDeal",
                data: values,
                success(data) {
                    message.success("提交成功");
                    closeFn(true);
                },
            });
        });
    };

    useEffect(() => {
        if (open) {
            ref.current.form.resetFields();
        }
    }, [open]);

    return (
        <Modal
            open={open}
            title={"标记解决"}
            onCancel={() => {
                ref.current.form.resetFields();
                closeFn();
            }}
            onOk={() => {
                submit();
            }}>
            <div style={{ textAlign: "left", margin: "0 10px 20px 10px", paddingLeft: "10%" }}>确定异常已解决了吗</div>
            <DTEditForm
                configs={configs}
                layout={{
                    mode: "appoint",
                    colNum: 1,
                }}
                ref={ref}
            />
        </Modal>
    );
};
