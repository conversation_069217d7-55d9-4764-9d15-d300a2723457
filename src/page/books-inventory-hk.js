import React, { useState, useEffect } from "react";
import { ConfigCenter, Uploader, lib, event } from "react-single-app";
import { Checkbox, Button, Modal, Switch, message, Space } from "antd";
import moment from "moment";
import BottomTag from "../components/BottomTag";
import EditTable from "../components/EditTable";

class App extends ConfigCenter {
    constructor(props) {
        super(props);
        this.state.modalTitle = "新增账册";
        // 编辑/新建url
        this.state.upUrl = "/ccs/customsBookItem/upset";
        // 获取详情url
        this.state.detailUrl = "/ccs/customsBookItem/detail";
        this.state.tabsList = [
            {
                tab: "账册库存详情",
                dataList: [],
                type: "TABLE",
                columns: [
                    {
                        dataIndex: "goodsSeqNo",
                        formType: "INPUT",
                        required: true,
                        title: "商品序号",
                        width: 140,
                    },
                    {
                        dataIndex: "productId",
                        formType: "INPUT",
                        required: true,
                        title: "商品料号",
                        width: 200,
                    },
                    {
                        dataIndex: "hsCode",
                        formType: "SELECT",
                        required: true,
                        title: "HS编码",
                        width: 400,
                        extra: "/ccs/customs/listHs",
                    },
                    {
                        dataIndex: "goodsName",
                        formType: "INPUT",
                        required: true,
                        title: "商品名称",
                        width: 600,
                    },
                    {
                        dataIndex: "goodsModel",
                        formType: "INPUT",
                        required: true,
                        title: "商品规格型号",
                        width: 140,
                    },
                    {
                        dataIndex: "originCountry",
                        formType: "SELECT",
                        required: true,
                        title: "国别",
                        width: 140,
                        extra: "/ccs/customs/listCountry",
                    },
                    {
                        dataIndex: "goodsUnit",
                        formType: "SELECT",
                        required: true,
                        title: "申报计量单位",
                        width: 140,
                        extra: "/ccs/customs/listUom",
                    },
                    {
                        dataIndex: "firstUnit",
                        formType: "SELECT",
                        required: true,
                        title: "法定计量单位",
                        width: 140,
                        extra: "/ccs/customs/listUom",
                    },
                    {
                        dataIndex: "secondUnit",
                        formType: "SELECT",
                        title: "第二法定计量单位",
                        width: 160,
                        extra: "/ccs/customs/listUom",
                    },
                    {
                        dataIndex: "declarePrice",
                        formType: "INPUT",
                        title: "申报单价金额",
                        width: 140,
                    },
                    {
                        dataIndex: "currCode",
                        formType: "INPUT",
                        title: "申报币制",
                        width: 140,
                    },
                    {
                        dataIndex: "invtNo",
                        formType: "INPUT",
                        title: "记账清单编号",
                        width: 140,
                    },
                    {
                        dataIndex: "invtGoodsNo",
                        formType: "INPUT",
                        title: "记账清单商品序号",
                        width: 200,
                    },
                    {
                        dataIndex: "customsBookId",
                        formType: "SELECT",
                        title: "账册编号",
                        width: 200,
                        extra: "/ccs/customsBook/listAllBookNo",
                    },
                    {
                        dataIndex: "inDate",
                        formType: "DATE",
                        title: "最近入仓（核增）日期",
                        width: 240,
                    },
                    {
                        dataIndex: "avgPrice",
                        formType: "INPUT",
                        title: "平均美元单价",
                        width: 200,
                    },
                    {
                        dataIndex: "totalAmt",
                        formType: "INPUT",
                        title: "库存美元总价",
                        width: 140,
                    },
                    {
                        dataIndex: "inQty",
                        formType: "INPUT",
                        title: "入仓数量",
                        width: 140,
                    },
                    {
                        dataIndex: "inLegalQty",
                        formType: "INPUT",
                        title: "入仓法定数量",
                        width: 200,
                    },
                    {
                        dataIndex: "inSecondLegalQty",
                        formType: "INPUT",
                        title: "第二入仓法定数量",
                        width: 200,
                    },
                    {
                        dataIndex: "remark",
                        formType: "INPUT",
                        title: "备注",
                        width: 200,
                    },
                ],
            },
        ];
    }
    componentDidMount() {
        this.getConfigList();
    }

    renderProductId(row) {
        return (
            <span className="link" onClick={() => this.showDetail(row)}>
                {row.productId}
            </span>
        );
    }

    getConfigList() {
        let tabsList = this.state.tabsList;
        tabsList.map(item => {
            if (item.columns) {
                item.columns.map(ite => {
                    ite.formType === "SELECT" &&
                        lib.request({
                            url: ite.extra,
                            needMask: false,
                            success: res => {
                                ite.selectData = res || [];
                                this.setState({
                                    tabsList,
                                });
                            },
                        });
                });
            }
        });
        this.setState({
            tabsList,
        });
    }

    // 从换行字符串转化成用，连接的字符串
    transValue(value) {
        if (typeof value !== "string") return value;
        if (!value) return "";
        if (value.indexOf("\n") === -1) return value.trim().replace(/ /g, "\t");
        return value
            .split("\n")
            .map(item => item.trim().replace(/ /g, "\t"))
            .join(",");
    }

    renderLeftOperation() {
        return (
            <Space>
                <Button onClick={() => this.batchOperation(1)}>批量启用</Button>
                <Button onClick={() => this.batchOperation(0)}>批量禁用</Button>
            </Space>
        );
    }

    // 禁用启用
    batchOperation(enable) {
        let rows = this.state.dataList.filter(item => item.checked),
            list = [];
        if (rows.length === 0) {
            message.warning("请选择数据");
            return;
        }
        rows.map(item => list.push(item.id));
        lib.request({
            url: "/ccs/customsBookItem/batchEnable",
            method: "POST",
            data: {
                ids: list,
                enable,
            },
            needMask: true,
            success: res => {
                if (res.errorMessage) {
                    message.error(res.errorMessage);
                } else {
                    message.success("修改成功");
                    this.load(true);
                }
            },
        });
    }

    spanClick(e, row) {
        let modal = Modal.confirm({
            cancelText: "取消",
            okText: "确定",
            content: `确认${row.enable === 1 ? "禁用" : "启用"}吗？`,
            onOk: res => {
                lib.request({
                    url: "/ccs/customsBookItem/batchEnable",
                    method: "POST",
                    data: {
                        ids: [row.id],
                        enable: row.enable ? 0 : 1,
                    },
                    needMask: true,
                    success: res => {
                        if (res.errorMessage) {
                            message.error(res.errorMessage);
                        } else {
                            message.success("修改成功");
                            this.load(true);
                            modal.destroy();
                        }
                    },
                });
            },
            onCancel() {
                modal.destroy();
            },
        });
    }
    tabSubmit(values) {
        console.log(this.state.tabsList[0].dataList[0].enable);
        if (this.state.tabsList[0].dataList[0].enable === 1) {
            message.warning("启用状态无法编辑");
            return;
        }
        values.id = this.state.id;
        lib.request({
            url: "/ccs/customsBookItem/upset",
            method: "POST",
            data: values,
            needMask: true,
            success: res => {
                this.load(true);
                this.clearBottomTabInfo();
                message.success("修改成功");
            },
        });
    }

    renderDetail(tabsList, id) {
        let props = {
            tabsList,
            showSubmitBtn: false,
            id: tabsList[0].dataList[0].id,
            tabSubmit: this.tabSubmit.bind(this),
        };
        return <BottomTag {...props}></BottomTag>;
    }

    showDetail(row) {
        this.setState({
            id: row.id,
        });
        this.getConfigList();
        lib.request({
            url: this.state.detailUrl,
            data: {
                id: row.id,
            },
            needMask: true,
            success: res => {
                let result = JSON.parse(JSON.stringify(res));
                result.inDate = moment(result.inDate);
                let { tabsList } = this.state;
                tabsList.map(item => {
                    item.dataList = [result];
                });
                this.setDetailData(tabsList, result.id);
            },
        });
    }

    enableStatus(row) {
        return (
            <React.Fragment>
                {row.enable === 1 && <Switch checked={row.enable} onChange={e => this.spanClick(e, row)}></Switch>}
                {row.enable === 0 && <Switch checked={row.enable} onChange={e => this.spanClick(e, row)}></Switch>}
            </React.Fragment>
        );
    }
}
export default App;
