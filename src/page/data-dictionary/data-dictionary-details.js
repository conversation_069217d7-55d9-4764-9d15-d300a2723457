import React, { useEffect } from "react";
import { lib, SearchList, getConfigDataUtils } from "react-single-app";
import axios from "axios";
import { Modal, Button, message, Space, Form, Input } from "antd";

export default class DataDictionaryDetails extends SearchList {
    constructor(props) {
        super(props);
        this.state.type = lib.getParam("type");
        this.state.datatitle = lib.getParam("datatitle");
    }

    getConfig() {
        let url = getConfigDataUtils.getDataUrlByDataId(563);
        return axios.get(url).then(res => res.data.data);
    }

    renderRightOperation() {
        return (
            <>
                <Button
                    type="primary"
                    onClick={() => {
                        this.setState({
                            visible: true,
                        });
                    }}>
                    新增
                </Button>
            </>
        );
    }

    renderModal() {
        return (
            <ConfigureModal
                visible={this.state.visible}
                row={this.state.row}
                type={this.state.type}
                datatitle={this.state.datatitle}
                close={success => {
                    if (success) {
                        this.load();
                    }
                    this.setState({
                        visible: false,
                        row: null,
                    });
                }}
            />
        );
    }

    getOperation(row) {
        return (
            <Space style={{ display: "flex" }}>
                <a
                    onClick={() => {
                        this.setState({
                            visible: true,
                            row: row,
                        });
                    }}>
                    编辑
                </a>
                <a
                    onClick={() => {
                        this.deleteHandle(row.id);
                    }}>
                    删除
                </a>
            </Space>
        );
    }

    beforeImport() {
        let enableImport = false;
        return {
            disableImport: enableImport,
            importExtendParam: `type=${this.state.type}`,
        };
    }
    deleteHandle = id => {
        let modal = Modal.confirm({
            title: "提示",
            content: "确定要删除吗？",
            onOk: () => {
                lib.request({
                    url: "/ccs/dictionary/delete",
                    data: { id },
                    needMask: true,
                    success: res => {
                        message.success("删除成功");
                        this.load();
                        modal.destroy();
                    },
                });
            },
            onCancel: () => modal.destroy(),
        });
    };
}

function ConfigureModal({ visible, close, row, type, datatitle }) {
    useEffect(() => {
        if (visible) {
            form.setFieldsValue(row);
        } else {
            form.resetFields();
        }
    }, [visible]);
    const [form] = Form.useForm();

    const handleFinish = val => {
        if (!!row) {
            lib.request({
                url: "/ccs/dictionary/update",
                data: { ...val, id: row.id, type: type },
                needMask: true,
                success: res => {
                    message.success("编辑成功");
                    close(true);
                },
            });
        } else {
            lib.request({
                url: "/ccs/dictionary/insert",
                data: { ...val, type: type },
                success: res => {
                    message.success("新增成功");
                    close(true);
                },
            });
        }
    };
    const handleCancel = () => {
        close();
    };
    const handleOk = () => {
        form.submit();
    };
    return (
        (<Modal
            onOk={handleOk}
            open={visible}
            onCancel={handleCancel}
            title={row ? `编辑${datatitle}配置` : `新增${datatitle}配置`}>
            <Form onFinish={handleFinish} form={form} labelCol={{ span: 6 }} wrapperCol={{ span: 16 }}>
                <Form.Item
                    label="编码名称"
                    name="name"
                    rules={[
                        {
                            required: true,
                            message: "请输入编码名称",
                        },
                    ]}>
                    <Input />
                </Form.Item>
                <Form.Item
                    label="数据值"
                    name="code"
                    rules={[
                        {
                            required: true,
                        },
                    ]}>
                    <Input />
                </Form.Item>
            </Form>
        </Modal>)
    );
}
