import React, { useEffect, useState } from "react";
import { lib, SearchList, getConfigDataUtils, ConfigFormCenter } from "react-single-app";
import axios from "axios";
import { Modal, Button, message, Space, Form, Input, Switch, Select } from "antd";

export default class DataDictionary extends SearchList {
    constructor(props) {
        super(props);
        this.state.buttons = [];
    }
    getConfig() {
        let url = getConfigDataUtils.getDataUrlByDataId(624);
        return axios.get(url).then(res => res.data.data);
    }

    renderRightOperation() {
        return (
            <Space>
                <Button
                    onClick={() => {
                        this.addHandle();
                    }}
                    type="primary">
                    新增
                </Button>
            </Space>
        );
    }

    upset(row, data) {
        lib.request({
            url: "/ccs/dictionary/enableSwitch",
            method: "POST",
            data: {
                id: row.id,
                ...data,
            },
            needMask: true,
            success: () => {
                this.load(true);
                message.success("操作成功");
            },
        });
    }

    enableFunc(row) {
        let content = row.enable === 1 ? "是否禁用?" : "是否启用";
        Modal.confirm({
            cancelText: "取消",
            okText: "确定",
            title: "提示",
            content: content,
            onOk: () => {
                let enable;
                if (row.enable === 2) {
                    enable = 1;
                } else if (row.enable === 1) {
                    enable = 2;
                }
                this.upset(row, { enable: enable });
            },
        });
    }

    enableStatus(row) {
        return (
            <React.Fragment>
                <Switch
                    checked={row.enable === 1}
                    checkedChildren="ON"
                    unCheckedChildren="OFF"
                    onChange={() => this.enableFunc(row)}
                />
            </React.Fragment>
        );
    }

    getOperation(row) {
        return (
            <Space style={{ display: "flex" }}>
                {row.enable == 2 ? <a onClick={() => this.editHandle(row)}>编辑</a> : null}
                <a
                    onClick={() => {
                        this.DataDictionaryDetails(row);
                    }}>
                    查看
                </a>
            </Space>
        );
    }
    editHandle(row) {
        this.setState({
            showModal: true,
            rowData: row,
        });
    }

    addHandle() {
        this.setState({
            showModal: true,
        });
    }
    DataDictionaryDetails = row => {
        lib.openPage(`/data-dictionary-details?type=${row.code}&page_title=数据字典详情&datatitle=${row.name}`, () =>
            this.load(),
        );
    };

    renderModal() {
        return (
            <React.Fragment>
                {
                    <EditModal
                        rowData={this.state.rowData}
                        dialogClose={ok => {
                            this.setState({
                                rowData: null,
                                showModal: false,
                            });
                            if (ok) {
                                this.load();
                            }
                        }}
                        showModal={this.state.showModal}
                    />
                }
            </React.Fragment>
        );
    }
}
function EditModal({ rowData, showModal, dialogClose }) {
    const ref = React.useRef();
    const handleCancel = () => {
        dialogClose(false);
    };
    const handleOk = () => {
        ref.current.submitForm();
    };
    const beforeSubmit = values => {
        values.type = "type";
        if (rowData) values.id = rowData.id;
        return values;
    };
    const onConfigLoadSuccess = config => {
        config.baseInfo.children.map(item => {
            if (item.type === "single-select" && !item.fromParam) {
                ref.current.initSelect(item);
            }
        });
        if (rowData) {
            ref.current.setMergeDetail(rowData);
        }
    };
    const onSubmitSuccess = () => {
        message.success(rowData ? "编辑数据成功" : "添加数据成功");
        dialogClose(true);
    };
    return (
        (<Modal
            destroyOnClose
            onCancel={handleCancel}
            onOk={handleOk}
            title={rowData ? "编辑数据" : "添加数据"}
            open={showModal}>
            <ConfigFormCenter
                ref={ref}
                confData={data}
                submitUrl={rowData ? "/ccs/dictionary/update" : "/ccs/dictionary/insert"}
                beforeSubmit={beforeSubmit}
                onConfigLoadSuccess={onConfigLoadSuccess}
                onSubmitSuccess={onSubmitSuccess}
            />
        </Modal>)
    );
}

const data = {
    baseInfo: {
        children: [
            {
                label: "数据名称",
                editEnable: true,
                name: "name",
                type: "textInput",
                rules: [
                    {
                        required: true,
                    },
                ],
            },
            {
                label: "数据类型",
                editEnable: true,
                name: "mainType",
                type: "single-select",
                from: "/ccs/dictionary/listMainType",
                rules: [
                    {
                        required: true,
                    },
                ],
            },
            {
                label: "数据标识",
                editEnable: true,
                name: "code",
                type: "textInput",
                rules: [
                    {
                        required: true,
                    },
                ],
            },
        ],
        label: "",
        name: "baseInfo",
        isGroup: true,
        className: "baseInfo",
    },
};
