import React, { useState, useEffect, useImperativeHandle } from "react";
import { Tabs } from "antd";
import { lib } from "react-single-app";

export const DataDictionaryType = {
    /**关区*/
    PORT: "port",
    /**保函关区*/
    GUARANTEECUSTOMS: "guaranteeCustoms",
    /**原产国*/
    COUNTRY: "country",
    /**申报单位*/
    UOM: "uom",
    /**币值*/
    CURRENCY: "currency",
};
export const TabsArray = [
    { id: DataDictionaryType.PORT, name: "关区" },
    { id: DataDictionaryType.GUARANTEECUSTOMS, name: "保函关区" },
    { id: DataDictionaryType.COUNTRY, name: "原产国" },
    { id: DataDictionaryType.UOM, name: "申报单位" },
    { id: DataDictionaryType.CURRENCY, name: "币值" },
];

export function StatusTabs({ tabOnChange }) {
    const [activeKey, setActiveKey] = useState();
    return (
        <Tabs
            defaultActiveKey={DataDictionaryType.PORT}
            activeKey={activeKey}
            onChange={e => {
                setActiveKey(e);
                tabOnChange(e);
            }}>
            {TabsArray.map(item => (
                <Tabs.TabPane tab={item.name} key={item.id} />
            ))}
        </Tabs>
    );
}
