import React from "react";
import { Config<PERSON>enter, lib, Uploader, getConfigDataUtils, SearchList, HOC, event } from "react-single-app";
import "../../shop-good.less";
import { Button, Modal, message, Tabs, Table, Tag, Space, Input, Tooltip, Image as AntImage } from "antd";
import J<PERSON>Z<PERSON> from "jszip";
import FileSaver from "file-saver";
import moment from "moment";
import axios from "axios";
import SyncCancelOrderModal from "./sync-cancel-order";
import "./cancel-order-manage.less";

const TabPane = Tabs.TabPane;
const { TextArea } = Input;
@HOC.mapAuthButtonsToState({ buttonCodeArr: [] })
class App extends SearchList {
    constructor(props) {
        super(props);
        this.state.count = {
            cacelCount: 0,
            rejectCount: 0,
            returnCount: 0,
            totalCount: 0,
        };
        this.state.tabsList = [
            {
                title: "全部",
                data: {
                    overTimeSearchType: null,
                },
                count: 0,
            },
            {
                title: "撤单待取消超3d",
                data: {
                    overTimeSearchType: "cancelCalloffWaiting3dTo5d",
                },
                count: 0,
            },
            {
                title: "撤单待取消超5d",
                data: {
                    overTimeSearchType: "cancelCalloffWaiting5dAndMore",
                },
                count: 0,
            },
            {
                title: "退货待取消超7d",
                data: {
                    overTimeSearchType: "refundCalloffWaiting7dTo10d",
                },
                count: 0,
            },
            {
                title: "退货待取消超10d",
                data: {
                    overTimeSearchType: "refundCalloffWaiting10dAndMore",
                },
                count: 0,
            },
            {
                title: "取消中超24h",
                data: {
                    overTimeSearchType: "calloffing24hTo36h",
                },
                count: 0,
            },
            {
                title: "取消中超36h",
                data: {
                    overTimeSearchType: "calloffing36hAndMore",
                },
                count: 0,
            },
        ];
        this.state.tabsIndex = "0";
        this.onSearchReset = this.onSearchReset.bind(this);
    }
    getConfig() {
        let url = getConfigDataUtils.getDataUrlByDataId(595);
        return axios.get(url).then(res => res.data.data);
    }
    componentDidMount() {
        const beginCreateTime = moment().subtract(2, "month").format("YYYY-MM-DD");
        const endCreateTime = moment().format("YYYY-MM-DD");
        this.changeImmutable({
            beginCreateTime: Number(moment(beginCreateTime + " 00:00:00.000").format("x")),
            endCreateTime: Number(moment(endCreateTime + " 23:59:59.999").format("x")),
        });
        this.getCounts({
            beginCreateTime: Number(moment(beginCreateTime + " 00:00:00.000").format("x")),
            endCreateTime: Number(moment(endCreateTime + " 23:59:59.999").format("x")),
        });
        event.on("onSearchReset", this.onSearchReset);
    }
    componentWillUnmount() {
        event.off("onSearchReset", this.onSearchReset);
    }
    onSearchReset() {
        this.setState({
            tabsIndex: "0",
        });
    }

    onSearch(search) {
        // lib.request({
        //     url: "/ccs/calloff/calloffOrderCount",
        //     data: search,
        //     success: res => {
        //         this.setState({
        //             count: res,
        //         });
        //     },
        // });
        this.getCounts();
    }

    getCounts() {
        lib.request({
            url: "/ccs/calloff/inventoryCalloffOverTimeCount",
            methods: "post",
            data: { ...this.state.search },
            success: res => {
                const { tabsList } = this.state;
                tabsList[1].count = res.cancelCalloffWaiting3dTo5d || 0;
                tabsList[2].count = res.cancelCalloffWaiting5dAndMore || 0;
                tabsList[3].count = res.refundCalloffWaiting7dTo10d || 0;
                tabsList[4].count = res.refundCalloffWaiting10dAndMore || 0;
                tabsList[5].count = res.calloffing24hTo36h || 0;
                tabsList[6].count = res.calloffing36hAndMore || 0;
                this.setState({ tabsList: [...tabsList] });
            },
        });
    }

    cancelOrder(row) {
        this.setState({
            calloffVisible: true,
            id: row.id,
        });
    }

    rejectOrder(row) {
        this.setState({
            rejectVisible: true,
            id: row.id,
        });
    }

    getCheckedRows() {
        return this.state.selectedRows;
    }
    // 海关售后回执
    getCusAfterSalesCallback(row) {
        return (
            <Tooltip title={row.cusAfterSalesCallbackDetail}>
                <span>{row.cusAfterSalesCallbackDesc}</span>
            </Tooltip>
        );
    }
    showTrackLog(row) {
        return (
            <div>
                <a
                    onClick={() => {
                        lib.openPage(`/declaration-manage-detail?orderId=${row.orderId}&page_title=申报单详情`, () =>
                            this.load(),
                        );
                    }}>
                    {row.declareOrderNo}
                </a>
                <br />
                {row.orderTagList &&
                    row.orderTagList.map(items => {
                        return (
                            <Tag color={"blue"} key={items}>
                                {items}
                            </Tag>
                        );
                    })}
                {row.label && <Tag color={"red"}>{row.label}</Tag>}
            </div>
        );
    }

    cancellation(row) {
        let modal = Modal.confirm({
            cancelText: "取消",
            okText: "确定",
            title: "提示",
            content: `确定撤单吗？`,
            onOk: () => {
                lib.request({
                    url: "/ccs/calloff/cancelCalloff",
                    data: {
                        id: row.id,
                    },
                    needMask: true,
                    success: res => {
                        message.success("撤单成功");
                        this.load(true);
                        modal.destroy();
                    },
                });
            },
            onCancel: () => {
                modal.destroy();
            },
        });
    }

    returnGood(row) {
        let modal = Modal.confirm({
            cancelText: "取消",
            okText: "确定",
            title: "提示",
            content: `确定退货吗？`,
            onOk: () => {
                lib.request({
                    url: "/ccs/calloff/returnCalloff",
                    data: {
                        id: row.id,
                    },
                    needMask: true,
                    success: res => {
                        message.success("退货成功");
                        this.state.selectedRows = [];
                        this.load(true);
                        modal.destroy();
                    },
                });
            },
            onCancel: () => {
                this.setState({
                    selectedIdList: [],
                    selectedRows: [],
                });
                modal.destroy();
            },
        });
    }

    returnGoods(row) {
        // let list = this.state.generateAsync.filter((item) => item.checked);
        let list = this.getCheckedRows();
        let sns = "";
        if (list && list.length) {
            list.map(item => {
                sns += item.orderSn + ",";
            });
            sns = sns.slice(0, -1);
        } else {
            message.warning("请选择数据");
            return;
        }
        this.setState({
            declareIds: sns,
            returnGoodsVisible: true,
        });
    }

    renderOperationTopView() {
        const { tabsList, tabsIndex } = this.state;
        return (
            <Tabs
                activeKey={tabsIndex}
                onChange={item => {
                    this.setState({
                        tabsIndex: item,
                    });
                    this.changeImmutable({ ...tabsList[item].data });
                    // this.getCounts(tabsList[item].data);
                }}>
                {tabsList.map((item, index) => (
                    <TabPane key={index} tab={index > 0 ? `${item.title}(${item.count})` : `${item.title}`}></TabPane>
                ))}
            </Tabs>
        );
    }

    renderLeftOperation() {
        let { count, buttons } = this.state;
        return (
            <React.Fragment>
                <Space>
                    <Button
                        onClick={row => {
                            this.returnGoods(row);
                        }}>
                        退货
                    </Button>
                    <Button type="primary" onClick={() => this.cancelLations()}>
                        撤单
                    </Button>
                    <Button onClick={() => this.batchDownload()}>批量下载</Button>
                    <Button onClick={() => this.syncCancelOrder()}>同步取消单</Button>
                    {buttons.includes("BATCH-CANCEL") && <Button onClick={() => this.batchCancel()}>批量驳回</Button>}
                    {/* <span style={{ paddingLeft: 20 }}>
                        当前撤单待取消 {count.cancelOrderToCancel} , 撤单取消中 {count.cancelOrderCanceling} ,
                        退货待取消 {count.refundToCancel} , 退货取消中
                        {count.refundCanceling}
                    </span> */}
                </Space>
            </React.Fragment>
        );
    }

    batchCancel() {
        const { selectedIdList } = this.state;
        if (selectedIdList.length === 0) {
            return message.warning("请选择至少一条数据");
        }
        this.setState({
            batchRejectVisible: true,
            batchRejectReason: "",
        });
    }

    async generatorZip(list, modal) {
        lib.wait();
        const zip = new JSZip();
        const cache = {};
        const promiseList = [];
        await list.forEach(ite => {
            ite.picList.map(item => {
                const promise = this.getFile(item).then(({ data, src }) => {
                    let src_arr = src.split(".");
                    const file_name = `${ite.logisticsNo}-${moment().format("YYYY-MM-DD")}.${
                        src_arr[src_arr.length - 1]
                    }`;
                    zip.file(file_name, data, {
                        binary: true,
                    });
                    cache[file_name] = data;
                });
                promiseList.push(promise);
            });
        });
        Promise.all(promiseList).then(() => {
            zip.generateAsync({
                type: "blob",
            }).then(content => {
                lib.waitEnd();
                modal.destroy();
                FileSaver.saveAs(content, "取消凭证.zip");
            });
        });
    }

    getFile = url => {
        return new Promise((resolve, reject) => {
            axios
                .get(url, {
                    headers: {
                        "Cache-control": "no-cache",
                    },
                    responseType: "arraybuffer",
                })
                .then(data => {
                    resolve({ data: data.data, src: url });
                })
                .catch(error => reject(error));
        });
    };

    batchDownload() {
        let list = this.getCheckedRows()
            .filter(item => item.picList?.length)
            .reduce((prev, curr) => [...prev, curr], []);
        if (list.length) {
            let modal = Modal.confirm({
                title: "提示",
                content: `请确认下载${list.length}条取消凭证?`,
                onOk: () => this.generatorZip(list, modal),
                onCancel: () => {
                    modal.destroy();
                    this.setState({
                        selectedIdList: [],
                        selectedRows: [],
                    });
                },
            });
        } else {
            message.warning("请选择包含有取消凭证的数据");
        }
    }

    syncCancelOrder() {
        this.setState({ showSyncCancelOrderModal: true });
    }

    renderImg(row) {
        return row.picList?.[0] && <AntImage src={row.picList?.[0] + "?s=" + Math.random().toString()} width={50} />;
    }

    importFile({ src }, { id }) {
        lib.request({
            url: "/ccs/calloff/uploadPic",
            data: {
                id,
                picUrl: src,
            },
            needMask: true,
            success: res => {
                this.load();
                message.success("上传凭证成功");
            },
        });
    }

    myOperation(row) {
        const { buttons } = this.state;
        return (
            <Space>
                {row.calloffType === "直接取消" && row.calloffStatus === "待取消" && (
                    <a className="link" onClick={() => this.cancelOrder(row)}>
                        取消订单
                    </a>
                )}
                {/* {row.calloffType === "撤单" && row.calloffStatus === "待取消" && <a className="link" onClick={() => this.cancellation(row)}>撤单</a>} */}
                {/* {row.calloffType === "退货" && row.calloffStatus === "待取消" && <a className="link" onClick={() => this.returnGood(row)}>退货</a>} */}
                {(row.calloffStatus === "待取消" ||
                    (row.calloffStatus === "取消中" && row.calloffType !== "直接取消")) && (
                    <a className="link" onClick={() => this.rejectOrder(row)}>
                        驳回
                    </a>
                )}
                {row.calloffStatus === "取消驳回" && (
                    <a className="link" onClick={() => this.reCancel(row)}>
                        重新取消订单
                    </a>
                )}
                {row.calloffType === "撤单" && (
                    <a className="link" style={{ position: "relative" }}>
                        <Uploader
                            allowTypes={["jpg", "png", "pdf", "jpeg", "webp", "bmp", "tif", "gif"]}
                            onChange={file => this.importFile(file, row)}
                            style={{
                                width: "100%",
                                height: "100%",
                                opacity: 0,
                                position: "absolute",
                                zIndex: 999,
                                top: 0,
                                left: 0,
                            }}
                        />
                        上传
                    </a>
                )}
            </Space>
        );
    }

    reCancel(row) {
        let modal = Modal.confirm({
            title: "提示",
            content: "确认重新取消订单吗？",
            okText: "确定",
            cancelText: "取消",
            onOk: () => {
                lib.request({
                    url: "/ccs/calloff/cancelReject",
                    data: {
                        id: row.id,
                    },
                    needMask: true,
                    success: res => {
                        message.success("重新取消订单");
                        this.load(true);
                        modal.destroy();
                    },
                });
            },
            onCancel: () => {
                modal.destroy(),
                    this.setState({
                        selectedIdList: [],
                        selectedRows: [],
                    });
            },
        });
    }

    rejectModalOk() {
        let { rejectReason, id } = this.state;
        lib.request({
            url: "/ccs/calloff/reject",
            data: {
                id,
                rejectReason,
            },
            needMask: true,
            success: res => {
                message.success("驳回成功");
                this.load(true);
                this.setState({
                    rejectReason: "",
                    rejectVisible: false,
                });
            },
        });
    }

    calloffModalOk() {
        let { calloffReason, id } = this.state;
        lib.request({
            url: "/ccs/calloff/directCalloff",
            data: {
                id,
                calloffReason,
            },
            needMask: true,
            success: res => {
                message.success("取消成功");
                this.load(true);
                this.setState({
                    calloffReason: "",
                    calloffVisible: false,
                });
            },
        });
    }

    goodsReturnFunc() {
        if (!this.state.returnGoodsReason) {
            message.warning("请输入退货原因");
            return;
        }
        lib.request({
            url: "/ccs/calloff/applyRefundOrder",
            data: {
                sns: this.state.declareIds,
                reason: this.state.returnGoodsReason,
            },
            needMask: true,
            success: res => {
                this.setState({
                    returnGoodsVisible: false,
                    refundReason: "",
                    returnGoodsReason: "",
                    editRow: {},
                    declareIds: "",
                    resultVisiable: true,
                    returnDetail: res,
                });
            },
        });
    }

    // 撤单
    cancelLations() {
        let list = this.getCheckedRows();
        if (list.length) {
            let sns = "";
            list.map(item => {
                sns += item.orderSn + ",";
            });
            sns = sns.slice(0, -1);
            const modal = Modal.confirm({
                cancelText: "取消",
                okText: "确定",
                title: "确定撤单吗",
                onOk: () => {
                    lib.request({
                        url: "/ccs/calloff/applyCancel",
                        data: {
                            sns,
                        },
                        needMask: true,
                        success: res => {
                            modal.destroy();
                            this.setState({
                                resultVisiable: true,
                                returnDetail: res,
                            });
                        },
                    });
                },
                onCancel: () => {
                    modal.destroy(),
                        this.setState({
                            selectedIdList: [],
                            selectedRows: [],
                        });
                },
            });
        } else {
            message.warning("请选择数据");
        }
    }

    renderModal() {
        let {
            rejectVisible,
            resultVisiable,
            returnGoodsVisible,
            showSyncCancelOrderModal,
            returnGoodsReason,
            rejectReason,
            calloffVisible,
            calloffReason,
            returnDetail,
            batchRejectVisible,
            batchRejectReason,
            selectedIdList,
        } = this.state;
        const columns1 = [
            {
                title: "申报单号",
                dataIndex: "declareNo",
            },
            {
                title: "清单编号",
                dataIndex: "inveNo",
            },
            {
                title: "运单号",
                dataIndex: "logisticsNo",
            },
        ];
        const columns2 = [
            {
                title: "申报单号",
                dataIndex: "declareNo",
            },
            {
                title: "清单编号",
                dataIndex: "inveNo",
            },
            {
                title: "错误信息",
                dataIndex: "message",
            },
        ];
        return (
            <React.Fragment>
                <Modal
                    title="退货"
                    okText="确定"
                    cancelText="取消"
                    open={returnGoodsVisible}
                    onCancel={() => {
                        this.setState({
                            returnGoodsVisible: false,
                            returnGoodsReason: "",
                        });
                    }}
                    onOk={() => this.goodsReturnFunc()}>
                    请输入退货原因
                    <TextArea
                        value={returnGoodsReason}
                        onChange={e => {
                            this.setState({
                                returnGoodsReason: e.target.value,
                            });
                        }}></TextArea>
                </Modal>
                <Modal
                    title={"操作成功数"}
                    open={resultVisiable}
                    width={800}
                    onCancel={() => {
                        this.setState({
                            resultVisiable: false,
                        });
                    }}
                    footer={
                        <Button
                            onClick={() => {
                                this.setState({
                                    resultVisiable: false,
                                    selectedIdList: [],
                                    selectedRows: [],
                                });
                                this.load(true);
                            }}>
                            关闭
                        </Button>
                    }>
                    <Tabs defaultActiveKey="0">
                        <TabPane tab={`操作成功(${returnDetail?.successCount})`} key="0">
                            <Table dataSource={returnDetail?.successRecordList} columns={columns1} rowKey="idx"></Table>
                        </TabPane>
                        <TabPane tab={`操作失败(${returnDetail?.failCount})`} key="1">
                            <Table dataSource={returnDetail?.failRecordList} columns={columns2} rowKey="idx"></Table>
                        </TabPane>
                    </Tabs>
                    {/* </Tabs> */}
                </Modal>
                <Modal
                    title="驳回单据"
                    open={rejectVisible}
                    okText="确认驳回"
                    onOk={() => this.rejectModalOk()}
                    onCancel={() => this.setState({ rejectVisible: false, rejectReason: "" })}>
                    <TextArea
                        placeholder="请输入驳回原因"
                        value={rejectReason}
                        onChange={e => {
                            this.setState({ rejectReason: e.currentTarget.value });
                        }}
                    />
                </Modal>
                <Modal
                    title="批量驳回"
                    visible={batchRejectVisible}
                    okText="确认驳回"
                    onOk={() => {
                        if (!this.state.batchRejectReason) {
                            message.error("请输入驳回原因");
                            return;
                        }
                        lib.request({
                            url: "/ccs/calloff/rejectBatch",
                            data: {
                                idList: selectedIdList,
                                rejectReason: this.state.batchRejectReason,
                            },
                            success: () => {
                                message.success("操作成功");
                                this.setState({
                                    batchRejectVisible: false,
                                });
                                this.load();
                            },
                        });
                    }}
                    onCancel={() => this.setState({ batchRejectVisible: false, batchRejectReason: "" })}>
                    <TextArea
                        placeholder="请输入驳回原因"
                        value={batchRejectReason}
                        onChange={e => {
                            this.setState({ batchRejectReason: e.currentTarget.value });
                        }}
                    />
                </Modal>
                <Modal
                    title="取消单据"
                    open={calloffVisible}
                    okText="确认取消"
                    onOk={() => this.calloffModalOk()}
                    onCancel={() => this.setState({ calloffVisible: false, calloffReason: "" })}>
                    <TextArea
                        placeholder="请输入取消原因"
                        value={calloffReason}
                        onChange={e => {
                            this.setState({ calloffReason: e.currentTarget.value });
                        }}
                    />
                </Modal>
                <SyncCancelOrderModal
                    showSyncCancelOrderModal={showSyncCancelOrderModal}
                    closeModal={() => {
                        this.setState({ showSyncCancelOrderModal: false });
                    }}
                />
            </React.Fragment>
        );
    }
}

export default App;
