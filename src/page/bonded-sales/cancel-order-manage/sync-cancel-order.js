import React, { useState, useEffect, Fragment, useRef } from "react";
import { ConfigFormCenter, lib } from "react-single-app";
import { Modal, Table, Button } from "antd";
import moment from "moment";
import { Tabs } from "antd";

export const syncCancelOrderData = () => {
    return {
        baseInfo: {
            children: [
                {
                    label: "账册编码",
                    labelCol: { span: 5 },
                    wrapperCol: { span: 19 },
                    editEnable: true,
                    type: "single-select",
                    name: "bookId",
                    from: "/ccs/jdServProvider/listAuthBook",
                    rules: [
                        {
                            required: true,
                            message: "账册编码!",
                        },
                    ],
                },
                {
                    label: "获取时间",
                    labelCol: { span: 5 },
                    wrapperCol: { span: 19 },
                    editEnable: true,
                    name: "getTime",
                    type: "dateInput",
                    customConfig: {
                        showTime: true,
                        isRange: true,
                        defaultValue: [moment().startOf("day"), moment().endOf("day")],
                    },
                    rules: [
                        {
                            required: true,
                            message: "请选择获取时间!",
                        },
                    ],
                },
                {
                    label: "申报单号",
                    labelCol: { span: 5 },
                    wrapperCol: { span: 19 },
                    name: "declareOrderNo",
                    editEnable: true,
                    type: "textInput",
                },
            ],
            label: "",
            name: "baseInfo",
            isGroup: true,
            className: "sync-shift-baseInfo",
        },
    };
};

//同步取消接单modal
function SyncCancelOrderModal({ showSyncCancelOrderModal, closeModal }) {
    const ref = useRef();
    const [previewData, setPreviewData] = useState([]);
    const [showSyncCancelOrderDetailModal, setShowSyncCancelOrderDetailModal] = useState(false);
    const [subtasksId, setSubtasksId] = useState();
    let [tabKey, setTabKey] = useState("1");
    const configData = syncCancelOrderData();
    const handleCancel = () => {
        close();
    };
    const close = success => {
        setTabKey("1");
        setPreviewData([]);
        closeModal(success);
    };
    const handleOk = () => {
        //确认明细关闭弹框
        if (tabKey === "2") {
            close(true);
        } else {
            ref.current.submitForm();
        }
    };
    const onConfigLoadSuccess = config => {
        config.baseInfo.children.map(item => {
            if (item.type === "single-select") {
                ref.current.initSelect(item);
            }
        });
        ref.current.setMergeDetail({ bookId: 10 });
    };
    const onInitSelectSuccess = () => {};
    const beforeSubmit = values => {
        let timeArr = values.getTime.map(it => it.valueOf());
        delete values.getTime;
        values.beginCalloffTime = timeArr[0];
        values.endCalloffTime = timeArr[1];
        return values;
    };
    const onSubmitSuccess = data => {
        setTabKey("2");
        setPreviewData(data);
    };
    const checkDetail = subtasksId => {
        setSubtasksId(subtasksId);
        setShowSyncCancelOrderDetailModal(true);
    };
    const columns = [
        {
            title: "任务ID",
            dataIndex: "taskId",
            width: 230,
        },
        {
            title: "开始日期",
            dataIndex: "staCreateTime",
            width: 130,
        },
        {
            title: "结束日期",
            dataIndex: "endCreateTime",
            width: 130,
        },
        {
            title: "状态",
            dataIndex: "statusStr",
            width: 110,
            render: (text, record, index) => {
                return (
                    <>
                        {text}

                        {/*
                       下版本开发
                    */}
                        {/* {
                        record.status === 1 && <Button type="link" size="small">删除</Button>
                    } */}
                    </>
                );
            },
        },
        {
            title: "备注",
            dataIndex: "remark",
            width: 120,
            render: (text, record, index) => {
                return record.status == 2 ? (
                    <Fragment>
                        {
                            //queryType  1 - 申报单号查询 查看明细，2 - 时间查询  新增xx条
                            record.queryType == 1 && (
                                <Button type={"link"} onClick={() => checkDetail(record.remark)}>
                                    查看明细
                                </Button>
                            )
                        }
                        {
                            //queryType  1 - 申报单号查询 查看明细，2 - 时间查询  新增xx条
                            record.queryType == 2 && (
                                <span>
                                    新增<span style={{ color: "red" }}>{record.remark}</span>条
                                </span>
                            )
                        }
                    </Fragment>
                ) : null;
            },
        },
    ];
    return (
        <Modal
            destroyOnClose
            onCancel={handleCancel}
            onOk={handleOk}
            title={"同步取消单"}
            width={tabKey === "2" ? 785 : 546}
            open={showSyncCancelOrderModal}>
            <Tabs
                defaultActiveKey={"1"}
                activeKey={tabKey}
                onChange={key => {
                    setTabKey(key);
                    if (key == "2") {
                        lib.request({
                            url: "/ccs/calloff/uploadPic/preview",
                            needMask: true,
                            success: data => {
                                setPreviewData(data);
                            },
                        });
                    }
                }}>
                <Tabs.TabPane tab={"同步筛选"} key={"1"} forceRender={true} />
                <Tabs.TabPane tab={"任务中心"} key={"2"} forceRender={true} />
            </Tabs>
            <>
                {tabKey === "1" && (
                    <ConfigFormCenter
                        ref={ref}
                        confData={configData}
                        disableEdit={true}
                        onInitSelectSuccess={onInitSelectSuccess}
                        onConfigLoadSuccess={onConfigLoadSuccess}
                        submitUrl={"/ccs/calloff/uploadPic/preview"}
                        onSubmitSuccess={onSubmitSuccess}
                        beforeSubmit={beforeSubmit}
                    />
                )}
                {tabKey === "2" && (
                    <Table dataSource={previewData} columns={columns} scroll={{ y: 400 }} rowKey={"taskId"} />
                )}
            </>
            <SyncCancelOrderDetailModal
                subtasksId={subtasksId}
                showSyncCancelOrderModal={showSyncCancelOrderDetailModal}
                closeModal={() => {
                    setSubtasksId(null);
                    setShowSyncCancelOrderDetailModal(false);
                }}
            />
        </Modal>
    );
}

//同步取消接单查看明细modal
function SyncCancelOrderDetailModal({ subtasksId, showSyncCancelOrderModal, closeModal }) {
    const [previewData, setPreviewData] = useState([]);
    useEffect(() => {
        if (subtasksId) {
            lib.request({
                url: "/ccs/calloff/uploadPic/queryDetails",
                data: { subtasksId: subtasksId },
                needMask: true,
                success: data => {
                    setPreviewData(data);
                },
            });
        }
    }, [subtasksId]);
    const handleCancel = () => {
        close();
    };
    const close = success => {
        closeModal(success);
    };
    const handleOk = () => {
        close(true);
    };

    const columns = [
        {
            title: "申报单号",
            dataIndex: "declareOrderNo",
            width: 159,
        },
        {
            title: "状态",
            dataIndex: "statusStr",
            width: 110,
        },
    ];
    return (
        <Modal
            destroyOnClose
            onCancel={handleCancel}
            onOk={handleOk}
            title={"同步取消单"}
            open={showSyncCancelOrderModal}>
            <Table dataSource={previewData} columns={columns} scroll={{ y: 400 }} rowKey={"taskId"} />
        </Modal>
    );
}

export default SyncCancelOrderModal;
