import React from "react";
import { lib, SearchList, getConfigDataUtils } from "react-single-app";
import axios from "axios";
import "./track-log.less";

export default class TrackLog extends SearchList {
    constructor(props) {
        super(props);
    }
    getConfig() {
        return axios.get(getConfigDataUtils.getDataUrlByDataId(376)).then(res => res.data.data);
    }
    load(_, toTop) {
        let { pagination, search, config } = this.state;
        var data = {};
        this.setState({ _loading: true });
        Object.assign(data, pagination, search);
        // 分页查询
        lib.request({
            url: config.page.api,
            data: data,
            success: data => {
                console.log(data, "data");
                this.setState({
                    pagination: data.page,
                    dataList: data.dataList || [],
                    _loading: false,
                });
                toTop && (document.querySelector(".table-panel .ant-table-body").scrollTop = 0);
                setTimeout(this.resetSize, 100);
            },
            fail: () => {
                this.setState({
                    _loading: false,
                });
            },
        });
    }
}
