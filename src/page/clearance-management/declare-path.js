import React, { useState, useEffect, useRef } from "react";
import { Config<PERSON>enter, ConfigFormCenter, lib } from "react-single-app";
import { Checkbox, Button, Tooltip, Modal, Input, Select, Form, message, Space, Switch, Row, Tag } from "antd";
import "./declare-path.less";
import NewModal from "../../components/NewModal";
import EditGuaranteeModal from "./component/edit-guarantee-modal";
import EditDDCBWCSModal from "./component/edit-DDCBWCS-modal";
import EditDeclareProxyConfigModal from "./component/edit-declare-proxy-config-modal";
import EditQDBWModal from "./component/edit-QDBW-modal";
import NewDeclareModal from "./component/new-declare-modal";
import SetTagsModal from "./component/set-tags-modal";
function getInitialValue(editRow, key) {
    if (!editRow) return "";
    return editRow[key] || "";
}

class DeclarePath extends ConfigCenter {
    constructor(props) {
        super(props);
        this.state.editDrawer = {
            show: false,
        };
        this.state.modalTitle = "新增路径";
        this.state.modalProxyConfigTitle = "代理申报配置（订单）";
        this.state.upUrl = "/ccs/route/upsetV2";
        this.state.guaranteeVisible = false;
        this.state.editQDBWVisible = false;
        this.state.editDDBWVisible = false;
        this.state.groupList = [
            { ccs: "/ccs/company/listWithDSQY", list: [], labelName: "电商企业", labelKey: "ebcId" },
            { ccs: "/ccs/company/listWithDSPT", list: [], labelName: "电商平台", labelKey: "ebpId" },
            { ccs: "/ccs/company/listWithDBQY", list: [], labelName: "担保企业", labelKey: "assureCompanyId" },
            {
                ccs: "/ccs/company/listWithBWCSQY",
                list: [],
                labelName: "订单报文传输企业",
                labelKey: "orderDeclareCompanyId",
                tooltip: "无代理申报自动启用默认申报方式（前往企业管理查看），已配置代理申报将优先选择代理申报方式",
                formItemLayout: {
                    labelCol: { span: 6 },
                    wrapperCol: { span: 18 },
                },
                type: "customsOrder",
                listSource: "customsOrder",
            },
            {
                ccs: "/ccs/company/listDxpIdById",
                list: [],
                labelName: "订单传输节点",
                labelKey: "orderDeclareDxpId",
                formItemLayout: {
                    labelCol: { span: 6 },
                    wrapperCol: { span: 18 },
                },
                type: "customsOrder",
                listResult: ["orderDeclareCompanyId"],
            },
            {
                ccs: "/ccs/company/listWithBWCSQY",
                list: [],
                labelName: "运单报文传输企业",
                labelKey: "logisticsDeclareCompanyId",
                tooltip: "无代理申报自动启用默认申报方式（前往企业管理查看），已配置代理申报将优先选择代理申报方式",
                formItemLayout: {
                    labelCol: { span: 6 },
                    wrapperCol: { span: 18 },
                },
                type: "shipment",
                listSource: "shipment",
            },
            {
                ccs: "/ccs/company/listDxpIdById",
                list: [],
                labelName: "运单传输节点",
                labelKey: "logisticsDeclareDxpId",
                formItemLayout: {
                    labelCol: { span: 6 },
                    wrapperCol: { span: 18 },
                },
                type: "shipment",
                listResult: ["logisticsDeclareCompanyId"],
            },
            {
                ccs: "/ccs/company/listWithSBQY",
                list: [],
                labelName: "清单报文传输企业",
                labelKey: "listDeclareCompanyId",
                action: [],
                tooltip: "无代理申报自动启用默认申报方式（前往企业管理查看），已配置代理申报将优先选择代理申报方式",
                formItemLayout: {
                    labelCol: { span: 6 },
                    wrapperCol: { span: 18 },
                },
                type: "inventory",
                listSource: "inventory",
            },
            {
                ccs: "/ccs/company/listDxpIdById",
                list: [],
                labelName: "清单传输节点",
                labelKey: "listDeclareDxpId",
                formItemLayout: {
                    labelCol: { span: 6 },
                    wrapperCol: { span: 18 },
                },
                type: "inventory",
                listResult: ["listDeclareCompanyId"],
            },
            { ccs: "/ccs/customsBook/listAllInUseBookNo", list: [], labelName: "账册编号", labelKey: "customsBookId" },
        ];
        this.state.actionList = [];
        this.state.proxyConfig = [];
        this.state.configList = [
            {
                type: "TEXT",
                labelName: "默认方式",
                labelKey: "declareCode",
                required: true,
                message: "请输入路由标识",
                maxLength: 32,
                autocomplete: "off",
            },
            {
                type: "SELECT",
                labelName: "代理申报",
                labelKey: "proxyCode",
                required: true,
                message: "请输入路径名称",
                list: [],
            },
        ];
        this.state.nowType = [];
        this.state.idList = [];
    }

    componentDidMount() {}

    renderList(row) {
        return row.actionDescList.join(",");
    }

    // getCheckboxProps=(row)=>{return {disabled:row.enableStatus}}

    getConfigList() {
        this.state.groupList.map(item => {
            this.getSelectData(item);
        });
        this.getActionList();
    }

    getActionList() {
        lib.request({
            url: "/ccs/route/listAction",
            needMask: true,
            success: json => {
                this.setState({
                    actionList: json,
                });
            },
        });
    }

    routeTagFn(record) {
        // 返回RGB颜色字符串
        // return `rgb(${r},${g},${b})`;
        return record.routeTagDesc?.map(item => {
            // const r = Math.floor(Math.random() * 256);
            // const g = Math.floor(Math.random() * 256);
            // const b = Math.floor(Math.random() * 256);
            return <Tag>{item}</Tag>;
        });
    }

    getSelectData(item) {
        // 依赖上级数据获取类型的Select不做初始化请求
        if (item.listResult) return;
        lib.request({
            url: item.ccs,
            needMask: false,
            success: json => {
                if (!(json instanceof Array)) {
                    return;
                }
                json.map(item => {
                    item.id = item.id || item.value || item.code;
                    item.name = item.name || item.desc;
                });
                item.list = json;
                this.setState(this.state);
            },
        });
    }
    renderLeftOperation() {
        return (
            <Space>
                <Button onClick={this.batchEnable.bind(this, 1)} type="primary">
                    批量启用
                </Button>
                <Button onClick={this.batchEnable.bind(this, 0)} type="primary">
                    批量禁用
                </Button>
                <Button onClick={this.openNewModal.bind(this, "guaranteeVisible")} type="primary">
                    修改担保企业
                </Button>
                <Button onClick={this.openNewModal.bind(this, "editQDBWVisible")} type="primary">
                    修改清单报文传输企业
                </Button>
                <Button onClick={this.openNewModal.bind(this, "editDDBWVisible")} type="primary">
                    修改订单报文传输企业
                </Button>
                <Button onClick={this.openNewModal.bind(this, "editTagsVisible")} type="primary">
                    批量设置标签
                </Button>
            </Space>
        );
    }

    entityWarehouseName(record) {
        // entityWarehouseName
        return (
            <>
                <Tooltip title={record.entityWarehouseName}>
                    <span>{record.entityWarehouseName}</span>
                </Tooltip>
            </>
        );
    }

    openNewModal = keyVal => {
        let selectedRows = this.state.dataList.filter(item => item.checked),
            list = [];
        let idList = selectedRows.reduce((prev, curr) => [...prev, curr.id], []);
        if (idList.length === 0) {
            message.warning("请选择数据");
            return;
        }
        let result = { idList };
        result[keyVal] = true;
        this.setState(result);
    };

    batchEnable = status => {
        let selectedRows = this.state.dataList.filter(item => item.checked),
            list = [];
        let idList = selectedRows.reduce((prev, curr) => [...prev, curr.id], []);
        if (idList.length === 0) {
            message.warning("请选择数据");
            return;
        }
        lib.request({
            url: "/ccs/route/batchEnableSwitch",
            data: {
                idList,
                status,
            },
            needMask: true,
            success: res => {
                if (status === 0) {
                    message.success(`批量禁用成功`);
                } else if (status === 0) {
                    message.success(`批量启用成功`);
                }
                this.load();
            },
        });
    };
    renderRightOperation() {
        return (
            <React.Fragment>
                <Button
                    type="primary"
                    onClick={() => {
                        this.setState({
                            visible: true,
                            flag: false,
                            modalTitle: "新增路径",
                            nowType: [],
                        });
                        this.getConfigList();
                    }}>
                    新增路径
                </Button>
            </React.Fragment>
        );
    }

    // handleOk(values) {
    //     if (this.state.editRow) {
    //         values.id = this.state.editRow.id
    //     }
    //     for (let i in values) {
    //         if (values[i] === '') {
    //             delete values[i]
    //         }
    //     }
    //     lib.request({
    //         url: this.state.upUrl,
    //         data: values,
    //         method: 'POST',
    //         success: res => {
    //             if (res) {
    //                 this.setState({
    //                     visible: false,
    //                     editRow: null,
    //                 })
    //                 this.load()
    //                 // form.resetFields()
    //             }
    //         }
    //     })
    // }

    handleOk(values, modalForm) {
        if (this.state.editRow) {
            values.id = this.state.editRow.id;
        }
        for (let i in values) {
            if (values[i] === null) {
                delete values[i];
            }
        }
        values.proxyDeclareConfigList = this.state.proxyConfig;
        // 数据筛选 （如果传输节点清空，清空对应的proxyDeclareConfigList配置）
        // 'inventoryRefund', 'inventory', 'inventoryCancel': '订单',orderDeclareDxpId
        // 'shipment': '运单',logisticsDeclareDxpId
        // 'inventory': '清单',listDeclareDxpId
        if (!values["listDeclareDxpId"]) {
            values.proxyDeclareConfigList = values.proxyDeclareConfigList.filter(
                item => !["inventoryRefund", "inventory", "inventoryCancel"].includes(item.type),
            );
        }
        if (!values["logisticsDeclareDxpId"]) {
            values.proxyDeclareConfigList = values.proxyDeclareConfigList.filter(
                item => !["shipment"].includes(item.type),
            );
        }
        if (!values["orderDeclareDxpId"]) {
            values.proxyDeclareConfigList = values.proxyDeclareConfigList.filter(
                item => !["customsOrder"].includes(item.type),
            );
        }
        lib.request({
            url: this.state.upUrl,
            data: values,
            method: "POST",
            needMask: true,
            success: res => {
                if (res) {
                    this.setState({
                        visible: false,
                        editRow: null,
                        proxyConfig: [],
                    });
                    this.load(true);
                    modalForm.resetFields();
                }
            },
        });
    }

    handleCancel() {
        this.setState({
            visible: false,
            editRow: null,
        });
    }

    selectChange(nowType) {
        this.setState({
            nowType,
        });
    }

    getProxyCode(configType) {
        let proxyCode, declareCode, declareQueue;
        let index = this.state.proxyConfig.findIndex(item => item.type === configType);
        if (~index) {
            proxyCode = this.state.proxyConfig[index].proxyCode;
            declareCode = this.state.proxyConfig[index].declareCode;
            declareQueue = this.state.proxyConfig[index].declareQueue;
        }
        return { proxyCode: proxyCode, declareCode: declareCode, declareQueue: declareQueue };
    }

    showProxyConfigModal(configType, companyId, nodeVal) {
        let proxyCode = this.getProxyCode(configType);

        let inventoryCancelProxyCode, inventoryRefundProxyCode;
        if (configType === "inventory") {
            inventoryCancelProxyCode = this.getProxyCode("inventoryCancel");
            inventoryRefundProxyCode = this.getProxyCode("inventoryRefund");
        }
        let proxyConfigModalData = {
            configType: configType,
            companyId: companyId,
            dxpId: nodeVal,
            declareCode: proxyCode.declareCode,
            proxyCode: proxyCode.proxyCode,
            inventoryCancelDeclareCode: inventoryCancelProxyCode?.declareCode,
            inventoryCancelProxyCode: inventoryCancelProxyCode?.proxyCode,
            inventoryRefundDeclareCode: inventoryRefundProxyCode?.declareCode,
            inventoryRefundProxyCode: inventoryRefundProxyCode?.proxyCode,
            flag: this.state.flag,
            declareQueue: proxyCode.declareQueue,
        };
        this.setState({
            proxyConfigModalData: proxyConfigModalData,
            configListVisible: true,
        });
    }

    setDymanicSelectData(listSource, id) {
        const { groupList } = this.state;
        for (let i = 0; i < groupList.length; i++) {
            if (groupList[i].listResult && groupList[i].listResult.includes(listSource)) {
                lib.request({
                    url: groupList[i].ccs,
                    data: { id },
                    success: res => {
                        groupList[i].list = res;
                        this.setState({
                            groupList: groupList,
                        });
                    },
                });

                return;
            }
        }
    }

    renderModal() {
        return (
            <React.Fragment>
                <NewDeclareModal
                    {...{
                        flag: this.state.flag,
                        handleOk: this.handleOk.bind(this),
                        handleCancel: this.handleCancel.bind(this),
                        visible: this.state.visible,
                        editRow: this.state.editRow,
                        title: this.state.modalTitle,
                        actionList: this.state.actionList,
                        groupList: this.state.groupList,
                        nowType: this.state.nowType,
                        selectChange: this.selectChange.bind(this),
                        showProxyConfigModal: this.showProxyConfigModal.bind(this),
                        clearProxyConfigDetailByType: this.clearProxyConfigDetailByType.bind(this),
                        setDymanicSelectData: this.setDymanicSelectData.bind(this),
                    }}
                />
                {/* 代理申报配置modal */}
                <EditDeclareProxyConfigModal
                    showModal={this.state.configListVisible}
                    modalData={this.state.proxyConfigModalData}
                    dialogClose={(success, value) => {
                        let proxyConfig = this.state.proxyConfig;
                        if (success) {
                            proxyConfig = this.setProxyConfig(
                                proxyConfig,
                                value.proxyCode,
                                value.declareCode,
                                this.state.proxyConfigModalData.configType,
                                value.declareQueue,
                            );
                            if (this.state.proxyConfigModalData.configType === "inventory") {
                                this.setProxyConfig(
                                    proxyConfig,
                                    value.inventoryCancelProxyCode,
                                    value.inventoryCancelDeclareCode,
                                    "inventoryCancel",
                                    value.declareQueue,
                                );
                                this.setProxyConfig(
                                    proxyConfig,
                                    value.inventoryRefundProxyCode,
                                    value.inventoryRefundDeclareCode,
                                    "inventoryRefund",
                                    value.declareQueue,
                                );
                            }
                        }
                        this.setState({
                            configListVisible: false,
                            proxyConfig: proxyConfig,
                        });
                    }}
                />
                {/* 修改担保企业 */}
                <EditGuaranteeModal
                    showModal={this.state.guaranteeVisible}
                    idList={this.state.idList}
                    dialogClose={success => {
                        if (success) {
                            message.success("批量修改成功");
                            this.load();
                        }
                        this.setState({
                            guaranteeVisible: false,
                        });
                    }}
                />
                {/* 修改清单报文传输企业 */}
                <EditQDBWModal
                    showModal={this.state.editQDBWVisible}
                    idList={this.state.idList}
                    dialogClose={success => {
                        if (success) {
                            message.success("批量修改成功");
                            this.load();
                        }
                        this.setState({
                            editQDBWVisible: false,
                        });
                    }}
                />
                {/* 修改订单报文传输企业 */}
                <EditDDCBWCSModal
                    showModal={this.state.editDDBWVisible}
                    idList={this.state.idList}
                    dialogClose={success => {
                        if (success) {
                            message.success("批量修改成功");
                            this.load();
                        }
                        this.setState({
                            editDDBWVisible: false,
                        });
                    }}
                />
                {/* 批量设置标签 */}
                <SetTagsModal
                    showModal={this.state.editTagsVisible}
                    idList={this.state.idList}
                    dialogClose={success => {
                        if (success) {
                            message.success("批量设置成功");
                            this.load();
                        }
                        this.setState({
                            editTagsVisible: false,
                        });
                    }}
                />
            </React.Fragment>
        );
    }

    setProxyConfig(proxyConfig, proxyCode, declareCode, configType, declareQueue) {
        let index = proxyConfig.findIndex(item => item.type === configType);
        let items = {
            proxyCode: proxyCode,
            declareCode: declareCode,
            type: configType,
            declareQueue: declareQueue,
        };
        if (~index) {
            proxyConfig[index] = items;
        } else {
            proxyConfig.push(items);
        }
        return proxyConfig;
    }

    handleEnable(row, enable) {
        let data = JSON.parse(JSON.stringify(row));
        data.enable = enable;
        for (let i in data) {
            if (data[i] === null) {
                delete data[i];
            }
        }
        lib.request({
            url: this.state.upUrl,
            data,
            method: "POST",
            needMask: true,
            success: res => {
                this.load(true);
                if (res) {
                    message.success(`${enable ? "启用" : "禁用"}路径成功`);
                }
            },
        });
    }

    enableStatus(row) {
        return (
            <React.Fragment>
                {row.enable ? (
                    <Switch checked={row.enable} onChange={() => this.unenableFunc(row)}></Switch>
                ) : (
                    <Switch checked={row.enable} onChange={() => this.handleEnable(row, 1)}></Switch>
                )}
            </React.Fragment>
        );
    }

    unenableFunc(row) {
        Modal.confirm({
            cancelText: "取消",
            okText: "确定",
            title: "提示",
            content: "是否禁用?",
            onOk: () => {
                this.handleEnable(row, 0);
            },
        });
    }

    editFunc(row) {
        const { groupList } = this.state;

        const sourceMap = {};
        groupList.forEach(item => {
            if (item.listSource && row[item.labelKey]) {
                sourceMap[item.labelKey] = row[item.labelKey];
            }
        });
        const requestAll = [];
        groupList.forEach((item, index) => {
            if (item.listResult && sourceMap[item.listResult[0]]) {
                requestAll.push(
                    new Promise((resolve, reject) => {
                        lib.request({
                            url: item.ccs,
                            data: { id: sourceMap[item.listResult[0]] },
                            success: data => {
                                resolve({ data: data, index, index });
                            },
                            fail: () => {
                                reject();
                            },
                        });
                    }),
                );
            }
        });
        Promise.all(requestAll).then(res => {
            res.map(item => {
                groupList[item.index].list = item.data;
            });
            this.setState({
                flag: false,
                editRow: row,
                visible: true,
                groupList,
                nowType: row.actionList,
                modalTitle: "编辑路径",
            });
            this.getConfigList();
            //获取路径代理申报配
            this.getProxyConfigDetail(row);
        });
    }

    getProxyConfigDetail(row) {
        lib.request({
            url: "/ccs/route/listDeclareConfig",
            data: { id: row.id },
            needMask: true,
            success: json => {
                this.setState({
                    proxyConfig: json || [],
                });
            },
        });
    }

    clearProxyConfigDetailByType(type) {
        let proxyConfig = this.state.proxyConfig.filter(item => {
            if (item.type !== type) {
                return !(type === "inventory" && (item.type === "inventoryCancel" || item.type === "inventoryRefund"));
            } else {
                return false;
            }
        });
        this.setState({
            proxyConfig: proxyConfig,
        });
    }

    lookFunc(row) {
        const { groupList } = this.state;

        const sourceMap = {};
        groupList.forEach(item => {
            if (item.listSource && row[item.labelKey]) {
                sourceMap[item.labelKey] = row[item.labelKey];
            }
        });
        const requestAll = [];
        groupList.forEach((item, index) => {
            if (item.listResult && sourceMap[item.listResult[0]]) {
                requestAll.push(
                    new Promise((resolve, reject) => {
                        lib.request({
                            url: item.ccs,
                            data: { id: sourceMap[item.listResult[0]] },
                            success: data => {
                                resolve({ data: data, index, index });
                            },
                            fail: () => {
                                reject();
                            },
                        });
                    }),
                );
            }
        });
        Promise.all(requestAll).then(res => {
            res.map(item => {
                groupList[item.index].list = item.data;
            });
            this.setState({
                flag: true,
                editRow: row,
                visible: true,
                nowType: row.actionList,
                modalTitle: "查看路径",
                groupList,
            });
            this.getConfigList();
            //获取路径代理申报配
            this.getProxyConfigDetail(row);
        });
    }

    myOperation(row) {
        return (
            <React.Fragment>
                <Space>
                    {row.enable === 0 && (
                        <span className="link" onClick={() => this.editFunc(row)}>
                            编辑
                        </span>
                    )}
                    <span className="link" onClick={() => this.lookFunc(row)}>
                        查看
                    </span>
                </Space>
            </React.Fragment>
        );
    }
}

export default DeclarePath;
