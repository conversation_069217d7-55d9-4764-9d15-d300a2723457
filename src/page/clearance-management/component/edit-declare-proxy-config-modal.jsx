import React, { useState, useEffect, useRef } from "react";
import { ConfigCenter, ConfigFormCenter, lib } from "react-single-app";
import { Checkbox, Button, Modal, Input, Select, Form, message, Space, Switch, Row } from "antd";
// import "./declare-path.less";
// import NewModal from "../../components/NewModal";

function EditDeclareProxyConfigModal({ showModal, dialogClose, modalData }) {
    const labelCompanyName = {
        customsOrder: "订单",
        shipment: "运单",
        inventory: "清单",
    };
    const labelCompanyUrl = {
        customsOrder: "/ccs/company/listWithBWCSQY",
        shipment: "/ccs/company/listWithBWCSQY",
        inventory: "/ccs/company/listWithSBQY",
    };
    const data = {
        baseInfo: {
            children: [
                {
                    label: `${labelCompanyName[modalData?.configType]}报文传输企业`,
                    editEnable: false,
                    disabled: true,
                    name: "companyId",
                    wrapperCol: 16,
                    type: "single-select",
                    from: labelCompanyUrl[modalData?.configType],
                    rules: [
                        {
                            required: true,
                            message: "请选择报文传输企业!",
                        },
                    ],
                },

                {
                    label: "传输节点",
                    editEnable: false,
                    disabled: true,
                    name: "dxpId",
                    wrapperCol: 16,
                    allowClear: true,
                    type: "single-select",
                    from: "/ccs/company/listDxpIdById",
                    // rules: [
                    //     {
                    //         required: true,
                    //         message: "请选择传输节点!"
                    //     }
                    // ]
                },
                {
                    type: "single-select",
                    label: "隔离队列",
                    name: "declareQueue",
                    allowClear: true,
                    list: [],
                    from: "/ccs/company/listDeclareQueue",
                    // rules: [
                    //     {
                    //         required: true,
                    //         message: "请选择隔离队列!"
                    //     }
                    // ]
                },
                {
                    label: "当前方式",
                    editEnable: false,
                    name: "declareCode",
                    wrapperCol: 16,
                    type: "single-select",
                    allowClear: true,
                    from: "/ccs/company/viewDeclareWayByDxpId",
                    rules: [
                        {
                            required: true,
                            message: "请选择当前方式!",
                        },
                    ],
                },
                {
                    label: "代理申报",
                    editEnable: false,
                    name: "proxyCode",
                    wrapperCol: 16,
                    type: "single-select",
                    from: "/ccs/declareWay/listByType",
                    allowClear: true,
                    fromParams: [{ key: "type", default: modalData?.configType }],
                    customConfig: { allowClear: true, showSearch: true },
                },
            ],
            label: "",
            name: "baseInfo",
            isGroup: true,
            className: "baseInfo-proxyCode",
        },
    };
    if (modalData && modalData.configType === "inventory") {
        data.baseInfo.children.push(
            {
                label: "当前方式(清关撤单)",
                editEnable: false,
                name: "inventoryCancelDeclareCode",
                wrapperCol: 16,
                type: "single-select",
                from: "/ccs/company/viewDeclareWayByDxpId",
                rules: [
                    {
                        required: true,
                        message: "请选择清关撤单默认方式!",
                    },
                ],
            },
            {
                label: "代理申报(清关撤单)",
                editEnable: false,
                name: "inventoryCancelProxyCode",
                wrapperCol: 16,
                customConfig: { allowClear: true, showSearch: true },
                type: "single-select",
                // from: "/ccs/company/viewDeclareWayByDxpId",
                from: "/ccs/declareWay/listByType",
                allowClear: true,
                fromParams: [{ key: "type", default: "inventoryCancel", required: true }],
            },
            {
                label: "当前方式(清关退货)",
                editEnable: false,
                name: "inventoryRefundDeclareCode",
                wrapperCol: 16,
                type: "single-select",
                from: "/ccs/company/viewDeclareWayByDxpId",
                rules: [
                    {
                        required: true,
                        message: "请选择清关取消默认方式!",
                    },
                ],
            },
            {
                label: "代理申报(清关退货)",
                editEnable: false,
                name: "inventoryRefundProxyCode",
                wrapperCol: 16,
                type: "single-select",
                customConfig: { allowClear: true, showSearch: true },
                // from: "/ccs/company/viewDeclareWayByDxpId",
                from: "/ccs/declareWay/listByType",
                fromParams: [{ key: "type", default: "inventoryRefund", required: true }],
            },
        );
    }
    const ref = React.useRef();
    const handleCancel = () => {
        dialogClose(false);
    };
    const handleOk = () => {
        ref.current.submitForm();
    };
    const beforeSubmit = values => {
        dialogClose(true, values);
    };

    const onConfigLoadSuccess = config => {
        //这里暂时先重复请求，待ConfigFormCenter完善
        config.baseInfo.children.map(item => {
            if (item.type === "single-select" && !item.fromParam) {
                let type = modalData.configType;
                if (item.name === "inventoryCancelProxyCode" || item.name === "inventoryCancelDeclareCode") {
                    type = "inventoryCancel";
                } else if (item.name === "inventoryRefundProxyCode" || item.name === "inventoryRefundDeclareCode") {
                    type = "inventoryRefund";
                }
                let data = {};
                // 查询是否有请求默认值
                if (item.fromParams) {
                    item.fromParams.forEach(obj => {
                        data[obj.key] = obj.default;
                    });
                }
                if (Object.keys(data).length === 0) {
                    data =
                        item.from === "/ccs/company/listDxpIdById"
                            ? { id: modalData.companyId }
                            : { type: type, companyId: modalData.companyId, dxpId: modalData.dxpId };
                }

                lib.request({
                    data: data,
                    url: item.from,
                    needMask: false,
                    success: data => {
                        let selectData = {};
                        selectData[item.name] = data;
                        // 两种不同的请求做兼容
                        if ("declareConfigList" in data) {
                            ref.current.updateSelectList(item, data.declareConfigList);
                        } else {
                            ref.current.updateSelectList(item, data);
                        }
                        if (!modalData.declareQueue && "declareQueue" in data) {
                            modalData.declareQueue = data.declareQueue;
                        }
                        //判断 declareCode 、inventoryCancelDeclareCode 和inventoryRefundDeclareCode 是否为空，空则去企业默认
                        if (item.name === "declareCode" && !modalData.declareCode) {
                            modalData.declareCode = data.defaultCode;
                        } else if (
                            item.name === "inventoryCancelDeclareCode" &&
                            !modalData.inventoryCancelDeclareCode
                        ) {
                            modalData.inventoryCancelDeclareCode = data.defaultCode;
                        } else if (
                            item.name === "inventoryRefundDeclareCode" &&
                            !modalData.inventoryRefundDeclareCode
                        ) {
                            modalData.inventoryRefundDeclareCode = data.defaultCode;
                        }
                        ref.current.setMergeDetail({
                            ...modalData,
                        });
                    },
                });
            }
        });
    };

    useEffect(() => {
        if (ref?.form && modalData && modalData.companyId && modalData.dxpId) {
            ref.form.setFieldValue("companyId", modalData.companyId);
            ref.form.setFieldValue("dxpId", modalData.dxpId);
            ref.form.setFieldValue("declareQueue", modalData.declareQueue);
        }
    }, [modalData?.companyId, modalData?.dxpId]);

    return (
        <Modal destroyOnClose onCancel={handleCancel} onOk={handleOk} title={"代理申报配置"} open={showModal}>
            <ConfigFormCenter
                ref={ref}
                disableEdit={modalData?.flag || false}
                confData={data}
                onConfigLoadSuccess={onConfigLoadSuccess}
                beforeSubmit={beforeSubmit}
            />
        </Modal>
    );
}

export default EditDeclareProxyConfigModal;
