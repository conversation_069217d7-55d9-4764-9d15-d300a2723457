import React, { useState, useEffect, useRef } from "react";
import { ConfigCenter, ConfigFormCenter, lib } from "react-single-app";
import { Checkbox, Button, Modal, Input, Select, Form, message, Space, Switch, Row } from "antd";
import NewModal from "../../../components/NewModal";
/**
 * 修改担保企业
 * @param showModal: boolean,
 * @param dialogClose: (success:boolean)=>{}
 * @param idList: number[]
 * @returns ReactElement
 */
function EditGuaranteeModal({ showModal, dialogClose, idList }) {
    const [config, setConfig] = useState([
        {
            type: "SELECT",
            labelName: "担保企业",
            labelKey: "assureCompanyId",
            list: [],
            required: true,
            from: "/ccs/company/listWithDBQY",
        },
    ]);
    const [form] = Form.useForm();
    const handleOk = val => {
        lib.request({
            url: "/ccs/route/batchUpdateAssureCompany",
            data: {
                idList: idList,
                assureCompanyId: val.assureCompanyId,
            },
            fail: () => {},
            success: () => {
                dialogClose(true);
            },
        });
    };

    const handleCancel = () => {
        dialogClose(false);
    };

    useEffect(() => {
        Promise.all(
            config.map(item => {
                return new Promise((resolve, reject) => {
                    lib.request({
                        url: item.from,
                        fail: data => {
                            reject(data);
                        },
                        success: data => {
                            resolve(data);
                        },
                    });
                });
            }),
        ).then(res => {
            res.map((obj, index) => {
                config[index].list = obj;
            });
            setConfig(config);
        });
    }, []);

    return (
        <NewModal
            title="修改担保企业"
            onOk={handleOk}
            onCancel={handleCancel}
            configList={config}
            visible={showModal}
            form={form}
            editRow={{}}
            formItemLayout={{
                labelCol: { span: 8 },
                wrapperCol: { span: 14 },
            }}
            modalStyle={{ width: "500px" }}
        />
    );
}

export default EditGuaranteeModal;
