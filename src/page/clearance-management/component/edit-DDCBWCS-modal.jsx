import React, { useState, useEffect, useRef } from "react";
import { Config<PERSON>enter, ConfigFormCenter, lib } from "react-single-app";
import { Checkbox, Button, Modal, Input, Select, Form, message, Space, Switch, Row } from "antd";
import NewModal from "../../../components/NewModal";
// 修改订单报文传输企业
function EditDDCBWCSModal({ showModal, dialogClose, idList }) {
    // 表单值存储
    const formValues = useRef({});
    //
    const recodeSources = useRef({});
    // 请求数据缓存
    const requestDataCache = useRef([]);
    const formRef = useRef(null);
    const [config, setConfig] = useState([
        {
            type: "SELECT",
            labelName: "订单报文传输企业",
            labelKey: "assureCompanyId",
            list: [],
            required: true,
            from: "/ccs/company/listWithBWCSQY",
        },
        {
            type: "SELECT",
            labelName: "传输节点",
            labelKey: "dxpId",
            list: [],
            // required: true,
            from: "/ccs/company/listDxpIdById",
            allowClear: true,
            fromParams: [{ key: "id", from: "assureCompanyId", required: true }],
        },
        {
            type: "SELECT",
            labelName: "隔离队列",
            labelKey: "declareQueue",
            list: [],
            from: "/ccs/company/viewDeclareQueueByDxpId",
            allowClear: true,
            // required: true,
            fromParams: [
                { key: "companyId", from: "assureCompanyId", required: true },
                { key: "dxpId", from: "dxpId", required: true },
            ],
        },
        {
            type: "SELECT",
            labelName: "当前方式",
            labelKey: "declareCode",
            list: [],
            required: true,
            from: "/ccs/company/viewDeclareWayByDxpId",
            allowClear: true,
            valType: "customsOrder",
            fromParams: [
                { key: "companyId", from: "assureCompanyId", required: true },
                { key: "dxpId", from: "dxpId", required: true },
                { key: "type", default: "customsOrder", required: true },
            ],
        },
        {
            type: "SELECT",
            labelName: "代理申报",
            labelKey: "proxyCode",
            list: [],
            from: "/ccs/declareWay/listByType",
            valType: "customsOrder",
            allowClear: true,
            fromParams: [
                // {key:"companyId", from:"assureCompanyId", required:true},
                // {key:"dxpId", from: "dxpId", required:true},
                { key: "type", default: "customsOrder", required: true },
            ],
        },
    ]);
    const [form] = Form.useForm();
    const handleOk = val => {
        // 数据处理
        const proxyDeclareConfigList = [];
        const maps = {};
        config.map(item => {
            maps[item.labelKey] = item.valType;
        });
        for (let i in val) {
            if (i === "dxpId") continue;
            if (i === "assureCompanyId") continue;
            if (i === "declareQueue") continue;
            if (
                proxyDeclareConfigList.filter(item => {
                    return item.type === maps[i];
                }).length === 0
            ) {
                proxyDeclareConfigList.push({
                    declareCode: i.toLocaleLowerCase().indexOf("declarecode") > -1 ? val[i] : "",
                    proxyCode: i.toLocaleLowerCase().indexOf("proxycode") > -1 ? val[i] : "",
                    type: maps[i],
                });
            } else {
                proxyDeclareConfigList.forEach(item => {
                    if (item.type === maps[i]) {
                        if (i.toLocaleLowerCase().indexOf("declarecode") > -1) {
                            item.declareCode = val[i];
                        }
                        if (i.toLocaleLowerCase().indexOf("proxycode") > -1) {
                            item.proxyCode = val[i];
                        }
                    }
                });
            }
        }
        lib.request({
            url: "/ccs/route/batchUpdateMessageTransCompany",
            data: {
                idList: idList,
                type: "DECLARE_ORDER",
                companyId: val.assureCompanyId,
                dxpId: val.dxpId,
                declareQueue: val.declareQueue,
                proxyDeclareConfigList: proxyDeclareConfigList,
            },
            fail: () => {},
            success: () => {
                dialogClose(true);
            },
        });
    };

    const handleCancel = () => {
        dialogClose(false);
    };

    useEffect(() => {
        if (!showModal) {
            clearRequestCache();
        } else {
            init();
        }
    }, [showModal]);
    const init = () => {
        config.map((item, index) => {
            if (!item.fromParams) {
                lib.request({
                    url: item.from,
                    success: data => {
                        if ("declareConfigList" in data) {
                            config[index].list = data["declareConfigList"];
                            formRef.current?.form.setFields([{ name: item.labelKey, value: data.defaultCode }]);
                        } else if ("declareQueueList" in data) {
                            config[index].list = data["declareQueueList"];
                            formRef.current?.form.setFields([{ name: item.labelKey, value: data.declareQueue }]);
                        } else {
                            config[index].list = data;
                        }
                        setConfig([...config]);
                    },
                });
            } else {
                // 收集依赖项对应的改变项，方便做状态重置
                item.fromParams.forEach((obj, i) => {
                    if (!obj.from) return;
                    if (recodeSources.current[obj.from]) {
                        recodeSources.current[obj.from].push(item.labelKey);
                    } else {
                        recodeSources.current[obj.from] = [item.labelKey];
                    }
                });
            }
        });
    };

    const clearRequestCache = () => {
        requestDataCache.current = [];
        recodeSources.current = {};
        formValues.current = {};
    };

    const getDataFromFormChange = (name, value) => {
        formValues.current[name] = value;
        // 重置改变的表单项所对应的依赖项的值
        if (Array.isArray(recodeSources.current[name])) {
            const values = recodeSources.current[name].map(obj => {
                return { name: obj, value: null };
            });
            formRef.current?.form.setFields(values);
        } else {
            return;
        }

        // 刷新数据请求记录
        const refreshArr = [];
        config.forEach((item, index) => {
            const { fromParams } = item;
            if (Array.isArray(fromParams) && fromParams.length !== 0) {
                const params = {};
                let paramsCount = 0; // 必填的参数总数量
                let hasValCount = 0; // 已经有值的参数数量
                fromParams.forEach(obj => {
                    if (obj.required) {
                        paramsCount++;
                    }
                    if (obj.default || formValues.current[obj.from]) {
                        params[obj.key] = formValues.current[obj.from] || obj.default;
                        hasValCount++;
                    }
                });
                // 当需要的参数全有时，推进refreshArr
                if (hasValCount === paramsCount && paramsCount !== 0) {
                    // 对旧的请求参数做比较，如果相同，取以前的，避免重复请求
                    const dataCache = requestDataCache.current.filter(del => {
                        if (del.labelKey !== item.labelKey) return false;
                        const oldData = Object.keys(del.params);
                        const newData = Object.keys(params);
                        if (oldData.length !== newData.length) return false;
                        for (let i = 0; i < oldData.length; i++) {
                            if (del.params[oldData[i]] !== params[oldData[i]]) return false;
                        }
                        return true;
                    });

                    if (dataCache.length === 0) {
                        // 取接口值
                        const arrItem = new Promise((resolve, reject) => {
                            lib.request({
                                url: item.from,
                                data: params,
                                success: data => {
                                    const responseResult = {
                                        index,
                                        data: data,
                                        params: params,
                                        labelKey: item.labelKey,
                                    };
                                    requestDataCache.current.push(responseResult);
                                    resolve(responseResult);
                                },
                                fail: () => {
                                    reject();
                                },
                            });
                        });
                        refreshArr.push(arrItem);
                    } else {
                        // 取缓存值
                        refreshArr.push(
                            new Promise((resolve, reject) => {
                                resolve({ ...dataCache[0] });
                            }),
                        );
                    }
                }
            }
        });
        Promise.all(refreshArr)
            .then(res => {
                res.map(item => {
                    const { index, data } = item;
                    if ("declareConfigList" in data) {
                        config[index].list = data["declareConfigList"];
                        formRef.current?.form.setFields([{ name: item.labelKey, value: data.defaultCode }]);
                    } else if ("declareQueueList" in data) {
                        config[index].list = data["declareQueueList"];
                        formRef.current?.form.setFields([{ name: item.labelKey, value: data.declareQueue }]);
                    } else {
                        config[index].list = data;
                    }
                });
                setConfig([...config]);
            })
            .catch(err => {});
    };
    return (
        <NewModal
            title="修改订单报文传输企业"
            onOk={handleOk}
            onCancel={handleCancel}
            configList={config}
            visible={showModal}
            ref={ref => (formRef.current = ref)}
            editRow={{}}
            formItemLayout={{
                labelCol: { span: 8 },
                wrapperCol: { span: 16 },
            }}
            modalStyle={{ width: "500px" }}
            onValuesChange={e => {
                for (let i in e) {
                    getDataFromFormChange(i, e[i]);
                }
            }}
        />
    );
}

export default EditDDCBWCSModal;
