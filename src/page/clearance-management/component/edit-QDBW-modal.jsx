import React, { useState, useEffect, useRef } from "react";
import { Config<PERSON>enter, ConfigFormCenter, lib } from "react-single-app";
import { Checkbox, Button, Modal, Input, Select, Form, message, Space, Switch, Row } from "antd";
import NewModal from "../../../components/NewModal";
function EditQDBWModal({ showModal, dialogClose, idList }) {
    const formValues = useRef({});
    const recodeSources = useRef({});
    const requestDataCache = useRef([]);
    const formRef = useRef(null);
    const [config, setConfig] = useState([
        {
            type: "SELECT",
            labelName: "清单报文传输企业",
            labelKey: "assureCompanyId",
            list: [],
            required: true,
            from: "/ccs/company/listWithSBQY",
        },
        {
            type: "SELECT",
            labelName: "传输节点",
            labelKey: "dxpId",
            list: [],
            // required: true,
            from: "/ccs/company/listDxpIdById",
            allowClear: true,
            fromParams: [{ key: "id", from: "assureCompanyId", required: true }],
        },
        {
            type: "SELECT",
            labelName: "隔离队列",
            labelKey: "declareQueue",
            list: [],
            allowClear: true,
            from: "/ccs/company/viewDeclareQueueByDxpId",
            // required: true,
            fromParams: [
                { key: "companyId", from: "assureCompanyId", required: true },
                { key: "dxpId", from: "dxpId", required: true },
            ],
        },
        {
            type: "SELECT",
            labelName: "当前方式",
            labelKey: "declareCode",
            list: [],
            required: true,
            allowClear: true,
            from: "/ccs/company/viewDeclareWayByDxpId",
            valType: "inventory",
            fromParams: [
                { key: "companyId", from: "assureCompanyId", required: true },
                { key: "dxpId", from: "dxpId", required: true },
                { key: "type", default: "inventory", required: true },
            ],
        },
        {
            type: "SELECT",
            labelName: "代理申报",
            labelKey: "proxyCode",
            list: [],
            required: false,
            from: "/ccs/declareWay/listByType",
            valType: "inventory",
            allowClear: true,
            fromParams: [
                // {key:"companyId", from:"assureCompanyId", required:true},
                // {key:"dxpId", from: "dxpId", required:true},
                { key: "type", default: "inventory", required: true },
            ],
        },
        {
            type: "SELECT",
            labelName: "当前方式（清关撤单）",
            labelKey: "inventoryCancelDeclareCode",
            list: [],
            allowClear: true,
            required: true,
            from: "/ccs/company/viewDeclareWayByDxpId",
            valType: "inventoryCancel",
            fromParams: [
                { key: "companyId", from: "assureCompanyId", required: true },
                { key: "dxpId", from: "dxpId", required: true },
                { key: "type", default: "inventoryCancel", required: true },
            ],
        },
        {
            type: "SELECT",
            labelName: "代理申报",
            labelKey: "inventoryCancelProxyCode",
            list: [],
            from: "/ccs/declareWay/listByType",
            valType: "inventoryCancel",
            allowClear: true,
            fromParams: [
                // {key:"companyId", from:"assureCompanyId", required:true},
                // {key:"dxpId", from: "dxpId", required:true},
                { key: "type", default: "inventoryCancel", required: true },
            ],
        },
        {
            type: "SELECT",
            labelName: "当前方式（清关退货）",
            labelKey: "inventoryRefundDeclareCode",
            list: [],
            required: true,
            allowClear: true,
            from: "/ccs/company/viewDeclareWayByDxpId",
            valType: "inventoryRefund",
            fromParams: [
                { key: "companyId", from: "assureCompanyId", required: true },
                { key: "dxpId", from: "dxpId", required: true },
                { key: "type", default: "inventoryRefund", required: true },
            ],
        },
        {
            type: "SELECT",
            labelName: "代理申报",
            labelKey: "inventoryRefundProxyCode",
            list: [],
            from: "/ccs/declareWay/listByType",
            valType: "inventoryRefund",
            allowClear: true,
            fromParams: [
                // {key:"companyId", from:"assureCompanyId", required:true},
                // {key:"dxpId", from: "dxpId", required:true},
                { key: "type", default: "inventoryRefund", required: true },
            ],
        },
    ]);
    const [form] = Form.useForm();
    const handleOk = val => {
        // 数据处理
        const proxyDeclareConfigList = [];
        const maps = {};
        config.map(item => {
            maps[item.labelKey] = item.valType;
        });
        for (let i in val) {
            if (i === "dxpId") continue;
            if (i === "assureCompanyId") continue;
            if (i === "declareQueue") continue;
            if (
                proxyDeclareConfigList.filter(item => {
                    return item.type === maps[i];
                }).length === 0
            ) {
                proxyDeclareConfigList.push({
                    declareCode: i.toLocaleLowerCase().indexOf("declarecode") > -1 ? val[i] : "",
                    proxyCode: i.toLocaleLowerCase().indexOf("proxycode") > -1 ? val[i] : "",
                    type: maps[i],
                });
            } else {
                proxyDeclareConfigList.forEach(item => {
                    if (item.type === maps[i]) {
                        if (i.toLocaleLowerCase().indexOf("declarecode") > -1) {
                            item.declareCode = val[i];
                        }
                        if (i.toLocaleLowerCase().indexOf("proxycode") > -1) {
                            item.proxyCode = val[i];
                        }
                    }
                });
            }
        }
        lib.request({
            url: "/ccs/route/batchUpdateMessageTransCompany",
            data: {
                idList: idList,
                type: "DECLARE_INVENTORY",
                companyId: val.assureCompanyId,
                dxpId: val.dxpId,
                declareQueue: val.declareQueue,
                proxyDeclareConfigList: proxyDeclareConfigList,
            },
            fail: () => {},
            success: () => {
                dialogClose(true);
            },
        });
    };

    const handleCancel = () => {
        dialogClose(false);
    };

    useEffect(() => {
        if (!showModal) {
            clearRequestCache();
        } else {
            init();
        }
    }, [showModal]);

    const init = () => {
        config.map((item, index) => {
            if (!item.fromParams) {
                lib.request({
                    url: item.from,
                    success: data => {
                        if ("declareConfigList" in data) {
                            config[index].list = data["declareConfigList"];
                            formRef.current?.form.setFields([{ name: item.labelKey, value: data.defaultCode }]);
                        } else if ("declareQueueList" in data) {
                            config[index].list = data["declareQueueList"];
                            formRef.current?.form.setFields([{ name: item.labelKey, value: data.declareQueue }]);
                        } else {
                            config[index].list = data;
                        }
                        setConfig([...config]);
                    },
                });
            } else {
                // 收集依赖项对应的改变项，方便做状态重置
                item.fromParams.forEach((obj, i) => {
                    if (!obj.from) return;
                    if (recodeSources.current[obj.from]) {
                        recodeSources.current[obj.from].push(item.labelKey);
                    } else {
                        recodeSources.current[obj.from] = [item.labelKey];
                    }
                });
            }
        });
    };

    const clearRequestCache = () => {
        requestDataCache.current = [];
        recodeSources.current = {};
        formValues.current = {};
    };

    const getDataFromFormChange = (name, value) => {
        formValues.current[name] = value;
        // 重置改变的表单项所对应的依赖项的值
        if (Array.isArray(recodeSources.current[name])) {
            const values = recodeSources.current[name].map(obj => {
                return { name: obj, value: null };
            });
            formRef.current?.form.setFields(values);
        } else {
            // 不影响其它FormItem的不做状态刷新
            return;
        }

        // 刷新数据请求记录
        const refreshArr = [];
        config.forEach((item, index) => {
            const { fromParams } = item;
            if (Array.isArray(fromParams) && fromParams.length !== 0) {
                const params = {};
                let paramsCount = 0; // 必填的参数总数量
                let hasValCount = 0; // 已经有值的参数数量
                fromParams.forEach(obj => {
                    if (obj.required) {
                        paramsCount++;
                    }
                    if (obj.default || formValues.current[obj.from]) {
                        params[obj.key] = formValues.current[obj.from] || obj.default;
                        hasValCount++;
                    }
                });
                // 当需要的参数全有时，推进refreshArr
                if (hasValCount === paramsCount && paramsCount !== 0) {
                    // 对旧的请求参数做比较，如果相同，取以前的，避免重复请求
                    const dataCache = requestDataCache.current.filter(del => {
                        if (del.labelKey !== item.labelKey) return false;
                        const oldData = Object.keys(del.params);
                        const newData = Object.keys(params);
                        if (oldData.length !== newData.length) return false;
                        for (let i = 0; i < oldData.length; i++) {
                            if (del.params[oldData[i]] !== params[oldData[i]]) return false;
                        }
                        return true;
                    });

                    if (dataCache.length === 0) {
                        // 取接口值
                        const arrItem = new Promise((resolve, reject) => {
                            lib.request({
                                url: item.from,
                                data: params,
                                success: data => {
                                    const responseResult = {
                                        index,
                                        data: data,
                                        params: params,
                                        labelKey: item.labelKey,
                                    };
                                    requestDataCache.current.push(responseResult);
                                    resolve(responseResult);
                                },
                                fail: () => {
                                    reject();
                                },
                            });
                        });
                        refreshArr.push(arrItem);
                    } else {
                        // 取缓存值
                        refreshArr.push(
                            new Promise((resolve, reject) => {
                                const data = { ...dataCache[0] };
                                resolve(data);
                            }),
                        );
                    }
                }
            }
        });
        Promise.all(refreshArr)
            .then(res => {
                res.map(item => {
                    const { index, data } = item;
                    if ("declareConfigList" in data) {
                        config[index].list = data["declareConfigList"];
                        formRef.current?.form.setFields([{ name: item.labelKey, value: data.defaultCode }]);
                    } else if ("declareQueueList" in data) {
                        config[index].list = data["declareQueueList"];
                        formRef.current?.form.setFields([{ name: item.labelKey, value: data.declareQueue }]);
                    } else {
                        config[index].list = data;
                    }
                });
                setConfig([...config]);
            })
            .catch(err => {});
    };

    return (
        <NewModal
            title="修改清单报文传输企业"
            onOk={handleOk}
            onCancel={handleCancel}
            configList={config}
            visible={showModal}
            ref={ref => (formRef.current = ref)}
            editRow={{}}
            formItemLayout={{
                labelCol: { span: 8 },
                wrapperCol: { span: 16 },
            }}
            modalStyle={{ width: "500px" }}
            onValuesChange={e => {
                for (let i in e) {
                    getDataFromFormChange(i, e[i]);
                }
            }}
        />
    );
}
export default EditQDBWModal;
