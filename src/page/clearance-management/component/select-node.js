import React, { useState, useEffect, useRef } from "react";
import { Input, Button, message, Form } from "antd";
import { DeleteTwoTone, PlusCircleTwoTone } from "@ant-design/icons";
import NewModal from "../../../components/NewModal";
import { lib } from "react-single-app";
import config from "./config";
import "./select-node.less";
const { columns } = config;

const MAX_NODE_LENGTH = 3;

const operateValueOfFormItem = value => {
    const arr1 = [];
    const arr2 = [];
    if (Array.isArray(value)) {
        for (let i = 0; i < value.length; i++) {
            if (value[i].nodeCode == 2) {
                arr1.push(value[i]);
            } else if (value[i].nodeCode == 3) {
                arr2.push(value[i]);
            }
        }
    }
    return {
        twoNodes: arr1,
        threeNodes: arr2,
    };
};

const SelectNode = ({
    type = "", //crossBorder 跨境 ；noCrossBorder 非跨境
    value,
    onChange,
    disabled,
}) => {
    const [twoNodes, setTwoNodes] = useState(operateValueOfFormItem(value).twoNodes);
    const [threeNodes, setThreeNodes] = useState(operateValueOfFormItem(value).threeNodes);
    const [editRow, setEditRow] = useState({});
    const [visible, setVisible] = useState(false);
    const [config, setConfig] = useState(columns);
    const [form] = Form.useForm();

    const curentNodeData = useRef({});

    useEffect(() => {
        if (value) {
            try {
                const valueObject = JSON.parse(value);
                form.setFieldsValue(valueObject);
            } catch (error) {}
        }
    }, [value]);

    const declareConfig = () => {
        const arr = columns.map(item => {
            return new Promise((resolve, reject) => {
                lib.request({
                    url: item.from ? item.from : "/ccs/declareWay/listByTypeAndNode",
                    data: item.from
                        ? {}
                        : {
                              type: item.labelKey,
                              transNode: curentNodeData.current?.nodeCode,
                          },
                    needMask: true,
                    success: data => {
                        resolve(data);
                    },
                    fail: (msg, code, data) => {
                        reject();
                    },
                });
            });
        });
        Promise.all(arr)
            .then(res => {
                res.map((item, index) => {
                    columns[index].list = item;
                });
                setConfig([...columns]);
            })
            .catch(err => {});
    };
    /**
     *
     * @param {*} item
     * @param {*} nodeType 1｜2
     * @param {*} index
     * @returns
     */
    const singleInput = (item, nodeType, index) => {
        return (
            <div key={index} className="single-input common-row">
                <Input
                    className="common-wid"
                    disabled={disabled}
                    value={item.dxpId}
                    onChange={e => {
                        changeNode(nodeType, e.target.value, index);
                    }}
                />
                {type === "crossBorder" && (
                    <Button
                        type="link"
                        disabled={disabled}
                        onClick={() => {
                            openModal(item, nodeType, index);
                        }}>
                        申报配置
                    </Button>
                )}
                <Button
                    type="link"
                    shape="circle"
                    icon={<DeleteTwoTone />}
                    disabled={disabled}
                    onClick={() => {
                        delNode(nodeType, index);
                    }}
                />
            </div>
        );
    };

    /**
     * 打开申报设置modal
     * @param {*} obj
     * @param {*} index
     */
    const openModal = (obj, nodeType, index) => {
        if (nodeType === 1) {
            curentNodeData.current = {
                nodeCode: 2,
                index,
            };
        } else {
            curentNodeData.current = {
                nodeCode: 3,
                index,
            };
        }
        declareConfig();
        try {
            if (obj.config) {
                const arr = JSON.parse(obj.config);
                arr.push({
                    type: "declareQueue",
                    declareCode: obj.declareQueue,
                });
                const result = {};
                arr.map(item => {
                    result[item.type] = item.declareCode;
                });
                setEditRow(result);
            }
        } catch (error) {
            console.log(error);
        }
        setVisible(true);
    };

    /**
     * 修改节点的drx值
     * @param {*} nodeType
     * @param {*} val
     * @param {*} index
     */
    const changeNode = (nodeType, val, index) => {
        if (nodeType === 1) {
            twoNodes[index].dxpId = val;
            setTwoNodes([...twoNodes]);
        } else {
            threeNodes[index].dxpId = val;
            setThreeNodes([...threeNodes]);
        }
        setFormItemOfValue();
    };

    /**
     * 删除节点
     * @param {*} nodeType
     * @param {*} index
     */
    const delNode = (nodeType, index) => {
        if (nodeType === 1) {
            twoNodes.splice(index, 1);
            setTwoNodes([...twoNodes]);
        } else {
            threeNodes.splice(index, 1);
            setThreeNodes([...threeNodes]);
        }
        setFormItemOfValue();
    };

    /**
     * 增加节点
     * @param {*} nodeType
     * @returns
     */
    const addNode = nodeType => {
        if (nodeType === 1) {
            if (twoNodes.length >= MAX_NODE_LENGTH) {
                message.warning("最多3条");
                return;
            }
            twoNodes.push({ nodeCode: 2, dxpId: "", config: "" });
            setTwoNodes([...twoNodes]);
        } else {
            if (threeNodes.length >= MAX_NODE_LENGTH) {
                message.warning("最多3条");
                return;
            }
            threeNodes.push({ nodeCode: 3, dxpId: "", config: "" });
            setThreeNodes([...threeNodes]);
        }
        setFormItemOfValue();
    };

    const setFormItemOfValue = () => {
        const result = [];
        for (let i = 0; i < Math.max(twoNodes.length, threeNodes.length); i++) {
            if (twoNodes[i]) {
                twoNodes[i].index = i;
                result.push(twoNodes[i]);
            }
            if (threeNodes[i]) {
                threeNodes[i].index = i;
                result.push(threeNodes[i]);
            }
        }
        onChange(result);
    };

    const handleOk = val => {
        let nodes;
        if (curentNodeData.current?.nodeCode === 2) {
            nodes = twoNodes;
        } else if (curentNodeData.current?.nodeCode === 3) {
            nodes = threeNodes;
        }
        const results = [];
        let declareQueue = "";
        for (let i in val) {
            if (i === "declareQueue") {
                declareQueue = val["declareQueue"];
                continue;
            }
            results.push({
                type: i,
                declareCode: val[i],
            });
        }
        const str = JSON.stringify(results);
        for (let i = 0; i < nodes.length; i++) {
            if (i === curentNodeData.current.index) {
                nodes[i].config = str;
                nodes[i].declareQueue = declareQueue;
            }
        }
        setFormItemOfValue();

        setVisible(false);
    };

    const handleCancel = () => {
        setVisible(false);
        setEditRow({});
        curentNodeData.current = {};
    };

    const props = {
        title: "申报设置",
        onOk: handleOk,
        onCancel: handleCancel,
        configList: config,
        visible,
        form: form,
        editRow: editRow,
        formItemLayout: {
            labelCol: { span: 8 },
            wrapperCol: { span: 14 },
        },
        modalStyle: { width: "500px" },
    };

    return (
        <div className="select-node">
            <div className="select-node-left">
                <h4>二级节点</h4>
                <div>
                    {twoNodes.map((item, index) => {
                        return singleInput(item, 1, index);
                    })}
                    <Button
                        className="common-wid common-row"
                        disabled={disabled}
                        onClick={() => {
                            addNode(1);
                        }}>
                        <PlusCircleTwoTone />
                        新增
                    </Button>
                </div>
            </div>
            <div className="selet-node-right">
                <h4>三级节点</h4>
                <div>
                    {threeNodes.map((item, index) => {
                        return singleInput(item, 2, index);
                    })}
                    <Button
                        className="common-wid common-row"
                        disabled={disabled}
                        onClick={() => {
                            addNode(2);
                        }}>
                        <PlusCircleTwoTone />
                        新增
                    </Button>
                </div>
            </div>
            <NewModal {...props} />
        </div>
    );
};
export default SelectNode;
