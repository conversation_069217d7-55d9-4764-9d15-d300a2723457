import React, { useState, useEffect, useRef } from "react";
import { lib } from "react-single-app";
import { DTEditForm, DTEditFormConfigs } from "@dt/components";
import { Modal } from "antd";
export default ({ showModal, idList, dialogClose }) => {
    const ref = useRef();

    return (
        <Modal
            open={showModal}
            title={"批量设置标签"}
            onOk={() => {
                ref.current.form.validateFields().then(res => {
                    lib.request({
                        url: "/ccs/route/batchUpdateRouteTag",
                        data: {
                            ...res,
                            idList: idList,
                        },
                        success: () => {
                            dialogClose(true);
                        },
                    });
                });
                // dialogClose()
            }}
            onCancel={() => {
                dialogClose();
            }}>
            <DTEditForm
                ref={ref}
                layout={{
                    mode: "appoint",
                    colNum: 1,
                }}
                configs={[
                    {
                        type: "SELECT",
                        fProps: {
                            name: "routeTag",
                            label: "路径标签",
                            rules: [{ required: true, message: "请选择路径标签" }],
                        },
                        cProps: {},
                        list: [],
                        dataUrl: "/ccs/dictionary/listRouteTag",
                    },
                ]}
            />
        </Modal>
    );
};
