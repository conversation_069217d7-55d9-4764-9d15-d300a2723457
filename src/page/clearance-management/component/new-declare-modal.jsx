import React, { useState, useEffect, useRef } from "react";
import { ConfigCenter, ConfigFormCenter, lib } from "react-single-app";
import { Checkbox, Button, Modal, Input, Select, Form, message, Space, Switch, Row } from "antd";

class NewDeclareModal extends React.Component {
    constructor() {
        super();
        this.state = {
            actionList: [],
        };
        this.formRef = React.createRef();
    }

    isShowItem(item, nowType) {
        // 清单申报时填写电商平台，电商企业，担保企业，清单报文传输企业
        if (
            nowType.indexOf("DECLARE_INVENTORY") !== -1 &&
            (item.labelName === "清单报文传输企业" ||
                item.labelName === "电商企业" ||
                item.labelName === "账册编号" ||
                item.labelName === "电商平台" ||
                item.labelName === "担保企业" ||
                item.labelName === "清单传输节点")
        ) {
            return true;
        }
        // 订单申报
        if (
            nowType.indexOf("DECLARE_ORDER") !== -1 &&
            (item.labelName === "订单报文传输企业" ||
                item.labelName === "电商企业" ||
                item.labelName === "电商平台" ||
                item.labelName === "账册编号" ||
                item.labelName === "担保企业" ||
                item.labelName === "订单传输节点")
        ) {
            return true;
        }
        if (
            nowType.indexOf("DECLARE_ORDER") !== -1 &&
            nowType.indexOf("DECLARE_INVENTORY") !== -1 &&
            (item.labelName === "订单报文传输节点" || item.labelName === "运单报文传输节点")
        ) {
            return true;
        }
        // 支付单申报
        if (nowType.indexOf("DECLARE_PAYMENT") !== -1 && item.labelName === "电商平台") {
            return true;
        }
        // 运单申报
        if (
            nowType.indexOf("DECLARE_LOGISTICS") !== -1 &&
            (item.labelName === "运单报文传输企业" ||
                item.labelName === "电商平台" ||
                item.labelName === "运单传输节点")
        ) {
            return true;
        }
        return false;
    }

    conversionData(data) {
        if (!data) return;
        Object.keys(data).map(key => {
            if (data[key] && typeof data[key] === "number") {
                data[key] = String(data[key]);
            }
        });
        return data;
    }

    proxyConfig(type, companyId, nodeVal) {
        this.props.showProxyConfigModal(type, nodeVal, companyId);
    }

    clearProxyConfigDetailByType(type) {
        this.props.clearProxyConfigDetailByType(type);
    }

    setDymanicSelectData(labelKey, val) {
        // 重置依赖labelKey项的值为空
        const { groupList } = this.props;
        const keys = [];
        groupList.forEach(item => {
            if (Array.isArray(item.listResult) && item.listResult.includes(labelKey)) {
                keys.push(item.labelKey);
            }
        });
        this.formRef.current.setFields(
            keys.map(item => {
                return { name: item, value: undefined };
            }),
        );

        // 更新依赖labelKey所对应的list
        this.props.setDymanicSelectData(labelKey, val);
    }
    render() {
        let {
            handleOk,
            handleCancel,
            visible,
            flag,
            form,
            editRow,
            title,
            actionList,
            groupList,
            selectChange,
            nowType,
        } = this.props;
        const formItemLayout = {
            labelCol: { span: 6 },
            wrapperCol: { span: 14 },
        };
        editRow = this.conversionData(editRow);
        return (
            <Modal
                okText="确定"
                cancelText="取消"
                title={title}
                open={visible}
                onOk={() => {
                    flag
                        ? handleCancel()
                        : this.formRef.current.validateFields().then(values => handleOk(values, this.formRef.current));
                }}
                width={800}
                maskClosable={false}
                onCancel={() => {
                    this.formRef.current.resetFields();
                    handleCancel();
                }}
                destroyOnClose>
                <Form ref={this.formRef} initialValues={editRow}>
                    <Form.Item
                        {...formItemLayout}
                        label="路径名称"
                        name="name"
                        rules={[{ required: true, message: "请输入路径名称", whitespace: true }]}>
                        <Input maxLength={32} disabled={flag} />
                    </Form.Item>
                    <Form.Item
                        {...formItemLayout}
                        label="路径标识"
                        name="code"
                        rules={[{ required: true, message: "请输入路径标识", whitespace: true }]}>
                        <Input maxLength={32} disabled={flag} />
                    </Form.Item>
                    <Form.Item
                        {...formItemLayout}
                        label="申报项"
                        name="actionList"
                        rules={[{ required: true, message: "请输入申报项" }]}>
                        <Checkbox.Group onChange={selectChange} disabled={flag}>
                            {actionList.map((item, index) => (
                                <Checkbox value={item.id} key={index}>
                                    {item.name}
                                </Checkbox>
                            ))}
                        </Checkbox.Group>
                    </Form.Item>
                    {groupList.map((item, index) => {
                        let itemLayout = item.formItemLayout || formItemLayout;
                        let hasProConfig =
                            item.labelKey === "orderDeclareDxpId" ||
                            item.labelKey === "logisticsDeclareDxpId" ||
                            item.labelKey === "listDeclareDxpId";
                        return (
                            this.isShowItem(item, nowType) &&
                            (hasProConfig ? (
                                <Form.Item
                                    required={true}
                                    allowClear
                                    {...itemLayout}
                                    key={index}
                                    label={item.labelName}
                                    tooltip={item.tooltip}>
                                    <Row>
                                        <Form.Item
                                            required
                                            style={{ width: "78%", marginBottom: "unset" }}
                                            name={item.labelKey}
                                            tooltip={item.tooltip}
                                            rules={[{ required: true, message: `请选择${item.labelName}` }]}>
                                            <Select
                                                // disabled={flag || item.list.length === 0}
                                                disabled={flag}
                                                showSearch
                                                optionFilterProp="children"
                                                allowClear
                                                onChange={e => {
                                                    this.clearProxyConfigDetailByType(item.type);
                                                }}>
                                                {item.list.map(ite => (
                                                    <Select.Option key={ite.id} value={String(ite.id)}>
                                                        {ite.name}
                                                    </Select.Option>
                                                ))}
                                            </Select>
                                        </Form.Item>
                                        <Button
                                            type={"link"}
                                            onClick={() => {
                                                let fieldsValue = this.formRef.current.getFieldValue(item.labelKey);
                                                let companyValue;
                                                const result = groupList.filter(obj => {
                                                    if (item.type === obj.type) {
                                                        return true;
                                                    }
                                                });
                                                if (result.length > 0) {
                                                    companyValue = this.formRef.current.getFieldValue(
                                                        result[0].labelKey,
                                                    );
                                                }
                                                if ((fieldsValue && companyValue) || flag) {
                                                    this.proxyConfig(item.type, fieldsValue, companyValue);
                                                } else {
                                                    message.error(`${item.labelName}未选择，请先选择再配置`);
                                                }
                                            }}>
                                            代理申报配置
                                        </Button>{" "}
                                    </Row>
                                </Form.Item>
                            ) : (
                                <Form.Item
                                    {...formItemLayout}
                                    key={index}
                                    label={item.labelName}
                                    name={item.labelKey}
                                    rules={[{ required: true, message: `请输入${item.labelName}` }]}>
                                    <Select
                                        disabled={flag}
                                        style={{ width: "100%" }}
                                        showSearch
                                        optionFilterProp="children"
                                        allowClear
                                        onChange={e => {
                                            if (item.listSource) {
                                                this.setDymanicSelectData(item.labelKey, e);
                                            }
                                        }}>
                                        {item.list.map(ite => (
                                            <Select.Option key={ite.id} value={String(ite.id)}>
                                                {ite.name}
                                            </Select.Option>
                                        ))}
                                    </Select>
                                </Form.Item>
                            ))
                        );
                    })}
                </Form>
            </Modal>
        );
    }
}
export default NewDeclareModal;
