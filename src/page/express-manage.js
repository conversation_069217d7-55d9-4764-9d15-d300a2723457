import React from "react";
import { Checkbox, But<PERSON>, Modal, Drawer, Input, Select, Form, Cascader, Table, message, Space, Switch } from "antd";
import NewModal from "../components/NewModal";
import { area_data } from "../common/areas";
import { ConfigCenter, lib } from "react-single-app";
// import FormItem from "antd/lib/form/FormItem";
const FormItem = Form.Item;
const { Option } = Select;
class App extends ConfigCenter {
    constructor(props) {
        super(props);
        this.state.modalTitle = "新增快递";
        this.state.upUrl = "/ccs/express/upset";
        this.state.configModalVisible = false;
        this.state.changeList = [];
        this.state.declareSystemList = [];
        this.state.modalDataList = [];
        this.formRef = React.createRef();
        this.state.configList = [
            {
                type: "INPUT",
                labelName: "快递名称",
                labelKey: "name",
                required: true,
                message: "请输入快递名称",
                maxLength: 32,
            },
            {
                type: "INPUT",
                labelName: "快递标识",
                labelKey: "code",
                required: true,
                message: "请输入快递标识",
                maxLength: 32,
            },
            {
                type: "SELECT",
                labelName: "物流企业",
                labelKey: "expressCompanyId",
                required: true,
                message: "请选择物流企业",
                list: [],
                ccs: "/ccs/company/listWithWLQY",
            },
        ];
        this.state.area_data = [];
        this.state.citys = [];
        this.state.areas = [];
        this.formRef = React.createRef();
    }
    componentDidMount() {
        // lib.checkIsLogin()
        this.getConfigList();
        this.setState({
            area_data: this.data2areas(area_data),
        });
    }

    fetchExpressList(id) {
        lib.request({
            url: "/ccs/express-filter/list",
            method: "GET",
            data: {
                id,
            },
            needMask: true,
            success: res => {
                if (res) {
                    this.setState({
                        changeList: res,
                    });
                }
            },
        });
    }

    fetchDeclareSystem() {
        lib.request({
            url: "/ccs/express/listLogisticsDeclareSystem",
            method: "GET",
            needMask: true,
            success: res => {
                if (res) {
                    this.setState({
                        declareSystemList: res,
                    });
                }
            },
        });
    }

    fetchRowDetail(id) {
        lib.request({
            url: "/ccs/express-filter/paging",
            data: {
                currentPage: 1,
                pageSize: 9999,
                id,
            },
            needMask: true,
            success: res => {
                if (res) {
                    this.setState({
                        modalDataList: res.dataList,
                    });
                }
            },
        });
    }

    data2areas(area_data) {
        const theData = [];
        if (!Array.isArray(area_data)) {
            Object.keys(area_data).map(item => {
                theData.push({
                    label: item,
                    value: item,
                    children: this.data2areas(area_data[item]),
                });
            });
        } else {
            area_data.map(ite => {
                theData.push({
                    label: ite,
                    value: ite,
                });
            });
        }
        return theData;
    }

    // 获取configList 里面的下拉数据
    getConfigList() {
        let configList = this.state.configList;
        configList.map(item => {
            if (item.ccs) {
                lib.request({
                    url: item.ccs,
                    needMask: false,
                    success: res => {
                        item.list = res || [];
                        this.setState({ configList });
                    },
                });
            }
        });
        this.setState({ configList });
    }

    newModalFunc() {
        this.setState({
            visible: true,
            modalTitle: "新增快递",
            editRow: null,
        });
        this.getConfigList();
    }

    renderRightOperation() {
        return (
            <React.Fragment>
                <Button type="primary" onClick={() => this.newModalFunc()}>
                    新增快递
                </Button>
            </React.Fragment>
        );
    }

    handleOk(values, modalForm) {
        if (this.state.editRow) {
            values.id = this.state.editRow.id;
        }
        for (let i in values) {
            if (values[i] === null) {
                delete values[i];
            }
        }
        delete values.expressCompanyName;
        lib.request({
            url: this.state.upUrl,
            data: values,
            method: "POST",
            needMask: true,
            success: res => {
                if (res) {
                    this.setState({
                        visible: false,
                        editRow: null,
                    });
                    this.load(true);
                }
            },
        });
    }

    handleCancel() {
        this.setState({
            visible: false,
            editRow: null,
        });
    }

    configModalOk() {
        this.formRef.current.validateFields().then(values => {
            lib.request({
                url: "/ccs/express/config",
                data: { ...values, id: this.state.editRow.id },
                method: "POST",
                needMask: true,
                success: res => {
                    message.success("操作成功");
                    this.formRef.current.resetFields();
                    this.setState({
                        configModalVisible: false,
                        editRow: null,
                    });
                    this.load(true);
                },
            });
        });
    }

    addConfig() {
        const { replaceExpressId, prov, city, area, editRow } = this.state;
        if (replaceExpressId && prov) {
            lib.request({
                url: "/ccs/express-filter/upset",
                method: "POST",
                data: {
                    area,
                    city,
                    prov,
                    // replaceExpressId
                    replaceExpressId: Number(replaceExpressId),
                    refExpressId: editRow.id,
                },
                needMask: true,
                success: res => {
                    if (res.result == "创建成功") {
                        this.fetchRowDetail(editRow.id);
                        this.setState({
                            replaceExpressId: "",
                            area: "",
                            city: "",
                            prov: "",
                        });
                    } else {
                        message.error(res.errorMessage);
                    }
                },
            });
        }
    }

    cancelFunc() {
        this.setState({
            configModalVisible: false,
            editRow: {},
            prov: "",
            city: "",
            area: "",
            replaceExpressId: "",
        });
    }

    cascaderChange(e) {
        this.setState({
            prov: e[0],
            city: e[1],
            area: e[2],
        });
    }

    provChange(e) {
        let { area_data } = this.state,
            citys = [];
        area_data.map(item => {
            if (item.value === e) {
                citys = item.children;
            }
        });
        this.setState({
            prov: e,
            citys,
            city: "",
            area: "",
            areas: [],
        });
    }

    cityChange(e) {
        let { citys } = this.state,
            areas = [];
        citys.map(item => {
            if (item.value === e) {
                areas = item.children;
            }
        });
        this.setState({
            city: e,
            areas,
            area: "",
        });
    }

    renderModal() {
        let {
            modalTitle,
            configList,
            visible,
            editRow,
            configModalVisible,
            area_data,
            changeList,
            declareSystemList,
            modalDataList,
            citys,
            areas,
            prov,
            city,
            area,
        } = this.state;
        let props = {
            title: modalTitle,
            onOk: this.handleOk.bind(this),
            onCancel: this.handleCancel.bind(this),
            configList,
            visible,
            // form: this.props.form,
            editRow,
        };
        const columns = [
            {
                title: "序号",
                dataIndex: "idx",
            },
            {
                title: "省",
                dataIndex: "prov",
            },
            {
                title: "市",
                dataIndex: "city",
            },
            {
                title: "区",
                dataIndex: "area",
            },
            {
                title: "替换快递",
                dataIndex: "replaceExpressName",
                render: param => {
                    return param ? param : "不替换";
                },
            },
            {
                title: "操作",
                render: row => {
                    return (
                        <a
                            className="link"
                            onClick={() => {
                                let modal = Modal.confirm({
                                    cancelText: "取消",
                                    okText: "确定",
                                    title: "确认删除吗？",
                                    onOk: () => {
                                        lib.request({
                                            url: "/ccs/express-filter/delete",
                                            data: {
                                                id: row.id,
                                            },
                                            needMask: true,
                                            success: res => {
                                                if (res) {
                                                    message.success("删除配置成功");
                                                    this.fetchRowDetail(this.state.editRow.id);
                                                    modal.destroy();
                                                }
                                            },
                                        });
                                    },
                                });
                            }}>
                            删除
                        </a>
                    );
                },
            },
        ];
        const formItemLayout = {
            labelCol: { span: 12 },
            wrapperCol: { span: 12 },
        };
        return (
            <React.Fragment>
                <NewModal {...props} />
                <Modal
                    title="配置"
                    open={configModalVisible}
                    width={500}
                    onOk={this.configModalOk.bind(this)}
                    onCancel={() => this.cancelFunc()}>
                    <div style={{ overflow: "hidden" }}>
                        <Form layout="inline" ref={this.formRef}>
                            <FormItem
                                style={{ width: 400 }}
                                labelCol={{ span: 12 }}
                                wrapperCol={{ span: 12 }}
                                label="运单申报系统"
                                required
                                name="logisticDeclareSystem">
                                <Select showSearch optionFilterProp="children">
                                    {declareSystemList.map((item, index) => {
                                        return (
                                            <Option value={item.id} key={index}>
                                                {item.name}
                                            </Option>
                                        );
                                    })}
                                </Select>
                            </FormItem>
                        </Form>
                    </div>
                </Modal>
            </React.Fragment>
        );
    }

    handleEnable(row, enable) {
        let data = JSON.parse(JSON.stringify(row));
        data.enable = enable;
        delete data.expressCompanyName;
        lib.request({
            url: this.state.upUrl,
            data,
            method: "POST",
            needMask: true,
            success: res => {
                if (res) {
                    this.load(true);
                }
            },
        });
    }
    enableStatus(row) {
        return (
            <React.Fragment>
                {row.enable === 1 && <Switch checked={row.enable} onChange={() => this.enableFunc(row)}></Switch>}
                {row.enable === 0 && <Switch checked={row.enable} onChange={() => this.handleEnable(row, 1)}></Switch>}
            </React.Fragment>
        );
    }

    myOperation(row) {
        return (
            <React.Fragment>
                <Space>
                    {row.enable === 0 && (
                        <React.Fragment>
                            <span className="link" onClick={() => this.editFunc(row)}>
                                编辑
                            </span>
                        </React.Fragment>
                    )}
                    <span className="link" onClick={() => this.configModal(row)}>
                        配置
                    </span>
                </Space>
            </React.Fragment>
        );
    }

    enableFunc(row) {
        Modal.confirm({
            cancelText: "取消",
            okText: "确定",
            title: "提示",
            content: "是否禁用?",
            onOk: () => {
                message.success("禁用成功");
                this.handleEnable(row, 0);
            },
        });
    }

    editFunc(row) {
        row.expressCompanyId = String(row.expressCompanyId);
        this.setState({
            editRow: row,
            visible: true,
            modalTitle: "编辑快递",
        });
        this.getConfigList();
    }

    configModal(row) {
        this.setState(
            {
                configModalVisible: true,
                editRow: row,
            },
            () => {
                this.formRef.current &&
                    this.formRef.current.setFieldsValue({
                        logisticDeclareSystem: row.logisticDeclareSystem,
                    });
            },
        );

        this.fetchExpressList(row.id);
        this.fetchRowDetail(row.id);
        this.fetchDeclareSystem();
    }
}
export default App;
