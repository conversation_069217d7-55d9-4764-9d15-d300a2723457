import React from "react";
import "./shop-good.less";
import { Checkbox, But<PERSON>, Modal, Drawer, Input, Select, Form, Card, Row, Col, Table, Alert, message } from "antd";
import NewModal from "../components/NewModal";
import SelectNode from "./clearance-management/component/select-node";
import { ConfigCenter, lib } from "react-single-app";
import "../common/event";
const TextArea = Input.TextArea;
function getInitialValue(editRow, key) {
    if (!editRow) return "";
    return editRow[key];
}
class App extends React.Component {
    constructor(props) {
        super(props);
        this.formRef = React.createRef();
        this.state = {
            modalTitle: "新增口岸",
            title: "新增企业",
            qualifyList: [],
            guaranteeCustoms: [],
            needDXPID: false,
            needorderDxpId: false,
            crossBorder: false,
            noCrossBorder: false,
            guaranteeVisiter: false,
            hideInput: true,
            configList: [
                {
                    type: "SELECT",
                    labelName: "口岸名称",
                    labelKey: "customs",
                    required: true,
                    message: "请选择口岸名称",
                    list: [],
                    ccs: "/ccs/customs/listDistrict",
                    onChange: this.selectOnchange.bind(this),
                },
                {
                    type: "INPUT",
                    labelName: "海关检验检疫编码",
                    labelKey: "ciqCode",
                    required: true,
                    message: "请输入海关检验检疫编码",
                    maxLength: 50,
                },
                {
                    type: "INPUT",
                    labelName: "地方备案编码",
                    labelKey: "code",
                    required: true,
                    message: "请输入地方备案编码",
                    maxLength: 10,
                },
                {
                    type: "INPUT",
                    labelName: "海关备案企业名称",
                    labelKey: "name",
                    required: true,
                    message: "请输入海关备案企业名称",
                    maxLength: 50,
                },
                {
                    type: "INPUT",
                    labelName: "检验检疫发件人",
                    labelKey: "ciqSender",
                    required: true,
                    message: "请输入检验检疫发件人",
                    needHide: true,
                    hide: true,
                },
                {
                    type: "INPUT",
                    labelName: "检验检疫发货地址",
                    labelKey: "ciqSenderAddress",
                    required: true,
                    message: "请输入检验检疫发货地址",
                    needHide: true,
                    hide: true,
                },
                {
                    type: "INPUT",
                    labelName: "检验检疫发件人电话",
                    labelKey: "ciqSenderMobile",
                    required: true,
                    message: "请输入检验检疫发件人电话",
                    needHide: true,
                    hide: true,
                },
            ],
            upUrl: "/ccs/company/upset",
            detail: {
                districtList: [],
            },
            portList: [],
        };
        this.changeHandle = this.changeHandle.bind(this);
    }

    componentDidMount() {
        // lib.checkIsLogin()
        let id = lib.getParam("theid");
        // 获取当前企业详情
        id &&
            lib.request({
                // lib.request({
                url: "/ccs/company/getById",
                data: { id: lib.getParam("theid") },
                needMask: true,
                success: res => {
                    this.state.title = "编辑企业";
                    let detail = res;
                    detail?.districtList.map((item, index) => {
                        item.index = index;
                    });
                    if (res.qualifyList.indexOf("SBQY") !== -1) {
                        this.setState({
                            needDXPID: true,
                        });
                    }
                    if (res.qualifyList.indexOf("BWCSQY") !== -1) {
                        this.setState({
                            needorderDxpId: true,
                        });
                    }
                    // if()
                    this.changeHandle(res.qualifyList);
                    this.setState({
                        detail,
                    });
                    // })
                    setTimeout(() => {
                        this.formRef.current.resetFields();
                    }, 0);
                },
            });
        // 获取所有企业资质
        lib.request({
            url: "/ccs/company/listQualify",
            needMask: true,
            success: res => {
                if (res) {
                    this.setState({
                        qualifyList: res || [],
                    });
                }
            },
        });
        // 获取口岸列表
        lib.request({
            url: "/ccs/customs/listDistrict",
            success: res => {
                this.setState({
                    theDistrictList: res,
                });
            },
        });

        //获取保函关区列表
        lib.request({
            url: "/ccs/customs/dictionary/guaranteeCustoms",
            success: res => {
                this.setState({
                    guaranteeCustoms: res,
                });
            },
        });

        this.setState({ notEdit: lib.getParam("page_title") === "查看企业" ? true : false });
        this.getConfigList();
    }

    getConfigList() {
        // 获取configList 里面的下拉数据
        let configList = this.state.configList;
        configList.map(item => {
            if (item.ccs) {
                lib.request({
                    url: item.ccs,
                    method: "GET",
                    needMask: false,
                    success: res => {
                        item.list = res || [];
                        this.setState({ configList });
                    },
                });
            }
        });
        this.setState({ configList });
    }

    submitHandle(values) {
        // const { form } = this.props;
        const { detail } = this.state;
        this.formRef.current
            .validateFields()
            .then(values => {
                let districtList = JSON.parse(JSON.stringify(detail.districtList));
                if (districtList.length) {
                    districtList.map(item => {
                        delete item.index;
                    });
                }
                values.districtList = districtList;
                detail.id && (values.id = detail.id);
                lib.request({
                    url: this.state.upUrl,
                    data: values,
                    method: "POST",
                    needMask: true,
                    success: res => {
                        // if (res) {
                        Modal.success({
                            title: detail.id ? "编辑成功" : "创建成功",
                            okText: "关闭页面",
                            onOk() {
                                lib.closePage();
                            },
                        });
                        // }
                    },
                });
            })
            .catch(err => {
                console.error(err);
            });
    }

    changeHandle(e) {
        this.setState({
            needDXPID: e.indexOf("SBQY") !== -1,
            needorderDxpId: e.indexOf("BWCSQY") !== -1,
            crossBorder: e.indexOf("BWCSQY") !== -1,
            noCrossBorder: e.indexOf("SBQY") !== -1,
            guaranteeVisiter: e.indexOf("DBQY") !== -1,
        });
    }

    changeGuranteeHandle(e) {
        // this.setState({
        //     guaranteeCustoms:e
        // })
    }

    handleOk(values) {
        let { detail, configList, theDistrictList } = this.state,
            flag = false;
        values.index = this.state.editIndex;
        configList.map(item => {
            if (item.labelKey === "customs") {
                item.list.map(ite => {
                    if (ite.id === values.customs) {
                        values.customsName = ite.name;
                    }
                });
            }
        });
        if (this.state.editRow) {
            detail.districtList.map(item => {
                if (item.customs === values.customs && values.customs !== this.state.editRow.customs) {
                    message.warning("口岸已存在");
                    flag = true;
                    return;
                }
            });
            if (flag) return;
            detail.districtList.splice(values.index, 1, values);
        } else {
            detail.districtList.map(item => {
                if (item.customs === values.customs) {
                    message.warning("口岸已存在");
                    flag = true;
                    return;
                }
            });
            if (flag) return;
            console.log(detail.districtList);
            detail.districtList.push(values);
        }
        this.setState(
            {
                modalVisible: false,
                detail: {},
                editRow: null,
            },
            () => {
                this.setState({ detail });
            },
        );
    }

    handleCancel() {
        this.setState({
            modalVisible: false,
            editRow: null,
        });
    }

    selectOnchange(e) {
        if (e === "TIANJIN") {
            this.configListChange(false);
        } else {
            this.configListChange(true);
        }
    }

    configListChange(bool) {
        let { configList } = this.state;
        configList.map(item => {
            if (item.needHide) {
                item.hide = bool;
            }
        });
        this.setState({ configList });
    }

    render() {
        const formLayout = {
            labelCol: { span: 8 },
            wrapperCol: { span: 16 },
        };
        const { form } = this.props;
        const {
            qualifyList,
            needDXPID,
            detail,
            needorderDxpId,
            noCrossBorder,
            crossBorder,
            notEdit,
            guaranteeVisiter,
            guaranteeCustoms,
        } = this.state;
        const columns = [
            {
                title: "口岸名称",
                dataIndex: "customsName",
                className: "textAlignCenter",
            },
            {
                title: "海关检验检疫编码",
                dataIndex: "ciqCode",
                className: "textAlignCenter",
            },
            {
                title: "地方备案编码",
                dataIndex: "code",
                className: "textAlignCenter",
            },
            {
                title: "海关备案企业名称",
                dataIndex: "name",
                className: "textAlignCenter",
            },
            {
                title: "检验检疫发件人",
                dataIndex: "ciqSender",
                className: "textAlignCenter",
            },
            {
                title: "检验检疫发货地址",
                dataIndex: "ciqSenderAddress",
                className: "textAlignCenter",
            },
            {
                title: "检验检疫发货人电话",
                dataIndex: "ciqSenderMobile",
                className: "textAlignCenter",
            },
            {
                title: "操作",
                className: "textAlignCenter",
                render: (row, e, i) => {
                    return (
                        !notEdit && (
                            <React.Fragment>
                                <Button
                                    type="link"
                                    onClick={() => {
                                        this.configListChange(row.customs !== "TIANJIN");
                                        this.getConfigList();
                                        this.setState({
                                            modalVisible: true,
                                            editRow: row,
                                            editIndex: i,
                                            modalTitle: "编辑口岸",
                                        });
                                    }}>
                                    修改
                                </Button>
                                <span style={{ padding: "0 14px" }}>|</span>
                                <Button
                                    type="link"
                                    onClick={() => {
                                        Modal.confirm({
                                            cancelText: "取消",
                                            okText: "确定",
                                            title: "提示",
                                            content: "确认删除?",

                                            onOk: () => {
                                                let { detail } = this.state;
                                                detail.districtList.splice(row.index, 1);
                                                this.setState(
                                                    {
                                                        detail: {},
                                                    },
                                                    () => {
                                                        this.setState({ detail });
                                                    },
                                                );
                                            },
                                        });
                                    }}>
                                    删除
                                </Button>
                            </React.Fragment>
                        )
                    );
                },
            },
        ];
        let modalProps = {
            name: "modal",
            title: this.state.modalTitle,
            onOk: this.handleOk.bind(this),
            onCancel: this.handleCancel.bind(this),
            configList: this.state.configList,
            visible: this.state.modalVisible,
            editRow: this.state.editRow,
            modalStyle: { width: "700px" },
            formItemLayout: {
                labelCol: { span: 8 },
                wrapperCol: { span: 14 },
            },
        };
        // if (!detail) return ""

        return (
            <Card>
                <NewModal {...modalProps} />
                <h3>{this.state.title}</h3>
                <Form ref={this.formRef} layout="horizontal" initialValues={detail}>
                    <Card>
                        <Row>
                            <Col span={16}>
                                <Form.Item
                                    label="企业名称"
                                    // {...formLayout}
                                    labelCol={{ span: 6 }}
                                    wrapperCol={{ span: 18 }}
                                    name="name"
                                    rules={[{ required: true, message: "请输入企业名称", whitespace: true }]}>
                                    <Input maxLength={32} disabled={notEdit} />
                                </Form.Item>
                            </Col>
                            {/* <Col span={12}>
                                <Form.Item
                                    label="海关总署编码"
                                    {...formLayout}
                                    name="code"
                                    rules={[{ required: true, message: "请输入总署编码", whitespace: true }]}>
                                    <Input disabled={notEdit} />
                                </Form.Item>
                            </Col> */}
                        </Row>
                        <Row>
                            <Col span={16}>
                                <Form.Item
                                    label="统一社会信用代码"
                                    // {...formLayout}
                                    labelCol={{ span: 6 }}
                                    wrapperCol={{ span: 18 }}
                                    name="uniformSocialCreditCode"
                                    rules={[
                                        {
                                            required: true,
                                            message: "请输入统一社会信用代码",
                                        },
                                        // {
                                        //     pattern: /^([0-9A-HJ-NPQRTUWXY]{2}\d{6}[0-9A-HJ-NPQRTUWXY]{10}|[1-9]\d{14})$/,
                                        //     message: '请输入匹配的代码'
                                        // }
                                    ]}>
                                    <Input maxLength={32} disabled={notEdit} />
                                </Form.Item>
                            </Col>
                        </Row>
                        <Row>
                            <Col span={16}>
                                <Form.Item
                                    label="备注"
                                    {...formLayout}
                                    labelCol={{ span: 6 }}
                                    wrapperCol={{ span: 18 }}
                                    name="remark">
                                    <Input disabled={notEdit} maxLength={999} />
                                </Form.Item>
                            </Col>
                        </Row>
                        <Row>
                            <Col span={12}>
                                <Form.Item
                                    label="海关总署编码"
                                    {...formLayout}
                                    name="shipperAndConsigneeCode"
                                    rules={[
                                        {
                                            required: true,
                                            message: "请输入进出口收发货人",
                                        },
                                    ]}>
                                    <FormChildFn>
                                        {({ value, onChange }) => (
                                            <div style={{ display: "flex", flexDirection: "column" }}>
                                                进出口收发货人
                                                <Input
                                                    value={value}
                                                    onChange={onChange}
                                                    maxLength={10}
                                                    disabled={notEdit}
                                                />
                                            </div>
                                        )}
                                    </FormChildFn>
                                </Form.Item>
                            </Col>
                            <Col offset={2} span={10}>
                                <Form.Item
                                    labelCol={{ span: 4 }}
                                    wrapperCol={{ span: 20 }}
                                    // {...formLayout}
                                    label=""
                                    name="customsCompanyCode"
                                    rules={[
                                        {
                                            required: true,
                                            message: "请输入报关企业",
                                        },
                                    ]}>
                                    <FormChildFn>
                                        {({ value, onChange }) => (
                                            <div style={{ display: "flex", flexDirection: "column" }}>
                                                报关企业
                                                <Input
                                                    value={value}
                                                    onChange={onChange}
                                                    maxLength={10}
                                                    disabled={notEdit}
                                                />
                                            </div>
                                        )}
                                    </FormChildFn>
                                </Form.Item>
                            </Col>
                        </Row>
                        <Row>
                            <Col span={12}>
                                <Form.Item
                                    label="业务类型配置"
                                    {...formLayout}
                                    name="unifiedCrossBroderCode"
                                    rules={[
                                        {
                                            required: true,
                                            message: "请输入跨境进口统一",
                                        },
                                    ]}>
                                    <FormChildFn>
                                        {({ value, onChange }) => (
                                            <div style={{ display: "flex", flexDirection: "column" }}>
                                                跨境进口统一
                                                <Input
                                                    value={value}
                                                    onChange={onChange}
                                                    maxLength={10}
                                                    disabled={notEdit}
                                                />
                                            </div>
                                        )}
                                    </FormChildFn>
                                </Form.Item>
                            </Col>
                            <Col offset={2} span={10}>
                                <Form.Item
                                    labelCol={{ span: 4 }}
                                    wrapperCol={{ span: 20 }}
                                    // {...formLayout}
                                    layout="vertical"
                                    label=""
                                    name="specialClientCode"
                                    rules={[
                                        {
                                            required: true,
                                            message: "请输入保税物流管理系统",
                                        },
                                    ]}>
                                    <FormChildFn>
                                        {({ value, onChange }) => (
                                            <div style={{ display: "flex", flexDirection: "column" }}>
                                                保税物流管理系统
                                                <Input
                                                    value={value}
                                                    onChange={onChange}
                                                    maxLength={10}
                                                    disabled={notEdit}
                                                />
                                            </div>
                                        )}
                                    </FormChildFn>
                                </Form.Item>
                            </Col>
                        </Row>
                        <Row>
                            <Col offset={4} span={12}>
                                <Form.Item
                                    {...formLayout}
                                    name="fbCode"
                                    rules={[
                                        {
                                            required: true,
                                            message: "请输入分类监管（浙电）",
                                        },
                                    ]}>
                                    <FormChildFn>
                                        {({ value, onChange }) => (
                                            <div style={{ display: "flex", flexDirection: "column" }}>
                                                分类监管（浙电）
                                                <Input
                                                    value={value}
                                                    onChange={onChange}
                                                    maxLength={10}
                                                    disabled={notEdit}
                                                />
                                            </div>
                                        )}
                                    </FormChildFn>
                                </Form.Item>
                            </Col>
                        </Row>
                        {/* <Row>
                        <Col span={24}>
                            <Form.Item label="备注"
                                name='remark' {...{
                                    labelCol: { span: 3 },
                                    wrapperCol: { span: 17 }
                                }}>
                                <TextArea disabled={notEdit} />
                            </Form.Item>
                        </Col>
                    </Row> */}
                        <Form.Item
                            {...{ labelCol: { span: 3 }, wrapperCol: { span: 20 } }}
                            label="企业资质"
                            name="qualifyList"
                            rules={[{ required: true, message: "请选择企业资质" }]}>
                            <Checkbox.Group disabled={notEdit} onChange={this.changeHandle}>
                                {qualifyList.map(item => {
                                    return (
                                        <Checkbox value={item.id} key={item.id}>
                                            {item.name}
                                        </Checkbox>
                                    );
                                })}
                            </Checkbox.Group>
                        </Form.Item>
                        {/* {needDXPID && <Form.Item {...{ labelCol: { span: 3 }, wrapperCol: { span: 6 } }} label="清关企业DXP" name="dxpId"
                        rules={[{ required: true, message: '请输入dxpId', whitespace: true }]}>
                        <Input disabled={notEdit} />
                    </Form.Item>}
                    {needorderDxpId && <Form.Item {...{ labelCol: { span: 3 }, wrapperCol: { span: 6 } }} label="报文传输DXP" name="orderDxpId"
                        rules={[{ required: true, message: '请输入dxpId', whitespace: true }]} >
                        <Input disabled={notEdit} />
                    </Form.Item>} */}
                        {crossBorder && (
                            <Form.Item
                                {...{ labelCol: { span: 5 }, wrapperCol: { span: 18 } }}
                                label="跨境数据交换申请传输节点"
                                name="crossBorderDataExchangeNodeList"
                                validateFirst={true}
                                rules={[
                                    { required: true, message: "请输入跨境数据交换申请传输节点的数据" },
                                    {
                                        validator: (_, value) => {
                                            if (Array.isArray(value)) {
                                                for (let i = 0; i < value.length; i++) {
                                                    if (!value[i].dxpId)
                                                        return Promise.reject(
                                                            `请设置${value[i].nodeCode}级节点第${
                                                                value[i].index + 1
                                                            }项的dxp值`,
                                                        );
                                                    if (!/^[A-Z]{6}\d{10}$/.test(value[i].dxpId))
                                                        return Promise.reject(
                                                            `${value[i].nodeCode}级节点第${
                                                                value[i].index + 1
                                                            }项的dxp值不合法`,
                                                        );
                                                    if (!value[i].config)
                                                        return Promise.reject(
                                                            `请设置${value[i].nodeCode}级节点第${
                                                                value[i].index + 1
                                                            }项的申报设置`,
                                                        );
                                                }
                                                if (value.length === 0) {
                                                    return Promise.reject("请输入跨境数据交换申请传输节点的数据");
                                                }
                                                return Promise.resolve();
                                            } else {
                                                return Promise.reject("请输入跨境数据交换申请传输节点的数据");
                                            }
                                        },
                                    },
                                ]}>
                                <SelectNode type={"crossBorder"} disabled={notEdit} />
                            </Form.Item>
                        )}
                        {noCrossBorder && (
                            <Form.Item
                                {...{ labelCol: { span: 5 }, wrapperCol: { span: 18 } }}
                                label="非跨境数据交换申请传输节点"
                                name="nonCrossBorderDataExchangeNodeList"
                                rules={[
                                    { required: true, message: "请输入非跨境数据交换申请传输节点的数据" },
                                    {
                                        validator: (_, value) => {
                                            if (Array.isArray(value)) {
                                                for (let i = 0; i < value.length; i++) {
                                                    if (!value[i].dxpId)
                                                        return Promise.reject(
                                                            `请设置${value[i].nodeCode}级节点第${
                                                                value[i].index + 1
                                                            }项的dxp值`,
                                                        );
                                                }
                                                if (value.length === 0) {
                                                    return Promise.reject("请输入跨境数据交换申请传输节点的数据");
                                                }
                                                return Promise.resolve();
                                            } else {
                                                return Promise.reject("请输入非跨境数据交换申请传输节点的数据");
                                            }
                                        },
                                    },
                                ]}>
                                <SelectNode type={"noCrossBorder"} disabled={notEdit} />
                            </Form.Item>
                        )}
                        {guaranteeVisiter && (
                            <Form.Item
                                {...{ labelCol: { span: 5 }, wrapperCol: { span: 18 } }}
                                label="保函关区"
                                name="guaranteeCustoms"
                                rules={[{ required: true, message: "请选择保函关区" }]}>
                                <Checkbox.Group disabled={notEdit} onChange={this.changeGuranteeHandle}>
                                    {guaranteeCustoms.map(item => {
                                        return (
                                            <Checkbox value={item.id} key={item.id}>
                                                {item.name}
                                            </Checkbox>
                                        );
                                    })}
                                </Checkbox.Group>
                            </Form.Item>
                        )}
                    </Card>
                </Form>

                <div style={{ paddingTop: 10 }}></div>
                <Alert message="录入企业信息前，请确认是否有特殊口岸的申报业务【如天津口岸】" type="warning" />
                <div className="title-div">
                    <h3>口岸信息录入</h3>
                    {!notEdit && (
                        <Button
                            type="primary"
                            onClick={() => {
                                this.getConfigList();
                                this.setState({
                                    modalVisible: true,
                                    modalTitle: "新增口岸",
                                });
                            }}>
                            添加口岸
                        </Button>
                    )}
                </div>
                <Card>
                    <Table
                        columns={columns}
                        dataSource={detail && detail.districtList}
                        rowKey={record => record.customs}
                        pagination={{ hideOnSinglePage: true }}
                        scroll={{ y: 150 }}></Table>
                </Card>
                {!notEdit && (
                    <div className="footer-div">
                        <Button onClick={() => this.submitHandle()} type="primary">
                            保存
                        </Button>
                    </div>
                )}
            </Card>
        );
    }
}

export default App;

const FormChildFn = ({ value, onChange, children }) => {
    const Com = children;
    if (Com) {
        return <Com value={value} onChange={onChange} />;
    }
    return null;
};
