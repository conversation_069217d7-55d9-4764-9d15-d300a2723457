.test-chart {
    display: flex;
    flex-direction: column;
    height: 100%;
    overflow: hidden;
    .search-conditions {
        background: #fff;
        padding: 20px;
    }
    .label {
        width: 80px;
        padding-right: 10px;
        text-align: right;
        display: inline-block;
    }
    .select {
        width: calc(100% - 120px);
    }
    .chart {
        background: #fff;
        margin-top: 10px;
        position: relative;
        flex-grow: 1;
        overflow: hidden;
        .type {
            position: absolute;
            left: 50%;
            top: 10px;
            transform: translateX(-50%);
            z-index: 10;
        }
        .mark {
            position: absolute;
            right: 50px;
            top: 20px;
            line-height: 1.5;
            background: rgba(150, 150, 150, 0.2);
            padding: 10px;
            border-radius: 5px;
            color: #333;
        }
        .download {
            position: absolute;
            left: 50px;
            top: 20px;
        }
    }
}
