import React from "react";
import { ConfigCenter, lib } from "react-single-app";
import "./shop-good.less";
import { Button, message, Tooltip } from "antd";
const statusDist = {
    10: "待申报",
    20: "等待回执",
    100: "已发行",
    "-10": "取消申报",
    "-1": "申报失败",
};
class App extends ConfigCenter {
    constructor(props) {
        super(props);
    }
    componentDidMount() {}
    getCheckedRows() {
        if (this.state.config.needBatchOperation === 1) {
            return [];
        }
        let rows = [];
        this.state.dataList.map(item => {
            if (item.rowChecked && !item.disabled) {
                rows.push(item);
            }
        });
        return rows;
    }

    renderRightOperation() {
        //渲染自己的按钮
        return (
            <React.Fragment>
                <Button onClick={() => this.retry()}>重试</Button>
            </React.Fragment>
        );
    }

    renderTooltip(row) {
        return (
            <Tooltip title={row.customsInventory.customsDetail}>
                <span>{row.customsInventory.customsStatusDesc}</span>
            </Tooltip>
        );
    }

    declareProject(row) {
        return (
            <span>
                {this.status2icon(row.payment.status)}
                {row.payment.status !== 0 && <span title={statusDist[row.payment.status]}>支付单申报</span>}
                {this.status2icon(row.order.status)}
                {row.order.status !== 0 && <span title={statusDist[row.order.status]}>订单申报</span>}
                {this.status2icon(row.logistics.status)}
                {row.logistics.status !== 0 && <span title={statusDist[row.logistics.status]}>运单申报</span>}
                {this.status2icon(row.customsInventory.status)}
                {row.customsInventory.status !== 0 && <span title={row.customsInventory.statusDesc}>清单申报</span>}
            </span>
        );
    }

    status2icon(status) {
        if (status === 10) {
            // 待申报
            return <i className="wait-icon"></i>;
        } else if (status === 20) {
            // 申报中
            return <i className="ing-icon"></i>;
        } else if (status === 100) {
            // 已放行
            return <i className="success-icon"></i>;
        } else if (status === -10) {
            // 取消申报
            return <i className="cancel-icon"></i>;
        } else if (status === -1) {
            // 申报失败
            return <i className="fail-icon"></i>;
        }
    }

    //重试按钮点击
    retry() {
        let list = this.getCheckedRows(),
            ids = "";
        list.map(item => {
            ids += item.id + ","; //被选择的信息逗号分隔
        });
        if (ids) {
            ids = ids.slice(0, -1); // 分隔开
        } else {
            message.error("请选择数据重试");
            return;
        }
        lib.request({
            //调用lib的request请求
            url: "/ccs/order/retryException",
            method: "POST",
            data: {
                ids,
            },
            needMask: true,
            success: res => {
                if (res) {
                    message.success("提交成功");
                }
                this.load(true);
            },
        });
    }
}
export default App;
