import React, { useState, useEffect } from "react";
import "./shop-good.less";
import event from "../common/event";
import NewModal from "../components/NewModal";
import { ConfigCenter, lib } from "react-single-app";

import { Checkbox, Button, Modal, Drawer, Input, Select, Form, message } from "antd";

class App extends ConfigCenter {
    constructor(props) {
        super(props);
        this.state.upUrl = "/ccs/pay/payMerchantCustomsInfo/upset";
        this.state.customs = [];
        this.state.editRow = {};
        this.state.configList = [
            {
                type: "SELECT",
                labelName: "口岸",
                labelKey: "custom",
                required: true,
                message: "请选择口岸",
                list: [],
                ccs: "/ccs/pay/payMerchantCustomsInfo/listCustoms",
            },
            {
                type: "INPUT",
                labelName: "海关备案编码",
                labelKey: "merchantCustomsCode",
                required: true,
                message: "请输入海关备案编码",
                maxLength: 50,
            },
            {
                type: "INPUT",
                labelName: "海关备案名称",
                labelKey: "merchantCustomsName",
                required: true,
                message: "请输入海关备案名称",
                maxLength: 50,
            },
        ];
    }

    componentDidMount() {
        lib.request({
            url: "/ccs/pay/payMerchantCustomsInfo/listCustoms",
            needMask: true,
            success: res => {
                this.setState({
                    customs: res,
                });
            },
        });
    }

    // 获取configList 里面的下拉数据
    getSelectList() {
        let configList = this.state.configList;
        configList.map(item => {
            if (item.ccs) {
                lib.request({
                    url: item.ccs,
                    needMask: false,
                    success: res => {
                        item.list = res || [];
                        this.setState({ configList });
                    },
                });
            }
        });
        this.setState({ configList });
    }

    renderRightOperation() {
        return (
            <React.Fragment>
                <Button
                    type="primary"
                    onClick={() => {
                        this.getSelectList();
                        this.setState({
                            visible: true,
                            modalTitle: "新增报关资质",
                        });
                    }}>
                    新增报关资质
                </Button>
            </React.Fragment>
        );
    }

    handleOk(values, modalForm) {
        if (this.state.editRow) {
            values.id = this.state.editRow.id;
        }
        values.merchantId = lib.getParam("merchantId");
        values.customs = values.custom;
        lib.request({
            url: this.state.upUrl,
            data: values,
            method: "POST",
            needMask: true,
            success: res => {
                if (res) {
                    this.setState({
                        visible: false,
                        editRow: {},
                    });
                    this.load(true);
                    modalForm.resetFields();
                }
            },
        });
    }

    handleCancel() {
        this.setState({
            visible: false,
            editRow: {},
        });
    }

    renderModal() {
        let { visible, modalTitle, editRow, customs } = this.state;
        const props = {
            visible,
            title: modalTitle,
            editRow,
            form: this.props.form,
            onOk: this.handleOk.bind(this),
            onCancel: this.handleCancel.bind(this),
            configList: this.state.configList,
        };
        customs.map(item => {
            if (editRow.customs === item.name) {
                editRow.custom = item.value;
            }
        });
        return (
            <React.Fragment>
                <NewModal {...props} />
            </React.Fragment>
        );
    }

    myOperation(row) {
        return (
            <React.Fragment>
                <span
                    className="link"
                    onClick={() => {
                        this.getSelectList();
                        this.setState({
                            editRow: row,
                            visible: true,
                            modalTitle: "编辑报关资质",
                        });
                    }}>
                    编辑
                </span>
            </React.Fragment>
        );
    }
}
export default App;
