import React from "react";
import { getConfigDataUtils, lib, SearchList, HOC, event } from "react-single-app";
import axios from "axios";
import { Tooltip, Space, Badge, Button, message, Modal } from "antd";
import MailEditModal, { MAIL_POOL_OPERATE_TYPE } from "../components/editModal";
import BusinessSettingModal from "../components/business-setting-modal";
import { StatusTabs } from "./mail-statustabs";
@HOC.mapAuthButtonsToState({ pagePath: "/workbench", buttonCodeArr: ["distribute", "businessSetting"] })
export default class MailPoolSetting extends SearchList {
    constructor(props) {
        super(props);
        this.statusTabRef = React.createRef();
        this.onSearchReset = this.onSearchReset.bind(this);
    }
    componentDidMount() {
        event.on("onSearchReset", this.onSearchReset);
        if (lib.getParam("searchIdKey")) {
            this.changeImmutable({ userId: lib.getParam("searchIdKey") });
        }
    }

    componentWillUnmount() {
        event.off("onSearchReset", this.onSearchReset);
    }

    onSearchReset() {
        this.statusTabRef.current.setActiveKey("-1");
        this.changeImmutable({ status: "" });
    }
    onSearch(search) {
        this.statusTabRef.current.getDetail(search);
    }

    renderOperationTopView() {
        return (
            <StatusTabs
                ref={this.statusTabRef}
                tabOnChange={e => {
                    if (e === "-1") {
                        this.changeImmutable({ status: "" });
                    } else {
                        this.changeImmutable({ status: Number(e) });
                    }
                }}
            />
        );
    }
    configLoadDefaultParams() {
        const status = this.statusTabRef.current.getActiveKey();
        return {
            status: status === "-1" ? null : status,
        };
    }
    getConfig() {
        return axios.get(getConfigDataUtils.getDataUrlByDataId(605)).then(res => res.data.data);
    }

    addBrowse(id) {
        lib.request({
            url: "/ccs/mailPool/addBrowse",
            data: { id },
        });
    }
    // 邮件标题
    getSubject(row) {
        return (
            <Tooltip placement="topLeft" title={row?.subject}>
                {row.subject}
            </Tooltip>
        );
    }
    myOperation(row) {
        return (
            <Space wrap>
                {row.status > 0 && (
                    <a
                        className={"link"}
                        onClick={() => {
                            this.addBrowse(row.id);
                            lib.openPage(`/mail-detail?page_title=邮件详情&id=${row.id}`, () => {
                                this.load();
                            });
                        }}>
                        查看
                    </a>
                )}
                {!row.status && (
                    <a
                        className={"link"}
                        onClick={() => {
                            this.addBrowse(row.id);
                            lib.openPage(`/mail-detail?page_title=邮件详情&id=${row.id}`, () => {
                                this.load();
                            });
                        }}>
                        认领
                    </a>
                )}
                {this.state.buttonAuth?.distribute && (
                    <a
                        className={"link"}
                        onClick={() => {
                            this.setState({
                                showModal: true,
                                showModalType: MAIL_POOL_OPERATE_TYPE.distribute,
                                editId: row.id,
                            });
                        }}>
                        分配
                    </a>
                )}
                {row.status === 3 && (
                    <a
                        className={"link"}
                        onClick={() => {
                            this.setState({
                                showModal: true,
                                showModalType: MAIL_POOL_OPERATE_TYPE.reprocess,
                                editId: row.id,
                            });
                        }}>
                        重新处理
                    </a>
                )}
            </Space>
        );
    }
    // 邮件状态
    getStatusDesc(row) {
        const logisticsStatus = [
            { name: "待处理", status: "warning", id: 1 },
            { name: "处理中", status: "processing", id: 2 },
            { name: "无需处理", status: "default", id: 3 },
            { name: "已挂起", status: "warning", id: 5 },
            { name: "已完成", status: "success", id: 4 },
        ];
        const status = logisticsStatus.filter(item => item.id === row.status);
        return <Badge status={status[0]?.status} text={status[0]?.name} />;
    }
    renderRightOperation() {
        return <Space>{this.state.buttonAuth?.businessSetting && <BusinessSettingModal />}</Space>;
    }
    renderLeftOperation() {
        const status = this.statusTabRef?.current?.getActiveKey();
        return (
            <>
                <Button
                    type="primary"
                    onClick={() => {
                        this.returnFunc();
                    }}>
                    批量分配
                </Button>
                {status === "0" && (
                    <Button
                        onClick={() => {
                            this.returnFunc1();
                        }}>
                        批量认领
                    </Button>
                )}
            </>
        );
    }
    getCheckedRows() {
        let { selectedRows } = this.state;
        return selectedRows;
    }
    calcIds() {
        let list = this.getCheckedRows(),
            ids = "";
        list.map(item => {
            ids += item.id + ",";
        });
        return ids.slice(0, -1);
    }

    returnFunc() {
        let ids = this.calcIds();
        if (ids == "") {
            message.warning("请选择数据");
            return;
        }
        this.setState({
            showModal: true,
            showModalType: MAIL_POOL_OPERATE_TYPE.bulkallocation,
            ids: ids,
        });
    }

    returnFunc1() {
        let ids = this.calcIds();
        if (ids == "") {
            message.warning("请选择数据");
            return;
        }
        Modal.confirm({
            title: "批量认领后邮件转为无需处理，确定继续吗？",
            onOk: () => {
                lib.request({
                    url: "/ccs/mailPool/claimBatch",
                    needMask: true,
                    data: { ids: ids },
                    success: () => {
                        message.success("批量认领成功");
                        this.load();
                    },
                });
            },
            okText: "确定",
            cancelText: "取消",
        });
    }

    addHandle() {
        this.setState({
            showModal: true,
            showModalType: MAIL_POOL_OPERATE_TYPE.distribute,
        });
    }

    renderModal() {
        return (
            <React.Fragment>
                {
                    <MailEditModal
                        id={this.state.editId}
                        ids={this.state.ids}
                        showModal={this.state.showModal}
                        type={this.state.showModalType}
                        dialogClose={isSuccess => {
                            this.setState({
                                showModal: false,
                                editId: null,
                                showModalType: null,
                                ids: null,
                            });
                            if (isSuccess) {
                                this.load();
                            }
                        }}
                    />
                }
            </React.Fragment>
        );
    }
}
