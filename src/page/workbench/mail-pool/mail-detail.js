import React, { useEffect, useState, useMemo, Fragment } from "react";
import { Button, message, Tag, Typography, Modal, Space, Divider } from "antd";
import { event, lib } from "react-single-app";
import "./mail-detail.less";
import DescriptionItem from "../../../components/description-item";
import MailEditModal, { MAIL_POOL_OPERATE_TYPE } from "../components/editModal";
import { LinkOutlined } from "@ant-design/icons";
import { LogDetail } from "../components/log-detail";

const { Title } = Typography;
export default function MailDetail() {
    const id = lib.getParam("id");
    const [detail, setDetail] = useState();
    useEffect(() => {
        getDetail();
    }, []);

    function getDetail() {
        lib.request({
            url: "/ccs/mailPool/detail",
            data: { id: id || lib.getParam("id") },
            needMask: true,
            success: res => {
                setDetail(res);
            },
        });
    }

    return (
        <>
            {detail && (
                <div className={"mail-detail-container"}>
                    <MailHeader detail={detail} getDetail={getDetail} />
                    <MailContent detail={detail} />
                </div>
            )}
        </>
    );
}

function MailHeader({ detail, getDetail }) {
    const [showModal, setShowModal] = useState(false);
    const [showModalType, setShowModalType] = useState();
    return (
        <div className={"mail-header"}>
            <div className={"title-container"}>
                <div className={"left-title-wrapper"}>
                    <Title level={5}>
                        {detail.subject}
                        {!detail.status && (
                            <Tag color={"#FF9D00"} style={{ marginLeft: "8px" }}>
                                待认领
                            </Tag>
                        )}
                    </Title>
                </div>
                <div className={"right-operation-panel"}>
                    <Space>
                        {detail.status ? (
                            <>
                                <LogDetail mailId={detail.id} />
                                <Button
                                    type={"primary"}
                                    onClick={() => {
                                        setShowModalType(MAIL_POOL_OPERATE_TYPE.transfer);
                                        setShowModal(true);
                                    }}>
                                    转交
                                </Button>
                                <Button
                                    type={"primary"}
                                    onClick={() => {
                                        setShowModalType(MAIL_POOL_OPERATE_TYPE.handle);
                                        setShowModal(true);
                                    }}>
                                    处理
                                </Button>
                            </>
                        ) : (
                            <Button
                                type={"primary"}
                                onClick={() => {
                                    setShowModalType(MAIL_POOL_OPERATE_TYPE.claim);
                                    setShowModal(true);
                                }}>
                                认领
                            </Button>
                        )}
                    </Space>
                </div>
            </div>
            {detail.status > 0 && (
                <div className={"detail-info"}>
                    <DescriptionItem label={"处理人："} content={detail.userName} />
                    <DescriptionItem label={"处理状态："} content={detail.statusDesc} />
                    <DescriptionItem
                        label={"认领时间："}
                        content={new Date(detail.claimTime).format("yyyy-MM-dd hh:mm:ss")}
                    />
                </div>
            )}
            {
                <MailEditModal
                    id={detail.id}
                    showModal={showModal}
                    type={showModalType}
                    dialogClose={isSuccess => {
                        setShowModal(false);
                        if (isSuccess) {
                            let refresh_event = lib.getParam("refresh_event");
                            if (refresh_event) {
                                event.emit(refresh_event, true);
                            }
                            getDetail();
                        }
                    }}
                />
            }
        </div>
    );
}

function MailContent({ detail }) {
    const mailCCList = useMemo(() => {
        return detail.mailCCList?.map((item, index) => (
            <Fragment key={index}>
                <span style={{ color: "#333333" }}>{item.name} </span>
                <span style={{ color: "#999" }}>{item.account}; </span>
            </Fragment>
        ));
    }, [detail]);
    const toMailList = useMemo(() => {
        return detail.toMailList?.map((item, index) => (
            <Fragment key={index}>
                <span>{item.name} </span>
                <span style={{ color: "#999" }}>{item.account}; </span>
            </Fragment>
        ));
    }, [detail]);
    const attachFile = useMemo(() => {
        return (
            <Space wrap>
                {detail.attachmentList?.map((item, index) => {
                    let extension = item.name.substring(item.name.lastIndexOf(".") + 1);
                    let fileName = item.name.substring(0, item.name.lastIndexOf(".") + 1);
                    return (
                        <div
                            className={"attachment-container"}
                            key={index}
                            onClick={() => {
                                window.open(item.src, "_blank");
                            }}>
                            <div className={"attachment-icon"}>
                                <LinkOutlined />
                            </div>
                            <div className={"attachment-name"}>{fileName}</div>
                            <div className={"attachment-extension"}>{extension}</div>
                        </div>
                    );
                })}
            </Space>
        );
    }, [detail]);

    return (
        <div className={"mail-content"}>
            <div className={"mail-content-header"}>
                <BrowseListModal browseList={detail.browseList} />
                <DescriptionItem
                    label={"发件人"}
                    content={
                        <span>
                            <span>{detail.addresserName} </span>
                            <span style={{ color: "#999" }}>{detail.addresserAccount} </span>
                        </span>
                    }
                    labelClassName={"mail-content-header-label"}
                />
                <DescriptionItem
                    label={"发给"}
                    content={<span>{toMailList}</span>}
                    labelClassName={"mail-content-header-label"}
                />
                {detail.mailCCList && detail.mailCCList.length > 0 && (
                    <DescriptionItem
                        label={"抄送"}
                        content={<span>{mailCCList}</span>}
                        labelClassName={"mail-content-header-label"}
                        contentClassName={"mail-content-header-content"}
                    />
                )}
                <DescriptionItem
                    label={"时间"}
                    content={new Date(detail.receiptTime).format("yyyy-MM-dd hh:mm:ss")}
                    labelClassName={"mail-content-header-label"}
                />
                {detail.attachmentList && detail.attachmentList.length > 0 && (
                    <DescriptionItem label={"附件"} content={attachFile} labelClassName={"mail-content-header-label"} />
                )}
            </div>
            <Divider />
            <div dangerouslySetInnerHTML={{ __html: detail.content }} />
        </div>
    );
}

function BrowseListModal({ browseList }) {
    const [visible, setVisible] = useState(false);
    return (<>
        <Modal
            title={"浏览列表"}
            okText={"我知道了"}
            width={800}
            destroyOnClose
            open={visible}
            wrapClassName={"browse-list-modal"}
            cancelButtonProps={{ style: { display: "none" } }}
            onCancel={() => setVisible(false)}
            onOk={() => setVisible(false)}>
            <div className={"browse-list"}>
                <div>
                    <span className={"browse-list-count"}>浏览（{browseList?.length}）</span>
                    <div className={"browse-list-count-bg"} />
                    <div className={"browse-list-count-line"} />
                </div>

                {browseList?.map((item, index) => {
                    return (
                        <div className={"browse-list-item"} key={index}>
                            <span className={"browse-list-item-name"}>{item.userName}</span>
                            <span className={"browse-list-item-date"}>
                                {new Date(item.createTime).format("yyyy-MM-dd hh:mm:ss")}
                            </span>
                        </div>
                    );
                })}
            </div>
        </Modal>
        <span className={"browse-list-count"} onClick={() => setVisible(true)}>
            {" "}
            浏览（{browseList?.length || 0}）
        </span>
    </>);
}
