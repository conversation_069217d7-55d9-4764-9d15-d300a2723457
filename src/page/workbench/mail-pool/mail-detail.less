@import "../../../index.less";

.mail-detail {
    overflow: hidden scroll !important;

    .mail-detail-container {
        background-color: #f6f7fa;
        padding-bottom: 64px;

        .mail-header {
            padding: 24px 0 0px 16px;

            .title-container {
                display: flex;
                justify-content: space-between;
                .right-operation-panel {
                    margin-right: 16px;
                }
            }

            .detail-info {
                display: flex;
                justify-content: flex-start;
                gap: 8px;
            }
        }

        .mail-content {
            background: #ffffff;
            border-radius: 2px;
            min-height: 600px;
            padding: 16px;
            margin: 16px;
            overflow-y: scroll;
            position: relative;

            &-header {
                &-label {
                    min-width: 40px;
                }

                &-content {
                    min-width: 40px;
                    word-break: break-all;
                }

                .browse-list-count {
                    position: absolute;
                    right: 16px;
                    cursor: pointer;
                    font-size: 12px;
                    font-weight: 400;
                    color: @mainColor;
                    line-height: 20px;
                }

                .attachment-container {
                    width: 200px;
                    background: #f6f7fa;
                    border-radius: 2px;
                    padding: 2px 4px;
                    cursor: pointer;
                    display: flex;

                    .attachment-icon {
                        color: @mainColor;
                        margin-right: 4px;
                    }

                    &:hover {
                        .attachment-name {
                            color: @mainColor;
                        }

                        .attachment-extension {
                            color: @mainColor;
                        }
                    }

                    .attachment-name {
                        margin-left: 4px;
                        color: #333333;
                        overflow: hidden;
                        text-overflow: ellipsis;
                        white-space: nowrap;
                        word-break: break-all;
                    }
                }
            }
        }
    }
}

.browse-list-modal {
    .browse-list {
        max-height: 500px;
        overflow-y: auto;
        padding: 0 16px;

        &-item {
            display: flex;
            justify-content: space-between;
            height: 40px;
        }

        &-count {
            color: @mainColor;

            &-bg {
                margin-top: 8px;
                width: 83px;
                height: 3px;
                background: @mainColor;
                border-radius: 100px 100px 0px 0px;
            }

            &-line {
                height: 1px;
                margin-bottom: 10px;
                background: #f0f0f0;
            }
        }
    }
}
