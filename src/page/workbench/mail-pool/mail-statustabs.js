import React, { useState, useEffect, useImperative<PERSON>andle } from "react";
import { Tabs } from "antd";
import { lib } from "react-single-app";

export const StatusTabs = React.forwardRef(({ tabOnChange, type }, ref) => {
    const [activeKey, setActiveKey] = useState();
    const [tabs, settabs] = useState([]);
    useEffect(() => {}, []);
    function getDetail(search) {
        delete search.status;
        lib.request({
            url: "/ccs/mailPool/getMailTabNum",
            data: search,
            method: "POST",
            needMask: true,
            success: res => {
                settabs(res || []);
            },
        });
    }

    useImperativeHandle(ref, () => ({
        getDetail: getDetail,
        setActiveKey: setActiveKey,
        getActiveKey: () => activeKey,
    }));

    return (
        <Tabs
            defaultActiveKey={"-1"}
            activeKey={activeKey}
            onChange={e => {
                setActiveKey(e);
                tabOn<PERSON>hange(e);
            }}>
            <Tabs.TabPane tab={"全部"} key={"-1"} />
            {tabs.map(item => {
                return <Tabs.TabPane tab={`${item.name}(${item?.num || 0})`} key={item.status} />;
            })}
        </Tabs>
    );
});
