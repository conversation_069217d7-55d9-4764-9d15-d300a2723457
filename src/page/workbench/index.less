@import "../../index.less";
.customs-workbench {
    background: #f3f4f7;
    height: 100%;
    min-width: 1008px;
    overflow-y: scroll;
    padding: 16px;

    .arrow {
        margin-left: 4px;
        width: 16px;
        height: 16px;
        font-weight: 200;
        color: #bbbbbb;
    }

    .mail-wait-detail {
        padding: 16px;
        background: #fff;
        border-radius: 2px;
        margin-bottom: 16px;
        .title {
            display: flex;
            justify-content: space-between;
            // height: 56px;
            align-items: center;
            margin-bottom: 8px;
        }

        .empty-desc {
            text-align: center;
            font-size: 14px;
            font-weight: 400;
            color: #999999;
            line-height: 20px;
        }
    }

    .mail-wait-pick {
        padding: 16px 8px;
        background: #fff;
        // margin-left: 16px;
        border-radius: 2px;

        .title {
            margin-left: 8px;
            display: flex;
            justify-content: space-between;

            .tag {
                cursor: pointer;
                display: flex;

                .container {
                    display: flex;
                    background: #ffefd5;
                    padding: 0 8px;
                    height: 22px;
                    align-items: center;
                    border-radius: 2px;

                    &.empty {
                        background: #f6f7fa;

                        .content {
                            color: #999999;
                        }

                        .count {
                            color: #333333;
                            font-family: D-DIN;
                        }
                    }

                    .content {
                        font-size: 12px;
                        font-weight: 400;
                        color: #ff9d00;
                        line-height: 20px;
                    }

                    .count {
                        margin-left: 8px;
                        font-size: 16px;
                        font-weight: 500;
                        color: #ff9d00;
                        line-height: 16px;
                        font-family: D-DIN;
                    }
                }
            }
        }

        .pick-list-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            height: 38px;
            padding: 0 8px;

            &:hover {
                background: #f6f7fa;
                border-radius: 4px;
            }

            &-title {
                font-size: 14px;
                font-weight: 400;
                color: #333333;
                line-height: 22px;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
                word-break: break-all;
                cursor: pointer;
            }
            &-title:hover {
                color: #0268ff;
            }
            &-date {
                text-align: right;
                min-width: 60px;
                font-size: 14px;
                font-weight: 400;
                color: #999999;
                line-height: 22px;
            }
        }
    }

    .goods-record,
    .bonded-after-sales,
    .customs-clearance-business,
    .item-wait-handle {
        background: #ffffff;
        border-radius: 2px;
        padding: 0 16px 8px;

        .title {
            display: flex;
            justify-content: flex-start;
            // height: 48px;
            padding-top: 16px;
            align-items: center;
        }

        .content:hover {
            cursor: pointer;

            .content-title,
            .content-count {
                color: @mainColor;
            }
        }

        .content-title {
            font-size: 12px;
            font-weight: 500;
            color: #666666;

            .icon {
                font-size: 24px;
            }
        }

        .content-count {
            margin-top: 10px;
            margin-bottom: 13px;
            font-size: 24px;
            font-weight: 500;
            font-family: D-DIN;
            color: #333333;
            line-height: 28px;
        }
        .content_str {
            margin-top: 12px;
            margin-bottom: 13px;
        }
        .out-time-content {
            font-size: 12px;
            font-weight: 500;
            color: #e83c3c;
            line-height: 24px;
        }

        .early-warning-content {
            font-size: 12px;
            font-weight: 500;
            color: #ff9d00;
            line-height: 24px;
        }
    }

    .goods-record {
        min-height: 166px;
    }

    .bonded-after-sales {
        // height: 166px;

        .content-group {
            display: flex;
            flex-direction: row;
            justify-content: space-between;
        }
    }

    .customs-clearance-business {
        margin-top: 18px;

        // .content-wrapper {
        //   min-width: 262px;
        // }

        .content-card {
            min-height: 116px;
            padding: 12px 16px;
            background: #f6f7fa;
            border-radius: 2px;
        }

        .content-title {
            font-size: 16px;
            font-weight: 500;
            color: #333333;
            line-height: 24px;
            display: flex;
            flex-direction: row;
            align-items: center;

            .icon {
                font-size: 24px;
                margin-right: 8px;
            }
        }

        .group {
            margin-top: 14px;
        }

        .item {
            width: 100%;
            padding-left: 8px;

            .content {
                font-size: 16px;
                font-family: D-DIN;
                line-height: 24px;
            }

            &:hover {
                cursor: pointer;
                background: rgba(0, 0, 0, 0.04);

                .content {
                    color: @mainColor;
                }
            }
        }
    }

    .item-wait-handle {
        margin-top: 18px;

        .abnormal {
            border: #f59a23 1px solid;
            border-radius: 15px;
            color: #f59a23;
            text-align: center;
        }

        .number {
            a {
                color: rgba(0, 0, 0, 0.85);
            }
        }

        .empty-desc {
            text-align: center;
            font-size: 14px;
            font-weight: 400;
            color: #999999;
            line-height: 20px;
        }
    }

    .time-refresh {
        margin-bottom: 18px;
        width: 100%;
        display: flex;
        flex-direction: row;
        justify-content: flex-end;

        a {
            color: #333;
            width: 100%;
            height: 100%;
        }
    }
}
