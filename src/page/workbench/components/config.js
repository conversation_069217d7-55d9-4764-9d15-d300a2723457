/**
 * 认领
 * @type
 */
const CLAIM_DATA = {
    baseInfo: {
        children: [
            {
                label: "处理状态",
                name: "status",
                editEnable: false,
                type: "radioSelect",
                list: [
                    { id: 1, name: "需处理" },
                    { id: 3, name: "无需处理" },
                ],
                rules: [
                    {
                        required: true,
                        message: "请选择!",
                    },
                ],
            },
            {
                label: "备注",
                editEnable: false,
                name: "remark",
                type: "textarea",
                hidden: true,
                customConfig: { maxLength: 200 },
            },
            {
                label: "业务类型",
                editEnable: false,
                name: "code",
                type: "single-select",
                from: "/ccs/dictionary/businessType",
                customConfig: { showSearch: true },
                hidden: true,
                rules: [
                    {
                        required: true,
                    },
                ],
            },
        ],
        label: "",
        name: "baseInfo",
        isGroup: true,
        className: "baseInfo",
    },
};
const DISTRIBUTE_DATA = {
    baseInfo: {
        children: [
            {
                label: "请选择处理人员",
                name: "userId",
                editEnable: false,
                type: "single-select",
                from: "/ccs/dictionary/handler/filter",
                customConfig: { showSearch: true },
                rules: [
                    {
                        required: true,
                        message: "请选择处理人员!",
                    },
                ],
            },
            {
                label: "业务类型",
                editEnable: false,
                name: "code",
                type: "single-select",
                from: "/ccs/dictionary/businessType",
                customConfig: { showSearch: true },
                rules: [
                    {
                        required: true,
                        message: "请选择业务类型!",
                    },
                ],
            },
        ],
        label: "",
        name: "baseInfo",
        isGroup: true,
        className: "baseInfo",
    },
};
//转交
const TRANSFER_DATA = {
    baseInfo: {
        children: [
            {
                label: "请选择处理人员",
                name: "userId",
                editEnable: false,
                type: "single-select",
                from: "/ccs/dictionary/handler/filter",
                customConfig: { showSearch: true },
                rules: [
                    {
                        required: true,
                        message: "请选择处理人员!",
                    },
                ],
            },
            {
                label: "处理状态",
                editEnable: false,
                name: "status",
                type: "single-select",
                from: "/ccs/mailPool/getMailStatus",
                customConfig: { showSearch: true, idKey: "code" },
                rules: [
                    {
                        required: true,
                        message: "请选择处理状态!",
                    },
                ],
            },
        ],
        label: "",
        name: "baseInfo",
        isGroup: true,
        className: "baseInfo",
    },
};
// 处理
const HANDLE_DATA = {
    baseInfo: {
        children: [
            {
                label: "业务类型",
                editEnable: false,
                name: "code",
                type: "single-select",
                from: "/ccs/dictionary/businessType",
                customConfig: { showSearch: true },
                rules: [
                    {
                        required: true,
                        message: "请选择业务类型!",
                    },
                ],
            },
            {
                label: "处理状态",
                editEnable: false,
                name: "status",
                type: "single-select",
                from: "/ccs/mailPool/getMailStatus",
                customConfig: { showSearch: true, idKey: "code" },
                rules: [
                    {
                        required: true,
                        message: "请选择处理状态!",
                    },
                ],
            },
            {
                label: "关联单据",
                editEnable: false,
                name: "associatedNumber",
                type: "textInput",
            },
            {
                label: "备注",
                editEnable: false,
                name: "remark",
                type: "textarea",
                customConfig: { maxLength: 200 },
            },
        ],
        label: "",
        name: "baseInfo",
        isGroup: true,
        className: "baseInfo",
    },
};
//重新处理
const REPROCESS_DATA = {
    baseInfo: {
        children: [
            {
                label: "业务类型",
                name: "code",
                editEnable: false,
                type: "single-select",
                from: "/ccs/dictionary/businessType",
                rules: [
                    {
                        required: true,
                        message: "请选择业务类型!",
                    },
                ],
            },
            {
                label: "处理状态",
                editEnable: false,
                name: "status",
                type: "single-select",
                from: "/ccs/mailPool/getMailStatus",
                customConfig: { showSearch: true, idKey: "code" },
                rules: [
                    {
                        required: true,
                        message: "请选择处理状态!",
                    },
                ],
            },
            {
                label: "关联单据",
                editEnable: false,
                name: "associatedNumber",
                type: "textInput",
            },
            {
                label: "备注",
                editEnable: false,
                name: "remark",
                type: "textarea",
                customConfig: { maxLength: 200 },
            },
        ],
        label: "",
        name: "baseInfo",
        isGroup: true,
        className: "baseInfo",
    },
};
//批量分配
const BULK_ALLOCATION = {
    baseInfo: {
        children: [
            {
                label: "请选择处理人员",
                name: "userId",
                editEnable: false,
                type: "single-select",
                from: "/ccs/dictionary/handler/filter",
                customConfig: { showSearch: true },
                rules: [
                    {
                        required: true,
                        message: "请选择处理人员!",
                    },
                ],
            },
            {
                label: "业务类型",
                editEnable: false,
                name: "code",
                type: "single-select",
                from: "/ccs/dictionary/businessType",
                customConfig: { showSearch: true },
                rules: [
                    {
                        required: true,
                        message: "请选择业务类型!",
                    },
                ],
            },
        ],
        label: "",
        name: "baseInfo",
        isGroup: true,
        className: "baseInfo",
    },
};
export { CLAIM_DATA, DISTRIBUTE_DATA, TRANSFER_DATA, HANDLE_DATA, REPROCESS_DATA, BULK_ALLOCATION };
