import React, { useEffect, useState } from "react";
import { lib } from "react-single-app";
import { useAntdTable } from "ahooks";
import { But<PERSON>, Drawer, Table } from "antd";

export function LogDetail({ mailId }) {
    const [visible, setVisible] = useState(false);
    const defaultPageSize = 10;
    const ref = React.useRef();
    const timeOutIdRef = React.useRef();
    const [tableHeight, setTableHeight] = useState();
    useEffect(() => {
        if (visible) {
            timeOutIdRef.current = setTimeout(() => {
                setTableHeight(ref.current.parentElement.offsetHeight - 160);
            }, 100);
            run({ current: 1, pageSize: defaultPageSize });
        }
        return () => {
            clearTimeout(timeOutIdRef.current);
        };
    }, [visible]);
    const getTableData = ({ current, pageSize }) => {
        return new Promise((resolve, reject) => {
            lib.request({
                url: "/ccs/operationLog/list",
                data: { mailId, currentPage: current, pageSize },
                success: res => {
                    resolve({
                        total: res.page.totalCount,
                        list: res.dataList,
                    });
                },
                fail: e => {
                    reject(e);
                },
            });
        });
    };
    const { tableProps, run } = useAntdTable(getTableData, {
        defaultPageSize: 20,
        manual: true,
    });
    const columns = [
        {
            title: "类型",
            dataIndex: "name",
            width: 100,
        },
        {
            title: "操作内容",
            dataIndex: "content",
            width: 230,
        },
        {
            title: "操作人",
            dataIndex: "createName",
            width: 100,
        },
        {
            title: "操作时间",
            dataIndex: "createTime",
            width: 150,
            render: (text, row, index) => {
                return new Date(text).format("yyyy-MM-dd hh:mm:ss");
            },
        },
    ];
    const onClose = () => {
        setVisible(false);
    };
    return (<>
        <Drawer
            title={"操作日志"}
            onClose={onClose}
            open={visible}
            width={800}
            bodyStyle={{ overflow: "hidden" }}>
            <div rootClassName="table-panel" ref={ref}>
                {visible && (
                    <Table
                        columns={columns}
                        scroll={{ y: tableHeight }}
                        rowKey={"id"}
                        size="small"
                        dataSource={tableProps?.dataSource}
                        onChange={tableProps?.onChange}
                        loading={tableProps?.loading}
                        pagination={{
                            size: "default",
                            ...tableProps?.pagination,
                            showSizeChanger: true,
                            showQuickJumper: true,
                            showTotal: total => `总共 ${total} 条`,
                        }}
                    />
                )}
            </div>
        </Drawer>
        <Button
            type={"link"}
            onClick={() => {
                setVisible(true);
            }}>
            查看操作日志
        </Button>
    </>);
}
