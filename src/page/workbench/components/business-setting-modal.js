import React, { useState, useEffect } from "react";
import { <PERSON><PERSON>, Drawer, Form, Input, Space, Typography, Alert, Fragment, message } from "antd";
import { DeleteOutlined, PlusOutlined } from "@ant-design/icons";
import "./business-setting-modal.less";
import { lib } from "react-single-app";

const { Title } = Typography;
/**
 * 业务配置
 */
export default function BusinessSettingModal() {
    const [visible, setVisible] = useState(false);
    const [form] = Form.useForm();
    useEffect(() => {
        if (visible) {
            lib.request({
                url: "/ccs/dictionary/businessType",
                success: res => {
                    form.setFieldsValue({ businessList: res });
                },
            });
        }
    }, [visible]);
    const onClose = () => {
        setVisible(false);
    };
    const onOk = () => {
        form.submit();
    };
    const onFinish = values => {
        let dataList = values.businessList.slice(7);
        lib.request({
            url: "/ccs/dictionary/businessType/update",
            data: { dataList },
            success: res => {
                onClose();
                message.success("保存业务类型成功！");
            },
        });
    };
    const removeItem = (names, remove) => {
        let itemData = form.getFieldValue(["businessList", names]);
        if (itemData && itemData.id) {
            lib.request({
                url: "/ccs/mailPool/checkCode",
                data: { codeList: [itemData.id] },
                success: res => {
                    if (res?.status) {
                        lib.request({
                            url: "/ccs/dictionary/businessType/delete",
                            data: { id: itemData.id },
                            success: res => {
                                remove(names);
                                message.success("删除业务类型成功！");
                            },
                        });
                    } else {
                        message.error("当前有正在被使用的单据！");
                    }
                },
            });
        } else {
            remove(names);
        }
    };
    return (<>
        <Drawer
            title={"业务类型配置"}
            onClose={onClose}
            open={visible}
            width={520}
            bodyStyle={{ overflow: "hidden" }}
            footerStyle={{ textAlign: "end" }}
            footer={
                <Space>
                    <Button onClick={onClose}>取消</Button>
                    <Button onClick={onOk} type="primary">
                        确定
                    </Button>
                </Space>
            }>
            <div rootClassName="business-setting-modal">
                <Title level={5} rootClassName={"business-setting-modal-title"}>
                    业务类型
                </Title>
                <Alert
                    message={"初始化的7种类型不可编辑和删除，新增配置的类型编辑后则会更新历史单据"}
                    type={"info"}
                    showIcon
                    rootClassName={"business-setting-modal-alert"}
                />
                <Form rootClassName={"business-setting-modal-form"} form={form} onFinish={onFinish}>
                    <Form.List name={"businessList"}>
                        {(fields, { add, remove }, { errors }) => (
                            <>
                                {fields.map((field, index) => (
                                    <Space key={`${field.key}`}>
                                        <Form.Item hidden={true} {...field} noStyle name={[field.name, "id"]}>
                                            <Input disabled={true} />
                                        </Form.Item>

                                        <Form.Item>
                                            <Form.Item
                                                {...field}
                                                name={[field.name, "name"]}
                                                rules={[{ required: true, message: "请输入" }]}
                                                noStyle>
                                                <Input
                                                    placeholder={"请输入"}
                                                    rootStyle={{ width: "360px" }}
                                                    disabled={index < 7}
                                                />
                                            </Form.Item>
                                            {index >= 7 ? (
                                                <DeleteOutlined
                                                    rootClassName={"dynamic-delete-button"}
                                                    onClick={() => removeItem(field.name, remove)}
                                                />
                                            ) : null}
                                        </Form.Item>
                                    </Space>
                                ))}
                                <Button
                                    type="dashed"
                                    onClick={() => add()}
                                    rootStyle={{ width: "360px" }}
                                    icon={<PlusOutlined />}>
                                    新建类型
                                </Button>
                            </>
                        )}
                    </Form.List>
                </Form>
            </div>
        </Drawer>
        <Button
            onClick={() => {
                setVisible(true);
            }}
            type="primary">
            业务类型配置
        </Button>
    </>);
}
