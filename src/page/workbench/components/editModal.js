import React, { useState } from "react";
import { Modal } from "antd";
import { ConfigFormCenter } from "react-single-app";
import { CLAIM_DATA, DISTRIBUTE_DATA, HANDLE_DATA, REPROCESS_DATA, TRANSFER_DATA, BULK_ALLOCATION } from "./config";
import { BussinessType } from "../../customs-clearance-manage/customs-clearance-detail2";

export const STATUS_TYPE = {
    //1待处理，2处理中，3无需处理，4已挂起，5已完成
    PENDING: 1,
    IN_PROGRESS: 2,
    NOT_NEEDED: 3,
    SUSPENDED: 4,
    COMPLETED: 5,
};
export const MAIL_POOL_OPERATE_TYPE = {
    /**
     * 认领
     */
    claim: {
        code: 1,
        name: "认领",
        config: CLAIM_DATA,
    },
    /**
     * 分配
     */
    distribute: {
        code: 2,
        name: "分配",
        config: DISTRIBUTE_DATA,
    },
    /**
     * 转交
     */
    transfer: {
        code: 3,
        name: "转交",
        config: TRANSFER_DATA,
    },
    /**
     * 4 处理
     */
    handle: {
        code: 4,
        name: "处理",
        config: HANDLE_DATA,
    },
    /**
     * 5 重新处理
     */
    reprocess: {
        code: 5,
        name: "重新处理",
        config: REPROCESS_DATA,
    },
    /**
     * 6批量分配
     */
    bulkallocation: {
        code: 6,
        name: "批量分配",
        config: BULK_ALLOCATION,
    },
};
/**
 * 固定的业务类型
 * @type {(string)[]}
 */
const FIXED_BUSSINESS_TYPE = [
    BussinessType.BUSSINESS_REFUND_INAREA,
    BussinessType.BUSSINESS_SECTION_OUT,
    BussinessType.BUSSINESS_SECTION_IN,
    BussinessType.BUSSINESS_SECTIONINNER_OUT,
    BussinessType.BUSSINESS_SECTIONINNER_IN,
    BussinessType.BUSSINESS_ONELINE_IN,
    BussinessType.BUSSINESS_DESTORY,
];
/**
 *
 * @param detail   弹框展示详情
 * @param showModal 显示弹框
 * @param type   MAIL_POOL_OPERATE_TYPE  操作类型
 * @param dialogClose 弹框关闭
 */
export default function MailEditModal({ ids, id, showModal, type, dialogClose }) {
    const ref = React.useRef();
    const [selectChangeDesc, setSelectChangeDesc] = useState({});
    const handleCancel = () => {
        dialogClose(false);
    };
    const handleOk = () => {
        ref.current.submitForm();
    };
    const beforeSubmit = values => {
        if (id) {
            values.id = id;
        }
        if (ids) {
            values.ids = ids;
        }
        if (type && ids == null) {
            values.type = type.code;
        }
        return Object.assign(values, selectChangeDesc);
    };
    const onConfigLoadSuccess = config => {
        config.baseInfo.children.map(item => {
            ref.current.initSelect(item);
        });
    };
    const onSubmitSuccess = () => {
        dialogClose(true);
    };
    const beforeSetDetail = values => {
        return values;
    };
    const confData = () => {
        return type.config;
    };
    const onSinglesSelectChange = desc => {
        let config = ref.current.config;
        switch (desc.name) {
            case "userId":
                selectChangeDesc.userName = desc.value.children;
                setSelectChangeDesc(selectChangeDesc);
                break;
            case "code":
                selectChangeDesc.codeName = desc.value.children;
                setSelectChangeDesc(selectChangeDesc);
                if (type === MAIL_POOL_OPERATE_TYPE.handle || type === MAIL_POOL_OPERATE_TYPE.reprocess) {
                    const status = ref.current.getFormFiled("status");
                    config.baseInfo.children.map(item => {
                        if (item.name === "associatedNumber") {
                            item.rules =
                                FIXED_BUSSINESS_TYPE.some(item => item == desc.value.value) &&
                                status === STATUS_TYPE.COMPLETED
                                    ? [{ required: true }]
                                    : [];
                        }
                    });
                    console.log("config", config);
                    ref.current.changeConfig(config);
                }
                break;
            case "status":
                if (type === MAIL_POOL_OPERATE_TYPE.claim) {
                    let value = desc.value.target.value;
                    config.baseInfo.children.map(item => {
                        if (item.name === "code") {
                            item.hidden = value !== 1;
                        } else if (item.name === "remark") {
                            item.hidden = value === 1;
                        }
                    });
                    ref.current.changeConfig(config);
                }
                if (type === MAIL_POOL_OPERATE_TYPE.handle || type === MAIL_POOL_OPERATE_TYPE.reprocess) {
                    const code = ref.current.getFormFiled("code");
                    config.baseInfo.children.map(item => {
                        if (item.name === "associatedNumber") {
                            item.rules =
                                FIXED_BUSSINESS_TYPE.some(item => item == code) &&
                                desc.value.value === STATUS_TYPE.COMPLETED
                                    ? [{ required: true }]
                                    : [];
                        }
                    });
                    ref.current.changeConfig(config);
                }
                break;
            default:
                break;
        }
    };

    return (
        (<Modal destroyOnClose onCancel={handleCancel} onOk={handleOk} title={type?.name} open={showModal}>
            {type && (
                <ConfigFormCenter
                    ref={ref}
                    confData={confData()}
                    submitUrl={ids ? "/ccs/mailPool/updateMailBatch" : "/ccs/mailPool/updateMail"}
                    beforeSetDetail={beforeSetDetail}
                    beforeSubmit={beforeSubmit}
                    onConfigLoadSuccess={onConfigLoadSuccess}
                    onSubmitSuccess={onSubmitSuccess}
                    onSinglesSelectChange={onSinglesSelectChange}
                />
            )}
        </Modal>)
    );
}
