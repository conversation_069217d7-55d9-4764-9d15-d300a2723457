import React, { useState, useEffect, useMemo, useRef, forwardRef, useImperativeHandle } from "react";
import {
    Button,
    InputNumber,
    Table,
    Typography,
    Empty,
    Space,
    Row,
    Col,
    Pagination,
    Badge,
    Tooltip,
    Form,
    Select,
    Tabs,
} from "antd";
import { usePagination } from "ahooks";
import Icon, {
    RightOutlined,
    QuestionCircleOutlined,
    CopyOutlined,
    MinusOutlined,
    PlusOutlined,
} from "@ant-design/icons";
import icon_empty from "../../asset/image/icon_empty.png";
import "./index.less";
import DescriptionItem from "../../components/description-item";
import IconDestroy from "../../asset/image/icon_destory.svg";
import IconOnlineIn from "../../asset/image/icon_oneline_in.svg";
import IconRefundInArea from "../../asset/image/icon_refund_in_area.svg";
import IconSectionIn from "../../asset/image/icon_section_in.svg";
import IconSectionOut from "../../asset/image/icon_section_out.svg";
import IconSectioninnerIn from "../../asset/image/icon_sectioninner_in.svg";
import IconSectioninnerOut from "../../asset/image/icon_sectioninner_out.svg";
import { lib } from "react-single-app";
import MailEditModal, { MAIL_POOL_OPERATE_TYPE } from "./components/editModal";
import { data } from "../cross_border_entrance/nuclear-release-manage/detail/view-config";
/**
 * 个人工作台
 */
export default function Workbench() {
    const [dataCount, setDataCount] = useState();
    const [refreshMethods, setRefreshMethods] = useState({});
    const itemWaitRef = useRef();
    const mailDetailRef = useRef();
    const mailPickRef = useRef();
    const getMethods = () => {
        const m = {};
        Object.assign(m, itemWaitRef.current, mailDetailRef.current, mailPickRef.current);
        setRefreshMethods(m);
    };
    const requestParams = useRef({
        goodsRecordWareHouseCode: "all",
        afterSalesAreaCompanyId: "all",
        invBusinessAreaCompanyId: "all",
    });

    useEffect(() => {
        getMethods();
        lib.request({
            url: "/ccs/businessCenter/dataCount",
            data: { ...requestParams.current },
            success: res => {
                setDataCount(res);
            },
        });
    }, []);

    const refresh = data => {
        requestParams.current = Object.assign(requestParams.current, data);
        lib.request({
            url: "/ccs/businessCenter/dataCount",
            data: { ...requestParams.current },
            success: res => {
                setDataCount(res);
            },
        });
    };
    return (
        <div className={"customs-workbench"}>
            <Row>
                <TimedRefresh refreshMethods={refreshMethods} />
            </Row>
            <Row gutter={16}>
                <Col span={17}>
                    <MailWaitDetail ref={mailDetailRef} />
                    <Row gutter={16}>
                        <Col span={7}>
                            <GoodsRecord dataCount={dataCount} refresh={refresh} />
                        </Col>
                        <Col span={17}>
                            <BondedAfterSales dataCount={dataCount} refresh={refresh} />
                        </Col>
                    </Row>
                    <CustomsClearanceBusiness dataCount={dataCount} refresh={refresh} />
                </Col>
                <Col span={7}>
                    <MailWaitPick ref={mailPickRef} />
                    <ItemWaitHandle ref={itemWaitRef} />
                </Col>
            </Row>
        </div>
    );
}

/**
 * 我的邮件待处理
 */
const MailWaitDetail = forwardRef((props, ref) => {
    const [showModal, setShowModal] = useState(false);
    const [showModalType, setShowModalType] = useState();
    const [id, setEditId] = useState();
    let [refresh, setRefresh] = useState(0);
    const columns = [
        {
            title: "邮件接收时间",
            dataIndex: "receiptTime",
            width: 150,
            render: (text, row, index) => {
                return new Date(text).format("yyyy-MM-dd hh:mm:ss");
            },
        },
        {
            title: "邮件标题",
            dataIndex: "subject",
            width: 200,
            ellipsis: true,
            render: (text, row, index) => {
                return (
                    <Tooltip placement="topLeft" title={text}>
                        {text}
                    </Tooltip>
                );
            },
        },
        {
            title: "发件人",
            dataIndex: "addresserName",
            width: 150,
        },
        {
            title: "处理状态",
            dataIndex: "statusDesc",
            width: 100,
            render: (text, record, index) => {
                if (record.status === 1) {
                    return <Badge status="warning" text={record.statusDesc} />;
                } else if (record.status === 2) {
                    return <Badge status="processing" text={record.statusDesc} />;
                } else {
                    return <Badge status="warning" text={record.statusDesc} />;
                }
            },
        },
        {
            title: "业务类型",
            dataIndex: "codeName",
            width: 120,
        },
        {
            title: "最新处理时间",
            dataIndex: "updateTime",
            width: 150,
            render: (text, row, index) => {
                return new Date(text).format("yyyy-MM-dd hh:mm:ss");
            },
        },
        {
            title: "操作",
            dataIndex: "operator",
            fixed: "right",
            width: 120,
            render: (text, row, index) => {
                return (
                    <Space>
                        <a
                            className={"link"}
                            onClick={() => {
                                lib.request({
                                    url: "/ccs/mailPool/addBrowse",
                                    data: { id: row.id },
                                }).then(res => {
                                    lib.openPage(`/mail-detail?page_title=邮件详情&id=${row.id}`, () => {
                                        setRefresh(++refresh);
                                    });
                                });
                            }}>
                            处理
                        </a>
                        <a
                            className={"link"}
                            onClick={() => {
                                setShowModalType(MAIL_POOL_OPERATE_TYPE.transfer);
                                setShowModal(true);
                                setEditId(row.id);
                            }}>
                            转交
                        </a>
                    </Space>
                );
            },
        },
    ];

    useImperativeHandle(ref, () => ({
        getMailDetail: () => {
            getTableData({ current: 1, pageSize: pagination.pageSize });
        },
    }));

    const getTableData = ({ current, pageSize }) => {
        return new Promise((resolve, reject) => {
            lib.request({
                url: "/ccs/mailPool/pendingList",
                data: { currentPage: current, pageSize },
                success: res => {
                    resolve({
                        total: res.page.totalCount,
                        list: res.dataList,
                    });
                },
                fail: e => {
                    reject(e);
                },
            });
        });
    };

    const { data, loading, pagination } = usePagination(getTableData, {
        refreshDeps: [refresh],
        defaultPageSize: 5,
    });
    return (
        <div className={"mail-wait-detail"}>
            <div className={"title"}>
                <Typography.Title level={5}>我的邮件待处理</Typography.Title>
                <Button type={"link"} onClick={() => lib.openPage("/my-mail-items?page_title=我的邮件事项")}>
                    更多
                    <RightOutlined style={{ fontSize: 10, marginLeft: 4 }} />
                </Button>
            </div>

            {data?.list.length > 0 ? (
                <div>
                    <Table
                        loading={loading}
                        dataSource={data?.list}
                        tableLayout={"fixed"}
                        columns={columns}
                        scroll={{ x: 990 }}
                        size={"small"}
                        pagination={false}
                    />
                    <Pagination
                        size={"small"}
                        current={pagination.current}
                        pageSize={pagination.pageSize}
                        total={data?.total}
                        onChange={pagination.onChange}
                        pageSizeOptions={[5, 10, 20, 30, 50, 100]}
                        onShowSizeChange={pagination.onChange}
                        showQuickJumper
                        showSizeChanger
                        style={{ marginTop: 16, textAlign: "right" }}
                    />
                </div>
            ) : (
                <div className={"empty-desc"}>暂无待处理邮件</div>
            )}
            {
                <MailEditModal
                    id={id}
                    showModal={showModal}
                    type={showModalType}
                    dialogClose={isSuccess => {
                        setShowModal(false);
                        if (isSuccess) {
                            setRefresh(++refresh);
                        }
                    }}
                />
            }
        </div>
    );
});

/**
 * 邮件池待认领
 */
const MailWaitPick = forwardRef((props, ref) => {
    const [pickData, setPickData] = useState({});

    useEffect(() => {
        getPickList();
    }, []);

    useImperativeHandle(ref, () => ({
        getMailPick: getPickList,
    }));

    function getPickList() {
        lib.request({
            url: "/ccs/mailPool/claimList",
            success: res => {
                setPickData(res);
            },
        });
    }

    const pickList = useMemo(() => {
        return pickData.dataList?.map((item, index) => {
            return (
                <div
                    key={index}
                    className={"pick-list-item"}
                    onClick={() => {
                        let id = item.id;
                        lib.request({
                            url: "/ccs/mailPool/addBrowse",
                            data: { id: id },
                        }).then(res => {
                            lib.openPage(`/mail-detail?page_title=邮件详情&id=${id}`, () => {
                                getPickList();
                            });
                        });
                    }}>
                    <div className={"pick-list-item-title"}>{item.subject}</div>
                    <div className={"pick-list-item-date"}>{item.receiptTime}</div>
                </div>
            );
        });
    }, [pickData?.dataList]);

    return (
        <div className={"mail-wait-pick"}>
            <div className={"title "}>
                <Typography.Title level={5}>邮件池待认领</Typography.Title>
                <div
                    className={"tag"}
                    onClick={() => {
                        lib.openPage("/mail-pool?page_title=邮件池");
                    }}>
                    <div className={`container ${pickData?.total ? "" : "empty"}`}>
                        <div className={"content"}>待认领</div>
                        <div className={"count"}>{pickData?.total || 0}</div>
                    </div>
                    <div className={"arrow"}>
                        <RightOutlined style={{ fontSize: 8 }} />
                    </div>
                </div>
            </div>
            {pickData?.total > 0 ? (
                pickList
            ) : (
                <Empty description={"暂无待认领邮件"} image={icon_empty} imageStyle={{ height: "180px" }} />
            )}
        </div>
    );
});

/**
 *商品备案
 */
function GoodsRecord({ dataCount, refresh }) {
    const [selects, setSelects] = useState([{ name: "全部", id: "all" }]);
    const [value, setValue] = useState("all");

    useEffect(() => {
        lib.request({
            url: "/ccs/businessCenter/listWareHouse",
            success: data => {
                setSelects(data);
            },
        });
    }, []);

    return (
        <div className={"goods-record"}>
            <div className={"title"}>
                <Typography.Title level={5}>备案待处理</Typography.Title>
            </div>
            <div>
                <Form.Item label="仓库">
                    <Select
                        value={value}
                        onChange={e => {
                            setValue(e);
                            refresh && refresh({ goodsRecordWareHouseCode: e });
                        }}>
                        {selects.map(item => {
                            return (
                                <Select.Option value={item.id} name={item.name}>
                                    {item.name}
                                </Select.Option>
                            );
                        })}
                    </Select>
                </Form.Item>
            </div>
            <div
                className={"content"}
                onClick={() => {
                    let url = "";
                    if (value && value !== "all")
                        url = `/goods-record?page_title=商品备案&compatible=true&warehouseSn=${value}`;
                    else url = `/goods-record?page_title=商品备案&compatible=true`;
                    lib.openPage(url);
                }}>
                <div className={"content-title"}>
                    <span>备案待审核</span>
                    <RightOutlined style={{ marginLeft: "4px", fontSize: 8 }} />
                </div>
                <div className={"content-count"}>{dataCount?.goodsRecords.pendingCount || 0}</div>
                <div className={"content_str"}>
                    <Space>
                        <DescriptionItem
                            content={dataCount?.goodsRecords.overTimeCount || 0}
                            label={"超时"}
                            contentClassName={"out-time-content"}
                        />
                        <DescriptionItem
                            content={dataCount?.goodsRecords.earlyWarningCount || 0}
                            label={"预警"}
                            contentClassName={"early-warning-content"}
                        />
                    </Space>
                </div>
            </div>
        </div>
    );
}

const bondedText = (
    <div>
        取消单：工作台统计取消单管理【待取消】单据状态的汇总数量
        <br />
        撤单：工作台统计撤单管理【初始化】【申报中】【待总署审核】审核状态的汇总数量
        <br />
        退货：工作台统计退货管理【初始化】【待总署审核】审核状态的汇总数量
        <br />
        退货入仓：工作台统计退货管理【清关待创建】【核注待创建】【核注待完成】退货状态的汇总数量
    </div>
);

/**
 * 保税售后
 */
function BondedAfterSales({ dataCount, refresh }) {
    const [selects, setSelects] = useState([{ name: "全部", id: "all" }]);
    const [value, setValue] = useState("all");

    useEffect(() => {
        lib.request({
            url: "/ccs/businessCenter/listAreaCompany",
            success: data => {
                setSelects(data);
            },
        });
    }, []);
    return (
        <div className={"bonded-after-sales"}>
            <div className={"title"}>
                <Typography.Title level={5}>保税售后待处理</Typography.Title>
                <Tooltip autoAdjustOverflow placement="top" overlayStyle={{ minWidth: "500px" }} title={bondedText}>
                    <QuestionCircleOutlined
                        style={{ color: "#faad14", fontSize: "16px", marginLeft: "5px", marginBottom: "8px" }}
                    />
                </Tooltip>
            </div>
            <div>
                <Form.Item label="区内企业" style={{ width: "400px" }}>
                    <Select
                        value={value}
                        onChange={e => {
                            setValue(e);
                            refresh && refresh({ afterSalesAreaCompanyId: e });
                        }}>
                        {selects.map(item => {
                            return (
                                <Select.Option value={item.id} name={item.name}>
                                    {item.name}
                                </Select.Option>
                            );
                        })}
                    </Select>
                </Form.Item>
            </div>
            <Row gutter={16}>
                <Col span={"6"} className={"content-wrapper"}>
                    <div
                        className={"content"}
                        onClick={() => {
                            let url = "";
                            if (value && value !== "all")
                                url = `/cancel-order-manage?config_id=1604371714154398&page_title=取消单管理&calloffStatus=0&areaCompanyId=${
                                    value !== "all" ? value : ""
                                }`;
                            else
                                url = `/cancel-order-manage?config_id=1604371714154398&page_title=取消单管理&calloffStatus=0`;
                            lib.openPage(url);
                        }}>
                        <div className={"content-title"}>
                            取消单
                            <RightOutlined style={{ marginLeft: "4px", fontSize: 8 }} />
                        </div>
                        <div className={"content-count"}>{dataCount?.postSale.cancelCount || 0}</div>
                    </div>
                </Col>
                <Col span={"6"} className={"content-wrapper"}>
                    <div
                        className={"content"}
                        onClick={() => {
                            let url = "";
                            if (value && value !== "all")
                                url = `/cancel-lations-manage?page_title=撤单管理&areaCompanyId=${
                                    value !== "all" ? value : ""
                                }`;
                            else url = `/cancel-lations-manage?page_title=撤单管理`;
                            lib.openPage(url);
                        }}>
                        <div className={"content-title"}>
                            撤单
                            <RightOutlined style={{ marginLeft: "4px", fontSize: 8 }} />
                        </div>
                        <div className={"content-count"}>{dataCount?.postSale.cancelInfoCount || 0}</div>
                    </div>
                </Col>
                <Col span={"6"} className={"content-wrapper"}>
                    <div
                        className={"content"}
                        onClick={() => {
                            let url = "";
                            if (value && value !== "all")
                                url = `/goods-return-manage?page_title=退货管理&compatible=true&areaCompanyId=${
                                    value !== "all" ? value : ""
                                }`;
                            else url = `/goods-return-manage?page_title=退货管理&compatible=true`;
                            lib.openPage(url);
                        }}>
                        <div className={"content-title"}>
                            退货
                            <RightOutlined style={{ marginLeft: "4px", fontSize: 8 }} />
                        </div>
                        <div className={"content-count"}>{dataCount?.postSale.refundOrder || 0}</div>
                    </div>
                </Col>
                <Col span={"6"} className={"content-wrapper"}>
                    <div
                        className={"content"}
                        onClick={() => {
                            let url = "";
                            if (value && value !== "all")
                                url = `/goods-return-manage?page_title=退货管理&compatible=true&areaCompanyId=${
                                    value !== "all" ? value : ""
                                }`;
                            else url = `/goods-return-manage?page_title=退货管理&compatible=true`;
                            lib.openPage(url);
                        }}>
                        <div className={"content-title"}>
                            退货入仓
                            <RightOutlined style={{ marginLeft: "4px", fontSize: 8 }} />
                        </div>
                        <div className={"content-count"}>{dataCount?.postSale.refundInWarehouse || 0}</div>
                    </div>
                </Col>
            </Row>
        </div>
    );
}

const customsText = (
    <span>
        清关单：工作台统计清关单管理【提交材料（待审核）】【审核通过】【已创建】【已完善】状态的汇总数量
        <br />
        核注单：工作台统计核注单管理【生成核注单】【清关开始】【清关服务中】【清关失败】【清关完成（放行）】状态的汇总数量
        <br />
        核放单：工作台统计核放单管理由核注单生成的核放单【已创建】【申报中】【申报失败】【作废申报中】状态的汇总数量
    </span>
);

/**
 * 清关业务
 */
function CustomsClearanceBusiness({ dataCount, refresh }) {
    const [selects, setSelects] = useState([{ name: "全部", id: "all" }]);
    const [value, setValue] = useState("all");

    useEffect(() => {
        lib.request({
            url: "/ccs/businessCenter/listAreaCompany",
            success: data => {
                setSelects(data);
            },
        });
    }, []);

    return (
        <div className={"customs-clearance-business"}>
            <div className={"title"}>
                <Typography.Title level={5}>清关业务待处理</Typography.Title>
                <Tooltip
                    autoAdjustOverflow
                    placement="topRight"
                    overlayStyle={{ minWidth: "500px" }}
                    title={customsText}>
                    <QuestionCircleOutlined
                        style={{ color: "#faad14", fontSize: "16px", marginLeft: "5px", marginBottom: "8px" }}
                    />
                </Tooltip>
            </div>
            <div>
                <Form.Item label="区内企业" style={{ width: "400px" }}>
                    <Select
                        value={value}
                        onChange={e => {
                            setValue(e);
                            refresh && refresh({ invBusinessAreaCompanyId: e });
                        }}>
                        {selects.map(item => {
                            return (
                                <Select.Option value={item.id} name={item.name}>
                                    {item.name}
                                </Select.Option>
                            );
                        })}
                    </Select>
                </Form.Item>
            </div>
            <Row gutter={[16, 16]}>
                <Col span={"8"} className={"content-wrapper"} key={1}>
                    <ContentCard
                        cardTitle={"一线入境"}
                        cardIcon={IconOnlineIn}
                        count={dataCount?.onelineInCount}
                        id="ONELINE_IN"
                        value={value}
                    />
                </Col>
                <Col span={"8"} className={"content-wrapper"} key={2}>
                    <ContentCard
                        cardTitle={"区间流转（入）"}
                        cardIcon={IconSectionIn}
                        count={dataCount?.sectionIn}
                        id="SECTION_IN"
                        value={value}
                    />
                </Col>
                <Col span={"8"} className={"content-wrapper"} key={3}>
                    <ContentCard
                        cardTitle={"区内流转（入）"}
                        cardIcon={IconSectioninnerIn}
                        showChecklist={false}
                        count={dataCount?.sectioninnerIn}
                        id="SECTIONINNER_IN"
                        value={value}
                    />
                </Col>
                <Col span={"8"} className={"content-wrapper"} key={4}>
                    <ContentCard
                        cardTitle={"区间流转（出）"}
                        cardIcon={IconSectionOut}
                        count={dataCount?.sectionOut}
                        id="SECTION_OUT"
                        value={value}
                    />
                </Col>
                <Col span={"8"} className={"content-wrapper"} key={5}>
                    <ContentCard
                        cardTitle={"区内流转（出）"}
                        cardIcon={IconSectioninnerOut}
                        showChecklist={false}
                        count={dataCount?.sectioninnerOut}
                        id="SECTIONINNER_OUT"
                        value={value}
                    />
                </Col>
                <Col span={"8"} className={"content-wrapper"} key={6}>
                    <ContentCard
                        cardTitle={"退货入区"}
                        cardIcon={IconRefundInArea}
                        showChecklist={false}
                        count={dataCount?.refundInarea}
                        id="REFUND_INAREA"
                        value={value}
                    />
                </Col>
                <Col span={"8"} className={"content-wrapper"} key={7}>
                    <ContentCard
                        cardTitle={"销毁"}
                        cardIcon={IconDestroy}
                        count={dataCount?.destory}
                        value={value}
                        id="DESTORY"
                    />
                </Col>
                <Col span={"8"} className={"content-wrapper"} key={8}>
                    <ContentCard
                        cardTitle={"一线退运"}
                        cardIcon={IconOnlineIn}
                        showChecklist={true}
                        count={dataCount?.onelineRefund}
                        id="ONLINE_REFUND"
                        value={value}
                    />
                </Col>
                <Col span={"8"} className={"content-wrapper"} key={9}>
                    <ContentCard
                        cardTitle={"保税物流转大贸"}
                        cardIcon={IconSectionIn}
                        showChecklist={true}
                        count={dataCount?.bondedToTrade}
                        id="BONDED_TO_TRADE"
                        value={value}
                    />
                </Col>
                <Col span={"8"} className={"content-wrapper"} key={10}>
                    <ContentCard
                        cardTitle={"后续补税"}
                        cardIcon={IconDestroy}
                        showChecklist={false}
                        count={dataCount?.subsequentTax}
                        id="SUBSEQUENT_TAX"
                        value={value}
                    />
                </Col>
            </Row>
        </div>
    );
}

const ContentCard = ({ cardTitle, cardIcon, showChecklist = true, count, id, value }) => {
    return (
        <div className={"content-card"}>
            <div className={"content-title"}>
                <Icon component={cardIcon} className={"icon"} />
                <span>{cardTitle}</span>
            </div>

            <Row gutter={[16, 8]} className={"group"}>
                <Col span={"12"}>
                    <DescriptionItem
                        content={count?.inventoryOrderInfo || 0}
                        label={"清关单"}
                        className={"item"}
                        onClick={() => {
                            let url = "";
                            if (value && value !== "all")
                                url = `/customs-clearance-manage?page_title=清关单管理&areaCompanyId=${
                                    value !== "all" ? value : ""
                                }`;
                            else url = `/customs-clearance-manage?page_title=清关单管理`;
                            lib.openPageV2({
                                url: url,
                                params: { bussinessType: id },
                            });
                        }}
                    />
                </Col>
                <Col span={"12"}>
                    <DescriptionItem
                        content={count?.endorsement || 0}
                        label={"核注单"}
                        className={"item"}
                        onClick={() => {
                            let url = "";
                            if (value && value !== "all")
                                url = `/nuclear-note-manage?page_title=核注单管理&areaCompanyId=${
                                    value !== "all" ? value : ""
                                }`;
                            else url = `/nuclear-note-manage?page_title=核注单管理`;
                            lib.openPageV2({
                                url: url,
                                params: { bussinessType: id },
                            });
                        }}
                    />
                </Col>
                {showChecklist && (
                    <Col span={"12"}>
                        <DescriptionItem
                            content={count?.checklist || 0}
                            label={"核放单"}
                            className={"item"}
                            onClick={() => {
                                let url = "";
                                if (value && value !== "all")
                                    url = `/nuclear-release-manage?page_title=核放单管理&bussinessType=${id}&areaCompanyId=${
                                        value !== "all" ? value : ""
                                    }`;
                                else url = `/nuclear-release-manage?page_title=核放单管理&bussinessType=${id}`;
                                lib.openPage(url);
                            }}
                        />
                    </Col>
                )}
            </Row>
        </div>
    );
};

/**
 * 料号流水待处理
 */
const ItemWaitHandle = forwardRef((props, ref) => {
    const [dataType, setDataType] = useState("1");
    const [data, setData] = useState([]);
    const [loading, setLoading] = useState(false);
    const [pagination, setPagination] = useState({
        currentPage: 1,
        pageSize: 20,
        totalCount: 0,
        totalPage: 0,
    });
    const columns = [
        {
            title: "核注清单编号",
            dataIndex: "realOrderNo",
            width: 130,
            ellipsis: true,
            render: (text, row, index) => {
                return (
                    <div className="number">
                        {text}
                        <a onClick={() => copyOrderNo(text)}>
                            <CopyOutlined />
                        </a>
                    </div>
                );
            },
        },
        {
            title: "异常类型",
            dataIndex: "abnormalType",
            width: 100,
            render: (text, row, index) => {
                return <div className="abnormal">{text}</div>;
            },
        },
    ];

    useEffect(() => {
        getTableData(1);
    }, [dataType]);

    useImperativeHandle(ref, () => ({
        getItemData: () => {
            setDataType("1");
            getTableData(1);
        },
    }));

    const getTableData = (pageNumber, pageSize) => {
        setLoading(true);
        lib.request({
            url: "/ccs/businessCenter/itemStockListPaging",
            data: {
                dataType: dataType,
                currentPage: pageNumber,
                pageSize: pageSize || pagination.pageSize,
            },
            success: res => {
                setData(res.dataList);
                setPagination(res.page);
            },
        });
        setLoading(false);
    };

    const copyOrderNo = e => {
        navigator.clipboard.writeText(e);
    };

    const handleChange = e => {
        setDataType(e);
    };

    return (
        <div className={"item-wait-handle"}>
            <div className={"title"}>
                <Typography.Title level={5}>料号流水待处理</Typography.Title>
                <Tooltip
                    autoAdjustOverflow
                    placement="topRight"
                    overlayStyle={{ minWidth: "500px" }}
                    title={customsText}></Tooltip>
            </div>

            <Tabs defaultActiveKey="1" activeKey={dataType} onChange={handleChange}>
                <Tabs.TabPane tab="清关平台" key="1"></Tabs.TabPane>
                <Tabs.TabPane tab="海关平台" key="2"></Tabs.TabPane>
            </Tabs>

            <Table
                loading={loading}
                dataSource={data}
                tableLayout={"fixed"}
                columns={columns}
                size={"small"}
                scroll={{ y: 700 }}
                pagination={false}
            />
            <Pagination
                size={"small"}
                current={pagination?.currentPage}
                pageSize={pagination?.pageSize}
                total={pagination?.totalCount}
                showSizeChanger={true}
                style={{ marginTop: 16, textAlign: "right" }}
                onChange={getTableData}
            />
        </div>
    );
});

function TimedRefresh({ refreshMethods }) {
    const [text, setText] = useState("开始刷新");
    const [timeUnit, setTimeUnit] = useState("minutes");
    const [time, setTime] = useState(5);
    const [minusDisabled, setMinusDisabled] = useState(false);
    const [disabled, setDisabled] = useState(false);
    const selectOption = [
        {
            value: "hours",
            label: "时",
        },
        {
            value: "minutes",
            label: "分",
        },
        {
            value: "seconds",
            label: "秒",
        },
    ];

    useEffect(() => {
        let interval;
        let num = 0;
        switch (timeUnit) {
            case "seconds":
                num = time * 1000;
                break;
            case "minutes":
                num = time * 60 * 1000;
                break;
            default:
                num = time * 60 * 60 * 1000;
        }

        if (disabled) {
            interval = setInterval(() => {
                refreshMethods.getItemData();
                refreshMethods.getMailPick();
                refreshMethods.getMailDetail();
            }, num);
        }
        return () => {
            if (disabled) {
                clearInterval(interval);
            }
        };
    }, [disabled]);

    const minusTime = () => {
        if (time <= 1) {
            setMinusDisabled(true);
        } else {
            setTime(time - 1);
        }
    };

    const button1 = (
        <a disabled={minusDisabled} onClick={minusTime}>
            <MinusOutlined style={{ fontSize: "10px" }} />
        </a>
    );
    const button2 = (
        <a onClick={() => setTime(time + 1)}>
            <PlusOutlined style={{ fontSize: "10px" }} />
        </a>
    );

    const changeText = () => {
        if (text === "开始刷新") {
            setText("停止刷新");
            setDisabled(true);
        } else {
            setText("开始刷新");
            setDisabled(false);
        }
    };

    return (
        <div className={"time-refresh"}>
            <Row gutter={10}>
                <Col>
                    <Button type="primary" onClick={changeText}>
                        {text}
                    </Button>
                </Col>
                <Col>
                    <InputNumber
                        defaultValue={5}
                        style={{ width: 120 }}
                        addonBefore={button1}
                        addonAfter={button2}
                        value={time}
                        disabled={disabled}
                        onChange={value => setTime(value)}
                        controls={false}
                    />
                </Col>
                <Col>
                    <Select
                        defaultValue={timeUnit}
                        style={{
                            width: 120,
                        }}
                        onChange={value => setTimeUnit(value)}
                        options={selectOption}
                        disabled={disabled}
                    />
                </Col>
            </Row>
        </div>
    );
}
