import React from "react";
import { Table, Descriptions } from "antd";

/**
 * 操作日志
 */
export default function OperationLog({ detail = {} }) {
    const columns = [
        {
            title: "操作",
            dataIndex: "logName",
        },
        {
            title: "时间",
            dataIndex: "createTime",
            render: value => {
                return new Date(value).format("yyyy-MM-dd hh:mm:ss");
            },
        },
        {
            title: "操作账号",
            dataIndex: "updateBy",
        },
        {
            title: "说明",
            dataIndex: "logDetail",
        },
    ];
    return (
        <React.Fragment>
            <Descriptions title="操作日志"></Descriptions>
            <Table
                dataSource={detail.inventoryOrderLogDTOList}
                columns={columns}
                rowKey="id"
                pagination={false}></Table>
        </React.Fragment>
    );
}
