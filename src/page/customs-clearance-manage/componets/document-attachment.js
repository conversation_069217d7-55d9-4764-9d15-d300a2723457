import React, { useEffect, useState, useRef } from "react";
import { lib, Uploader } from "react-single-app";
import { Button, message, Table, Descriptions, Modal, Space } from "antd";

/***
 * 单据附件  from customs-clearance-detail2.js
 * @constructor
 */
export default function DocumentAttachment() {
    let id = lib.getParam("id");
    const [dataSource, setDataSource] = useState([]);
    const uploaderRef = useRef();

    useEffect(() => {
        getDetail();
    }, []);

    function getDetail() {
        lib.request({
            url: "/ccs/invenorder/viewInventoryOrderAttach",
            data: {
                id: id,
            },
            needMask: true,
            success: res => {
                setDataSource(res);
            },
        });
    }
    function previewFunc(row) {
        let newWindow = window.open();
        let img = new Image();
        img.src = row.attachPath;
        newWindow.document.body.appendChild(img);
    }

    function getBlob(url) {
        return new Promise(resolve => {
            const xhr = new XMLHttpRequest();

            xhr.open("GET", url, true);
            xhr.responseType = "blob";
            xhr.onload = () => {
                if (xhr.status === 200) {
                    resolve(xhr.response);
                }
            };

            xhr.send();
        });
    }

    /**
     * 保存
     * @param  {Blob} blob
     * @param  {String} filename 想要保存的文件名称
     */
    function saveAs(blob, filename) {
        if (window.navigator.msSaveOrOpenBlob) {
            navigator.msSaveBlob(blob, filename);
        } else {
            const link = document.createElement("a");
            const body = document.querySelector("body");

            link.href = window.URL.createObjectURL(blob);
            link.download = filename;

            // fix Firefox
            link.style.display = "none";
            body.appendChild(link);

            link.click();
            body.removeChild(link);

            window.URL.revokeObjectURL(link.href);
        }
    }

    /**
     * 下载
     * @param  {String} url 目标文件地址
     * @param  {String} filename 想要保存的文件名称
     */
    function download(url, filename) {
        getBlob(url).then(blob => {
            saveAs(blob, filename);
        });
    }

    function downloadFunc(row) {
        // console.log(row, "row");
        // return
        lib.request({
            url: "/ccs/invenorder/trace-log-download",
            data: {
                id: row.id,
            },
            needMask: true,
            success: res => {
                if (res) {
                    // window.open(row.attachPath);
                    download(row.attachPath, row.attachName);
                }
            },
        });
    }

    function deleteFunc(row) {
        Modal.confirm({
            cancelText: "取消",
            okText: "确定",
            title: "确认删除该附件吗？",
            onOk: () => {
                lib.request({
                    url: "/ccs/invenorder/delete-attach",
                    data: {
                        attachId: row.id,
                        orderId: id,
                    },
                    needMask: true,
                    success: res => {
                        if (res) {
                            message.success("删除成功");
                            getDetail();
                        }
                    },
                });
            },
        });
    }

    const columns = [
        {
            title: "文件名称",
            dataIndex: "attachName",
        },
        {
            title: "操作",
            render: row => {
                return (
                    <React.Fragment>
                        <Space>
                            {row.attachName.indexOf("jpg") !== -1 || row.attachName.indexOf("png") !== -1 ? (
                                <a className="link" onClick={() => previewFunc(row)}>
                                    预览
                                </a>
                            ) : row.attachName.indexOf("pdf") !== -1 ? (
                                <a className="link" href={row.attachPath} target="_blank">
                                    预览
                                </a>
                            ) : (
                                <a
                                    className="link"
                                    href={`https://view.officeapps.live.com/op/view.aspx?src=${row.attachPath}`}
                                    // href={`${row.attachPath}`}
                                    target="_blank">
                                    预览
                                </a>
                            )}
                            <a className="link" onClick={() => downloadFunc(row)}>
                                下载
                            </a>
                            <a className="link" onClick={() => deleteFunc(row, id)}>
                                删除
                            </a>
                        </Space>
                    </React.Fragment>
                );
            },
        },
    ];

    function importFile({ src, name }) {
        lib.request({
            url: "/ccs/invenorder/upload-attach",
            data: {
                url: src,
                id: lib.getParam("id"),
                fileName: name,
            },
            needMask: true,
            success: res => {
                if (res.code == 0) {
                    message.success("附件上传成功");
                    getDetail();
                    uploaderRef.current.querySelector(".react-single-app-uploader input").value = "";
                } else {
                    message.error("附件上传失败");
                }
            },
        });
    }

    return (
        <React.Fragment>
            <Descriptions title="单据附件"></Descriptions>

            <div style={{ float: "right", paddingBottom: 20 }} ref={uploaderRef}>
                <Button type="primary">
                    <Uploader
                        onChange={file => importFile(file)}
                        style={{
                            width: "100%",
                            height: "100%",
                            opacity: 0,
                            position: "absolute",
                            zIndex: 999,
                            top: 0,
                            left: 0,
                        }}
                    />
                    上传单据附件
                </Button>
            </div>
            <div style={{ padding: "0 20px" }}>
                <Table dataSource={dataSource} columns={columns} rowKey="id" pagination={false} />
            </div>
        </React.Fragment>
    );
}
