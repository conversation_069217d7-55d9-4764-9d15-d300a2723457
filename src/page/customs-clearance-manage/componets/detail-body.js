import React, { useState, useEffect, Fragment, useRef } from "react";
import {
    Button,
    Input,
    Select,
    Form,
    message,
    Tabs,
    Row,
    Col,
    Table,
    Descriptions,
    Modal,
    InputNumber,
    Space,
    Tooltip,
    Tag,
    Divider,
} from "antd";
import { ConfigFormCenter, event, lib, Uploader, UploadFile } from "react-single-app";
import { QuestionCircleOutlined, ExclamationCircleFilled } from "@ant-design/icons";

const TabPane = Tabs.TabPane;
const FormItem = Form.Item;
const Option = Select.Option;
import _ from "lodash";
import { BussinessType, CustomsClearanceStatus, TransitFlag } from "../customs-clearance-detail2";
import BodyDetailInfo from "./body-detail-info";

import NewModal from "../../../components/NewModal";
import HeaderTableOperation from "./header-table-operation";
import { dangerousFlagKeysList, dangerousFlagKeys, orderTagList } from "./view-config";

export default function DetailBody({
    detail = {},
    countries = [],
    currency = [],
    setDetail,
    fetchDetail,
    inveBusinessType,
}) {
    let [formRef] = Form.useForm();
    let [dataList, setDataList] = useState([]);
    let [isOld, setOldChange] = useState("new");
    let [isValues, setValues] = useState("");
    let [listItems, setListItems] = useState([]);
    let [visible, setVisible] = useState(false);
    let [previewRow, setPreviewRow] = useState([]);
    let [importResult, setImportResult] = useState({});
    let [previewModalVisible, setPreviewModalVisible] = useState(false);
    let [showHeaderTableOperation, setShowHeaderTableOperation] = useState(false);
    let [editRow, setEditRow] = useState();
    let [editIndex, setIndex] = useState();
    let [bodyEditType, setBodyEditType] = useState("edit");
    let auditStatus = lib.getParam("auditStatus"),
        id = lib.getParam("id");
    let [tallyDetailDataSource, setTallyDetailDataSource] = useState([]);
    let [tallyDetailLoading, setTallyDetailLoading] = useState(false);
    const [current, setCurrent] = useState({});
    const [source, setSource] = useState({});
    const [sourceOpen, setSourceOpen] = useState(false);
    const currentRef = useRef();
    const [selectedRowKeys, setSelectedRowKeys] = useState([]);
    const [babelBol, setBabelBol] = useState(false);
    let [columns, setColumns] = useState([
        {
            title: "通关校验",
            dataIndex: "verifyResultList",
            width: 160,
            render: (text, record, index) => {
                return (
                    <Space wrap>
                        {record.verifyResultList &&
                            record.verifyResultList.map(item => {
                                return (
                                    <Tooltip title={item.note}>
                                        <Tag color="red">{item.desc}</Tag>
                                    </Tooltip>
                                );
                            })}
                    </Space>
                );
            },
        },
        {
            title: "行号",
            width: 60,
            render: (_, __, index) => index + 1,
        },
        {
            title: "报关单商品序号",
            width: 150,
            dataIndex: "declareCustomsGoodsSeqNo",
            key: "declareCustomsGoodsSeqNo",
        },
        {
            title: "是否新品",
            dataIndex: "oldOrNew",
            width: 100,
            render: param => (param === "new" ? "是" : "否"),
        },
        {
            title: "商品sku",
            dataIndex: "skuId",
            width: 160,
        },
        {
            title: "统一料号",
            dataIndex: "originProductId",
            width: 160,
        },
        {
            title: "海关备案料号",
            dataIndex: "productId",
            width: 160,
            render: (text, record, index) => {
                return (
                    <div>
                        {text}
                        {record.productIdTagDesc && <Tag color="red">{record.productIdTagDesc}</Tag>}
                    </div>
                );
            },
        },

        {
            title: "备案序号",
            dataIndex: "goodsSeqNo",
            width: 120,
            render: (text, record, index) => {
                return record.oldOrNew === "new" ? "" : text;
            },
        },
        {
            title: (
                <Tooltip title="记账核注清单的账册序号">
                    记账金二序号
                    <QuestionCircleOutlined style={{ marginLeft: 4 }} />
                </Tooltip>
            ),
            dataIndex: "customsCallBackSeqNo",
            width: 135,
        },
        {
            title: "商品名称",
            dataIndex: "goodsName",
            width: 300,
        },
        {
            title: "商品编码",
            dataIndex: "hsCode",
            width: 100,
        },
        {
            title: "规格型号",
            dataIndex: "goodsModel",
            width: 300,
        },
        {
            title: "申报计量单位",
            dataIndex: "unitDesc",
            width: 120,
        },
        {
            title: "申报数量",
            dataIndex: "declareUnitQfy",
            width: 120,
        },
        {
            title: "法定第一单位",
            dataIndex: "firstUnitDesc",
            width: 120,
        },
        {
            title: "法定数量（单）",
            dataIndex: "firstUnitQfy",
            width: 140,
        },
        {
            title: "法定数量（总）",
            dataIndex: "firstUnitQfys",
            width: 140,
        },
        {
            title: "法定第二单位",
            dataIndex: "secondUnitDesc",
            width: 120,
        },
        {
            title: "第二法定数量（单）",
            dataIndex: "secondUnitQfy",
            width: 160,
        },
        {
            title: "第二法定数量（总）",
            dataIndex: "secondUnitQfys",
            width: 160,
            render: item => {
                return item ? item : "";
            },
        },
        {
            title: "净重(kg)",
            dataIndex: "netweights",
            width: 100,
        },
        {
            title: "毛重(kg)",
            dataIndex: "grossWeight",
            width: 100,
        },
        {
            title: "申报单价",
            dataIndex: "declarePrice",
            width: 140,
        },
        {
            title: "申报总价",
            dataIndex: "declareTotalPrice",
            width: 120,
        },
        {
            title: "原产国(地区)",
            dataIndex: "originCountryDesc",
            width: 120,
        },
        {
            title: "最终目的国(地区)",
            dataIndex: "destinationCountryDesc",
            width: 150,
        },
        {
            title: "商品条码",
            dataIndex: "goodsBar",
            width: 150,
        },
        {
            title: (
                <Tooltip title="请同时更改币制和申报单价">
                    币制
                    <QuestionCircleOutlined style={{ marginLeft: 4 }} />
                </Tooltip>
            ),
            dataIndex: "currencyDesc",
            width: 120,
        },
        {
            title: "关联一线入境报关单号",
            dataIndex: "customsEntryNo",
            width: 150,
        },

        {
            title: "征免方式",
            dataIndex: "avoidTaxMethod",
            width: 120,
        },
        {
            title: "危化品标志",
            dataIndex: "dangerousFlag",
            key: "dangerousFlag",
            width: 120,
            render: (text, row, index) => {
                return dangerousFlagKeys[text] || "";
            },
        },
        {
            title: "物种证明附件",
            dataIndex: "avoidTaxMethod",
            width: 120,
            render: (text, row, index) => {
                return (
                    row.speciesCertificateAttachmentName && (
                        <Space wrap>
                            <a href={row.speciesCertificateAttachmentUrl} target="_blank" className={"link"}>
                                {row.speciesCertificateAttachmentName}
                            </a>
                            <a
                                className={"link"}
                                onClick={() => {
                                    row.speciesCertificateAttachmentName = null;
                                    row.speciesCertificateAttachmentUrl = null;
                                    listItems[index] = row;
                                    setListItems([...listItems]);
                                }}>
                                删除
                            </a>
                        </Space>
                    )
                );
            },
        },
        {
            title: "操作",
            width: 250,
            fixed: "right",
            render: (text, row, index) => {
                return (
                    <Space wrap>
                        {[
                            CustomsClearanceStatus.STATUS_CREATED, //已创建
                            CustomsClearanceStatus.STATUS_PERFECT, //已完善
                            CustomsClearanceStatus.STATUS_CONFIRMING, //提交资料（待确认）
                            CustomsClearanceStatus.STATUS_AUDITED, //审核通过
                            CustomsClearanceStatus.STATUS_ENDORSEMENT, //生成核注单
                            CustomsClearanceStatus.STATUS_START_STORAGED, //已暂存
                            CustomsClearanceStatus.STATUS_FAILURE, //清关异常
                        ].some(item => item === detail.status) && (
                            <Fragment>
                                <a
                                    className={"link"}
                                    onClick={e => {
                                        e.nativeEvent.stopImmediatePropagation();
                                        e.stopPropagation();
                                        setShowHeaderTableOperation(true);
                                        setEditRow(row);
                                        setIndex(index);
                                        setBodyEditType("edit");
                                    }}>
                                    编辑
                                </a>
                                {row.endangered && !row.speciesCertificateAttachmentName && (
                                    <UploadFile
                                        listType={"text"}
                                        showUploadList={true}
                                        maxCount={1}
                                        uploadButton={<a className={"link"}>上传物种证明</a>}
                                        onChange={file => {
                                            row.speciesCertificateAttachmentName = file[0].name;
                                            row.speciesCertificateAttachmentUrl = file[0].url;
                                            listItems[index] = row;
                                            setListItems([...listItems]);
                                        }}
                                        fileKey={"ccs/customs-clearance-detail"}
                                        accept={".doc,.docx,.pdf,.xlsx,.xls"}
                                        action={"//dante-img.oss-cn-hangzhou.aliyuncs.com"}
                                    />
                                )}

                                <a
                                    onClick={e => {
                                        e.nativeEvent.stopImmediatePropagation();
                                        e.stopPropagation();
                                        let modal = Modal.confirm({
                                            cancelText: "取消",
                                            okText: "确定",
                                            title: "提示",
                                            content: "确认删除吗？",
                                            onOk: () => {
                                                if (
                                                    selectedRowKeys.includes(
                                                        `${row.skuId}${row.productId}${row.goodsSeqNo}`,
                                                    )
                                                ) {
                                                    setSelectedRowKeys(selectedRowKeys => {
                                                        const i = selectedRowKeys.indexOf(
                                                            `${row.skuId}${row.productId}${row.goodsSeqNo}`,
                                                        );
                                                        selectedRowKeys.splice(i, 1);
                                                        return selectedRowKeys.slice();
                                                    });
                                                    if (
                                                        currentRef.current &&
                                                        `${currentRef.current.skuId}${currentRef.current.productId}${currentRef.current.goodsSeqNo}` ===
                                                            `${row.skuId}${row.productId}${row.goodsSeqNo}`
                                                    ) {
                                                        setCurrent({});
                                                        currentRef.current = {};
                                                    }
                                                }
                                                setListItems(listItems => {
                                                    listItems.splice(index, 1);
                                                    listItems.map((item, index) => {
                                                        item.currentIndex = index + 1;
                                                    });
                                                    return listItems.slice();
                                                });
                                                modal.destroy();
                                            },
                                            onCancel: () => modal.destroy(),
                                        });
                                    }}>
                                    删除
                                </a>
                            </Fragment>
                        )}
                        {[
                            BussinessType.BUSSINESS_ONELINE_IN,
                            BussinessType.BUSSINESS_SECTIONINNER_IN,
                            BussinessType.BUSSINESS_SECTION_IN,
                        ].includes(detail?.inveBusinessType) && (
                            <a
                                onClick={e => {
                                    e.nativeEvent.stopImmediatePropagation();
                                    e.stopPropagation();
                                    lib.request({
                                        url: "/ccs/invenorder/getItemLogisticsInfo",
                                        data: {
                                            id: row.id,
                                        },
                                        success: data => {
                                            setSource(data);
                                            setSourceOpen(true);
                                        },
                                    });
                                }}>
                                查看溯源
                            </a>
                        )}
                    </Space>
                );
            },
        },
    ]);
    let previewColumns = [
        {
            title: "理货编号",
            dataIndex: "tallyOrderNo",
            width: 150,
        },
        {
            title: "出库单号",
            dataIndex: "outBoundNo",
            width: 180,
        },
        {
            title: "货品名称",
            dataIndex: "goodsName",
            width: 150,
        },
        {
            title: "SKU",
            dataIndex: "sku",
            width: 150,
        },
        {
            title: "料号",
            dataIndex: "productId",
            width: 100,
        },
        {
            title: "计划理货数量",
            dataIndex: "planTallyQty",
            width: 120,
        },
        {
            title: "实际理货数量",
            dataIndex: "actualTallyQty",
            width: 120,
        },
        {
            title: "备注",
            dataIndex: "remark",
            width: 120,
        },
    ];
    let tableColumns = [
        {
            title: "理货编号",
            dataIndex: "tallyOrderNo",
        },
        {
            title: "出库单号",
            dataIndex: "outBoundNo",
        },
        {
            title: "操作",
            render: row => {
                return (
                    <Space>
                        <span
                            className="link"
                            onClick={() => {
                                lib.request({
                                    url: "/ccs/invenorder/getTallyReportDetailById",
                                    data: {
                                        id: row.id,
                                    },
                                    needMask: true,
                                    success: res => {
                                        setVisible(true);
                                        setPreviewRow(res || []);
                                    },
                                });
                            }}>
                            预览
                        </span>
                    </Space>
                );
            },
        },
    ];
    let tallyDetailColumns = [
        {
            title: "商品sku",
            dataIndex: "skuCode",
        },
        {
            title: "商品名称",
            dataIndex: "skuName",
        },
        {
            title: "商品条码",
            dataIndex: "upcCode",
        },
        {
            title: "商品属性",
            dataIndex: "skuQualityDesc",
        },
        {
            title: "生产日期",
            dataIndex: "manufDate",
        },
        {
            title: "失效日期",
            dataIndex: "expireDate",
        },
        {
            title: "理货数量",
            dataIndex: "skuQty",
        },
    ];
    const columns1 = [
        {
            title: "行号",
            dataIndex: "idx",
        },
        {
            title: "金二序号",
            dataIndex: "goodsSeqNo",
        },
        {
            title: "统一料号",
            dataIndex: "productId",
        },
        {
            title: "海关备案料号",
            dataIndex: "customsRecordProductId",
        },
        {
            title: "申报数量",
            dataIndex: "declareQty",
        },
    ];
    const columns2 = [
        {
            title: "行号",
            dataIndex: "idx",
        },
        {
            title: "金二序号",
            dataIndex: "goodsSeqNo",
        },
        {
            title: "统一料号",
            dataIndex: "productId",
        },
        {
            title: "海关备案料号",
            dataIndex: "customsRecordProductId",
        },
        {
            title: "错误信息",
            dataIndex: "errorMsg",
        },
    ];

    useEffect(() => {
        let listItems = detail?.inventoryOrderItemDTOList || [];
        if (listItems.length) {
            listItems.map((item, index) => {
                item.declareUnitQfy = parseInt(item.declareUnitQfy);
                item.index = `${item.productId}.${index}`;
                item.currentIndex = index + 1;
                if (item.isNew && !item.oldOrNew) {
                    item.oldOrNew = item.isNew;
                }

                item.netweights = Number(item.netweight);
                item.netweight = (item.netweight * item.declareUnitQfy).toFixed(2);
                item.firstUnitQfys = parseFloat((item.firstUnitQfy * item.declareUnitQfy).toFixed(5));
                if (item.secondUnitQfy) {
                    item.secondUnitQfys = parseFloat((item.secondUnitQfy * item.declareUnitQfy).toFixed(5));
                }
            });
            setListItems([...listItems]);
        } else {
            setListItems([]);
        }
        setColumns(columns => {
            let index = columns.findIndex(item => item.dataIndex === "planDeclareQty");
            if (detail.channel === 1) {
                if (!~index) {
                    let declareUnitQfyIndex = columns.findIndex(item => item.dataIndex === "declareUnitQfy");
                    if (declareUnitQfyIndex !== -1) {
                        columns.splice(
                            declareUnitQfyIndex,
                            0,
                            {
                                title: "计划申报数量",
                                dataIndex: "planDeclareQty",
                                width: 160,
                            },
                            {
                                title: "实际理货数量",
                                dataIndex: "actualTallyQty",
                                width: 160,
                            },
                        );
                    }
                    let index = columns.findIndex(item => item.dataIndex === "declareUnitQfy");
                    columns[index].title = "最终申报数量";
                }
            }
            let customsCallBackSeqNoIndex = columns.findIndex(item => item.dataIndex === "customsCallBackSeqNo");
            if (customsCallBackSeqNoIndex !== -1 && detail?.transitFlag !== TransitFlag.TRANSIT) {
                columns.splice(customsCallBackSeqNoIndex, 1);
            }
            return columns.slice();
        });
    }, [detail]);

    // 是否新品切换
    function valuesChange(changeVal, allVal) {
        if (changeVal.oldOrNew) {
            formRef.setFieldsValue({ value: "" });
            setValues("");
            lib.request({
                url: "/ccs/customsGoods/matchByProductIdOrGoodsSeqNo",
                data: {
                    oldOrNew: changeVal.oldOrNew,
                    bookId: String(detail.areaBookId),
                    queryInfo: isValues,
                },
                needMask: true,
                success: res => {
                    if (res) {
                        let dataList = res;
                        dataList.map((item, index) => {
                            item.id = `${item.goodsSeqNo}.${item.productId}`;
                        });
                        // setDataList(dataList);
                        setOldChange(changeVal.oldOrNew);
                    }
                },
            });
        }
    }

    // 新增表体数据
    function finishHandle(values) {
        let theData = dataList.find(item => item.id === values.value);
        if (
            listItems.some(
                item =>
                    `${item.skuId}${item.productId}${item.goodsSeqNo}` ===
                    `${theData.skuId}${theData.productId}${theData.goodsSeqNo}`,
            )
        ) {
            message.warning("请勿重复添加");
            return;
        }
        lib.request({
            url: "/ccs/customsGoods/findItemDetailByV2",
            data: {
                oldOrNew: values.oldOrNew,
                productId: values.oldOrNew === "old" ? theData.productId : theData.originProductId,
                skuId: theData.skuId,
                bookId: String(detail.areaBookId),
                goodsSeqNo: theData.goodsSeqNo,
                inventoryOrderId: lib.getParam("id"),
            },
            needMask: true,
            success: res => {
                if (res && res.result) {
                    res.result.netweights = res.result.netweight;
                    // if ([BussinessType.BUSSINESS_DESTORY,
                    // BussinessType.INVENTORY_PROFIT,
                    // BussinessType.RANDOM_INSPECTION_DECLARATION].includes(detail.inveBusinessType))
                    //     res.result.dangerousFlag = "0";
                    setEditRow(res.result);
                    setBodyEditType("add");
                    setShowHeaderTableOperation(true);
                } else {
                    message.error(res.errorMessage);
                }
            },
        });
    }

    function getWmsTallyDetail() {
        setTallyDetailLoading(true);
        lib.request({
            url: "/ccs/invenorder/getWmsTallyDetail",
            data: {
                id: id,
            },
            needMask: true,
            success: res => {
                setTallyDetailDataSource(res);
                message.success("打托明细获取成功");
            },
        })
            .then(() => setTallyDetailLoading(false))
            .catch(() => setTallyDetailLoading(false));
    }

    // 添加通关教研数据
    function getCheckData(items) {
        items.map(item => {
            item.isNew = item.oldOrNew;
            if (!item.secondUnitDesc) {
                delete item.secondUnitDesc;
            }
            if (!item.secondUnit) {
                delete item.secondUnit;
            }
            if (!item.secondUnitQfys) {
                delete item.secondUnitQfys;
            }
            if (!item.secondUnitQfy) {
                delete item.secondUnitQfy;
            }
        });
        return new Promise((resolve, reject) => {
            lib.request({
                url: "/ccs/invenorder/itemVerify",
                data: {
                    invenOrderId: id,
                    listOrderItems: items,
                },
                success: data => {
                    const result = items.map((item, index) => {
                        item.verifyResultList = data[index];
                        return { ...item };
                    });
                    resolve(result);
                },
            });
        });
    }

    // 保存数据
    function submitHandle() {
        if (listItems.length === 0) {
            message.warning("请选择数据");
            return;
        }
        let flag = false;
        listItems.map(item => {
            item.isNew = item.oldOrNew;
            item.netweight = item.netweights; //不知道为啥要这样赋值，之前人写的🤢
            if (!item.declareUnitQfy) {
                flag = true;
                message.warning(`${item.goodsName}的申报单位数量为0`);
            }
            if (!item.secondUnitDesc) {
                delete item.secondUnitDesc;
            }
            if (!item.secondUnit) {
                delete item.secondUnit;
            }
            if (!item.secondUnitQfys) {
                delete item.secondUnitQfys;
            }
            if (!item.secondUnitQfy) {
                delete item.secondUnitQfy;
            }
        });
        if (inveBusinessType != "REFUND_INAREA") {
            let repeatArry = [];
            const expressCode = listItems.reduce(
                (prev, curr) => [...prev, `${curr.skuId}-${curr.productId}-${curr.goodsSeqNo}`],
                [],
            );
            for (let i = 0; i < expressCode.length; i++) {
                for (let j = i + 1; j < expressCode.length; j++) {
                    if (expressCode[i] === expressCode[j]) {
                        repeatArry.push(expressCode[i]);
                    }
                }
            }
            if (repeatArry.length != 0) {
                let repeatMessage;
                repeatMessage = repeatArry.join(",");
                message.error(`sku-料号:${repeatMessage} 存在重复`);
                return;
            }
        }

        if (flag) return;
        lib.request({
            url: "/ccs/invenorder/build-inventory-order-item",
            data: {
                invenOrderId: id,
                listOrderItems: listItems,
            },
            needMask: true,
            success: res => {
                if (res.errorMessage) {
                    message.warning(res.errorMessage);
                } else {
                    fetchDetail({ activeKey: "1", id: lib.getParam("id") });
                    message.success("编辑成功");
                }
            },
        });
    }

    // 弹窗关闭
    function okHandle() {
        setVisible(false);
        setPreviewRow([]);
    }

    // 导入文件
    function importFile({ src, name }) {
        lib.request({
            url: "/ccs/invenorder/preImport",
            data: {
                id,
                url: src,
            },
            needMask: true,
            success: res => {
                setImportResult(res);
                setPreviewModalVisible(true);
            },
        });
    }

    //预览弹窗确认
    function modalOk() {
        if (importResult.successCount == 0) {
            message.warning("暂无有效数据");
            return;
        }
        lib.request({
            url: "/ccs/invenorder/importExcel",
            data: {
                id,
                recordList: importResult.successRecordList,
            },
            needMask: true,
            success: res => {
                message.success("导入成功");
                setPreviewModalVisible(false);
                setImportResult({});
                getCheckData(res).then(data => {
                    setDetail(detail => {
                        return lib.clone({
                            ...detail,
                            inventoryOrderItemDTOList: data,
                        });
                    });
                });
            },
        });
    }

    function modalCancel() {
        setImportResult({});
        setPreviewModalVisible(false);
    }

    function debounce(func, wait) {
        return value => {
            clearTimeout(func.timer);
            func.timer = setTimeout(function () {
                func.call(this, value);
            }, wait);
        };
    }

    function searchClick(value) {
        if (value) {
            fetch(value, data => setDataList(data));
        } else {
            // setValues("")
            setDataList([]);
        }
    }

    let timeout;

    function fetch(value, callback) {
        if (timeout) {
            clearTimeout(timeout);
            timeout = null;
        }

        function fake() {
            lib.request({
                url: "/ccs/customsGoods/matchByProductIdOrGoodsSeqNoV2",
                data: {
                    oldOrNew: isOld,
                    bookId: String(detail.areaBookId),
                    queryInfo: value,
                    inventoryOrderId: lib.getParam("id"),
                },
                needMask: true,
                success: res => {
                    if (res) {
                        let dataList = res;
                        if (isOld == "new") {
                            dataList.map((item, index) => {
                                item.id = `${item.productId}`;
                            });
                        } else {
                            dataList.map((item, index) => {
                                item.id = `${item.goodsSeqNo}.${item.productId}`;
                            });
                        }
                        callback(dataList);
                        setValues(value);
                    }
                },
            });
        }

        timeout = setTimeout(fake, 100);
    }

    function renderModal() {
        return (
            <Fragment>
                {/* 导入预览弹窗 */}
                <Modal
                    width="800px"
                    open={previewModalVisible}
                    title={`提交预览  (修改数量${importResult.totalCount})`}
                    onOk={modalOk}
                    okText="提交"
                    onCancel={modalCancel}>
                    <Tabs defaultActiveKey="0">
                        <TabPane tab={`校验成功(${importResult.successCount})`} key="0">
                            <Table
                                dataSource={importResult.successRecordList}
                                columns={columns1}
                                rowKey="productId"></Table>
                        </TabPane>
                        <TabPane tab={`校验失败(${importResult.failCount})`} key="1">
                            <Table
                                dataSource={importResult.failRecordList}
                                columns={columns2}
                                rowKey="productId"></Table>
                        </TabPane>
                    </Tabs>
                </Modal>
                {editRow && (
                    <HeaderTableOperation
                        editRow={editRow}
                        detail={detail}
                        countryList={countries}
                        currencyList={currency}
                        bodyEditType={bodyEditType}
                        showHeaderTableOperation={showHeaderTableOperation}
                        closeModal={(success, formFieldsValue) => {
                            setShowHeaderTableOperation(false);
                            if (success) {
                                const arr = [...listItems];
                                if (bodyEditType === "edit") {
                                    arr[editIndex] = { ...arr[editIndex], ...formFieldsValue };
                                    getCheckData(arr).then(data => {
                                        setListItems([...data]);
                                    });
                                } else {
                                    getCheckData([formFieldsValue]).then(data => {
                                        setListItems([...arr, ...data]);
                                    });
                                }

                                if (
                                    current &&
                                    `${formFieldsValue.skuId}${formFieldsValue.productId}${formFieldsValue.goodsSeqNo}` ===
                                        `${current.skuId}${current.productId}${current.goodsSeqNo}`
                                ) {
                                    setCurrent({ ...formFieldsValue });
                                }
                            }
                        }}
                    />
                )}
                <NewModal
                    visible={babelBol}
                    configList={[
                        {
                            labelKey: "dangerousFlag",
                            labelName: "危化品标志",
                            type: "SELECT",
                            list: dangerousFlagKeysList,
                            required: true,
                        },
                    ]}
                    title={"批量设置危化品标志"}
                    onOk={data => {
                        if (
                            [
                                BussinessType.BUSSINESS_DESTORY,
                                BussinessType.INVENTORY_PROFIT,
                                BussinessType.RANDOM_INSPECTION_DECLARATION,
                            ].includes(inveBusinessType)
                        ) {
                            message.error("业务类型：销毁、盘盈、抽检申报，不允许设置危化品标志为是/否");
                            return;
                        }
                        listItems.map(item => {
                            if (selectedRowKeys.includes(`${item.skuId}${item.productId}${item.goodsSeqNo}`)) {
                                item.dangerousFlag = data.dangerousFlag;
                                item = { ...item };
                            }
                        });

                        // const arr = JSON.parse(JSON.stringify(listItems))
                        const arr = lib.clone(listItems);
                        setBabelBol(false);
                        setListItems(arr);

                        const result = arr.filter(
                            item =>
                                `${item.skuId}${item.productId}${item.goodsSeqNo}` ===
                                `${current.skuId}${current.productId}${current.goodsSeqNo}`,
                        );
                        if (result.length > 0) {
                            setCurrent({ ...result[0] });
                        }
                    }}
                    onCancel={() => {
                        setBabelBol(false);
                    }}
                />
                <NewModal
                    visible={sourceOpen}
                    configList={[
                        {
                            labelKey: "skuId",
                            labelName: "SKU",
                            type: "TEXT",
                            disabled: true,
                        },
                        {
                            labelKey: "originProductId",
                            labelName: "统一料号",
                            type: "TEXT",
                            disabled: true,
                        },
                        {
                            labelKey: "productId",
                            labelName: "海关备案料号",
                            type: "TEXT",
                            disabled: true,
                        },
                        {
                            labelKey: "customsEntryNo",
                            labelName: "报关单号",
                            type: "TEXT",
                            disabled: true,
                        },

                        {
                            labelKey: "customsEntryTime",
                            labelName: "报关日期",
                            type: "TEXT",
                            disabled: true,
                        },
                        {
                            labelKey: "originCountry",
                            labelName: "原产国",
                            type: "TEXT",
                            disabled: true,
                        },
                        {
                            labelKey: "shipmentCountry",
                            labelName: "启运国",
                            type: "TEXT",
                            disabled: true,
                        },
                        {
                            labelKey: "fromLocation",
                            labelName: "起运港(始发机场)",
                            type: "TEXT",
                            disabled: true,
                        },
                        {
                            labelKey: "transportMode",
                            labelName: "运输方式",
                            type: "TEXT",
                            disabled: true,
                        },
                        {
                            labelKey: "entryPort",
                            labelName: "进境口岸",
                            type: "TEXT",
                            disabled: true,
                        },
                    ]}
                    title={"查看溯源"}
                    cancelText={() => null}
                    okText={"关闭"}
                    editRow={source}
                    onCancel={() => {
                        setSourceOpen(false);
                    }}
                    onOk={() => {
                        setSourceOpen(false);
                    }}
                />
            </Fragment>
        );
    }

    /**
     * 生成调出调入清关单
     */
    function generateTransitInOutOrder() {
        lib.request({
            url: "/ccs/invenorder/generateTransitInOutOrder",
            data: { id: id },
            success: res => {
                message.success("生成调出调入清关单成功");
            },
        });
    }

    /**
     * 重新生成表体
     */
    function reCreateBody() {
        Modal.confirm({
            title: "重新生成表体",
            content: <span style={{ color: "rgba(0, 0, 0, 0.65)" }}>是否重新生成清关单表体（撤销原修改内容）</span>,
            okText: "确认",
            onOk() {
                return new Promise((resolve, reject) => {
                    lib.request({
                        url: "/ccs/invenorder/regenerateItem",
                        data: { id: id },
                        success: res => {
                            resolve(res);
                            fetchDetail({ activeKey: "1", id: lib.getParam("id") });
                            message.success("重新生成表体成功");
                        },
                        fail: e => {
                            reject(e);
                        },
                    });
                });
            },
            cancelText: "取消",
        });
    }

    /**
     * 导出表体
     */
    function exportBody() {
        lib.request({
            url: "/ccs/invenorder/exportItemExcel",
            needMask: true,
            data: { id: id },
            success: json => {
                lib.openPage("/excel/download-center?page_title=下载中心");
            },
        });
    }
    const descriptionsExtra = () => {
        return (
            <Space>
                <Button
                    onClick={() => {
                        showSetDestoryBabel();
                    }}>
                    批量设置危化品标志
                </Button>
                {(detail.channel === 1 ||
                    (Array.isArray(detail.orderTagList) && detail.orderTagList.includes(orderTagList.CWLABEL)) ||
                    detail.channel === 4) && <Button onClick={() => reCreateBody()}>重新生成表体</Button>}
                {detail.transitFlag === TransitFlag.TRANSIT && (
                    <Button onClick={() => generateTransitInOutOrder()}>生成调出调入清关单</Button>
                )}
                <Button onClick={() => exportBody()}>导出</Button>
                {[
                    BussinessType.BUSSINESS_ONELINE_IN,
                    BussinessType.BUSSINESS_SECTIONINNER_IN,
                    BussinessType.BUSSINESS_SECTION_IN,
                ].includes(detail?.inveBusinessType) && (
                    <Button
                        onClick={() => {
                            const importExtendParamBase64 = window.btoa(`inventoryOrderId=${lib.getParam("id")}`);
                            let url = `/excel/import-data?page_title=导入溯源&code=${encodeURIComponent(
                                "IMPORT_INVENTORY_ITEM_LOGISTICS_INFO",
                            )}&importExtendParam=${encodeURIComponent(importExtendParamBase64)}`;
                            lib.openPage(url, () => {
                                fetchDetail({ activeKey: "1", id: lib.getParam("id") });
                            });
                        }}>
                        导入溯源
                    </Button>
                )}
            </Space>
        );
    };

    const showSetDestoryBabel = () => {
        if (selectedRowKeys.length == 0) {
            return message.warn("请勾选需要修改的数据");
        }
        setBabelBol(true);
    };

    function checkEndangeredUploadFile() {
        let notUpload = false;
        for (let i = 0; i < listItems.length; i++) {
            let listItem = listItems[i];
            if (listItem.endangered && !listItem.speciesCertificateAttachmentName) {
                notUpload = true;
                break;
            }
        }
        return notUpload;
    }

    function createNuclearNote() {
        let content = "是否生成核注单?";
        if (checkEndangeredUploadFile()) {
            content = "存在表体涉濒危未上传物种证明，是否核注?";
        }
        Modal.confirm({
            title: "生成核注单",
            content: <span style={{ color: "rgba(0, 0, 0, 0.65)" }}>{content}</span>,
            okText: "确认",
            onOk() {
                return new Promise((resolve, reject) => {
                    lib.request({
                        url: "/ccs/invenorder/generateEndorsement",
                        data: { id: id },
                        success: res => {
                            resolve(res);
                            refreshNotification();
                            fetchDetail({ activeKey: "1", id: lib.getParam("id") });
                            message.success("生成核注单成功");
                        },
                        fail: e => {
                            reject(e);
                        },
                    });
                });
            },
            cancelText: "取消",
        });
    }

    /**
     * 刷新通知
     */
    function refreshNotification() {
        let refresh_event = lib.getParam("refresh_event");
        if (refresh_event) {
            event.emit(refresh_event, true);
        }
    }

    let showSaveBtn = [
        CustomsClearanceStatus.STATUS_CREATED, //已创建
        CustomsClearanceStatus.STATUS_PERFECT, //已完善
        CustomsClearanceStatus.STATUS_CONFIRMING, //提交资料（待确认）
        CustomsClearanceStatus.STATUS_AUDITED, //审核通过
        CustomsClearanceStatus.STATUS_ENDORSEMENT, //生成核注单
        CustomsClearanceStatus.STATUS_START_STORAGED, //已暂存
        CustomsClearanceStatus.STATUS_FAILURE, //清关异常
    ].some(item => item === detail.status);

    return (
        <React.Fragment>
            {renderModal()}
            {lib.getParam("subOrderSn") ? (
                <Descriptions
                    title={
                        <Tooltip placement="right" title="主清关单汇总合单全部表体信息">
                            清关单表体
                            <QuestionCircleOutlined style={{ marginLeft: 4 }} />
                        </Tooltip>
                    }
                    extra={descriptionsExtra()}
                />
            ) : (
                <Descriptions title={"清关单表体"} extra={descriptionsExtra()} />
            )}
            <BodyDetailInfo detail={current} />
            {detail.channel !== 1 &&
                detail.transitFlag !== TransitFlag.TRANSIT_IN &&
                detail.transitFlag !== TransitFlag.TRANSIT_OUT &&
                !detail?.orderTagList?.includes(orderTagList.CWLABEL) &&
                !(
                    detail.status === CustomsClearanceStatus.STATUS_COMPLETE ||
                    detail.status === CustomsClearanceStatus.STATUS_DISCARE ||
                    detail.status === CustomsClearanceStatus.STATUS_FINISH
                ) && (
                    <Form
                        form={formRef}
                        onValuesChange={valuesChange}
                        onFinish={finishHandle}
                        labelCol={{ span: 8 }}
                        wrapperCol={{ span: 16 }}>
                        <Row>
                            <Col span={6}>
                                <FormItem
                                    label="是否新品"
                                    name="oldOrNew"
                                    rules={[{ required: true, message: "请选择是否新品" }]}>
                                    <Select>
                                        <Option value="new">是</Option>
                                        <Option value="old">否</Option>
                                    </Select>
                                </FormItem>
                            </Col>
                            <Col span={12} offset={1}>
                                <FormItem noStyle shouldUpdate={(prev, curr) => prev.oldOrNew !== curr.oldOrNew}>
                                    {({ getFieldValue }) =>
                                        getFieldValue("oldOrNew") === "old" ? (
                                            <FormItem
                                                label="序号"
                                                name="value"
                                                rules={[{ required: true, message: "请选择序号" }]}>
                                                <Select
                                                    showSearch
                                                    filterOption={false}
                                                    // onSearch={value => searchClick(value)}>
                                                    onSearch={value => debounce(searchClick, 300)(value)}>
                                                    {dataList.map((item, index) => {
                                                        return (
                                                            <Option value={item.id} key={index}>
                                                                {item.goodsSeqNo}-{item.productId}-
                                                                {item.recordProductName}
                                                            </Option>
                                                        );
                                                    })}
                                                </Select>
                                            </FormItem>
                                        ) : (
                                            <Form.Item
                                                label="料号"
                                                name="value"
                                                rules={[{ required: true, message: "请选择料号" }]}>
                                                <Select
                                                    showSearch
                                                    filterOption={false}
                                                    onSearch={value => debounce(searchClick, 300)(value)}>
                                                    {dataList.map((item, index) => {
                                                        return (
                                                            <Option value={item.id} key={index}>
                                                                {`${item.productId}(${item.skuId})`}-
                                                                {item.recordProductName}
                                                            </Option>
                                                        );
                                                    })}
                                                </Select>
                                            </Form.Item>
                                        )
                                    }
                                </FormItem>
                            </Col>
                            <Col span={6} offset={1}>
                                {(!detail.orderTagList ||
                                    (Array.isArray(detail.orderTagList) &&
                                        !detail.orderTagList.includes(orderTagList.CWLABEL))) && (
                                    <Space>
                                        <Button type="primary" htmlType="submit">
                                            新增
                                        </Button>
                                        <Button>
                                            <Uploader
                                                onChange={file => importFile(file)}
                                                style={{
                                                    width: "100%",
                                                    height: "100%",
                                                    opacity: 0,
                                                    position: "absolute",
                                                    zIndex: 999,
                                                    top: 0,
                                                    left: 0,
                                                }}
                                            />
                                            导入
                                        </Button>
                                    </Space>
                                )}
                            </Col>
                        </Row>
                    </Form>
                )}
            <Table
                dataSource={listItems}
                columns={columns}
                scroll={{ y: 500 }}
                rowKey={record => `${record.skuId}${record.productId}${record.goodsSeqNo}`}
                rowSelection={{
                    type: "checkbox",
                    selectedRowKeys: selectedRowKeys,
                    preserveSelectedRowKeys: false,
                    onChange: (row, selectedRows) => {
                        setSelectedRowKeys(row);
                        if (selectedRows.length === 1) {
                            setCurrent(selectedRows[0]);
                            currentRef.current = { ...selectedRows[0] };
                        } else {
                            setCurrent({});
                            currentRef.current = {};
                        }
                    },
                }}
                onRow={record => {
                    return {
                        onClick: event => {
                            if (selectedRowKeys.includes(`${record.skuId}${record.productId}${record.goodsSeqNo}`)) {
                                const numIndex = selectedRowKeys.indexOf(
                                    `${record.skuId}${record.productId}${record.goodsSeqNo}`,
                                );
                                selectedRowKeys.splice(numIndex, 1);
                            } else {
                                selectedRowKeys.push(`${record.skuId}${record.productId}${record.goodsSeqNo}`);
                            }
                            if (selectedRowKeys.length === 1) {
                                const result = listItems.filter(
                                    item => `${item.skuId}${item.productId}${record.goodsSeqNo}` === selectedRowKeys[0],
                                );
                                setCurrent(result[0]);
                                currentRef.current = { ...result[0] };
                            } else {
                                setCurrent({});
                                currentRef.current = {};
                            }
                            setSelectedRowKeys([...selectedRowKeys]);
                        }, // 点击行
                    };
                }}
                pagination={false}
            />
            <div className="btn-wrap">
                <Space>
                    {(detail.status === CustomsClearanceStatus.STATUS_AUDITED ||
                        detail.status === CustomsClearanceStatus.STATUS_PERFECT) &&
                        detail.inveBusinessType !== BussinessType.FB_IN &&
                        detail.inveBusinessType !== BussinessType.FB_OUT &&
                        listItems.length === listItems.filter(item => !!item.originProductId).length && (
                            <Button onClick={() => createNuclearNote()}>生成核注单</Button>
                        )}
                    {showSaveBtn && (
                        <Button type="primary" onClick={submitHandle}>
                            保存
                        </Button>
                    )}
                </Space>
            </div>
            {showSaveBtn && <Divider />}
            {[
                BussinessType.BUSSINESS_REFUND_INAREA,
                BussinessType.BUSSINESS_SECTION_IN,
                BussinessType.BUSSINESS_SECTIONINNER_IN,
                BussinessType.BUSSINESS_ONELINE_IN,
            ].every(item => item !== detail?.inveBusinessType) && (
                <div>
                    <div className={"tallyDetail"}>
                        <div className={"title"}>打托明细</div>
                        <Button type={"primary"} loading={tallyDetailLoading} onClick={() => getWmsTallyDetail()}>
                            获取打托明细
                        </Button>
                    </div>
                    {!!tallyDetailDataSource?.length && (
                        <Table
                            dataSource={tallyDetailDataSource}
                            columns={tallyDetailColumns}
                            pagination={false}
                            rowKey="id"
                        />
                    )}
                </div>
            )}
            {!!detail?.inventoryOrderTallyReportVOList?.length && (
                <React.Fragment>
                    <div className="ant-descriptions-title" style={{ width: "100%", margin: "10px 0" }}>
                        相关文件
                    </div>
                    <Table
                        dataSource={detail?.inventoryOrderTallyReportVOList}
                        columns={tableColumns}
                        pagination={false}
                        rowKey="id"
                    />
                </React.Fragment>
            )}
            {/* 预览文件弹窗 */}
            <Modal open={visible} title="预览" width="1000px" onOk={okHandle} onCancel={okHandle}>
                <Table
                    dataSource={previewRow}
                    pagination={false}
                    columns={previewColumns}
                    scroll={{ x: "max-content" }}
                    rowKey="id"
                />
            </Modal>
        </React.Fragment>
    );
}
