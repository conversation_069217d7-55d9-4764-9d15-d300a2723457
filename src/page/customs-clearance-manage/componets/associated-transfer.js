import React, { useState, useEffect } from "react";
import { Button, message, Modal, Descriptions, Space } from "antd";
import { lib } from "react-single-app";
import { BussinessType } from "../customs-clearance-detail2";
export default ({ selectedRows, reload }) => {
    const [data, setData] = useState([]);
    const [open, setOpen] = useState(false);
    const checkedData = () => {
        if (selectedRows.length !== 2) {
            return message.error("请勾选2个清关单进行关联调拨");
        }
        lib.request({
            url: "/ccs/invenorder/viewAssociateTransferInfo",
            data: {
                inveOrderSn: selectedRows[0].inveCustomsSn,
                associatedOrderSn: selectedRows[1].inveCustomsSn,
            },
            success: data => {
                console.log("data:", data);
                setOpen(true);
                setData([data.outWarehouseInfoVO, data.inWarehouseInfoVO]);
            },
        });
    };

    const transfer = () => {
        lib.request({
            url: "/ccs/invenorder/associateTransfer",
            data: {
                inveOrderSn: selectedRows[0].inveCustomsSn,
                associatedOrderSn: selectedRows[1].inveCustomsSn,
            },
            success: data => {
                message.success("关联成功");
                setOpen(false);
                reload();
            },
        });
    };

    return (
        <>
            <Button
                onClick={() => {
                    checkedData();
                }}>
                关联调拨
            </Button>
            <Modal
                title="关联调拨"
                open={open}
                onCancel={() => {
                    setOpen(false);
                }}
                okText="保存"
                onOk={() => {
                    transfer();
                }}>
                <Space align="start">
                    {data.map((item, index) => (
                        <Descriptions title={index === 0 ? "【出】仓库" : "【入】仓库"} layout="vertical" column={1}>
                            <Descriptions.Item label="清关单号" labelStyle={{ fontWeight: 600 }}>
                                {item.inveCustomsSn || "-"}
                            </Descriptions.Item>
                            <Descriptions.Item label="业务类型" labelStyle={{ fontWeight: 600 }}>
                                {item.businessTypeName || "-"}
                            </Descriptions.Item>
                            <Descriptions.Item label="清关企业" labelStyle={{ fontWeight: 600 }}>
                                {item.inveCompanyName || "-"}
                            </Descriptions.Item>
                            <Descriptions.Item label="仓库类型" labelStyle={{ fontWeight: 600 }}>
                                {item.warehouseTypeName || "-"}
                            </Descriptions.Item>
                            <Descriptions.Item label="实体仓名称" labelStyle={{ fontWeight: 600 }}>
                                {item.entityWarehouseName || "-"}
                            </Descriptions.Item>
                            <Descriptions.Item label="货主" labelStyle={{ fontWeight: 600 }}>
                                {item.ownerName || "-"}
                            </Descriptions.Item>
                        </Descriptions>
                    ))}
                </Space>
            </Modal>
        </>
    );
};
