import React, { useState, useEffect } from "react";
import { Button, Input, Form, message, Alert, Modal, Space, Row } from "antd";
import { lib, ConfigFormCenter, event } from "react-single-app";
import { detailHeaderConfig, orderTagList } from "./view-config";
import { FallBackIcon } from "../../access-management/goods-record/settled-svg";
import { ClockCircleFilled, CheckCircleFilled } from "@ant-design/icons";
import DocumentAttachment from "./document-attachment";
import { BussinessType, CustomsClearanceStatus } from "../customs-clearance-detail2";
import { detailHeaderClearValue } from "../detail-header-clear-value";

const FormItem = Form.Item;
const { TextArea } = Input;
// EMPTY("", "未知"),
//  STATUS_CREATED("CREATED", "已创建"),
//  STATUS_AUDITED("AUDITED", "已审核"),
//  STATUS_PERFECT("ERFECT", "已完善"),
//  STATUS_ENDORSEMENT("ENDORSEMENT","生成核注单"),
//  STATUS_START_STORAGING("START_STORAGING", "清关开始(核注暂存中)"),
//  STATUS_START_STORAGED("START_STORAGED", "清关开始(核注已暂存)"),
//  STATUS_SERVERING("SERVERING", "清关服务中"),
//  STATUS_COMPLETE("COMPLETE", "清关完成"),
//  STATUS_FAILURE("FAILURE", "清关失败"),
//  STATUS_FINISH("FINISH", "服务完成(核注通过已核扣)"),
//  STATUS_DISCARE("DISCARD", "作废");

// 审核按钮模块
function AuditFc({ detail, visible, closeModal }) {
    const id = lib.getParam("id");

    let [form] = Form.useForm();
    const handleClose = success => {
        closeModal(success);
    };
    useEffect(() => {
        form.setFieldsValue(detail);
    }, [detail]);

    function auditPass() {
        if (!detail.inveBusinessType) {
            message.error("业务类型不能为空,请编辑保存后再审核！");
            return;
        } else {
            if (detail.inveBusinessType === "") {
                if (!detail.twoStepFlag) {
                    message.error("是否两步申报为空,请编辑保存后再审核！");
                    return;
                }
            }
        }
        lib.request({
            url: "/ccs/invenorder/audit",
            data: {
                id,
            },
            needMask: true,
            success: res => {
                message.success("审核通过");
                handleClose(true);
            },
        });
    }

    const FormText = ({ value }) => {
        return <span>{value}</span>;
    };

    function handleCancel() {
        handleClose();
    }

    return (
        <Modal title="审核通过" open={visible} onOk={auditPass} onCancel={() => handleCancel()} destroyOnClose>
            {BussinessType.BUSSINESS_ONELINE_IN === detail.inveBusinessType && (
                <p>一线入境需线下核实是否两步申报（是：区港联动；否：普通清单)</p>
            )}
            <Form form={form}>
                <Form.Item label={"业务类型"} name={"inveBusinessTypeDesc"} required>
                    <FormText />
                </Form.Item>
                {BussinessType.BUSSINESS_ONELINE_IN === detail.inveBusinessType && (
                    <Form.Item label={"是否两步申报"} name={"twoStepFlagDesc"} required>
                        <FormText />
                    </Form.Item>
                )}
            </Form>
        </Modal>
    );
}

export default function DetailHeaders({ detail, setDetail, fetchDetail }) {
    const ref = React.useRef();
    /**
     * 顶部的提示条
     */
    const [topAlert, setAlert] = useState();
    const [disableEdit, setDisableEdit] = useState(true);
    const id = lib.getParam("id");
    const masterOrderSn = lib.getParam("masterOrderSn");
    const subOrderSn = lib.getParam("subOrderSn");
    let [visible, setVisible] = useState(false);
    useEffect(() => {
        if (detail) {
            ref.current.setMergeDetail(detail);
            ref.current.changeConfig(getBusinessDetailHeaderConfig(detail));
        }
    }, [detail]);
    useEffect(() => {
        if (detail) {
            switchTopStatusView(detail);
        }
    }, [disableEdit, detail]);
    const switchTopStatusView = detail => {
        let { status } = detail;
        let topAlert;
        switch (status) {
            case CustomsClearanceStatus.STATUS_CONFIRMING:
                topAlert = {
                    message: "待确认",
                    icon: <ClockCircleFilled style={{ fontSize: "16px", color: "#FAAD14" }} />,
                    renderTopAction: (
                        <Space style={{ marginBottom: -1 }}>
                            {disableEdit && (
                                <Button
                                    type="primary"
                                    onClick={() =>
                                        getDetailFormData()
                                            .then(value => {
                                                setVisible(true);
                                            })
                                            .catch(errorInfo => {
                                                message.error(errorInfo.errorFields[0].errors);
                                            })
                                    }>
                                    审核通过
                                </Button>
                            )}
                        </Space>
                    ),
                };
                break;
            case CustomsClearanceStatus.STATUS_AUDITED: //已审核
                topAlert = {
                    message: "审核通过",
                    icon: <CheckCircleFilled style={{ fontSize: "16px", color: "#73D13D" }} />,
                };
                break;

            default:
                break;
        }
        setAlert(topAlert);
    };
    const configData = getBusinessDetailHeaderConfig(detail);

    function getDetailFormData() {
        return ref.current.getForm.validateFields();
    }

    const beforeSetDetail = data => {
        return data;
    };
    const onConfigLoadSuccess = config => {
        config.baseInfo.children.map(item => {
            if (item.type === "single-select") {
                if (item.name === "finalEntityWarehouseCode") {
                    ref.current.initSelect(item, { customsBookId: detail?.finalBookId });
                } else if (item.name === "finalOwnerCode") {
                    ref.current.initSelect(item, {
                        entityWarehouseCode: detail?.finalEntityWarehouseCode,
                        openStatus: 1,
                    });
                } else {
                    ref.current.initSelect(item);
                }
            }
        });
        config.declareInfo.children.map(item => {
            if (item.type === "single-select" && !item.fromParam) {
                if (item.name === "inveBusinessType") {
                    ref.current.initSelect(item, { inventoryOrderId: id });
                } else {
                    ref.current.initSelect(item);
                }
            }
        });
        config.extraInfo.children.map(item => {
            if (item.type === "single-select" && !item.fromParam) {
                ref.current.initSelect(item);
            }
        });
    };
    const onSubmitSuccess = () => {
        fetchDetail({ activeKey: "0", id: lib.getParam("id") });
        setDisableEdit(true);
        message.success("保存成功");
    };

    function getBusinessDetailHeaderConfig(detail) {
        const config = detailHeaderConfig();
        function setItem(item) {
            let transfereeEnable;
            switch (item.name) {
                // *必填，显示字段，可编辑（已创建、已完善、提交资料(待确认)、已审核）
                // 业务类型
                case "inveBusinessType":
                    transfereeEnable = [
                        CustomsClearanceStatus.STATUS_CREATED,
                        CustomsClearanceStatus.STATUS_PERFECT,
                        CustomsClearanceStatus.STATUS_CONFIRMING,
                        CustomsClearanceStatus.STATUS_AUDITED,
                    ].some(item => item === detail?.status);
                    item.disabled = !transfereeEnable;
                    item.rules = transfereeEnable
                        ? [
                              {
                                  required: true,
                                  message: `请选择${item.label}`,
                              },
                          ]
                        : [];
                    break;
                case "transportMode":
                    transfereeEnable = [
                        CustomsClearanceStatus.STATUS_CREATED,
                        CustomsClearanceStatus.STATUS_PERFECT,
                        CustomsClearanceStatus.STATUS_CONFIRMING,
                        CustomsClearanceStatus.STATUS_AUDITED,
                        CustomsClearanceStatus.STATUS_ENDORSEMENT,
                        CustomsClearanceStatus.STATUS_START_STORAGED,
                        CustomsClearanceStatus.STATUS_FAILURE,
                    ].some(item => item === detail?.status);
                    item.disabled = !transfereeEnable;
                    item.rules = transfereeEnable
                        ? [
                              {
                                  required: true,
                                  message: `请选择${item.label}`,
                              },
                          ]
                        : [];
                    break;
                //货主
                case "selfOwnedVehicle":
                case "licensePlate":
                case "vehicleCostRemark":
                    item.disabled = [BussinessType.BUSSINESS_DESTORY, BussinessType.BUSSINESS_REFUND_INAREA].some(
                        item => item === detail?.inveBusinessType,
                    );
                    item.pattern = [
                        BussinessType.BUSSINESS_DESTORY,
                        BussinessType.BUSSINESS_REFUND_INAREA,
                        BussinessType.BUSSINESS_EMPTY,
                    ].some(item => item === detail?.inveBusinessType)
                        ? "readPretty"
                        : "editable";
                    break;
                case "masterOrderSn":
                    item.hidden = !detail?.masterOrderSn;
                    break;
                case "subOrderSn":
                    item.hidden = !detail?.subOrderSn;
                    break;

                //启运/运抵国
                case "shipmentCountry":
                    // 一线入境： 必填，显示字段，可编辑，下拉 其它： 显示字段，不赋值，不可编辑
                    transfereeEnable = [
                        BussinessType.BUSSINESS_ONELINE_IN,
                        BussinessType.ONLINE_REFUND,
                        BussinessType.BONDED_ONELINE_IN,
                    ].includes(detail?.inveBusinessType);
                    item.disabled = !transfereeEnable;
                    item.pattern = !transfereeEnable ? "readPretty" : "editable";
                    item.rules = transfereeEnable
                        ? [
                              {
                                  required: true,
                                  message: `请选择${item.label}`,
                              },
                          ]
                        : [];
                    break;
                //是否生成报关单
                case "declarationFlag":
                    // 一线入境： 必填，显示字段，可编辑，下拉 其它： 显示字段，不赋值，不可编辑
                    transfereeEnable =
                        [
                            BussinessType.BUSSINESS_ONELINE_IN,
                            BussinessType.ONLINE_REFUND,
                            BussinessType.BONDED_TO_TRADE,
                            BussinessType.BONDED_ONELINE_IN,
                            BussinessType.SUBSEQUENT_TAX,
                        ].includes(detail?.inveBusinessType) ||
                        (BussinessType.BUSSINESS_SECTION_IN === detail?.inveBusinessType &&
                            detail?.transportMode === "S");
                    item.disabled = !transfereeEnable;
                    item.pattern = !transfereeEnable ? "readPretty" : "editable";
                    item.rules = transfereeEnable
                        ? [
                              {
                                  required: true,
                                  message: `请选择${item.label}`,
                              },
                          ]
                        : [];
                    if (BussinessType.SUBSEQUENT_TAX === detail?.inveBusinessType) {
                        item.rules = [];
                    }
                    break;
                //报关单类型
                case "customsEntryType":
                    transfereeEnable =
                        [
                            BussinessType.ONLINE_REFUND,
                            BussinessType.BONDED_TO_TRADE,
                            BussinessType.BUSSINESS_ONELINE_IN,
                            BussinessType.BONDED_ONELINE_IN,
                            BussinessType.SUBSEQUENT_TAX,
                        ].includes(detail?.inveBusinessType) ||
                        (BussinessType.BUSSINESS_SECTION_IN === detail?.inveBusinessType &&
                            detail?.transportMode === "S");

                    item.pattern = !transfereeEnable ? "readPretty" : "editable";
                    item.rules = transfereeEnable
                        ? [
                              {
                                  required: true,
                                  message: `请选择${item.label}`,
                              },
                          ]
                        : [];
                    break;
                //报关企业
                case "customsEntryCompany":
                    // 一线入境： 必填，显示字段，可编辑，下拉 其它： 显示字段，不赋值，不可编辑
                    transfereeEnable = BussinessType.BUSSINESS_ONELINE_IN === detail?.inveBusinessType;
                    item.disabled = !transfereeEnable;
                    item.pattern = !transfereeEnable ? "readPretty" : "editable";
                    item.rules = transfereeEnable
                        ? [
                              {
                                  required: true,
                                  message: `请选择${item.label}`,
                              },
                          ]
                        : [];
                    break;
                //货代公司
                case "forwardingCompany":
                    if (
                        detail?.inveBusinessType === BussinessType.ONLINE_REFUND ||
                        detail?.inveBusinessType === BussinessType.BONDED_ONELINE_IN
                    ) {
                        item.pattern = "editable";
                        item.rules = [
                            {
                                required: true,
                                message: `请选择${item.label}`,
                            },
                        ];
                    }
                //到货港口/机场
                case "arrivalPort":
                    // 一线入境： 必填，显示字段，可编辑，输入框 其它： 显示字段，不赋值，不可编辑
                    transfereeEnable =
                        BussinessType.BUSSINESS_ONELINE_IN === detail?.inveBusinessType ||
                        detail?.inveBusinessType === BussinessType.BONDED_ONELINE_IN;
                    item.pattern = !transfereeEnable ? "readPretty" : "editable";
                    item.rules = transfereeEnable
                        ? [
                              {
                                  required: true,
                                  message: `请选择${item.label}`,
                              },
                          ]
                        : [];
                    if (detail?.inveBusinessType === BussinessType.ONLINE_REFUND) {
                        item.pattern = "editable";
                        item.rules = [
                            {
                                required: true,
                                message: `请选择${item.label}`,
                            },
                        ];
                    }
                    break;
                //提单号
                case "pickUpNo":
                    if (
                        detail?.inveBusinessType === BussinessType.ONLINE_REFUND ||
                        detail?.inveBusinessType === BussinessType.BONDED_ONELINE_IN
                    ) {
                        item.pattern = "editable";
                        item.rules = [
                            {
                                required: true,
                                message: `请选择${item.label}`,
                            },
                        ];
                    }
                //集装箱号
                case "conNo":
                    // 一线入境： 必填，显示字段，可编辑，输入框 其它： 显示字段，不赋值，不可编辑
                    transfereeEnable =
                        BussinessType.BUSSINESS_ONELINE_IN === detail?.inveBusinessType ||
                        detail?.inveBusinessType === BussinessType.BONDED_ONELINE_IN;
                    item.pattern = !transfereeEnable ? "readPretty" : "editable";
                    item.rules = transfereeEnable && [{ required: true }];
                    if (detail?.inveBusinessType === BussinessType.ONLINE_REFUND) {
                        item.pattern = "editable";
                        item.rules = [
                            {
                                required: true,
                                message: `请选择${item.label}`,
                            },
                        ];
                    }
                    break;
                //品名
                case "productName":
                    //退货入区和销毁：显示字段，不赋值，不可编辑 其它：*必填，显示字段，可编辑，输入框
                    transfereeEnable = [
                        BussinessType.BUSSINESS_DESTORY,
                        BussinessType.BUSSINESS_REFUND_INAREA,
                        BussinessType.BUSSINESS_EMPTY,
                        BussinessType.INVENTORY_PROFIT,
                        BussinessType.RANDOM_INSPECTION_DECLARATION,
                    ].every(item => item !== detail?.inveBusinessType);
                    item.pattern = !transfereeEnable ? "readPretty" : "editable";
                    item.rules = transfereeEnable && [{ required: true }];
                    if ([BussinessType.SUBSEQUENT_TAX].includes(detail?.inveBusinessType)) {
                        item.pattern = "readPretty";
                        item.rules = [];
                    }
                    break;
                //类目
                case "category":
                    //退货入区和销毁：显示字段，不赋值，不可编辑 其它：*必填，显示字段，可编辑，输入框
                    transfereeEnable = [
                        BussinessType.BUSSINESS_DESTORY,
                        BussinessType.BUSSINESS_REFUND_INAREA,
                        BussinessType.BUSSINESS_EMPTY,
                        BussinessType.INVENTORY_PROFIT,
                        BussinessType.RANDOM_INSPECTION_DECLARATION,
                    ].every(item => item !== detail?.inveBusinessType);
                    item.pattern = !transfereeEnable ? "readPretty" : "editable";
                    item.rules = transfereeEnable && [{ required: true, message: `请选择${item.label}` }];
                    if ([BussinessType.SUBSEQUENT_TAX].includes(detail?.inveBusinessType)) {
                        item.pattern = "readPretty";
                        item.rules = [];
                    }
                    break;
                //转出方
                case "transferor":
                //转入方
                case "transferee":
                    //一线入境、退货入区和销毁：显示字段，不赋值，不可编辑 其它：*必填，显示字段，可编辑，输入框
                    transfereeEnable = [
                        BussinessType.BUSSINESS_ONELINE_IN,
                        BussinessType.BUSSINESS_DESTORY,
                        BussinessType.BUSSINESS_REFUND_INAREA,
                        BussinessType.BUSSINESS_EMPTY,
                        BussinessType.BONDED_ONELINE_IN,
                        BussinessType.INVENTORY_PROFIT,
                        BussinessType.RANDOM_INSPECTION_DECLARATION,
                    ].every(item => item !== detail?.inveBusinessType);
                    item.pattern = !transfereeEnable ? "readPretty" : "editable";
                    item.rules = transfereeEnable ? [{ required: true }] : [{ required: false }];
                    ref.current?.getForm.validateFields(["transferor", "transferee"]);
                    if (
                        [BussinessType.ONLINE_REFUND, BussinessType.SUBSEQUENT_TAX].includes(detail?.inveBusinessType)
                    ) {
                        item.pattern = "readPretty";
                        item.rules = [];
                    }
                    break;
                //关联转入账册
                case "inAccountBook":
                // 关联核注清单编号
                case "associatedEndorsementNo":
                    transfereeEnable = [
                        BussinessType.BUSSINESS_SECTION_OUT,
                        BussinessType.BUSSINESS_SECTIONINNER_OUT,
                    ].some(item => item === detail?.inveBusinessType);
                    item.disabled = !transfereeEnable;
                    item.pattern = !transfereeEnable ? "readPretty" : "editable";
                    break;
                //关联转出账册
                case "outAccountBook":
                    transfereeEnable = [
                        BussinessType.BUSSINESS_SECTION_IN,
                        BussinessType.BUSSINESS_SECTIONINNER_IN,
                    ].some(item => item === detail?.inveBusinessType);
                    item.pattern = !transfereeEnable ? "readPretty" : "editable";
                    break;
                //是否两步申报
                case "twoStepFlag":
                    transfereeEnable =
                        BussinessType.BUSSINESS_ONELINE_IN === detail?.inveBusinessType &&
                        [
                            CustomsClearanceStatus.STATUS_CREATED,
                            CustomsClearanceStatus.STATUS_PERFECT,
                            CustomsClearanceStatus.STATUS_CONFIRMING,
                            CustomsClearanceStatus.STATUS_AUDITED,
                            CustomsClearanceStatus.STATUS_ENDORSEMENT,
                            CustomsClearanceStatus.STATUS_START_STORAGED,
                            CustomsClearanceStatus.STATUS_FAILURE,
                        ].some(item => detail?.status === item);
                    item.disabled = !transfereeEnable;

                    item.pattern = !transfereeEnable ? "readPretty" : "editable";
                    // 包含cw清关标签时，是否两步申报不可编辑
                    if (detail?.orderTagList?.includes(orderTagList.CWLABEL)) {
                        item.disabled = true;
                        item.pattern = "readPretty";
                    }
                    item.rules = transfereeEnable
                        ? [
                              {
                                  required: true,
                                  message: `请选择${item.label}`,
                              },
                          ]
                        : [];
                    break;

                //清关方式
                case "declareWay":
                    transfereeEnable =
                        BussinessType.BUSSINESS_ONELINE_IN === detail?.inveBusinessType && detail?.twoStepFlag;
                    item.disabled = !transfereeEnable;
                    item.pattern = !transfereeEnable ? "readPretty" : "editable";
                    item.rules = transfereeEnable
                        ? [
                              {
                                  required: true,
                                  message: `请选择${item.label}`,
                              },
                          ]
                        : [];
                    break;
                //报关单号
                case "customsEntryNo":
                    transfereeEnable =
                        BussinessType.BUSSINESS_ONELINE_IN === detail?.inveBusinessType && detail?.twoStepFlag;
                    item.disabled = !transfereeEnable;
                    item.pattern = !transfereeEnable ? "readPretty" : "editable";
                    item.rules = transfereeEnable
                        ? [
                              {
                                  required: true,
                              },
                          ]
                        : [];
                    break;
                case "actualArrivalDate":
                    transfereeEnable = BussinessType.BUSSINESS_ONELINE_IN === detail?.inveBusinessType;
                    item.pattern = !transfereeEnable ? "readPretty" : "editable";

                    item.rules = transfereeEnable
                        ? [
                              {
                                  required: true,
                              },
                          ]
                        : [];
                    if (detail?.inveBusinessType === BussinessType.BONDED_ONELINE_IN) {
                        item.pattern = "readPretty";
                        item.rules = [];
                    }
                    break;

                case "finalInveCompanyId":
                case "finalBookId":
                case "finalEntityWarehouseCode":
                case "finalOwnerCode":
                    transfereeEnable = 1 === detail?.transitFlag;
                    item.pattern = !transfereeEnable ? "readPretty" : "editable";
                    item.rules = transfereeEnable
                        ? [
                              {
                                  required: true,
                              },
                          ]
                        : [];
                    break;

                case "corrCusDeclareCompanyId":
                    transfereeEnable =
                        [
                            BussinessType.ONLINE_REFUND,
                            BussinessType.BUSSINESS_ONELINE_IN,
                            BussinessType.BONDED_ONELINE_IN,
                            BussinessType.SUBSEQUENT_TAX,
                        ].includes(detail?.inveBusinessType) ||
                        (BussinessType.BUSSINESS_SECTION_IN === detail?.inveBusinessType &&
                            detail?.transportMode === "S");
                    item.pattern = !transfereeEnable ? "readPretty" : "editable";
                    item.rules = transfereeEnable
                        ? [
                              {
                                  required: true,
                                  message: `请选择${item.label}`,
                              },
                          ]
                        : [];
                    break;
                case "rltCusInnerSFHRCompanyId":
                case "rltCusXFDYCompanyId":
                case "rltCusDeclareCompanyId":
                    transfereeEnable = BussinessType.BONDED_TO_TRADE === detail?.inveBusinessType;
                    item.pattern = !transfereeEnable ? "readPretty" : "editable";
                    item.rules = transfereeEnable
                        ? [
                              {
                                  required: true,
                                  message: `请选择${item.label}`,
                              },
                          ]
                        : [];
                    break;
                default:
                    break;
            }
            return item;
        }

        config.baseInfo.children.map(item => {
            setItem(item);
        });
        config.declareInfo.children.map(item => {
            setItem(item);
        });
        config.extraInfo.children.map(item => {
            setItem(item);
        });
        return config;
    }

    const beforeSubmit = values => {
        values.id = id;
        values.actualArrivalDate = values.actualArrivalDate?.valueOf();
        values.finalInveCompanyId = detail.finalInveCompanyId;
        delete values.expectedToPortTime;
        delete values.expectedOutAreaTime;
        return values;
    };
    const onSinglesSelectChange = desc => {
        let formFieldsValue = ref.current.getFormFieldsValue();

        function mapClearValue(inveBusinessType) {
            if (detailHeaderClearValue.hasOwnProperty(inveBusinessType)) {
                return detailHeaderClearValue[inveBusinessType];
            } else {
                return {};
            }
        }

        let config = ref.current.config;
        switch (desc.name) {
            case "inveBusinessType":
                let inveBusinessType = desc.value.value;
                detail.inveBusinessTypeDesc = desc.value.children;
                detail.inveBusinessType = inveBusinessType;
                let customsFlagDesc, inOrOutFlag, customsType;
                if (
                    [
                        BussinessType.BUSSINESS_ONELINE_IN,
                        BussinessType.ONLINE_REFUND,
                        BussinessType.BONDED_TO_TRADE,
                        BussinessType.BONDED_ONELINE_IN,
                        BussinessType.SUBSEQUENT_TAX,
                    ].includes(inveBusinessType)
                ) {
                    customsFlagDesc = "报关";
                } else {
                    customsFlagDesc = "非报关";
                }
                // 特殊综合保税区逻辑
                if (
                    formFieldsValue.inveBusinessType === BussinessType.BUSSINESS_SECTION_IN &&
                    formFieldsValue.transportMode === "S"
                ) {
                    customsFlagDesc = "报关";
                    customsType = 2;
                }
                if (
                    [
                        BussinessType.BUSSINESS_ONELINE_IN,
                        BussinessType.BUSSINESS_REFUND_INAREA,
                        BussinessType.BUSSINESS_SECTION_IN,
                        BussinessType.BUSSINESS_SECTIONINNER_IN,
                        BussinessType.BONDED_ONELINE_IN,
                        BussinessType.INVENTORY_PROFIT,
                    ].some(item => inveBusinessType === item)
                ) {
                    inOrOutFlag = "入区";
                } else {
                    inOrOutFlag = "出区";
                }

                // 非一线退运，保税物流转大贸
                let commonClear = {};
                if (![BussinessType.BONDED_TO_TRADE, BussinessType.ONLINE_REFUND].includes(inveBusinessType)) {
                    commonClear = {
                        corrCusDeclareCompanyId: null,
                        corrCusDeclareCompanyUSCC: null,
                        corrCusDeclareCompanyCode: null,
                        rltCusInnerSFHRCompanyId: null,
                        rltCusInnerSFHRCompanyUSCC: null,
                        rltCusInnerSFHRCompanyCode: null,
                        rltCusXFDYCompanyId: null,
                        rltCusXFDYCompanyUSCC: null,
                        rltCusXFDYCompanyCode: null,
                        rltCusDeclareCompanyId: null,
                        rltCusDeclareCompanyUSCC: null,
                        rltCusDeclareCompanyCode: null,
                        customsTypeDesc: null,
                        customsType: null,
                    };
                    ref.current.setMergeDetail(commonClear);
                }

                //置空
                let clearValue = mapClearValue(inveBusinessType);
                ref.current.setMergeDetail({
                    ...commonClear,
                    ...clearValue,
                    customsFlagDesc: customsFlagDesc || clearValue.customsFlagDesc,
                    customsType: customsType || clearValue.customsType || commonClear.customsType,
                    inOrOutFlag: inOrOutFlag,
                });
                ref.current.changeConfig(
                    getBusinessDetailHeaderConfig(Object.assign(detail, clearValue, formFieldsValue)),
                );
                const newDetail = {
                    ...detail,
                    ...commonClear,
                    ...clearValue,
                    inOrOutFlag: inOrOutFlag,
                };
                setDetail(newDetail);
                break;
            case "twoStepFlag":
                ref.current.changeConfig(getBusinessDetailHeaderConfig(Object.assign(detail, formFieldsValue)));
                ref.current.setMergeDetail({ declareWay: desc.value.value });
                break;

            case "finalBookId":
                let customsBookId = desc.value.value;
                config.baseInfo.children.map(item => {
                    if (item.name === "finalEntityWarehouseCode") {
                        ref.current.initSelect(item, { customsBookId: customsBookId });
                    }
                });
                ref.current.setMergeDetail({ finalEntityWarehouseCode: "", finalOwnerCode: "" });

                break;
            case "finalEntityWarehouseCode":
                let entityWarehouseCode = desc.value.value;
                let entityWarehouseName = desc.value.children;
                config.baseInfo.children.map(item => {
                    if (item.type === "single-select") {
                        if (item.name === "finalOwnerCode") {
                            ref.current.initSelect(item, { entityWarehouseCode: entityWarehouseCode, openStatus: 1 });
                        }
                    }
                });
                ref.current.setMergeDetail({ finalEntityWarehouseName: entityWarehouseName });
                break;
            case "finalOwnerCode":
                let finalOwnerName = desc.value.children;
                ref.current.setMergeDetail({ finalOwnerName: finalOwnerName });
                break;
            case "rltCusInnerSFHRCompanyId":
                const rltCusInnerSFHRCompany = desc.value.data;
                ref.current.setMergeDetail({
                    rltCusInnerSFHRCompanyCode: rltCusInnerSFHRCompany?.code,
                    rltCusInnerSFHRCompanyUSCC: rltCusInnerSFHRCompany?.uniformSocialCreditCode,
                });
                break;
            case "corrCusDeclareCompanyId":
                const corrCusDeclareCompany = desc.value.data;
                ref.current.setMergeDetail({
                    corrCusDeclareCompanyCode: corrCusDeclareCompany?.code,
                    corrCusDeclareCompanyUSCC: corrCusDeclareCompany?.uniformSocialCreditCode,
                });
                break;
            case "rltCusXFDYCompanyId":
                const rltCusXFDYCompany = desc.value.data;
                ref.current.setMergeDetail({
                    rltCusXFDYCompanyCode: rltCusXFDYCompany?.code,
                    rltCusXFDYCompanyUSCC: rltCusXFDYCompany?.uniformSocialCreditCode,
                });
                break;
            case "rltCusDeclareCompanyId":
                const rltCusDeclareCompany = desc.value.data;
                ref.current.setMergeDetail({
                    rltCusDeclareCompanyCode: rltCusDeclareCompany?.code,
                    rltCusDeclareCompanyUSCC: rltCusDeclareCompany?.uniformSocialCreditCode,
                });
                break;
            case "transportMode":
                //特殊综合保税区逻辑
                let objValue = {};
                if (
                    formFieldsValue.inveBusinessType === BussinessType.BUSSINESS_SECTION_IN &&
                    formFieldsValue.transportMode === "S"
                ) {
                    objValue = {
                        customsFlagDesc: "报关",
                        customsType: 2,
                    };
                }
                ref.current.setMergeDetail(objValue);
                ref.current.changeConfig(
                    getBusinessDetailHeaderConfig(Object.assign(detail, objValue, formFieldsValue)),
                );
                break;
            default:
                break;
        }
    };

    function editOrSave() {
        if (disableEdit) {
            setDisableEdit(false);
        } else {
            ref.current.getForm
                .validateFields()
                .then(res => {
                    if (res.inAccountBook && detail.pledgeOwnerFlag && detail.mailStatus === 1) {
                        return Modal.confirm({
                            title: "提示",
                            content: "关联转入账册已填写，请确认是否发送邮件至审核方？",
                            okText: "确定",
                            cancelText: "取消",
                            onOk: () => {
                                ref.current.submitForm();
                            },
                            onCancel: () => {},
                        });
                    }
                    ref.current.submitForm();
                })
                .catch(() => {});
        }
    }

    /**
     * 刷新通知
     */
    function refreshNotification() {
        let refresh_event = lib.getParam("refresh_event");
        if (refresh_event) {
            event.emit(refresh_event, true);
        }
    }

    return (
        <div>
            {detail && (
                <>
                    {topAlert && (
                        <Alert
                            className={"top-alert"}
                            message={topAlert.message}
                            description={topAlert.description}
                            type={topAlert.type}
                            icon={topAlert.icon}
                            showIcon
                            action={topAlert.renderTopAction}
                        />
                    )}
                    {[
                        CustomsClearanceStatus.STATUS_CREATED,
                        CustomsClearanceStatus.STATUS_PERFECT,
                        CustomsClearanceStatus.STATUS_CONFIRMING,
                        CustomsClearanceStatus.STATUS_AUDITED,
                        CustomsClearanceStatus.STATUS_FAILURE,
                        CustomsClearanceStatus.STATUS_ENDORSEMENT,
                        CustomsClearanceStatus.STATUS_START_STORAGING,
                        CustomsClearanceStatus.STATUS_START_STORAGED,
                    ].some(item => item === detail.status) && (
                        <Row justify={"end"} style={{ marginTop: "10px", marginRight: "15px" }}>
                            <Button
                                onClick={() => {
                                    editOrSave();
                                }}>
                                {disableEdit ? "编辑" : "保存"}
                            </Button>
                        </Row>
                    )}
                    <ConfigFormCenter
                        ref={ref}
                        formProps={{
                            layout: "horizontal",
                            formItemLayout: { labelCol: { span: 10 }, wrapperCol: { span: 16 } },
                        }}
                        confData={configData}
                        disableEdit={disableEdit}
                        onConfigLoadSuccess={onConfigLoadSuccess}
                        onSinglesSelectChange={onSinglesSelectChange}
                        beforeSetDetail={beforeSetDetail}
                        submitUrl={"/ccs/invenorder/editInventoryHead"}
                        beforeSubmit={beforeSubmit}
                        onSubmitSuccess={onSubmitSuccess}
                    />
                    <DocumentAttachment />
                    {detail && (
                        <AuditFc
                            detail={detail}
                            visible={visible}
                            closeModal={success => {
                                if (success) {
                                    refreshNotification();
                                    fetchDetail({ activeKey: "0", id: lib.getParam("id") });
                                }
                                setVisible(false);
                            }}
                        />
                    )}
                </>
            )}
        </div>
    );
}
