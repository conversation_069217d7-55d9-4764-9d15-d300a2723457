import React from "react";
import { Form, Switch } from "antd";
export const dangerousFlagKeys = {
    0: "否",
    1: "是",
};

export const dangerousFlagKeysList = Object.keys(dangerousFlagKeys).map(item => {
    return { name: dangerousFlagKeys[item], value: item };
});

export function detailHeaderConfig() {
    return {
        baseInfo: {
            children: [
                {
                    label: "清关单号",
                    editEnable: true,
                    name: "inveCustomsSn",
                    type: "text",
                },
                {
                    label: "主清关单号",
                    editEnable: true,
                    name: "masterOrderSn",
                    type: "text",
                    hidden: true,
                },
                {
                    label: "子清关单号",
                    editEnable: true,
                    name: "subOrderSn",
                    type: "text",
                    hidden: true,
                },
                {
                    label: "进出标志",
                    editEnable: false,
                    name: "inOrOutFlag",
                    type: "text",
                },
                {
                    label: "清关企业",
                    editEnable: false,
                    name: "inveCompanyName",
                    type: "text",
                },
                {
                    label: "区内账册编号",
                    editEnable: true,
                    type: "text",
                    name: "areaBookNo",
                },
                {
                    label: "创建时间",
                    editEnable: true,
                    type: "text",
                    name: "createTime",
                },
                {
                    label: "关联调入清关单号",
                    editEnable: true,
                    type: "text",
                    name: "associatedInOrderSn",
                },
                {
                    label: "关联调出清关单号",
                    editEnable: true,
                    type: "text",
                    name: "associatedOutOrderSn",
                },
                {
                    label: "起点仓货主",
                    editEnable: false,
                    name: "outsetOwnerName",
                    type: "text",
                },

                {
                    label: "起点仓",
                    editEnable: false,
                    name: "outsetEntityWarehouseName",
                    type: "text",
                },
                {
                    label: "目的仓货主",
                    editEnable: false,
                    name: "destinationOwnerName",
                    type: "text",
                },

                {
                    label: "目的仓",
                    editEnable: false,
                    name: "destinationEntityWarehouseName",
                    type: "text",
                },

                {
                    label: "货主是否自备车辆",
                    editEnable: false,
                    name: "selfOwnedVehicle",
                    list: [
                        { id: 1, name: "是" },
                        { id: 0, name: "否" },
                    ],
                    type: "single-select",
                },
                {
                    label: "车牌号",
                    editEnable: false,
                    name: "licensePlate",
                    type: "textInput",
                },
                {
                    label: "车辆费用备注",
                    editEnable: false,
                    name: "vehicleCostRemark",
                    type: "textInput",
                },
                {
                    label: "托数",
                    editEnable: true,
                    name: "palletsNums",
                    type: "text",
                },
                {
                    label: "预计到港日期",
                    editEnable: false,
                    name: "expectedToPortTime",
                    type: "text",
                },
                {
                    label: "预计出区日期",
                    editEnable: false,
                    name: "expectedOutAreaTime",
                    type: "text",
                },
                {
                    label: "是否为中转账册",
                    editEnable: false,
                    name: "transitFlagDesc",
                    type: "text",
                },
                {
                    label: "清关企业(终点)",
                    editEnable: false,
                    name: "finalInveCompanyName",
                    type: "text",
                },
                {
                    label: "账册编号(终点)",
                    editEnable: false,
                    name: "finalBookId",
                    type: "single-select",
                    from: "/ccs/customsBook/effective/listBookNoByAccBookAuth",
                },
                {
                    label: "终点仓",
                    editEnable: false,
                    name: "finalEntityWarehouseCode",
                    type: "single-select",
                    from: "/ccs/entityWarehouse/listEntityByCustomsBook",
                },

                {
                    editEnable: false,
                    name: "finalEntityWarehouseName",
                    type: "text",
                    customConfig: { style: { display: "none" } },
                },
                {
                    label: "终点仓货主",
                    editEnable: false,
                    name: "finalOwnerCode",
                    type: "single-select",
                    from: "/ares-admin/owner/listV2",
                    customConfig: { valueKey: "ownerName", idKey: "ownerCode" },
                },
                {
                    editEnable: false,
                    name: "finalOwnerName",
                    type: "text",
                    customConfig: { style: { display: "none" } },
                },
                {
                    label: "备注",
                    name: "remark",
                    editEnable: false,
                    type: "textInput",
                },
                {
                    label: "分区结转单号",
                    name: "carryOverNo",
                    editEnable: true,
                    type: "text",
                },
            ],
            label: "基本信息",
            name: "baseInfo",
            isGroup: true,
            className: "baseInfo",
        },
        declareInfo: {
            children: [
                {
                    label: "核注企业单号",
                    editEnable: true,
                    type: "text",
                    name: "endorsementSn",
                },

                {
                    label: "预录入核注编号",
                    editEnable: true,
                    type: "text",
                    name: "preNo",
                },
                {
                    label: "核注清单编号",
                    name: "refHzInveNo",
                    editEnable: true,
                    type: "text",
                },
                {
                    label: "业务类型",
                    name: "inveBusinessType",
                    editEnable: false,
                    type: "single-select",
                    from: "/ccs/invenorder/listBusinessTypeById",
                    rules: [
                        {
                            required: true,
                            message: "请选择业务类型",
                        },
                    ],
                },
                {
                    label: "运输方式",
                    editEnable: false,
                    name: "transportMode",
                    type: "single-select",
                    // from: "/ccs/invenorder/listTransportV2",
                    from: "/ccs/dictionary/listTransportMode",
                    customConfig: { together: true },
                },
                {
                    label: "启运/运抵国",
                    editEnable: false,
                    name: "shipmentCountry",
                    type: "single-select",
                    from: "/ccs/customs/listCountry",
                    customConfig: { showSearch: true },
                    rules: [
                        {
                            required: true,
                            message: "请选择启运/运抵国",
                        },
                    ],
                },
                {
                    label: "进出境关别",
                    editEnable: false,
                    name: "entryExitCustoms",
                    type: "textInput",
                    rules: [
                        {
                            validator: (_, value) => {
                                if (value && !RegExp("^[0-9]{1,4}$").test(value)) {
                                    return Promise.reject(new Error("请输入大于0的整数,且长度不允许超过4位"));
                                }
                                return Promise.resolve();
                            },
                        },
                        {
                            required: true,
                        },
                    ],
                },
                {
                    label: "是否两步申报",
                    editEnable: false,
                    name: "twoStepFlag",
                    type: "single-select",
                    list: [
                        { id: 1, name: "是" },
                        { id: 0, name: "否" },
                    ],
                },
                {
                    label: "清关方式",
                    editEnable: false,
                    name: "declareWay",
                    type: "single-select",
                    list: [
                        { id: 0, name: "先报后理" },
                        { id: 1, name: "先理后报" },
                    ],
                },
                {
                    label: "报关单号",
                    editEnable: false,
                    name: "customsEntryNo",
                    type: "textInput",

                    rules: [
                        {
                            max: 18,
                            message: "长度不允许超过18位",
                        },
                        {
                            required: true,
                        },
                    ],
                },
                {
                    label: "报关标志",
                    editEnable: false,
                    name: "customsFlagDesc",
                    type: "text",
                },
                {
                    label: "是否生成报关单",
                    editEnable: false,
                    name: "declarationFlag",
                    type: "single-select",
                    list: [
                        { id: 1, name: "是" },
                        { id: 0, name: "否" },
                    ],
                    rules: [
                        {
                            required: true,
                        },
                    ],
                },
                {
                    label: "报关单类型",
                    editEnable: false,
                    name: "customsEntryType",
                    type: "single-select",
                    from: "/ccs/customs/listEntryType",
                    rules: [
                        {
                            required: true,
                            message: "请选择报关单类型!",
                        },
                    ],
                },
                {
                    label: "报关类型",
                    editEnable: false,
                    name: "customsType",
                    type: "single-select",
                    disabled: true,
                    list: [
                        {
                            name: "关联报关",
                            id: 1,
                        },
                        {
                            name: "对应报关",
                            id: 2,
                        },
                    ],
                },
                {
                    label: "对应报关单申报单位名称",
                    editEnable: false,
                    name: "corrCusDeclareCompanyId",
                    type: "single-select",
                    from: "/ccs/company/listInfoWithBGQY",
                    showSearch: true,
                },
                {
                    label: "对应报关单申报单位USCC",
                    editEnable: false,
                    name: "corrCusDeclareCompanyUSCC",
                    type: "text",
                },
                {
                    label: "对应报关单申报单位编码",
                    editEnable: false,
                    name: "corrCusDeclareCompanyCode",
                    type: "text",
                },

                // {
                //     label: "报关企业",
                //     editEnable: false,
                //     name: "customsEntryCompany",
                //     type: "single-select",
                //     from: "/ccs/customs/listEntryCompany",
                //     rules: [
                //         {
                //             required: true,
                //             message: "请选择报关企业!"
                //         }
                //     ]
                // },
                {
                    label: "关联转入账册",
                    editEnable: false,
                    name: "inAccountBook",
                    type: "textInput",
                    rules: [
                        {
                            validator: (_, value) => {
                                if (!value) return Promise.resolve();
                                if (value && !RegExp("^[a-zA-Z]{1}[0-9]{4}[a-zA-Z]{1}[a-zA-Z0-9]{6}$").test(value)) {
                                    return Promise.reject(new Error("请输入合法的账册编号"));
                                }
                                return Promise.resolve();
                            },
                        },
                    ],
                },
                {
                    label: "关联转出账册",
                    name: "outAccountBook",
                    editEnable: false,
                    type: "textInput",
                },
                {
                    label: "关联核注清单编号",
                    name: "associatedEndorsementNo",
                    editEnable: false,
                    type: "textInput",
                },
                {
                    label: "关联报关单境内收发货人名称",
                    editEnable: false,
                    name: "rltCusInnerSFHRCompanyId",
                    type: "single-select",
                    from: "/ccs/company/listInfoWithSFHR",
                    showSearch: true,
                },
                {
                    label: "关联报关单境内收发货人USCC",
                    editEnable: false,
                    name: "rltCusInnerSFHRCompanyUSCC",
                    type: "text",
                },
                {
                    label: "关联报关单境内收发货人编码",
                    editEnable: false,
                    name: "rltCusInnerSFHRCompanyCode",
                    type: "text",
                },
                {
                    label: "关联报关单消费使用单位名称",
                    editEnable: false,
                    name: "rltCusXFDYCompanyId",
                    type: "single-select",
                    from: "/ccs/company/listInfoWithXFDW",
                    showSearch: true,
                },
                {
                    label: "关联报关单消费使用单位USCC",
                    editEnable: false,
                    name: "rltCusXFDYCompanyUSCC",
                    type: "text",
                },
                {
                    label: "关联报关单消费使用单位编码",
                    editEnable: false,
                    name: "rltCusXFDYCompanyCode",
                    type: "text",
                },
                {
                    label: "关联报关单申报单位名称",
                    editEnable: false,
                    name: "rltCusDeclareCompanyId",
                    type: "single-select",
                    from: "/ccs/company/listInfoWithBGQY",
                    showSearch: true,
                },
                {
                    label: "关联报关单申报单位USCC",
                    editEnable: false,
                    name: "rltCusDeclareCompanyUSCC",
                    type: "text",
                },
                {
                    label: "关联报关单申报单位编码",
                    editEnable: false,
                    name: "rltCusDeclareCompanyCode",
                    type: "text",
                },
            ],
            label: "通关填写信息",
            name: "baseInfo",
            isGroup: true,
            className: "declareInfo",
        },
        extraInfo: {
            children: [
                {
                    label: "提单号",
                    editEnable: false,
                    type: "textInput",
                    name: "pickUpNo",
                },
                {
                    label: "货代公司",
                    editEnable: false,
                    type: "single-select",
                    from: "/ccs/dictionary/listFreightForwardingCompany",
                    name: "forwardingCompany",
                },
                {
                    label: "集装箱号",
                    editEnable: false,
                    type: "textInput",
                    name: "conNo",
                },
                {
                    label: "进境口岸",
                    editEnable: false,
                    type: "textInput",
                    name: "entryPort",
                },
                {
                    label: "起运港/始发机场",
                    editEnable: false,
                    type: "textInput",
                    name: "fromLocation",
                },
                {
                    label: "到货港口/机场",
                    editEnable: false,
                    type: "single-select",
                    from: "/ccs/dictionary/listArrivePort",
                    name: "arrivalPort",
                },
                {
                    label: "品名",
                    editEnable: false,
                    type: "textInput",
                    name: "productName",
                },
                {
                    label: "类目",
                    editEnable: false,
                    type: "single-select",
                    from: "/ccs/invenorder/listCategory",
                    name: "category",
                },
                {
                    label: "转出方",
                    editEnable: false,
                    type: "textInput",
                    name: "transferor",
                },
                {
                    label: "转入方",
                    editEnable: false,
                    type: "textInput",
                    name: "transferee",
                },
                {
                    label: "实际到港日期",
                    editEnable: false,
                    type: "dateInput",
                    name: "actualArrivalDate",
                },
            ],
            label: "其他填写信息",
            name: "extraInfo",
            isGroup: true,
            className: "baseInfo",
        },
    };
}

export function detailBodyModalConfig(prop) {
    return {
        baseInfo: {
            children: [
                {
                    label: "是否新品",
                    editEnable: true,
                    name: "oldOrNew",

                    labelCol: { span: 10 },
                    type: "single-select",
                    list: [
                        { name: "是", id: "new" },
                        { name: "否", id: "old" },
                    ],
                },
                {
                    label: "商品料号",
                    editEnable: false,

                    labelCol: { span: 10 },
                    name: "productId",
                    type: "single-select",
                    from: "/ccs/invenorder/listProductId",
                },
                {
                    label: "备案序号",
                    editEnable: true,

                    labelCol: { span: 10 },
                    type: "single-select",
                    name: "goodsSeqNo",
                    from: "/ccs/invenorder/listSeqNoByProductIdV2",
                },
                {
                    label: "商品sku",
                    editEnable: false,
                    name: "skuId",

                    labelCol: { span: 10 },
                    type: "text",
                },
                {
                    label: "商品名称",

                    labelCol: { span: 10 },
                    editEnable: false,
                    name: "goodsName",
                    type: "textInput",
                },
                {
                    label: "商品编码",

                    labelCol: { span: 10 },
                    wrapperCol: { span: 14 },
                    editEnable: true,
                    name: "hsCode",
                    type: "single-select",
                    from: "/ccs/customs/listHsV2",
                    customConfig: { together: true, showSearch: true },
                },
                {
                    label: "规格型号",

                    labelCol: { span: 10 },
                    editEnable: false,
                    name: "goodsModel",
                    type: "textInput",
                    rules: [
                        {
                            required: true,
                            message: "请输入规格型号!",
                        },
                    ],
                },
                {
                    label: "申报计量单位",

                    labelCol: { span: 10 },
                    name: "unitDesc",
                    editEnable: true,
                    type: "text",
                },
                {
                    label: "申报数量",

                    labelCol: { span: 10 },
                    name: "declareUnitQfy",
                    editEnable: true,
                    type: "number",
                    rules: [
                        {
                            required: true,
                        },
                        {
                            pattern: RegExp("^[1-9]\\d*$"),
                            message: "请输入大于0的整数",
                        },
                    ],
                },
                {
                    label: "法定计量单位",

                    labelCol: { span: 10 },
                    name: "firstUnit",
                    disabled: true,
                    from: "/ccs/customs/listUom",
                    type: "single-select",
                },
                {
                    label: "法定数量（单）",

                    labelCol: { span: 10 },
                    name: "firstUnitQfy",
                    editEnable: true,
                    type: "number",
                    rules: [
                        {
                            required: true,
                        },
                        {
                            pattern: RegExp("^[1-9]\\d*$|^[1-9]\\d*\\.\\d*|0\\.\\d*[1-9]\\d*$"),
                            message: "请输入大于0的整数",
                        },
                    ],
                },
                {
                    label: "法定数量（总）",

                    labelCol: { span: 10 },
                    name: "firstUnitQfys",
                    editEnable: true,
                    type: "text",
                },
                {
                    label: "第二计量单位",

                    labelCol: { span: 10 },
                    name: "secondUnit",
                    from: "/ccs/customs/listUom",
                    disabled: true,
                    type: "single-select",
                },
                {
                    label: "法定第二数量(单)",

                    labelCol: { span: 10 },
                    name: "secondUnitQfy",
                    editEnable: true,
                    type: "number",
                },
                {
                    label: "法定第二数量(总)",

                    labelCol: { span: 10 },
                    name: "secondUnitQfys",
                    editEnable: true,
                    type: "text",
                },
                {
                    label: "申报单价",

                    labelCol: { span: 10 },
                    name: "declarePrice",
                    editEnable: true,
                    type: "number",
                    rules: [
                        {
                            required: true,
                        },
                        {
                            pattern: RegExp("^[1-9]\\d*$|^[1-9]\\d*\\.\\d*|0\\.\\d*[1-9]\\d*$"),
                            message: "请输入大于0的数",
                        },
                    ],
                },
                {
                    label: "申报总价",

                    labelCol: { span: 10 },
                    name: "declareTotalPrice",
                    editEnable: true,
                    type: "text",
                },
                {
                    label: "原产国(地区)",

                    labelCol: { span: 10 },
                    name: "originCountry",
                    editEnable: true,
                    type: "single-select",
                    list: prop.countryList,
                    customConfig: { together: true, showSearch: true },
                    rules: [
                        {
                            required: true,
                            message: "请选择原产国",
                        },
                    ],
                },
                {
                    label: "币制",

                    labelCol: { span: 10 },
                    name: "currency",
                    editEnable: true,
                    type: "single-select",
                    list: prop.currencyList,
                    customConfig: { together: true, showSearch: true },
                    rules: [
                        {
                            required: true,
                            message: "请选择币制",
                        },
                    ],
                },
                {
                    label: "净重（kg）",

                    labelCol: { span: 10 },
                    name: "netweights",
                    editEnable: true,
                    type: "number",
                    rules: [
                        {
                            required: true,
                        },
                        {
                            pattern: RegExp("^[1-9]\\d*$|^[1-9]\\d*\\.\\d*|0\\.\\d*[1-9]\\d*$"),
                            message: "请输入大于0的数",
                        },
                    ],
                },
                {
                    label: "毛重（kg）",

                    labelCol: { span: 10 },
                    name: "grossWeight",
                    editEnable: true,
                    type: "number",
                    rules: [
                        {
                            pattern: RegExp("^[1-9]\\d*$|^[1-9]\\d*\\.\\d*|0\\.\\d*[1-9]\\d*$"),
                            message: "请输入大于0的数",
                        },
                    ],
                },
                {
                    label: "最终目的国(地区)",

                    labelCol: { span: 10 },
                    name: "destinationCountry",
                    editEnable: true,
                    disabled: true,
                    type: "single-select",
                    customConfig: { showSearch: true },
                    list: prop.countryList,
                },
                {
                    label: "商品条码",

                    labelCol: { span: 10 },
                    name: "goodsBar",
                    editEnable: true,
                    type: "text",
                },
                {
                    label: "征免方式",

                    labelCol: { span: 10 },
                    name: "avoidTaxMethod",
                    editEnable: false,
                    type: "text",
                },
                {
                    label: "危化品标志",
                    labelCol: { span: 10 },
                    name: "dangerousFlag",
                    type: "single-select",
                    editEnable: false,
                    list: dangerousFlagKeysList,
                },
            ],
            label: "",
            name: "baseInfo",
            isGroup: true,
            className: "detail-body-modal",
        },
    };
}

export const derailBodyInfoConfig = {
    baseInfo: {
        children: [
            {
                label: "行号",
                editEnable: true,
                name: "currentIndex",
                type: "text",
            },
            {
                label: "是否新品",
                editEnable: false,
                name: "oldOrNew",
                // type: "text",
                disabled: true,
                type: "single-select",
                list: [
                    { name: "是", id: "new" },
                    { name: "否", id: "old" },
                ],
            },
            {
                label: "商品SKU",
                editEnable: false,
                name: "skuId",
                type: "text",
            },
            {
                label: "统一料号",
                editEnable: false,
                name: "originProductId",
                type: "text",
            },
            {
                label: "海关备案料号",
                editEnable: true,
                type: "text",
                name: "productId",
            },
            {
                label: "备案序号",
                editEnable: true,
                type: "text",
                name: "goodsSeqNo",
            },
            {
                label: "商品名称",
                editEnable: true,
                type: "text",
                name: "goodsName",
            },
            {
                label: "商品编码",
                editEnable: true,
                type: "text",
                name: "hsCode",
            },
            {
                label: "规格型号",
                editEnable: false,
                name: "goodsModel",
                type: "text",
            },

            {
                label: "申报计量单位",
                editEnable: false,
                name: "unitDesc",
                type: "text",
            },
            {
                label: "最终申报数量",
                editEnable: false,
                name: "declareUnitQfy",
                type: "text",
            },

            {
                label: "币制",
                editEnable: false,
                name: "currencyDesc",
                type: "text",
            },

            {
                label: "法定计量单位",
                editEnable: false,
                name: "firstUnitDesc",
                type: "text",
            },
            {
                label: "法定数量(单)",
                editEnable: false,
                name: "firstUnitQfy",
                type: "text",
            },
            {
                label: "法定数量(总)",
                editEnable: false,
                name: "firstUnitQfys",
                type: "text",
            },
            {
                label: "法定第二计量单位",
                editEnable: true,
                name: "secondUnitDesc",
                type: "text",
            },
            {
                label: "第二法定数量(单)",
                editEnable: false,
                name: "secondUnitQfy",
                type: "text",
            },
            {
                label: "第二法定数量(总)",
                editEnable: false,
                name: "secondUnitQfys",
                type: "text",
            },
            {
                label: "申报单价",
                editEnable: false,
                name: "declarePrice",
                type: "text",
            },
            {
                label: "申报总价",
                editEnable: false,
                name: "declareTotalPrice",
                type: "text",
            },
            {
                label: "原产国(地区)",
                editEnable: false,
                name: "originCountryDesc",
                type: "text",
            },
            {
                label: "毛重(kg)",
                editEnable: false,
                name: "grossWeight",
                type: "text",
            },
            {
                label: "净重(kg)",
                editEnable: false,
                name: "netweights",
                type: "text",
            },
            {
                label: "商品条码",
                name: "goodsBar",
                editEnable: true,
                type: "text",
            },
            {
                label: "征免方式",
                name: "avoidTaxMethod",
                editEnable: true,
                type: "text",
            },
            {
                label: "最终目的国(地区)",
                name: "destinationCountryDesc",
                editEnable: true,
                type: "text",
            },
            {
                label: "危化品标志",
                name: "dangerousFlag",
                editEnable: true,
                type: "renderContainer",
                render: (text, record, value, name, formId) => {
                    if (value == 0) {
                        return "否";
                    } else if (value == 1) {
                        return "是";
                    } else {
                        return null;
                    }
                },
            },
        ],
        label: "",
        name: "baseInfo",
        isGroup: true,
        // className: "baseInfo",
    },
};

export const orderTagList = {
    CWLABEL: 1024, // cw标记
};
