.detail-body-modal {
    .ant-form-item {
        width: 33% !important;
        margin-bottom: 20px !important;
    }
}
.customs-clearance-manage-tabs {
    .ant-tabs {
        height: fit-content;
    }
    .ant-tabs-tab {
        margin: 0 !important;
    }
}
.customs-clearance-detail2 {
    .ant-form-item-control-input-content {
        align-items: center;
        justify-content: flex-start;
        display: flex;
    }
    .ant-form-item-control-input-content span {
        // width: 100%;
        overflow: hidden;
        display: inline-block;
        word-break: break-all;
    }
    .content-form-main .group-component .ant-form-item {
        width: 500px;
    }
    .top-alert {
        min-height: 64px;

        .ant-alert-icon {
            font-size: 32px !important;
        }

        .ant-alert-message {
            font-size: 16px !important;
        }

        .ant-alert-description {
            color: rgba(0, 0, 0, 0.45);
        }
    }
    .ant-alert-info,
    .ant-alert-warning,
    .ant-alert-error {
        background: #fff;
        border: unset;
    }
    .tallyDetail {
        display: flex;
        align-items: center;
        .title {
            margin-right: 15px;
            font-weight: bold;
            font-size: 16px;
            line-height: 1.5715;
        }
    }
}
.text-line-overflow {
    white-space: break-spaces;

    .clamp-text {
        line-height: 24px;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-box-orient: vertical;
    }
}
