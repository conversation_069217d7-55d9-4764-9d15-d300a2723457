import React, { useEffect, useState } from "react";
import {
    Button,
    Modal,
    message,
    Icon,
    Upload,
    Space,
    Table,
    Input,
    Form,
    Tag,
    DatePicker,
    Tabs,
    Tooltip,
    Select,
    Dropdown,
    Menu,
} from "antd";
import { textToRGB } from "@/common/utils";
import { SearchList, lib, getConfigDataUtils, event, UploadFile, HOC } from "react-single-app";
{
    /* <ThunderboltFilled /> */
}
import NewModal from "../../components/NewModal";
import "../shop-good.less";
import { getPrivileges } from "../../common/auth";
import axios from "axios";
import "./customs-clearance-manage.less";
import { BussinessType, CustomsClearanceStatus } from "./customs-clearance-detail2";
import UpdateStatusModal from "../customs-clearance-system/components/update-status-modal";
import moment from "moment";
import { DTEditModal } from "@dt/components";
import AssociatedTransfer from "./componets/associated-transfer";
import { DownOutlined, ThunderboltFilled } from "@ant-design/icons";
import { tagToColor } from "../cross_border_entrance/customs-clearance-detail/enum";
import SortModal from "./sort-modal";
const { TextArea } = Input;
const FormItem = Form.Item;
const { TabPane } = Tabs;

export const MAILSTATUS = {
    EMPTY: 0, //空
    WAIT_SEND: 1, //待发送
    WAIT_REPLY: 2, //待回复
    PASS: 3, //通过
    REJECT: 4, //驳回
};

@HOC.mapAuthButtonsToState({ buttonCodeArr: ["exportAuth"] })
class App extends SearchList {
    constructor(props) {
        super(props);
        this.state.combineVisible = false;
        this.state.tableFieldList = [
            {
                title: "预录入核注清单编号",
                key: "preCheckOrderNo",
                type: "text",
                textAlign: "left",
                minWidth: "",
                divisor: "1",
            },
            {
                title: "核注单号",
                key: "checkOrderNo",
                type: "text",
                textAlign: "left",
                minWidth: "",
                divisor: "1",
            },
            {
                title: "核注清单状态",
                key: "checkStatus",
                type: "text",
                textAlign: "left",
                minWidth: "",
                divisor: "1",
            },
        ];
        this.state.entityWarehouseCode = ""; //仓库code
        this.state.entityWarehouseName = ""; // 仓库name
        this.state.ownerCode = ""; // 货主code
        this.state.ownerName = ""; //货主name
        this.changeConfigList = this.changeConfigList.bind(this);
        this.defaultConfig = [
            {
                //0
                type: "SELECT",
                labelName: "业务类型",
                labelKey: "inveBusinessType",
                required: true,
                message: "请选择业务类型",
                list: [],
                ccs: "/ccs/invenorder/listSelfBusinessType",
                onChange: e => {
                    this.state.configList[1].labelName = "清关企业";
                    this.state.configList[2].labelName = "账册编号";
                    this.state.configList[16].required = [
                        // "ONELINE_IN",
                        "SECTION_OUT",
                        "SECTIONINNER_OUT",
                        "SECTION_IN",
                        "SECTIONINNER_IN",
                    ].includes(e);
                    this.state.configList[17].required = ["ONELINE_IN"].includes(e);
                    switch (e) {
                        case BussinessType.BUSSINESS_REFUND_INAREA:
                            this.changeConfigList([]);
                            break;
                        case BussinessType.BUSSINESS_ONELINE_IN:
                            this.changeConfigList([3, 13, 14, 15, 16, 17]);
                            break;
                        case BussinessType.BUSSINESS_SECTION_OUT:
                        case BussinessType.BUSSINESS_SECTIONINNER_OUT:
                            this.changeConfigList([11, 13, 14, 16, 17]);
                            break;
                        case BussinessType.BUSSINESS_SECTION_IN:
                        case BussinessType.BUSSINESS_SECTIONINNER_IN:
                            this.changeConfigList([10, 13, 14, 16, 17]);
                            break;
                        case BussinessType.ONLINE_REFUND:
                            this.changeConfigList([9, 13, 14, 15, 16, 17]);
                            break;
                        case BussinessType.BONDED_TO_TRADE:
                            this.changeConfigList([9, 13, 14, 16, 17]);
                            break;
                        case BussinessType.SUBSEQUENT_TAX:
                            this.changeConfigList([9, 13, 14, 16, 17]);
                            break;
                        case BussinessType.BONDED_ONELINE_IN:
                            this.changeConfigList([13, 14, 15, 16, 17]);
                            break;
                        case BussinessType.INVENTORY_PROFIT:
                            this.changeConfigList([13, 14, 16, 17]);
                            break;
                        case BussinessType.RANDOM_INSPECTION_DECLARATION:
                            this.changeConfigList([13, 14, 16, 17]);
                            break;
                        case BussinessType.SIMPLE_PROCESSING:
                            this.changeConfigList([11, 13, 14, 16, 17]);
                            break;
                        default:
                            this.changeConfigList([]);
                    }
                },
            },
            {
                //1
                type: "SELECT",
                labelName: "清关企业",
                labelKey: "inveCompanyId",
                required: true,
                message: "请选择清关企业",
                list: [],
                isShows: true,
                // ccs: "/ccs/company/listWithSBQY",
                ccs: "/ccs/company/listBookIdWithQNQY",
                onChange: (e, form, selects) => {
                    if (selects[0] && selects[0].bookId) {
                        form.setFieldsValue({
                            bookId: selects[0].bookId,
                        });
                        this.changeBookIdList(selects[0].bookId, "entityWarehouseCode", form);
                    } else {
                        form.setFieldsValue({
                            bookId: "",
                            entityWarehouseCode: "",
                        });
                    }
                    lib.request({
                        url: "/ccs/customsBook/listBookByAreaCompany",
                        data: {
                            areaCompanyId: selects[0].id,
                        },
                        success: data => {
                            const configList = [...this.state.configList];
                            if (configList[2].labelKey === "bookId") {
                                configList[2].list = data;
                            }
                            this.setState({
                                configList,
                            });
                        },
                    });
                },
            },
            {
                //2
                type: "SELECT",
                labelName: "账册编号",
                labelKey: "bookId",
                required: true,
                message: "请选择账册编号",
                list: [],
                // ccs: "/ccs/customsBook/effective/listBookNoByAccBookAuth",
                onSelect: (e, form) => {
                    this.changeBookIdList(e, "entityWarehouseCode", form);
                    if (e !== this.state.bookId) {
                        form.setFieldsValue({
                            entityWarehouseCode: "",
                            bookId: e,
                        });
                    }
                },
            },
            {
                //3
                type: "SWITCH",
                labelName: "是否标记成中转账册",
                labelKey: "transitFlag",
                required: false,
                hide: true,
                onChange: e => {
                    if (e) {
                        this.state.configList[1].labelName = "清关企业(中转)";
                        this.state.configList[2].labelName = "账册编号(中转)";
                        this.changeConfigList([3, 4, 5, 6, 7, 8, 13, 14, 15]);
                    } else {
                        this.state.configList[1].labelName = "清关企业";
                        this.state.configList[2].labelName = "账册编号";
                        this.changeConfigList([3, 13, 14, 15, 16, 17]);
                    }
                },
            },
            {
                //4
                type: "SELECT",
                labelName: "清关企业(终点)",
                labelKey: "finalInveCompanyId",
                required: true,
                message: "清关企业(终点)",
                hide: true,
                list: [],
                ccs: "/ccs/company/listWithSBQY",
            },
            {
                //5
                type: "SELECT",
                labelName: "账册编号(终点)",
                labelKey: "finalBookId",
                required: true,
                message: "请选择账册编号(终点)",
                hide: true,
                list: [],
                ccs: "/ccs/customsBook/effective/listBookNoByAccBookAuth",
                onSelect: (e, form) => {
                    this.changeBookIdList(e, "finalEntityWarehouseCode");
                    if (e !== this.state.finalBookId) {
                        form.setFieldsValue({
                            finalEntityWarehouseCode: "",
                            finalBookId: e,
                        });
                    }
                },
            },
            {
                //6
                type: "SELECT",
                labelName: "中转目的仓",
                labelKey: "entityWarehouseCode",
                required: false,
                message: "请选择中转目的仓",
                hide: true,
                list: [],
                onSelect: (e, form) => {
                    this.changeOwnerList(e, "ownerCode");
                    this.setState({
                        entityWarehouseName: this.getOwnerHouse("entityWarehouseCode", e),
                    });
                    //
                    if (e !== this.state.entityWarehouseCode) {
                        form.setFieldsValue({
                            ownerCode: "",
                            entityWarehouseCode: e,
                        });
                    }
                },
            },
            {
                //7
                type: "SELECT",
                labelName: "终点仓",
                labelKey: "finalEntityWarehouseCode",
                required: true,
                message: "请选择终点仓",
                hide: true,
                list: [],
                onSelect: (e, form) => {
                    this.changeOwnerList(e, "finalOwnerCode");
                    this.setState({
                        finalEntityWarehouseName: this.getOwnerHouse("finalEntityWarehouseCode", e),
                    });
                    if (e !== this.state.finalEntityWarehouseCode) {
                        form.setFieldsValue({
                            finalOwnerCode: "",
                            finalEntityWarehouseCode: e,
                        });
                    }
                },
            },
            {
                //8
                type: "SELECT",
                labelName: "终点仓货主",
                labelKey: "finalOwnerCode",
                required: true,
                message: "请选择终点仓货主",
                hide: true,
                list: [],
                onChange: e => {
                    // getOwnerHouse
                    this.setState({
                        finalOwnerName: this.getOwnerHouse("finalOwnerCode", e),
                    });
                },
            },
            {
                //3->9
                type: "INPUT",
                labelName: "申请人",
                labelKey: "applyPerson",
                required: true,
                message: "请输入申请人",
            },
            {
                //4->10
                type: "INPUT",
                labelName: "关联转出账册",
                labelKey: "outAccountBook",
                hide: true,
            },
            {
                //5->11
                type: "INPUT",
                labelName: "关联转入账册",
                labelKey: "inAccountBook",
                hide: true,
            },
            {
                //6->12
                type: "INPUT",
                labelName: "关联核注清单编号",
                labelKey: "pickUpNo",
                hide: true,
            },
            {
                //7->13
                type: "SELECT", // 7
                labelName: "运输方式",
                labelKey: "transportMode",
                required: true,
                message: "请选择运输方式",
                hide: true,
                list: [],
                showValue: true,
                // ccs: "/ccs/invenorder/listTransportV2",
                ccs: "/ccs/dictionary/listTransportMode",
            },
            {
                //8->14
                type: "INPUT", // 8
                labelName: "进出境关别",
                labelKey: "entryExitCustoms",
                required: true,
                message: "请输入进出境关别",
                hide: true,
                validator: {
                    validator: (_, value) => {
                        if (value && !RegExp("^[0-9]{1,4}$").test(value)) {
                            return Promise.reject(new Error("请输入大于0的整数,且长度不允许超过4位"));
                        }
                        return Promise.resolve();
                    },
                },
            },
            {
                //9->15
                type: "SELECT",
                labelName: "启运/运抵国", // 9
                labelKey: "shipmentCountry",
                required: true,
                message: "请输入启运/运抵国",
                hide: true,
                list: [],
                ccs: "/ccs/customs/listCountry",
                together: true,
            },
            // 自己新加的
            {
                //10->16
                type: "SELECT",
                labelName: "实体仓名称",
                labelKey: "entityWarehouseCode",
                list: [],
                // showSearch: true,
                required: true,
                message: "请选择实体仓名称",
                hide: true,
                onSelect: (e, form) => {
                    this.changeOwnerList(e, "ownerCode");
                    this.setState({
                        entityWarehouseName: this.getOwnerHouse("entityWarehouseCode", e),
                    });
                    //
                    if (e !== this.state.entityWarehouseCode) {
                        form.setFieldsValue({
                            ownerCode: "",
                            entityWarehouseCode: e,
                        });
                    }
                },
            },
            {
                //11->17
                type: "SELECT",
                labelName: "货主",
                labelKey: "ownerCode",
                list: [],
                hide: true,
                onChange: e => {
                    // getOwnerHouse
                    this.setState({
                        ownerName: this.getOwnerHouse("ownerCode", e),
                    });
                },
                // ccs: "/ares-admin/owner/listByUserId",
                message: "请选择货主",
            },
            {
                //12->18
                type: "TEXTAREA",
                labelName: "备注",
                labelKey: "remark",
            },
        ];
        //历史遗留代码不好维护，请慎入
        const row = this.defaultConfig.map(item => {
            return { ...item };
        });
        this.state.configList = [...row];
        this.state.configListCombine = [
            {
                type: "TEXTAREA",
                labelName: "清关单号",
                labelKey: "remark",
                required: true,
                remark: "",
                tooltip: "请输入未生成核注的清关单号",
            },
            { type: "INPUT", labelName: "备注", labelKey: "remarks", isPlaceHolder: true },
        ];
        this.state.combineText = "";
        this.formRef = React.createRef();
        this.state.backhaulOpen = false;
        this.state.carinfoConfig = [
            {
                type: "TEXT",
                labelName: "业务类型",
                labelKey: "inveBusinessTypeDesc",
            },
            {
                type: "TEXT",
                labelName: "清关企业",
                labelKey: "inveCompanyName",
            },
            {
                type: "SELECT",
                labelName: "货主是否自备车辆",
                labelKey: "selfOwnedVehicle",
                list: [
                    {
                        id: 0,
                        name: "否",
                    },
                    {
                        id: 1,
                        name: "是",
                    },
                ],
            },
            {
                type: "INPUT",
                labelName: "车牌号",
                labelKey: "licensePlate",
                // maxLength: 128,
                validator: {
                    validator: (_, val) => {
                        if (val && val.length > 128) {
                            return Promise.reject("车牌号字符最多128位");
                        }
                        return Promise.resolve();
                    },
                },
            },
            {
                type: "TEXTAREA",
                labelName: "车辆费用备注",
                labelKey: "vehicleCostRemark",
                required: true,
                validator: {
                    validator: (_, val) => {
                        if (val && val.length > 512) {
                            return Promise.reject("车牌号字符最多512位");
                        }
                        return Promise.resolve();
                    },
                },
            },
        ];

        this.state.totalCount = [];
        this.state.uploadVisible = false;
        this.state.statusCount = {
            CREATED: 0,
            ERFECT: 0,
            CONFIRMING: 0,
            AUDITED: 0,
            ENDORSEMENT: 0,
            // START_STORAGING: 0,
            // START_STORAGED: 0,
            "START_STORAGED,START_STORAGING": 0,
            SERVERING: 0,
            FAILURE: 0,
            COMPLETE: 0,
        };
        // 发起中转弹框
        this.state.transferVisible = false;
        this.state.transferConfig = [
            {
                type: "INPUT",
                labelName: "清关企业(终点)",
                labelKey: "inveCompanyName",
                // required: true,
                disabled: true,
            },
            {
                type: "INPUT",
                labelName: "账册编号(终点)",
                labelKey: "bookNo",
                // required: true,
                disabled: true,
            },
            {
                type: "INPUT",
                labelName: "终点仓",
                labelKey: "entityWarehouseName",
                // required: true,
                disabled: true,
            },
            {
                type: "INPUT",
                labelName: "终点仓货主",
                labelKey: "ownerName",
                // required: true,
                disabled: true,
            },
            {
                type: "SELECT",
                labelName: "清关企业(中转)",
                labelKey: "inveCompanyId",
                required: true,
                message: "请选择清关企业(中转)",
                list: [],
                ccs: "/ccs/company/listWithSBQY",
            },
            {
                type: "SELECT",
                labelName: "账册编号(中转)",
                labelKey: "bookId",
                required: true,
                message: "请选择账册编号(中转)",
                onSelect: row => {
                    lib.request({
                        url: "/ccs/entityWarehouse/listEntityByCustomsBook",
                        data: {
                            customsBookId: row,
                        },
                        success: data => {
                            this.state.transferConfig[6].list = data;
                            this.setState({ transferConfig: [...this.state.transferConfig] });
                            this.transferRef.current.form.resetFields(["entityWarehouseCode"]);
                        },
                    });
                },
                list: [],
                ccs: "/ccs/customsBook/listAllInUseBookNo",
            },
            {
                type: "SELECT",
                labelName: "中转目的仓", // 9
                labelKey: "entityWarehouseCode",
                required: false,
                message: "请选择中转目的仓",
                list: [],
            },
        ];

        // 修改清关企业配置
        this.updateCompanyRef = React.createRef();
        this.state.updateCompanyConfig = [
            {
                type: "TEXT",
                labelName: "原清关企业",
                labelKey: "inveCompanyName",
                // required: true,
                disabled: true,
            },
            {
                type: "TEXT",
                labelName: "原账册编号",
                labelKey: "bookNo",
                // required: true,
                disabled: true,
            },
            {
                type: "SELECT",
                labelName: "新清关企业",
                labelKey: "companyId",
                required: true,
                list: [],
                onChange: val => {
                    lib.request({
                        url: "/ccs/customsBook/findBookByAreaCompanyId",
                        data: { companyId: val },
                        success: data => {
                            this.updateCompanyRef.current.form.setFieldsValue({ bookId: data });
                        },
                    });
                },
                ccs: "/ccs/company/listWithSBQYAll",
            },
            {
                type: "SELECT",
                labelName: "新账册编号",
                labelKey: "bookId",
                required: true,
                list: [],
                ccs: "/ccs/customsBook/listAllInUseBookNo",
            },
            {
                type: "SELECT",
                labelName: "结转明细待确认",
                labelKey: "needCarryOver",
                required: true,
                list: [
                    {
                        id: 0,
                        name: "无",
                    },
                    {
                        id: 1,
                        name: "有",
                    },
                ],
            },
        ];

        //生成非保核放单弹框
        this.state.modalOpen = false;
        this.state.fbChecklist = [];
        this.state.customsBookId = 0;
        this.state.customsBookNo = "";
        this.state.inventoryOrderId = "";

        // 加急
        this.state.urgentOpen = false;
    }
    getConfig() {
        return axios.get(getConfigDataUtils.getDataUrlByDataId(480)).then(res => res.data.data);
    }

    changeConfigList(arr) {
        let { configList } = this.state;
        let defaultShowArr = [0, 1, 2, 9, 18];
        configList.map((item, index) => {
            configList[index].hide =
                defaultShowArr.indexOf(index) === -1 ? (arr.indexOf(index) === -1 ? true : false) : false;
        });
        this.setState({ configList });
    }
    renderMailStatus(row) {
        return (
            <div>
                {row.mailStatusDesc}
                {!row.callbackErpRejectOrderFlag &&
                    row.status !== CustomsClearanceStatus.STATUS_DISCARE &&
                    row.mailStatus === MAILSTATUS.REJECT && (
                        <Tag color="red" style={{ marginLeft: "5px" }}>
                            回传失败
                        </Tag>
                    )}
            </div>
        );
    }
    // 获取货主实体仓名称
    getOwnerHouse(str, e) {
        let index = null;
        let names = null;
        this.state.configList.map(item => {
            if (item.labelKey == str) {
                index = item.list && item.list.findIndex(ite => ite.id == e);
                names = item.list && item.list[index].name;
            }
        });
        return names;
    }

    changeBookIdList(e, labelKey, form) {
        let { configList } = this.state;
        if (e) {
            lib.request({
                url: "/ccs/entityWarehouse/listEntityByCustomsBook", //
                needMask: false,
                data: {
                    customsBookId: e,
                },
                success: res => {
                    configList.map(item => {
                        if (item.labelKey === labelKey) {
                            item.list = res;
                        }
                    });
                    if (res[0] && res[0].id) {
                        console.log(res[0], labelKey);
                        form &&
                            form.setFieldsValue({
                                [labelKey]: res[0].id,
                            });
                        if (labelKey === "entityWarehouseCode") {
                            this.changeOwnerList(res[0].id, "ownerCode");
                            this.setState({
                                entityWarehouseName: res[0].name,
                            });
                        }
                    }
                    this.setState({ configList });
                },
            });
        }
    }

    // 货主动态改变
    changeOwnerList(e, labelKey) {
        let { configList } = this.state;
        if (e) {
            lib.request({
                url: "/ares-admin/owner/listV2", //
                needMask: false,
                data: {
                    entityWarehouseCode: e,
                    openStatus: 1,
                },
                success: res => {
                    configList.map(item => {
                        if (item.labelKey === labelKey) {
                            res.forEach(item => {
                                item.name = item.ownerName;
                                item.id = item.ownerCode;
                            });
                            item.list = res;
                        }
                    });
                    this.setState({ configList });
                },
            });
        }
    }

    resetOwnerList(labelKey) {
        let { configList } = this.state;
        configList.map(item => {
            if (item.labelKey == labelKey) {
                item.list = [];
            }
        });
        this.setState({ configList });
    }

    componentDidMount() {
        this.getPrivilegesCode();
        event.on("onSearchReset", this.onSearchReset.bind(this));
    }

    onSearchReset() {
        this.changeImmutable({
            status: "",
        });
        this.setState({
            status: "",
        });
    }

    renderExpandRow(row) {
        const columns = [
            {
                title: "预录入核注清单编号",
                dataIndex: "preCheckOrderNo",
            },
            {
                title: "核注单号",
                dataIndex: "checkOrderNo",
            },
            {
                title: "核注清单状态",
                dataIndex: "checkStatus",
            },
            {
                title: "创建时间",
                dataIndex: "createTime",
            },
        ];
        return row.listCheckItemInfo && row.listCheckItemInfo.length ? (
            <Table dataSource={row.listCheckItemInfo} columns={columns} pagination={false} />
        ) : null;
    }

    renderAttachment(row) {
        return (
            <a href={row.draftListAttachmentUrl} target={"_blank"}>
                {row.draftListAttachmentName}
            </a>
        );
    }

    // 获取configList 里面的下拉数据
    getSelectList() {
        const row = this.defaultConfig.map(item => {
            return { ...item };
        });
        const requests = [];
        row.map((item, index) => {
            // 可以加参数的 自己再写一个参数即可
            if (item.ccs) {
                requests.push(
                    new Promise((resolve, reject) => {
                        lib.request({
                            url: item.ccs,
                            needMask: false,
                            success: res => {
                                resolve({ data: res, index: index });
                            },
                            fail: () => {
                                reject();
                            },
                        });
                    }),
                );
            }
        });
        Promise.all(requests)
            .then(res => {
                res.map(item => {
                    row[item.index].list = item.data;
                });
                this.setState({ configList: row });
            })
            .catch(err => {});
    }

    getRowValue(row, key) {
        if (key === "") return "";
        let value = row;
        let keys = key.split(".");
        keys.map(item => {
            value = value[item];
        });
        return value;
    }

    renderStatus(row) {
        //清关
        const dist = {
            CREATED: "已创建",
            ERFECT: "已完善",
            SERVERING: "清关服务中",
            COMPLETE: "清关完成",
            DISCARD: "已作废",
            AUDITING: "待审核",
            REJECT: "已驳回",
            FAILURE: "清关失败",
        };
        return dist[row.status] ? dist[row.status] : "未知";
    }

    renderInveCustomsSn(row) {
        return (
            <React.Fragment>
                <div>
                    {
                        // renderInveCustomsSn
                        row.urgentProcessFlag && (
                            <Tooltip title="加急">
                                <ThunderboltFilled
                                    style={{ fontSize: "20px", color: "#F12089" }}
                                    onClick={() => {
                                        console.log("onClick");
                                        this.setState({
                                            urgentOpen: true,
                                        });
                                    }}
                                />
                            </Tooltip>
                        )
                    }
                    <a
                        onClick={() => {
                            lib.openPage(
                                `/customs-clearance-detail2?pageTitle=清关单详情&id=${row.id}&title=查看&createTime=${row.createTime}&subOrderSn=${row.subOrderSn}&masterOrderSn=${row.masterOrderSn}`,
                                () => {
                                    this.load(true);
                                },
                            );
                        }}>
                        {row.inveCustomsSn}
                    </a>
                    {row.orderTodoTagList && (
                        <div>
                            <Space wrap>
                                {row.orderTodoTagList.map(item => {
                                    return <Tag color="red"> {item}</Tag>;
                                })}
                            </Space>
                        </div>
                    )}
                </div>
            </React.Fragment>
        );
    }
    renderInveCustomsTagSn(row) {
        return (
            <>
                {row.orderTagList && (
                    <div
                        wrap
                        size="small"
                        style={{
                            display: "inline-flex",
                            flexWrap: "wrap",
                            gap: "2px",
                        }}>
                        {row.orderTagList &&
                            row.orderTagList.map(item => {
                                return <a style={{ color: tagToColor[item], padding: "6px 2px 6px 0px" }}>{item}</a>;
                            })}
                    </div>
                )}
            </>
        );
    }

    shopOperation(row) {
        const data = ["SERVERING", "COMPLETE", "DISCARD", "FAILURE"];
    }

    //判断权限
    getPrivilegesCode() {
        getPrivileges().then(list => {
            ["CUSTOMS-DECLARATION-MANAGE-INVALID"].map(code => {
                switch (code) {
                    case "CUSTOMS-DECLARATION-MANAGE-INVALID":
                        this.setState({
                            customsDeclarationManageDeclareInvalid: !!list.find(item => item.code == code),
                        });
                        break;
                    default:
                        break;
                }
            });
        });
    }

    showUpload(row) {
        this.setState({
            uploadVisible: true,
            editRow: row,
        });
    }
    transferRef = React.createRef();
    openTransfer(row) {
        this.getTransferConfigs();
        this.setState({
            transferVisible: true,
            editRows: row,
        });
    }

    transferCallback(row) {
        Modal.confirm({
            title: "中转回退",
            content: "确定取消清关单中转标记吗？",
            onOk: () => {
                lib.request({
                    url: "/ccs/invenorder/returnTransit",
                    data: { id: row.id },
                    needMask: true,
                    success: () => {
                        this.load();
                        message.success("回退成功");
                    },
                });
            },
        });
    }

    getTransferConfigs() {
        this.state.transferConfig.map(item => {
            if (item.ccs && item.type === "SELECT") {
                lib.request({
                    url: item.ccs,
                    data: {},
                    success: data => {
                        item.list = data;
                        this.setState({ transferConfig: this.state.transferConfig });
                    },
                });
            }
        });
    }

    renderMailRejectReason(row) {
        return (
            <Tooltip title={row.mailRejectReason}>
                <span>{row.mailRejectReason}</span>
            </Tooltip>
        );
    }

    resendMail(row) {
        lib.request({
            url: "/ccs/invenorder/resendMail",
            data: { id: row.id },
            needMask: true,
            success: () => {
                message.success("重新发送成功");
                this.load();
            },
            fail: () => {},
        });
    }

    resetReject(row) {
        lib.request({
            url: "/ccs/invenorder/retryCallbackRejectOrder",
            data: { id: row.id },
            needMask: true,
            success: () => {
                message.success("驳回重试成功");
                this.load();
            },
            fail: () => {},
        });
    }

    renderMailAddrList(row) {
        return <>{row.mailAddrList?.join(";")}</>;
    }

    renderGenerateFbChecklist(row) {
        this.setState({
            modalOpen: true,
            customsBookId: row.bookId,
            customsBookNo: row.bookNo,
            inventoryOrderId: row.id,
            editRow: row,
        });
        lib.request({
            url: "/ccs/invenorder/listFbChecklistTypeByBusiType",
            data: { businessType: row.inveBusinessType },
            needMask: true,
            success: res => {
                this.setState({
                    fbChecklist: res,
                });
            },
        });
    }

    myOperation(row) {
        let { customsDeclarationManageDeclareInvalid, buttons } = this.state;
        const btns = [
            {
                btnName: "上传报关单",
                auth: [
                    BussinessType.BUSSINESS_ONELINE_IN,
                    BussinessType.BUSSINESS_SECTIONINNER_IN,
                    BussinessType.BUSSINESS_SECTION_IN,
                ].includes(row.inveBusinessType),
                onClick: () => {
                    this.showUpload(row);
                },
                showInMore: true,
            },
            {
                btnName: "服务完成",
                auth:
                    row.channel === 4 &&
                    [CustomsClearanceStatus.STATUS_CONFIRMING].includes(row.status) &&
                    buttons.includes("finish-service"),
                onClick: () => {
                    // this.showUpload(row);
                    lib.request({
                        url: "/ccs/invenorder/manualFinish",
                        data: { id: row.id },
                        needMask: true,
                        success: () => {
                            this.load();
                            message.success("操作完成");
                        },
                    });
                },
                showInMore: false,
            },
            {
                btnName: "重发邮件",
                auth:
                    [MAILSTATUS.REJECT, MAILSTATUS.WAIT_REPLY].includes(row.mailStatus) &&
                    row.pledgeOwnerFlag &&
                    row.status !== CustomsClearanceStatus.STATUS_DISCARE,
                onClick: () => {
                    this.resendMail(row);
                },
                showInMore: false,
            },
            {
                btnName: "驳回重试",
                auth:
                    row.mailStatus === MAILSTATUS.REJECT &&
                    row.pledgeOwnerFlag &&
                    !row.callbackErpRejectOrderFlag &&
                    row.status !== CustomsClearanceStatus.STATUS_DISCARE,
                onClick: () => {
                    this.resetReject(row);
                },
                showInMore: false,
            },
            {
                btnName: "发起中转",
                auth:
                    row.inveBusinessType === BussinessType.BUSSINESS_ONELINE_IN &&
                    row.status === CustomsClearanceStatus.AUDITED &&
                    row.upstreamCancel === "否" &&
                    row.twoStepFlag === 0 &&
                    !row.transitFlag,
                onClick: () => {
                    this.openTransfer(row);
                },
                showInMore: false,
            },
            {
                btnName: "中转回退",
                auth:
                    row.inveBusinessType === BussinessType.BUSSINESS_ONELINE_IN &&
                    row.status === CustomsClearanceStatus.AUDITED &&
                    row.upstreamCancel === "否" &&
                    row.twoStepFlag === 0 &&
                    row.transitFlag,
                onClick: () => {
                    this.transferCallback(row);
                },
                showInMore: false,
            },
            {
                btnName: "查看主单",
                auth: (row.channel === 1 || row.channel === 4) && row.masterOrderSn,
                onClick: () => {
                    this.getMasterOlderId(row);
                },
                showInMore: false,
            },
            {
                btnName: "手动清关",
                auth: row.channel === 1 && row.status === CustomsClearanceStatus.STATUS_AUDITED,
                onClick: () => {
                    this.manualDeal(row);
                },
                showInMore: false,
            },
            {
                btnName: "发起约车",
                auth:
                    row.channel === 1 &&
                    row.status !== CustomsClearanceStatus.STATUS_CONFIRMING &&
                    row.status !== CustomsClearanceStatus.STATUS_DISCARE,
                onClick: () => {
                    Modal.confirm({
                        title: "发起约车",
                        content: "确定发起约车吗？",
                        onOk: () => {
                            lib.request({
                                url: "/danding-wcms/api/order/create/ccs",
                                data: {
                                    inveCustomsSn: row.inveCustomsSn,
                                },
                                success: data => {
                                    message.success("发起成功");
                                    lib.openPage(
                                        `/ride-hailing-detail?page_title=约车详情&id=${data.orderNo}&type=edit`,
                                    );
                                },
                            });
                        },
                    });
                },
                showInMore: true,
            },
            {
                btnName: "作废",
                auth:
                    row.channel !== 1 &&
                    row.channel !== 4 &&
                    !row.subOrderSn &&
                    customsDeclarationManageDeclareInvalid &&
                    (row.status === CustomsClearanceStatus.STATUS_CREATED ||
                        row.status === CustomsClearanceStatus.STATUS_CONFIRMING ||
                        row.status === CustomsClearanceStatus.STATUS_AUDITED ||
                        row.status === CustomsClearanceStatus.STATUS_PERFECT ||
                        row.status === CustomsClearanceStatus.STATUS_ENDORSEMENT ||
                        row.status === CustomsClearanceStatus.STATUS_START_STORAGING ||
                        row.status === CustomsClearanceStatus.STATUS_START_STORAGED ||
                        row.status === CustomsClearanceStatus.STATUS_FAILURE),
                onClick: () => {
                    this.invalidFunc(row);
                },
                showInMore: true,
            },
            {
                btnName: "解除合单",
                auth: row.subOrderSn && row.status === CustomsClearanceStatus.STATUS_PERFECT,
                onClick: () => {
                    this.unMergeClick(row);
                },
                showInMore: false,
            },
            {
                btnName: "备注车辆费用", // 单据来源位ccs：2或者erp：1的时候
                auth: [1, 2].includes(row.channel),
                onClick: () => {
                    this.recordCarInfo(row);
                },
                showInMore: true,
            },
            {
                btnName: "生成非保核放单",
                auth:
                    [BussinessType.FB_IN, BussinessType.FB_OUT].includes(row.inveBusinessType) &&
                    [CustomsClearanceStatus.AUDITED, CustomsClearanceStatus.STATUS_PERFECT].includes(row.status),
                onClick: () => {
                    this.renderGenerateFbChecklist(row);
                },
                showInMore: false,
            },
            {
                btnName: "解除关联",
                auth: row?.orderTagList?.includes("关联"),
                onClick: () => {
                    Modal.confirm({
                        title: "确认",
                        content: "确定解除关联调拨关系吗？",
                        onOk: () => {
                            lib.request({
                                url: "/ccs/invenorder/disassociateTransfer",
                                data: {
                                    id: row.id,
                                },
                                success: () => {
                                    message.success("解除成功");
                                    this.load();
                                },
                            });
                        },
                    });
                },
                showInMore: false,
            },
            {
                btnName: "接单",
                auth: row?.orderTagList?.includes("待接单") && buttons.includes("Receiving-orders"),
                onClick: () => {
                    Modal.confirm({
                        title: "确定接单吗？ ",
                        content: "接单前请核实清关单真实情况",
                        onOk: () => {
                            lib.request({
                                url: "/ccs/invenorder/takeOrder",
                                data: {
                                    id: row.id,
                                },
                                success: () => {
                                    message.success("接单成功");
                                    this.load();
                                },
                            });
                        },
                    });
                },
                showInMore: false,
            },
        ];
        const innerOperation = (
            <Dropdown
                overlay={
                    <Menu>
                        {btns
                            .filter(item => item.auth && item.showInMore)
                            .map(item => (
                                <Menu.Item
                                    onClick={() => {
                                        item.onClick && item.onClick();
                                    }}>
                                    {item.btnName}
                                </Menu.Item>
                            ))}
                    </Menu>
                }>
                <span className="link" style={{ fontFamily: "iconfont", padding: "10px 10px" }}>
                    更多
                    <DownOutlined />
                </span>
            </Dropdown>
        );
        // console.log(btns.filter(item => (item.auth && !item.showInMore)))
        return (
            <Space>
                {btns
                    .filter(item => item.auth && !item.showInMore)
                    .map(item => {
                        return (
                            <a type="link" onClick={item.onClick}>
                                {item.btnName}
                            </a>
                        );
                    })}
                {innerOperation}
            </Space>
        );
    }

    recordCarInfo(row) {
        this.setState({
            carInfoOpen: true,
            editRows: row,
        });
    }

    auditedModal(row) {
        lib.request({
            url: "/ccs/invenorder/audit",
            data: {
                id: row.id,
            },
            needMask: true,
            success: res => {
                message.success("审核通过");
                this.load(true);
            },
        });
    }

    rejectModal() {
        return (
            <Modal
                cancelText="取消"
                okText="确定"
                title="审核驳回"
                open={this.state.refuseModalVisible}
                onOk={() => this.batchRefuse()}
                onCancel={() =>
                    this.setState({
                        refuseModalVisible: false,
                        auditId: "",
                    })
                }
                destroyOnClose={true}>
                <Form ref={this.formRef} preserve={false}>
                    <FormItem label="驳回原因" name="reason" rules={[{ required: true, message: "请输入驳回原因" }]}>
                        <TextArea />
                    </FormItem>
                </Form>
            </Modal>
        );
    }

    batchRefuse(row) {
        let { reason, id } = this.state;
        this.formRef.current.validateFields().then(values => {
            lib.request({
                url: "/ccs/invenorder/reject",
                data: {
                    id,
                    reason: values.reason,
                },
                needMask: true,
                success: res => {
                    message.success("审核驳回");
                    this.setState({
                        refuseModalVisible: false,
                    });
                    this.load(true);
                },
            });
        });
    }

    adminInvalidFunc(row) {
        const modal = Modal.confirm({
            cancelText: "取消",
            okText: "确定",
            title: "提示",
            content: "确定作废该清关单吗？",
            onOk: res => {
                lib.request({
                    url: "/ccs/invenorder/adminDiscard",
                    data: {
                        id: row.id,
                    },
                    needMask: true,
                    success: res => {
                        if (res && res.code == -1) {
                            modal.destroy();
                            message.error(res.errorMessage);
                        } else {
                            message.success("作废成功");
                            this.load(true);
                            modal.destroy();
                        }
                    },
                });
            },
            onCancel: res => modal.destroy(),
        });
    }

    invalidFunc(row) {
        const modal = Modal.confirm({
            cancelText: "取消",
            okText: "确定",
            title: "提示",
            content: "确定作废该清关单吗？",
            onOk: res => {
                lib.request({
                    url: "/ccs/invenorder/discard",
                    data: {
                        id: row.id,
                    },
                    needMask: true,
                    success: res => {
                        if (res && res.code == -1) {
                            modal.destroy();
                            message.error(res.errorMessage);
                        } else {
                            message.success("作废成功");
                            this.load(true);
                            modal.destroy();
                        }
                    },
                });
            },
            onCancel: res => modal.destroy(),
        });
    }

    renderRightOperation() {
        const { buttons } = this.state;
        return (
            <React.Fragment>
                {buttons.includes("add") && (
                    <Button
                        type="primary"
                        onClick={() => {
                            this.setState({
                                modalVisible: true,
                            });
                            this.getSelectList();
                            this.resetOwnerList("ownerCode");
                        }}>
                        新增
                    </Button>
                )}
            </React.Fragment>
        );
    }

    renderLeftOperation() {
        let { totalCount, buttons } = this.state;
        let confirmIngCount = 0;
        let auditedCount = 0;
        totalCount.map(item => {
            switch (item.status) {
                case CustomsClearanceStatus.STATUS_CONFIRMING:
                    confirmIngCount = item.count;
                    break;
                case CustomsClearanceStatus.STATUS_AUDITED:
                    auditedCount = item.count;
                    break;
                default:
                    break;
            }
        });
        return (
            <Space>
                {buttons.includes("customs-clearance-contract") && (
                    <Button type="primary" onClick={() => this.combineClick()}>
                        清关合单
                    </Button>
                )}
                {buttons.includes("retry-return-order") && (
                    <Button type="primary" onClick={() => this.combineBackClick()}>
                        重试清关回传
                    </Button>
                )}

                {buttons.includes("associate-transfer") && (
                    <AssociatedTransfer
                        selectedRows={this.state.selectedRows}
                        reload={() => {
                            this.load();
                        }}
                    />
                )}
                {buttons.includes("UPDATE-CUMSTON-COMPANY") && (
                    <Button type="primary" onClick={() => this.updateCompany()}>
                        修改清关企业
                    </Button>
                )}
                {this.state.buttons?.includes("UPDATE-CUSTOM-CLEAR-STATUS") && (
                    <UpdateStatusModal
                        selected={this.state.selectedRows}
                        success={() => {
                            this.load();
                        }}
                        type={"customs"}
                    />
                )}
                {this.state.buttons?.includes("CREATE-WMS-CARRY-FORWARD") && (
                    <Button
                        onClick={() => {
                            Modal.confirm({
                                title: "触发WMS生成结转",
                                content: "确认触发WMS生成结转?",
                                onOk: () => {
                                    lib.request({
                                        url: "/ccs/invenorder/callWmsGenerateCarryOver",
                                        success: () => {
                                            message.success("触发生成结转成功");
                                            this.load();
                                        },
                                    });
                                },
                            });
                        }}>
                        wms生成结转
                    </Button>
                )}
                {this.state.buttons?.includes("MANUAL-FEEDBACK") && (
                    <Button
                        onClick={() => {
                            const { selectedRows } = this.state;
                            if (selectedRows.length === 0) {
                                return message.warning("请勾选数据");
                            }
                            if (selectedRows.length > 1) {
                                return message.warning("只能勾选一条数据");
                            }
                            this.setState({
                                backhaulOpen: true,
                            });
                        }}>
                        手工回传
                    </Button>
                )}
                {this.state.buttons?.includes("batch-urgent") && (
                    <Button
                        onClick={() => {
                            const { selectedRows } = this.state;
                            if (selectedRows.length === 0) {
                                return message.warning("请勾选数据");
                            }
                            Modal.confirm({
                                title: "确定",
                                content: "确定批量加急吗?",
                                onOk: () => {
                                    lib.request({
                                        url: "/ccs/invenorder/batchChangeUrgentProcessInventoryOrder",
                                        data: {
                                            inventoryOrderId: selectedRows.map(item => item.id),
                                            isUrgent: true,
                                        },
                                        success: () => {
                                            message.success("操作成功");
                                            this.load();
                                        },
                                    });
                                },
                            });
                        }}>
                        批量加急
                    </Button>
                )}

                {this.state.buttons?.includes("cancel-urgent") && (
                    <Button
                        onClick={() => {
                            const { selectedRows } = this.state;
                            if (selectedRows.length === 0) {
                                return message.warning("请勾选数据");
                            }
                            Modal.confirm({
                                title: "确定",
                                content: "确定取消加急吗?",
                                onOk: () => {
                                    lib.request({
                                        url: "/ccs/invenorder/batchChangeUrgentProcessInventoryOrder",
                                        data: {
                                            inventoryOrderId: selectedRows.map(item => item.id),
                                            isUrgent: false,
                                        },
                                        success: () => {
                                            message.success("操作成功");
                                            this.load();
                                        },
                                    });
                                },
                            });
                        }}>
                        取消加急
                    </Button>
                )}
            </Space>
        );
    }

    updateCompany() {
        const { selectedRows } = this.state;
        if (selectedRows.length === 0) {
            return message.warning("请勾选数据");
        }
        if (selectedRows.length > 1) {
            return message.warning("只能勾选一条数据");
        }
        const data = selectedRows[0];
        Promise.all([
            new Promise((resolve, reject) => {
                lib.request({
                    url: "/ccs/company/listWithSBQYAll",
                    success: data => {
                        resolve(data);
                    },
                });
            }),
            new Promise((resolve, reject) => {
                lib.request({
                    url: "/ccs/customsBook/listAllInUseBookNo",
                    success: data => {
                        resolve(data);
                    },
                });
            }),
        ]).then(res => {
            this.state.updateCompanyConfig[2].list = res[0];
            this.state.updateCompanyConfig[3].list = res[1];
            this.setState({
                updateCompanyConfig: this.state.updateCompanyConfig,
                updateCompanyVisible: true,
                editRows: {
                    id: data.id,
                    bookNo: data.bookNo,
                    inveCompanyName: data.inveCompanyName,
                },
            });
        });
    }

    getCustomsStatusNum() {
        lib.request({
            url: "/ccs/invenorder/getStatusCount",
            data: this.state.search,
            success: data => {
                const obj = {};
                data.forEach((item, index) => {
                    obj[item.status] = item.num || 0;
                });
                this.setState({
                    statusCount: obj,
                });
            },
        });
    }

    renderOperationTopView() {
        const statusCount = this.state.statusCount || {};
        const items = [
            { label: "全部", key: "" }, // 务必填写 key
            { label: `已创建(${statusCount["CREATED"]})`, key: "CREATED" },
            { label: `已完善(${statusCount["ERFECT"]})`, key: "ERFECT" },
            { label: `资料待确认(${statusCount["CONFIRMING"]})`, key: "CONFIRMING" },
            { label: `审核通过(${statusCount["AUDITED"]})`, key: "AUDITED" },
            { label: `生成核注单(${statusCount["ENDORSEMENT"]})`, key: "ENDORSEMENT" },
            {
                label: `清关开始(${statusCount["START_STORAGED,START_STORAGING"]})`,
                key: "START_STORAGED,START_STORAGING",
            },
            { label: `清关服务中(${statusCount["SERVERING"]})`, key: "SERVERING" },
            { label: `清关失败(${statusCount["FAILURE"]})`, key: "FAILURE" },
            { label: `清关完成(放行)(${statusCount["COMPLETE"]})`, key: "COMPLETE" },
            { label: `服务完成`, key: "FINISH" },
            { label: `已作废`, key: "DISCARD" },
        ];
        return (
            <Tabs
                className="customs-clearance-manage-tabs"
                defaultActiveKey={""}
                activeKey={this.state.status}
                items={items}
                onChange={key => {
                    this.changeImmutable({
                        status: key,
                    });
                    this.setState({
                        status: key,
                    });
                }}>
                {/* <TabPane tab={`全部`} key={""} />
                <TabPane tab={`已创建(${statusCount["CREATED"]})`} key={"CREATED"} />
                <TabPane tab={`已完善(${statusCount["ERFECT"]})`} key={"ERFECT"} />
                <TabPane tab={`资料待确认(${statusCount["CONFIRMING"]})`} key={"CONFIRMING"} />
                <TabPane tab={`审核通过(${statusCount["AUDITED"]})`} key={"AUDITED"} />
                <TabPane tab={`生成核注单(${statusCount["ENDORSEMENT"]})`} key={"ENDORSEMENT"} />
                <TabPane
                    tab={`清关开始(${statusCount["START_STORAGED,START_STORAGING"]})`}
                    key={"START_STORAGED,START_STORAGING"}
                />
                <TabPane tab={`清关服务中(${statusCount["SERVERING"]})`} key={"SERVERING"} />
                <TabPane tab={`清关失败(${statusCount["FAILURE"]})`} key={"FAILURE"} />
                <TabPane tab={`清关完成(放行)(${statusCount["COMPLETE"]})`} key={"COMPLETE"} />
                <TabPane tab={`服务完成`} key={"FINISH"} />
                <TabPane tab={`已作废`} key={"DISCARD"} /> */}
            </Tabs>
        );
    }

    getBatchId = () => {
        let { selectedRows } = this.state;
        return selectedRows.reduce((prev, curr) => [...prev, curr.id], []);
    };
    combineBackClick() {
        const idList = this.getBatchId();
        lib.request({
            // url: "/ccs/invenorder/retryCallBackERPStatus",
            url: "/ccs/invenorder/retryCallBack",
            data: {
                idList: idList,
            },
            success: () => {
                message.success("清关回传成功");
            },
        });
    }

    // 获取主单的id
    getMasterOlderId(row) {
        lib.request({
            url: "/ccs/invenorder/getMasterOrderInfoBySn",
            data: {
                masterOrderSn: row.masterOrderSn,
            },
            success: res => {
                let id = res.id;
                let auditStatus = res.auditStatus;
                let createTime = res.createTime;
                lib.openPage(
                    // `/customs-clearance-detail2?pageTitle=清关单详情&id=${id}&type=edit&title=查看`,
                    `/customs-clearance-detail2?pageTitle=清关单详情&id=${id}&title=查看&auditStatus=${auditStatus}&createTime=${createTime}&subOrderSn=${res.subOrderSn}`,
                    () => {
                        this.load(true);
                    },
                );
            },
        });
    }

    // 解除主单
    unMergeClick(row) {
        lib.request({
            url: "/ccs/invenorder/unmerge",
            data: {
                id: row.id,
            },
            success: res => {
                message.success("解除合单成功");
                this.load(true);
            },
        });
    }

    /**
     * 手动清关
     * @param row
     */
    manualDeal(row) {
        this.setState({
            editRow: row,
            manualDealModalVisible: true,
        });
    }

    combineClick() {
        const { selectedRows } = this.state;
        // const idArr = dataList.filter(item => item.id)
        let flag = true;
        let isAudit = true; // 是否审核通过
        let isNote = true; // 是否是核注单
        let isDiscard = true; // 是否是作废单
        let isSameOwner = true; // 货主是否相同
        let strs = "";
        let strss = "";

        selectedRows.forEach((item, index) => {
            if (item.status !== CustomsClearanceStatus.STATUS_AUDITED) {
                flag = false;
                isAudit = false;
                strss += item.inveCustomsSn + "/";
            }
            // 判断是否核注
            if (item.listCheckItemInfo && item.listCheckItemInfo.length) {
                isNote = false;
                flag = false;
                strs += item.inveCustomsSn + "/";
            }
            if (item.status == "DISCARD") {
                flag = false;
                // isNote = true;
                isDiscard = false;
            }

            if (
                selectedRows[index].inveBusinessTypeDesc !==
                selectedRows[index + 1 == selectedRows.length ? index : index + 1].inveBusinessTypeDesc
            ) {
                //flag = false
            }

            if (
                // selectedRows[index].ownerCode !==
                // selectedRows[index + 1 == selectedRows.length ? index : index + 1].ownerCode ||
                selectedRows[index].entityWarehouseCode !==
                selectedRows[index + 1 == selectedRows.length ? index : index + 1].entityWarehouseCode
            ) {
                isSameOwner = false;
                strs += item.inveCustomsSn + "/";
            }
        });
        // console.log(tempArr, "tempArr")
        // return
        if (flag) {
            switch (selectedRows.length) {
                case 0:
                    message.warning("请选择要合并的清关单");
                    break;
                case 1:
                    message.warning("请至少要选择合并二项");
                    break;
                default:
                    // console.log(tempArr);
                    // 货主
                    if (!isSameOwner) {
                        let modal = Modal.warning({
                            title: "提示",
                            content: strs.substring(0, strs.length - 1) + "仓库不一致",
                            width: 400,
                            closable: true,
                            onOk: () => {
                                modal.destroy();
                            },
                        });
                    } else {
                        let idList = [];
                        selectedRows.map(item => {
                            idList.push(item.inveCustomsSn);
                        });
                        lib.request({
                            url: "/ccs/invenorder/mergeCheck",
                            data: {
                                idList,
                            },
                            needMask: true,
                            success: res => {
                                if (res.code == 0) {
                                    let str = "";
                                    selectedRows.forEach(item => {
                                        str += item.inveCustomsSn + "\n";
                                    });
                                    const tempArrObj = [...this.state.configListCombine];
                                    tempArrObj[0].remark = str.substring(0, str.length - 1);
                                    this.setState({
                                        combineVisible: true,
                                        configListCombine: tempArrObj,
                                    });
                                } else {
                                    let modal = Modal.warning({
                                        title: "提示",
                                        content: res.errorMessage,
                                        width: 400,
                                        closable: true,
                                        // className: "audit",
                                        onOk: () => {
                                            modal.destroy();
                                        },
                                    });
                                }
                            },
                        });
                    }
            }
        } else {
            if (isAudit) {
                if (!isDiscard) {
                    let modal = Modal.warning({
                        title: "提示",
                        content: "清关状态必须要为已完善",
                        width: 400,
                        closable: true,
                        onOk: () => {
                            modal.destroy();
                        },
                    });
                } else {
                    if (!isNote) {
                        let modal = Modal.warning({
                            title: "提示",
                            content: strs.substring(0, strs.length - 1) + "已生成核注单",
                            width: 400,
                            closable: true,
                            onOk: () => {
                                modal.destroy();
                            },
                        });
                    }
                }
            } else {
                let modal = Modal.warning({
                    title: "提示",
                    content: strss.substring(0, strss.length - 1) + "必须要为审核通过状态",
                    width: 400,
                    closable: true,
                    className: "audit",
                    onOk: () => {
                        modal.destroy();
                    },
                });
            }
        }
        // message.warning("清单合并只允许区内/区间流转,并且类型要一致")
    }

    handleOk(values, modalForm) {
        if (values.entityWarehouseCode) {
            values.entityWarehouseName = this.state.entityWarehouseName;
        }
        if (values.ownerCode) {
            values.ownerName = this.state.ownerName;
        }
        if (values.finalOwnerCode) {
            values.finalOwnerName = this.state.finalOwnerName;
        }

        if (values.finalEntityWarehouseCode) {
            values.finalEntityWarehouseName = this.state.finalEntityWarehouseName;
        }
        values.transitFlag = values.transitFlag ? 1 : 0;
        lib.request({
            url: "/ccs/invenorder/createInventoryOrderV2",
            data: values,
            needMask: true,
            success: res => {
                message.success("新增清关单成功");
                this.load(true);
                this.changeConfigList([]);
                this.resetOwnerList("ownerCode");
                this.setState({
                    modalVisible: false,
                    entityWarehouseName: null,
                    // entityWarehouseCode: null,
                    ownerName: null,
                    finalEntityWarehouseName: null,
                });
            },
        });
    }

    customsEntryAttachUrlFn(row) {
        return (
            <>
                {row.customsEntryAttachUrl && (
                    <div>
                        <a href={row.customsEntryAttachUrl} target="_blank">
                            {row.customsEntryNo}.pdf
                        </a>
                        <Button
                            type="link"
                            onClick={() => {
                                this.delCustomsEntryAttachUrl(row, row.customsEntryNo);
                            }}>
                            删除
                        </Button>
                    </div>
                )}
            </>
        );
    }

    delCustomsEntryAttachUrl = (row, name) => {
        Modal.confirm({
            title: "删除确认",
            content: `是否删除${name}.pdf`,
            onOk: () => {
                lib.request({
                    url: "/ccs/invenorder/deleteCustomsEntryAttach",
                    data: {
                        id: row.id,
                    },
                    success: () => {
                        this.load();
                    },
                });
            },
            okText: "确认",
            cancelText: "取消",
        });
    };

    handleCancel() {
        this.changeConfigList([]);
        this.resetOwnerList("ownerCode");
        this.setState({
            modalVisible: false,
            tempStr: "",
        });
    }

    handleOkCombine(value) {
        let idList = value.remark.trim().split("\n").join(",");
        if (value.remarks && value.remarks.length > 128) {
            message.warning("备注不得超过128字");
        } else {
            if (idList.split(",").length >= 2) {
                lib.request({
                    url: "/ccs/invenorder/mergeCheck",
                    data: {
                        idList: idList.split(","),
                    },
                    needMask: true,
                    success: res => {
                        if (res.code == 0) {
                            lib.request({
                                url: "/ccs/invenorder/mergeInventoryOrder",
                                data: {
                                    ids: idList,
                                    remark: value.remarks || "",
                                },
                                success: res => {
                                    if (res.code == 0) {
                                        this.load(true);
                                        message.success("新增清关单成功");
                                        this.setState({
                                            combineVisible: false,
                                        });
                                    } else {
                                        message.error(res.errorMessage);
                                    }
                                },
                            });
                        } else {
                            let modal = Modal.warning({
                                title: "提示",
                                content: res.errorMessage || res.ex,
                                width: 400,
                                closable: true,
                                // className: "audit",
                                onOk: () => {
                                    modal.destroy();
                                },
                            });
                        }
                    },
                });
            } else {
                message.error("请至少选择两单");
            }
        }
    }

    handleCancelCombine() {
        this.setState({
            combineVisible: false,
        });
    }

    load(_, toTop) {
        super.load(_, toTop);
        this.getCustomsStatusNum();
    }

    transferOk = value => {
        lib.request({
            url: "/ccs/invenorder/startTransit",
            data: {
                inveCompanyId: +value.inveCompanyId,
                bookId: value.bookId,
                entityWarehouseCode: value.entityWarehouseCode,
                inveOrderId: this.state.editRows.id,
            },
            success: () => {
                this.load();
                message.success("中转成功");
                this.transferCancel();
            },
        });
    };

    transferCancel() {
        this.setState({
            editRows: null,
            transferVisible: false,
        });
    }

    renderModal(editRows) {
        let props = {
            title: "新增清关单",
            formItemLayout: {
                labelCol: { span: 8 },
                wrapperCol: { span: 14 },
            },
            onOk: this.handleOk.bind(this),
            onCancel: this.handleCancel.bind(this),
            configList: this.state.configList,
            visible: this.state.modalVisible,
            form: this.props.form,
            editRow: editRows,
        };

        const carInfoProps = {
            title: "备注车辆费用",
            onOk: data => {
                lib.request({
                    url: "/ccs/invenorder/updateInventoryOrderCarInformation",
                    data: {
                        id: this.state.editRows.id,
                        selfOwnedVehicle: data.selfOwnedVehicle,
                        licensePlate: data.licensePlate,
                        vehicleCostRemark: data.vehicleCostRemark,
                    },
                    success: () => {
                        message.success("更新成功");
                        this.load();
                        this.setState({
                            carInfoOpen: false,
                            editRows: {},
                        });
                    },
                });
            },
            onCancel: () => {
                this.setState({
                    carInfoOpen: false,
                    editRows: {},
                });
            },
            visible: this.state.carInfoOpen,
            editRow: this.state.editRows,
            configList: this.state.carinfoConfig,
        };

        let props1 = {
            title: "合并清关单",
            onOk: this.handleOkCombine.bind(this),
            onCancel: this.handleCancelCombine.bind(this),
            configList: this.state.configListCombine,
            visible: this.state.combineVisible,
            form: this.props.form,
            editRow: {
                remark: this.state.configListCombine?.[0].remark,
            },
        };

        let props2 = {
            title: "发起中转",
            onOk: this.transferOk.bind(this),
            onCancel: this.transferCancel.bind(this),
            configList: this.state.transferConfig,
            visible: this.state.transferVisible,
            editRows: this.state.editRows,
            ref: this.transferRef,
        };
        if (this.state.editRows) {
            const values = {
                inveCompanyName: this.state?.editRows?.inveCompanyName,
                bookNo: this.state?.editRows.bookNo,
                entityWarehouseName: this.state?.editRows.entityWarehouseName,
                ownerName: this.state.editRows?.ownerName,
            };
            this.transferRef.current.form.setFieldsValue(values);
        }

        const updateProps = {
            title: "修改清关企业",
            onOk: data => {
                lib.request({
                    url: "/ccs/invenorder/updateInventoryCompanyAndBook",
                    data: {
                        id: this.state.editRows?.id,
                        companyId: data.companyId,
                        bookId: data.bookId,
                        needCarryOver: data.needCarryOver,
                    },
                    success: () => {
                        message.success("修改成功");
                        this.load();
                        this.setState({
                            updateCompanyVisible: false,
                            editRows: {},
                        });
                    },
                    faile: () => {},
                });
            },
            onCancel: () => {
                this.setState({
                    updateCompanyVisible: false,
                    editRows: {},
                });
            },
            configList: this.state.updateCompanyConfig,
            visible: this.state.updateCompanyVisible,
            editRow: this.state.editRows,
            ref: this.updateCompanyRef,
        };
        return (
            <React.Fragment>
                {this.rejectModal()}
                <ManualDealModal
                    detail={this.state.editRow}
                    visible={this.state.manualDealModalVisible}
                    closeModal={success => {
                        this.load(true);
                        this.setState({
                            editRow: null,
                            manualDealModalVisible: false,
                        });
                    }}
                />
                <NewModal {...carInfoProps} />
                <NewModal {...props} />
                <NewModal {...props1} />
                <NewModal {...props2} />
                <UploadDealModal
                    detail={this.state.editRow}
                    visible={this.state.uploadVisible}
                    closeModal={success => {
                        if (success) {
                            this.load(true);
                        }
                        this.setState({
                            editRow: null,
                            uploadVisible: false,
                        });
                    }}
                />
                <RenderFbCheckModal
                    detail={this.state.editRow}
                    visible={this.state.modalOpen}
                    fbChecklist={this.state.fbChecklist}
                    customsBookId={this.state.customsBookId}
                    customsBookNo={this.state.customsBookNo}
                    inventoryOrderId={this.state.inventoryOrderId}
                    closeModal={success => {
                        if (success) {
                            this.load(true);
                        }
                        this.setState({
                            editRow: null,
                            modalOpen: false,
                        });
                    }}
                />
                <NewModal {...updateProps} />

                <DTEditModal
                    title="手工回传"
                    visible={this.state.backhaulOpen}
                    configList={[
                        {
                            fType: "SELECT",
                            labelKey: "reportStatus",
                            labelName: "回告状态",
                            list: [],
                            rules: [{ required: true }],
                            from: "/ccs/taotian/inventoryReportStatusList",
                        },
                        {
                            fType: "DATE",
                            labelKey: "occurredTime",
                            labelName: "发生时间",
                            showTime: true,
                            rules: [{ required: true }],
                        },
                    ]}
                    request={lib}
                    onOk={data => {
                        lib.request({
                            url: "/ccs/taotian/inventoryOrderInfoManualReport",
                            data: {
                                id: String(this.state.selectedRows[0].id),
                                reportStatus: String(data.reportStatus),
                                occurredTime: data.occurredTime.valueOf(),
                            },
                            success: response => {
                                message.success("手工回告成功");
                                this.load();
                                this.setState({
                                    selected: [],
                                    selectedRows: [],
                                    backhaulOpen: false,
                                });
                            },
                        });
                    }}
                    onCancel={() => {
                        this.setState({
                            backhaulOpen: false,
                        });
                    }}
                />
                <SortModal
                    open={this.state.urgentOpen}
                    onClose={load => {
                        this.setState({
                            urgentOpen: false,
                        });
                        if (load) {
                            this.load();
                        }
                    }}
                />
            </React.Fragment>
        );
    }
}

export default App;

// 手动
function ManualDealModal({ detail, visible, closeModal }) {
    let [form] = Form.useForm();
    useEffect(() => {
        if (visible) {
            form.setFieldsValue(detail);
        }
    }, [visible]);
    const handleClose = success => {
        closeModal(success);
        form.resetFields();
    };
    const onFinish = values => {
        values.id = detail.id;
        delete values.inveCustomsSn;
        lib.request({
            url: "/ccs/invenorder/manualDeal",
            data: values,
            needMask: true,
            success: res => {
                message.success("手动清关成功");
                handleClose(true);
            },
        });
    };

    function handleOk() {
        form.submit();
    }

    const FormText = ({ value }) => {
        return <span>{value}</span>;
    };

    function handleCancel() {
        handleClose();
    }

    return (
        <Modal title="手动清关" open={visible} onOk={handleOk} onCancel={() => handleCancel()} destroyOnClose>
            <p>状态回传：请确认此清关单实物已完成真实清关</p>
            <Form form={form} onFinish={onFinish}>
                <Form.Item label={"清关单号"} name={"inveCustomsSn"}>
                    <FormText />
                </Form.Item>
                <Form.Item label={"关联清关单号"} name={"associatedInventorySn"} rules={[{ required: true }]}>
                    <Input />
                </Form.Item>
            </Form>
        </Modal>
    );
}

// 上传报关单
function UploadDealModal({ detail, visible, closeModal }) {
    let [form] = Form.useForm();
    const [countryList, setCountrys] = useState([]);
    const [transports, setTransports] = useState([]);
    const handleClose = success => {
        closeModal(success);
        form.resetFields();
    };
    const onFinish = values => {
        values.id = detail.id;
        values.customsEntryTime = moment(values.customsEntryTime).valueOf();
        values.customsEntryAttachUrl = values.customsEntryAttachUrl?.[0]?.url;
        // values.customsEntryAttachName = values.customsEntryAttachUrl[0].name;
        lib.request({
            url: "/ccs/invenorder/uploadCustomsEntryAttach",
            data: values,
            needMask: true,
            success: res => {
                message.success("上传成功");
                handleClose(true);
            },
        });
    };

    function handleOk() {
        form.submit();
    }

    const FormText = ({ value }) => {
        return <span>{value}</span>;
    };

    function handleCancel() {
        handleClose();
    }

    const getCoutrys = () => {
        lib.request({
            url: "/ccs/customs/listCountry",
            success: res => {
                setCountrys(res);
            },
        });
    };
    const getTransports = () => {
        lib.request({
            url: "/ccs/dictionary/listTransportMode",
            success: res => {
                setTransports(res);
            },
        });
    };

    useEffect(() => {
        if (visible) {
            getCoutrys();
            getTransports();
        }
    }, [visible]);

    useEffect(() => {
        if (visible) {
            if (detail) {
                const result = { ...detail };
                result.customsEntryTime = moment(result.customsEntryTime);
                result.customsEntryAttachUrl = result.customsEntryAttachUrl
                    ? [{ url: result.customsEntryAttachUrl, name: result.customsEntryAttachUrl }]
                    : [];
                form.setFieldsValue(result);
            }
        }
    }, [detail]);
    return (
        <Modal title="上传报关单" open={visible} onOk={handleOk} onCancel={() => handleCancel()} destroyOnClose>
            <Form form={form} onFinish={onFinish} style={{ marginTop: "-10px" }}>
                <Button type="link" style={{ marginBottom: "20px" }}>
                    以下属于溯源信息，请按照报关单填写！
                </Button>
                <Form.Item label={"报关单号"} name={"customsEntryNo"} rules={[{ required: true }]}>
                    <Input />
                </Form.Item>
                <Form.Item label={"报关日期"} name={"customsEntryTime"} rules={[{ required: true }]}>
                    <DatePicker />
                </Form.Item>
                <Form.Item label={"启运国"} name={"shipmentCountry"} rules={[{ required: true }]}>
                    {/* <DatePicker /> */}

                    <Select
                        showSearch
                        placeholder="请选择"
                        optionFilterProp="children"
                        filterOption={(input, option) =>
                            option.children?.toLowerCase().indexOf(input.toLowerCase()) >= 0
                        }>
                        {countryList.map(item => (
                            <Select.Option value={item.id}>
                                {item.id}:{item.name}
                            </Select.Option>
                        ))}
                    </Select>
                </Form.Item>
                <Form.Item label={"起运港(始发机场)"} name={"fromLocation"} rules={[{ required: true }]}>
                    <Input />
                </Form.Item>
                <Form.Item label={"运输方式"} name={"transportMode"} rules={[{ required: true }]}>
                    <Select>
                        {transports.map(item => (
                            <Select.Option value={item.id}>
                                {item.id}:{item.name}
                            </Select.Option>
                        ))}
                    </Select>
                </Form.Item>
                <Form.Item label={"进境口岸"} name={"entryPort"} rules={[{ required: true }]}>
                    <Input />
                </Form.Item>
                <Form.Item
                    label={"附件"}
                    name={"customsEntryAttachUrl"}
                    rules={
                        detail?.inveBusinessType === BussinessType.BUSSINESS_ONELINE_IN
                            ? [{ required: true, message: "请上传附件" }]
                            : []
                    }>
                    <UploadFile
                        listType={"text"}
                        showUploadList={true}
                        maxCount={1}
                        uploadButton={<a className={"link"}>上传附件</a>}
                        fileKey={"ccs/customs-clearance-detail"}
                        accept={".pdf"}
                        action={"//dante-img.oss-cn-hangzhou.aliyuncs.com"}
                    />
                </Form.Item>
            </Form>
        </Modal>
    );
}
// 生成非保核放单
function RenderFbCheckModal({
    detail,
    visible,
    fbChecklist,
    customsBookNo,
    customsBookId,
    inventoryOrderId,
    closeModal,
}) {
    let [form] = Form.useForm();
    const handleClose = success => {
        closeModal(success);
        form.resetFields();
    };
    const onFinish = values => {
        lib.request({
            url: "/ccs/invenorder/generateFbChecklist",
            data: { ...values, inventoryOrderId: inventoryOrderId },
            needMask: true,
            success: () => {
                message.success("非保核放单生成完毕");
                closeModal(true);
            },
        });
    };

    function handleOk() {
        form.submit();
    }

    const FormText = ({ value }) => {
        return <span>{value}</span>;
    };

    function handleCancel() {
        handleClose();
    }

    // useEffect(() => {
    //     visible && detail && form.setFieldsValue({
    //         // businessType: detail.inveBusinessType,
    //         customsBookId: customsBookId,
    //         // applicant: detail.applyPerson,
    //         // vehicleInfos: detail.vehicleCostRemark
    //     })
    // }, [visible])

    useEffect(() => {
        if (fbChecklist.length > 0) {
            form.setFieldsValue({
                businessType: fbChecklist[0].id,
            });
        }
    }, [fbChecklist]);

    return (
        <Modal title="新增核放单" visible={visible} onOk={handleOk} onCancel={() => handleCancel()} destroyOnClose>
            <Form form={form} onFinish={onFinish} labelCol={{ span: 6 }} wrapperCol={{ span: 14 }}>
                <Form.Item label={"核放单类型"} name={"businessType"} rules={[{ required: true }]}>
                    <Select options={fbChecklist} fieldNames={{ label: "name", value: "id" }} />
                </Form.Item>
                <Form.Item
                    label={"账册编号"}
                    name={"customsBookId"}
                    rules={[{ required: true }]}
                    initialValue={customsBookId}>
                    <Select disabled options={[{ label: customsBookNo, value: customsBookId }]} />
                </Form.Item>
                <Form.Item label={"申请人"} name={"applicant"} rules={[{ required: true }]}>
                    <Input />
                </Form.Item>
                <Form.Item label={"车辆信息"} name={"vehicleInfos"}>
                    <Input placeholder="多个车牌号用','隔开" />
                </Form.Item>
            </Form>
        </Modal>
    );
}
