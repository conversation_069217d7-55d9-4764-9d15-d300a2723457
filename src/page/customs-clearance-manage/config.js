export const defaultConfig = [
    {
        //0
        type: "SELECT",
        labelName: "业务类型",
        labelKey: "inveBusinessType",
        required: true,
        message: "请选择业务类型",
        list: [],
        ccs: "/ccs/invenorder/list-bussiness-type",
        onChange: e => {
            switch (e) {
                case "REFUND_INAREA":
                    this.changeConfigList([]);
                    break;
                case "ONELINE_IN":
                    this.changeConfigList([3, 13, 14, 15, 16, 17]);
                    break;
                case "SECTION_OUT":
                case "SECTIONINNER_OUT":
                    this.changeConfigList([11, 13, 14, 16, 17]);
                    break;
                case "SECTION_IN":
                case "SECTIONINNER_IN":
                    this.changeConfigList([10, 13, 14, 16, 17]);
                    break;
                case "ONLINE_REFUND":
                    this.changeConfigList([9, 13, 14, 15, 16]);
                    break;
                case "BONDED_TO_TRADE":
                    this.changeConfigList([9, 13, 14, 16]);
                    break;
                case "SUBSEQUENT_TAX":
                    this.changeConfigList([9, 13, 14, 16]);
                    break;
                default:
                    this.changeConfigList([]);
            }
        },
    },
    {
        //1
        type: "SELECT",
        labelName: "清关企业",
        labelKey: "inveCompanyId",
        required: true,
        message: "请选择清关企业",
        list: [],
        isShows: true,
        ccs: "/ccs/company/listWithSBQY",
    },
    {
        //2
        type: "SELECT",
        labelName: "账册编号",
        labelKey: "bookId",
        required: true,
        message: "请选择账册编号",
        list: [],
        ccs: "/ccs/customsBook/effective/listBookNoByAccBookAuth",
        onSelect: (e, form) => {
            this.changeBookIdList(e, "entityWarehouseCode");
            if (e !== this.state.bookId) {
                form.setFieldsValue({
                    entityWarehouseCode: "",
                    bookId: e,
                });
            }
        },
    },
    {
        //3
        type: "SWITCH",
        labelName: "是否标记成中转账册",
        labelKey: "transitFlag",
        required: false,
        hide: true,
        onChange: e => {
            if (e) {
                this.state.configList[1].labelName = "清关企业(中转)";
                this.state.configList[2].labelName = "账册编号(中转)";
                this.changeConfigList([3, 4, 5, 6, 7, 8, 13, 14, 15]);
            } else {
                this.state.configList[1].labelName = "清关企业";
                this.state.configList[2].labelName = "账册编号";
                this.changeConfigList([3, 13, 14, 15, 16, 17]);
            }
        },
    },
    {
        //4
        type: "SELECT",
        labelName: "清关企业(终点)",
        labelKey: "finalInveCompanyId",
        required: true,
        message: "清关企业(终点)",
        hide: true,
        list: [],
        ccs: "/ccs/company/listWithSBQY",
    },
    {
        //5
        type: "SELECT",
        labelName: "账册编号(终点)",
        labelKey: "finalBookId",
        required: true,
        message: "请选择账册编号(终点)",
        hide: true,
        list: [],
        ccs: "/ccs/customsBook/effective/listBookNoByAccBookAuth",
        onSelect: (e, form) => {
            this.changeBookIdList(e, "finalEntityWarehouseCode");
            if (e !== this.state.finalBookId) {
                form.setFieldsValue({
                    finalEntityWarehouseCode: "",
                    finalBookId: e,
                });
            }
        },
    },
    {
        //6
        type: "SELECT",
        labelName: "中转目的仓",
        labelKey: "entityWarehouseCode",
        required: true,
        message: "请选择中转目的仓",
        hide: true,
        list: [],
        onSelect: (e, form) => {
            this.changeOwnerList(e, "ownerCode");
            this.setState({
                entityWarehouseName: this.getOwnerHouse("entityWarehouseCode", e),
            });
            //
            if (e !== this.state.entityWarehouseCode) {
                form.setFieldsValue({
                    ownerCode: "",
                    entityWarehouseCode: e,
                });
            }
        },
    },
    {
        //7
        type: "SELECT",
        labelName: "终点仓",
        labelKey: "finalEntityWarehouseCode",
        required: true,
        message: "请选择终点仓",
        hide: true,
        list: [],
        onSelect: (e, form) => {
            this.changeOwnerList(e, "finalOwnerCode");
            this.setState({
                finalEntityWarehouseName: this.getOwnerHouse("finalEntityWarehouseCode", e),
            });
            if (e !== this.state.finalEntityWarehouseCode) {
                form.setFieldsValue({
                    finalOwnerCode: "",
                    finalEntityWarehouseCode: e,
                });
            }
        },
    },
    {
        //8
        type: "SELECT",
        labelName: "终点仓货主",
        labelKey: "finalOwnerCode",
        required: true,
        message: "请选择终点仓货主",
        hide: true,
        list: [],
        onChange: e => {
            // getOwnerHouse
            this.setState({
                finalOwnerName: this.getOwnerHouse("finalOwnerCode", e),
            });
        },
    },
    {
        //3->9
        type: "INPUT",
        labelName: "申请人",
        labelKey: "applyPerson",
        required: true,
        message: "请输入申请人",
    },
    {
        //4->10
        type: "INPUT",
        labelName: "关联转出账册",
        labelKey: "outAccountBook",
        hide: true,
    },
    {
        //5->11
        type: "INPUT",
        labelName: "关联转入账册",
        labelKey: "inAccountBook",
        hide: true,
    },
    {
        //6->12
        type: "INPUT",
        labelName: "关联核注清单编号",
        labelKey: "pickUpNo",
        hide: true,
    },
    {
        //7->13
        type: "SELECT", // 7
        labelName: "运输方式",
        labelKey: "transportMode",
        required: true,
        message: "请选择运输方式",
        hide: true,
        list: [],
        together: true,
        // ccs: "/ccs/invenorder/listTransportV2",
        ccs: "/ccs/dictionary/listTransportMode",
    },
    {
        //8->14
        type: "INPUT", // 8
        labelName: "进出境关别",
        labelKey: "entryExitCustoms",
        required: true,
        message: "请输入进出境关别",
        hide: true,
        validator: {
            validator: (_, value) => {
                if (value && !RegExp("^[0-9]{1,4}$").test(value)) {
                    return Promise.reject(new Error("请输入大于0的整数,且长度不允许超过4位"));
                }
                return Promise.resolve();
            },
        },
    },
    {
        //9->15
        type: "SELECT",
        labelName: "启运国", // 9
        labelKey: "shipmentCountry",
        required: true,
        message: "请输入启运国",
        hide: true,
        list: [],
        ccs: "/ccs/customs/listCountry",
        together: true,
    },
    // 自己新加的
    {
        //10->16
        type: "SELECT",
        labelName: "实体仓名称",
        labelKey: "entityWarehouseCode",
        list: [],
        // showSearch: true,
        required: true,
        message: "请选择实体仓名称",
        hide: true,
        onSelect: (e, form) => {
            this.changeOwnerList(e, "ownerCode");
            this.setState({
                entityWarehouseName: this.getOwnerHouse("entityWarehouseCode", e),
            });
            //
            if (e !== this.state.entityWarehouseCode) {
                form.setFieldsValue({
                    ownerCode: "",
                    entityWarehouseCode: e,
                });
            }
        },
    },
    {
        //11->17
        type: "SELECT",
        labelName: "货主",
        labelKey: "ownerCode",
        list: [],
        hide: true,
        onChange: e => {
            // getOwnerHouse
            this.setState({
                ownerName: this.getOwnerHouse("ownerCode", e),
            });
        },
        // ccs: "/ares-admin/owner/listByUserId",
        message: "请选择货主",
    },
    {
        //12->18
        type: "TEXTAREA",
        labelName: "备注",
        labelKey: "remark",
    },
];
