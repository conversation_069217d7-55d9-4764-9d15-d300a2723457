import React, { useEffect, useRef, useState } from "react";
import { Table, Checkbox, Row, Col, Modal, message } from "antd";
import { SortableContainer, SortableElement, SortableHandle } from "react-sortable-hoc";
import { MenuOutlined } from "@ant-design/icons";
import { arrayMoveImmutable } from "array-move";
import { lib } from "react-single-app";
// 可拖拽的图标
const DragHandle = SortableHandle(() => <MenuOutlined style={{ cursor: "grab", color: "#999" }} />);

// 可排序的表格行
const SortableItem = SortableElement(props => <tr {...props} />);
const SortableBody = SortableContainer(props => <tbody {...props} />);

const columns = [
    {
        title: "拖拽",
        dataIndex: "sort",
        width: 50,
        className: "drag-visible",
        render: () => <DragHandle />,
    },
    {
        title: "清关单号",
        dataIndex: "inventoryOrderSn",
    },
    {
        title: "业务类型",
        dataIndex: "inveBusinessTypeDesc",
    },
    {
        title: "清关状态",
        dataIndex: "inventoryOrderStatusDesc",
    },
    {
        title: "出入库单号",
        dataIndex: "inOutOrderNo",
    },
    {
        title: "操作人",
        dataIndex: "operator",
    },
    {
        title: "创建时间",
        dataIndex: "createTime",
    },
];

const filterOptions = [
    "清关完成(放行)",
    "清关失败",
    "清关服务中",
    "清关开始",
    "生成核注单",
    "审核通过",
    "提交资料(待确认)",
];

const DragSortingTable = ({ open, onClose }) => {
    const [dataSource, setDataSource] = useState([]);
    const filterData = useRef([]);
    // const [filterData.current, setfilterData.current] = useState([]);
    const [filters, setFilters] = useState(["清关完成(放行)"]); // 默认显示全部状态

    const onSortEnd = ({ oldIndex, newIndex }) => {
        if (oldIndex !== newIndex) {
            // 更新当前排序数组
            const newData = arrayMoveImmutable(dataSource.slice(), oldIndex, newIndex).filter(el => !!el);

            // 更新缓存数据
            const cacheIndex = filterData.current.findIndex(
                item => item.inventoryOrderId === newData[newIndex].inventoryOrderId,
            );
            const cacheItem = filterData.current[cacheIndex];
            // 优先剔除在原始数组中该元素的位置
            filterData.current.splice(cacheIndex, 1);
            const replaceItem = dataSource[newIndex];
            const replaceIndex = filterData.current.findIndex(
                item => item.inventoryOrderId === replaceItem.inventoryOrderId,
            );

            if (newIndex === 0) {
                // 如果拖拽元素是放在当前显示数组的第一位，直接交换原始数组的位置；
                // [filterData.current[newIndex], filterData.current[cacheIndex]] = [filterData.current[cacheIndex], filterData.current[newIndex]]
                filterData.current.unshift(cacheItem);
            } else if (newIndex === newData.length - 1) {
                // 当拖拽元素是移到当前显示数组的最后一位时，以拖拽前数组最后一个元素为开始，查询原始数组这个位置之后的第一个隐藏元素；
                // 如果有，操作元素数组，将拖拽元素插入到这个元素之前
                // 如果没有，插入到元素数组的最后
                const insertIndex = filterData.current.findIndex(
                    item => item.inventoryOrderId === replaceItem.inventoryOrderId,
                );
                if (filterData.current[insertIndex + 1]) {
                    filterData.current.splice(insertIndex, 0, cacheItem);
                } else {
                    filterData.current.push(cacheItem);
                }
            } else {
                let insertIndex = replaceIndex;
                for (let i = replaceIndex - 1; i >= 0; i--) {
                    if (!filterData.current[i].isHide) {
                        insertIndex = i + 1;
                        break;
                    }
                }
                console.log("insertIndex:", insertIndex);
                filterData.current.splice(insertIndex, 0, cacheItem);
            }
            console.log(
                "filterData.current: ",
                [...filterData.current].map(item => item.inventoryOrderId),
            );

            setDataSource(newData);
        }
    };

    const DraggableContainer = props => (
        <SortableBody useDragHandle disableAutoscroll helperClass="row-dragging" onSortEnd={onSortEnd} {...props} />
    );

    const DraggableBodyRow = ({ className, style, ...restProps }) => {
        const index = dataSource.findIndex(x => x.inventoryOrderId === restProps["data-row-key"]);
        return <SortableItem index={index} {...restProps} />;
    };

    const handleFilterChange = checkedValues => {
        setFilters(checkedValues);
        // 根据过滤条件筛选数据
        const filteredData = filterData.current.filter(item => !checkedValues.includes(item.inventoryOrderStatusDesc));
        filterData.current.map(item => (item.isHide = checkedValues.includes(item.inventoryOrderStatusDesc)));
        // setfilterData.current([...filterData.current]);
        setDataSource([...filteredData]);
    };

    const getData = () => {
        // /invenorder/listUrgentProcess
        lib.request({
            url: "/ccs/invenorder/listUrgentProcess",
            data: {},
            success: res => {
                // setDataSource([...res]);
                // setfilterData.current([...res])
                filterData.current = [...res];
                const filteredData = filterData.current.filter(
                    item => !filters.includes(item.inventoryOrderStatusDesc),
                );
                filterData.current.map(item => (item.isHide = filters.includes(item.inventoryOrderStatusDesc)));
                // setfilterData.current([...filterData.current]);
                setDataSource([...filteredData]);
            },
        });
    };

    const submitSort = () => {
        console.log(
            "filterData.current: ",
            [...filterData.current].map(item => item.inventoryOrderId),
        );
        const ids = filterData.current.map(item => item.inventoryOrderId);
        lib.request({
            url: "/ccs/invenorder/saveUrgentSort",
            data: { inventoryOrderIdList: ids },
            success: res => {
                message.success("修改成功");
                onClose && onClose(true);
            },
        });
    };

    useEffect(() => {
        if (open) {
            setFilters(["清关完成(放行)"]);
            getData();
        }
    }, [open]);

    return (
        <Modal
            title="加急排序"
            open={open}
            onOk={() => {
                console.log(filterData.current);
                submitSort();
            }}
            onCancel={() => {
                onClose();
            }}
            width={1200}>
            <div>
                <Row style={{ marginBottom: 16 }}>
                    <Col>
                        过滤状态：
                        <Checkbox.Group options={filterOptions} value={filters} onChange={handleFilterChange} />
                    </Col>
                </Row>
                <Table
                    pagination={false}
                    dataSource={dataSource}
                    columns={columns}
                    rowKey="inventoryOrderId"
                    components={{
                        body: {
                            wrapper: DraggableContainer,
                            row: DraggableBodyRow,
                        },
                    }}
                />
            </div>
        </Modal>
    );
};

export default DragSortingTable;
