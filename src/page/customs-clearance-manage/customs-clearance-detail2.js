import React, { useState, useEffect } from "react";
import { Tabs, Steps } from "antd";
import { lib } from "react-single-app";
import { DocumentNumber } from "./componets/document-number";
import DetailBody from "./componets/detail-body";
import "../customs-clearance-detail.less";
import "./index.less";
import DetailHeaders from "./componets/detail-header";

const TabPane = Tabs.TabPane;
const { Step } = Steps;
export const CustomsClearanceStatus = {
    /**
     * "未知"
     */
    MPTY: "", // ,
    /**
     * 已创建
     */
    STATUS_CREATED: "CREATED",
    /**
     * 提交资料(待确认)
     */
    STATUS_CONFIRMING: "CONFIRMING",
    /**
     * 已审核
     */
    STATUS_AUDITED: "AUDITED", // "",
    /**
     * 已完善
     */
    STATUS_PERFECT: "ERFECT",
    /**
     * 生成核注单
     */
    STATUS_ENDORSEMENT: "ENDORSEMENT",
    /**
     * 清关开始(核注暂存中)
     */
    STATUS_START_STORAGING: "START_STORAGING", // "",
    /**
     * 清关开始(核注已暂存)
     */
    STATUS_START_STORAGED: "START_STORAGED",
    /**
     * 清关服务中
     */
    STATUS_SERVERING: "SERVERING",
    /**
     * 清关完成
     */
    STATUS_COMPLETE: "COMPLETE",
    /**
     * 清关失败
     */
    STATUS_FAILURE: "FAILURE",
    /**
     * 服务完成(核注通过已核扣)
     */
    STATUS_FINISH: "FINISH",
    /**
     * 作废
     */
    STATUS_DISCARE: "DISCARD",
    /**
     *  审核通过
     */
    AUDITED: "AUDITED",
};
export const BussinessType = {
    BUSSINESS_EMPTY: "",
    /**
     * 退货入区
     */
    BUSSINESS_REFUND_INAREA: "REFUND_INAREA",
    /**
     *  区间流转:(出),
     */
    BUSSINESS_SECTION_OUT: "SECTION_OUT",
    /**
     * 区间流转:(入)"
     */
    BUSSINESS_SECTION_IN: "SECTION_IN",
    /**
     * 区内流转(出)
     */
    BUSSINESS_SECTIONINNER_OUT: "SECTIONINNER_OUT",
    /**
     * 区内流转(入)
     */
    BUSSINESS_SECTIONINNER_IN: "SECTIONINNER_IN",
    /**
     * 一线入境
     */
    BUSSINESS_ONELINE_IN: "ONELINE_IN",
    /**
     *  销毁
     */
    BUSSINESS_DESTORY: "DESTORY",
    /**
     *  审核通过
     */
    AUDITED: "AUDITED",
    /**
     *  一线退运
     */
    ONLINE_REFUND: "ONLINE_REFUND",
    /**
     *  保税物流转大贸
     */
    BONDED_TO_TRADE: "BONDED_TO_TRADE",
    /**
     *  后续补税
     */
    SUBSEQUENT_TAX: "SUBSEQUENT_TAX",
    /**
     *  保税物流一线入境
     */
    BONDED_ONELINE_IN: "BONDED_ONELINE_IN",
    /**
     *  盘盈
     */
    INVENTORY_PROFIT: "INVENTORY_PROFIT",
    /**
     *  抽检申报
     */
    RANDOM_INSPECTION_DECLARATION: "RANDOM_INSPECTION_DECLARATION",
    /**
     *  非保入区
     */
    FB_IN: "FB_IN",
    /**
     *  非保出区
     */
    FB_OUT: "FB_OUT",
    /**
     *  简单加工
     */
    SIMPLE_PROCESSING: "SIMPLE_PROCESSING",
};
export const TransitFlag = {
    NULL: -1,
    /**
     * 不中转,
     */
    NON_TRANSIT: 0,
    /**
     * 中转主清关
     */
    TRANSIT: 1,
    /**
     * 中转调入清关
     */
    TRANSIT_IN: 2,
    /**
     * 中转调出清关
     */
    TRANSIT_OUT: 3,
};

export default function () {
    let id = lib.getParam("id");
    let [currency, setCurrency] = useState([]);
    let [countries, setCountries] = useState([]);
    let [detail, setDetail] = useState();
    let [activeKey, setActiveKey] = useState("0");
    let [flowState, setFlowState] = useState();
    let [inveBusinessType, setInveBusinessType] = useState();

    function fetchDetail({ activeKey = "0", id }) {
        lib.request({
            url: "/ccs/invenorder/getFlowState",
            data: {
                id: id || lib.getParam("id"),
            },
            needMask: true,
            success: res => {
                setFlowState(res);
            },
        });
        lib.request({
            url: "/ccs/invenorder/view-inventory-order",
            data: {
                id: id || lib.getParam("id"),
            },
            needMask: true,
            success: res => {
                if (!res.actualArrivalDate) delete res.actualArrivalDate;
                setInveBusinessType(res.inveBusinessType);
                if (activeKey == 2) {
                    setDetail(detail => {
                        return {
                            ...detail,
                            inventoryOrderRelationDTOList: res.inventoryOrderRelationDTOList,
                        };
                    });
                } else {
                    setDetail(res);
                }

                setActiveKey(activeKey);
            },
        });
    }

    // 获取详情
    useEffect(() => {
        id && fetchDetail({ activeKey: "0", id });
    }, [id]);
    // 获取下拉列表
    useEffect(() => {
        [
            ["/ccs/customs/listCountry", setCountries],
            ["/ccs/customs/listCurrency", setCurrency],
        ].map(([url, success]) => lib.request({ url, success }));
    }, []);

    return (
        <div className="customs-clearance-detail2">
            {flowState && flowState.statusList && (
                <Steps
                    current={flowState.currNode}
                    status={flowState.statusList[flowState.currNode].status}
                    labelPlacement={"vertical"}
                    style={{ margin: "15px 0" }}>
                    {flowState.statusList.map((item, index) => {
                        return <Step title={item.title} key={index} status={item.status} />;
                    })}
                </Steps>
            )}
            <Tabs activeKey={activeKey} onChange={e => setActiveKey(e)}>
                <TabPane tab="清关单表头" key="0">
                    <DetailHeaders {...{ detail, setDetail, fetchDetail }} />
                </TabPane>
                <TabPane tab="清关单表体" key="1">
                    <DetailBody {...{ detail, countries, currency, setDetail, fetchDetail, inveBusinessType }} />
                </TabPane>
                <TabPane tab="关联单证号" key="2">
                    <DocumentNumber {...{ detail, countries, currency, setDetail, fetchDetail }} />
                </TabPane>
            </Tabs>
        </div>
    );
}
