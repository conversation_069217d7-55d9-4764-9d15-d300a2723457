import React, { useState, useEffect } from "react";
import { Checkbox, But<PERSON>, Modal, Drawer, Input, Select, Form, Icon, message, Switch, Space } from "antd";
import { ConfigCenter, lib } from "react-single-app";
import NewModal from "../components/NewModal";
import "./shop-good.less";

class App extends ConfigCenter {
    constructor(props) {
        super(props);
        this.state.visible = false;
        this.state.modalTitle = "新增路由";
        this.state.upUrl = "/ccs/path/upset";
        this.state.configList = [
            {
                type: "INPUT",
                labelName: "路由名称",
                labelKey: "name",
                required: true,
                message: "请输入路由名称",
                maxLength: 32,
                autocomplete: "off",
            },
            {
                type: "INPUT",
                labelName: "路由标识",
                labelKey: "code",
                required: true,
                message: "请输入路由标识",
                maxLength: 32,
                autocomplete: "off",
            },
            {
                type: "SELECT",
                labelName: "路径名称",
                labelKey: "routeId",
                required: true,
                message: "请输入路径名称",
                list: [],
                ccs: "/ccs/route/listWithName",
            },
            {
                type: "INPUT",
                labelName: "实体仓编码",
                labelKey: "firstIdentify",
                required: true,
                message: "请输入实体仓编码",
                autocomplete: "off",
                maxLength: 100,
            },
            {
                type: "INPUT",
                labelName: "渠道编码",
                labelKey: "secondIdentify",
                required: false,
                message: "请输入渠道编码",
                autocomplete: "off",
                maxLength: 100,
            },
            {
                type: "INPUT",
                labelName: "店铺id",
                labelKey: "thirdIdentify",
                required: false,
                message: "请输入店铺id",
                autocomplete: "off",
                maxLength: 100,
            },
            {
                type: "TEXTAREA",
                labelName: "备注",
                labelKey: "remark",
                required: false,
                message: "请输入备注",
                maxLength: 512,
            },
        ];
        this.formRef = React.createRef();
    }
    componentDidMount() {
        this.getConfigList();
    }
    // 获取configList 里面的下拉数据
    getConfigList() {
        let configList = this.state.configList;
        configList.map(item => {
            if (item.ccs) {
                lib.request({
                    url: item.ccs,
                    needMask: false,
                    success: res => {
                        item.list = res || [];
                        this.setState({ configList });
                    },
                });
            }
        });
        this.setState({ configList });
    }

    renderModal() {
        let props = {
            title: this.state.modalTitle,
            onOk: this.handleOk.bind(this),
            onCancel: this.handleCancel.bind(this),
            configList: this.state.configList,
            visible: this.state.visible,
            editRow: this.state.editRow,
        };
        return (
            <React.Fragment>
                <NewModal {...props} />
            </React.Fragment>
        );
    }

    handleOk(values, modalForm) {
        if (this.state.editRow) {
            values.id = this.state.editRow.id;
        }
        for (let i in values) {
            if (values[i] === null) {
                delete values[i];
            }
        }
        lib.request({
            url: this.state.upUrl,
            data: values,
            method: "POST",
            needMask: true,
            success: res => {
                if (res) {
                    this.setState({
                        visible: false,
                        editRow: null,
                    });
                    this.load(true);
                }
            },
        });
    }

    handleCancel() {
        this.setState({
            visible: false,
            editRow: null,
        });
    }

    handleEnable(row, enable) {
        let data = JSON.parse(JSON.stringify(row));
        data.enable = enable;
        lib.request({
            url: this.state.upUrl,
            data,
            method: "POST",
            needMask: true,
            success: res => {
                if (res) {
                    this.setState({
                        checked: enable ? false : true,
                    });
                    this.load(true);
                    message.success(enable ? "启用成功" : "禁用成功");
                }
            },
        });
    }

    renderRightOperation() {
        return (
            <React.Fragment>
                <Button
                    type="primary"
                    onClick={() => {
                        this.setState({
                            visible: true,
                            modalTitle: "新增路由",
                            editRow: null,
                        });
                        this.getConfigList();
                    }}>
                    新增路由
                </Button>
            </React.Fragment>
        );
    }

    enableStatus(row) {
        return (
            <React.Fragment>
                {row.enable === 1 && (
                    <Switch
                        checked={row.enable}
                        onChange={() => {
                            Modal.confirm({
                                cancelText: "取消",
                                okText: "确定",
                                title: "提示",
                                content: "是否禁用?",
                                onOk: () => {
                                    this.handleEnable(row, 0); //传递当前行的数据，与要传递的参数enable
                                },
                            });
                        }}></Switch>
                )}
                {row.enable === 0 && <Switch checked={row.enable} onChange={() => this.handleEnable(row, 1)}></Switch>}
            </React.Fragment>
        );
    }
    myOperation(row) {
        return (
            <Space>
                {row.enable === 0 && (
                    <React.Fragment>
                        <span
                            className="link"
                            onClick={() => {
                                row.routeId = String(row.routeId);
                                this.setState({
                                    editRow: row,
                                    visible: true,
                                    modalTitle: "编辑路由",
                                });
                                this.getConfigList();
                            }}>
                            编辑
                        </span>
                    </React.Fragment>
                )}
            </Space>
        );
    }
}
export default App;
