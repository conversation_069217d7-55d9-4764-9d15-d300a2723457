import React, { useState, useEffect } from "react";
import {
    Button,
    Input,
    Select,
    Form,
    message,
    Tabs,
    Row,
    Col,
    Table,
    Descriptions,
    Upload,
    Modal,
    InputNumber,
    Space,
    Steps,
    Switch,
} from "antd";
// import "../base/base.less";
import event from "../common/event";
import TextArea from "antd/lib/input/TextArea";
import { ConfigCenter, lib } from "react-single-app";
import "./customs-clearance-detail.less";

const { Step } = Steps;

const TabPane = Tabs.TabPane;
const FormItem = Form.Item;
const Option = Select.Option;
const DesItem = Descriptions.Item;

class App extends React.Component {
    constructor() {
        super();
        this.state = {
            dataList: [],
            listItems: [],
            nowItem: "",
            detail: {},
            activeKey: "1",
            oldOrNew: "",
            countries: [],
            currency: [],
            type: "create",
            statusList: [],
            step: lib.getParam("auditStatus") == "AUDITING" ? 1 : 2,
            transList: [],
            // status: lib.getParam('auditStatus')=='AUDITING'?'error':'process'
        };
    }

    componentDidMount() {
        let id = lib.getParam("id");
        let type = lib.getParam("type");
        let auditStatus = lib.getParam("auditStatus");
        // 获取运输方式
        lib.request({
            url: "/ccs/invenorder/listTransportV2",
            needMask: true,
            success: res => {
                this.setState({
                    transList: res,
                });
            },
        });
        this.setState({ id, type, auditStatus });

        // 获取单证类型
        lib.request({
            url: "/ccs/invenorder/list-relation-status",
            needMask: true,
            success: res => {
                if (res) {
                    this.setState(
                        {
                            statusList: res,
                            relType: res[0].id,
                        },
                        () => {
                            this.loads(id, true);
                        },
                    );
                }
            },
        });

        // 获取国家数据
        lib.request({
            url: "/ccs/customs/listCountry",
            success: res => {
                this.setState({
                    countries: res,
                });
            },
        });
        // 获取币制
        lib.request({
            url: "/ccs/customs/listCurrency",
            success: res => {
                this.setState({
                    currency: res,
                });
            },
        });
    }

    fetchRecordList(item, detail) {
        return new Promise((resolve, reject) => {
            if (item.oldOrNew === "new") {
                resolve([]);
            } else {
                lib.request({
                    url: "/ccs/invenorder/listSeqNoByProductId",
                    data: {
                        productId: item.productId,
                        areaBookId: detail.areaBookId,
                    },
                    needMask: true,
                    success: resolve,
                    fail: reject,
                });
            }
        });
    }

    loads(id, bool) {
        lib.request({
            url: "/ccs/invenorder/view-inventory-order",
            data: {
                id: lib.getParam("id"),
            },
            needMask: true,
            success: res => {
                if (res) {
                    let detail = res;
                    if (detail.inventoryOrderItemDTOList && detail.inventoryOrderItemDTOList.length && bool) {
                        this.setState({
                            activeKey: "2",
                        });
                    }
                    if (detail.inventoryOrderRelationDTOList) {
                        detail.inventoryOrderRelationDTOList.map(item => {
                            if (item.relTypeDesc === "运单") {
                                item.yundan = item.relNo;
                            } else if (item.relTypeDesc === "报关单") {
                                item.baoguan = item.relNo;
                            }
                        });
                    }
                    if (bool && this.state.type === "edit") {
                        let listItems = detail.inventoryOrderItemDTOList;
                        let promiseList = [];
                        listItems.map((item, index) => {
                            item.declareUnitQfy = parseInt(item.declareUnitQfy);
                            item.index = `${item.productId}.${index}`;
                            item.oldOrNew = item.isNew;
                            item.netweights = item.netweight;
                            item.netweight = (item.netweight * item.declareUnitQfy).toFixed(2);
                            item.firstUnitQfys = parseFloat((item.firstUnitQfy * item.declareUnitQfy).toFixed(4));
                            item.secondUnitQfys = parseFloat((item.secondUnitQfy * item.declareUnitQfy).toFixed(4));
                            promiseList.push(this.fetchRecordList(item, detail));
                        });
                        Promise.all(promiseList)
                            .then(values => {
                                values.map((item, index) => {
                                    listItems[index].recordList = values[index];
                                });
                                this.setState({
                                    activeKey: "1",
                                    listItems,
                                });
                            })
                            .catch(err => {
                                listItems.map((item, index) => {
                                    item.recordList = [];
                                });
                                this.setState({
                                    activeKey: "1",
                                    listItems,
                                });
                            });
                    }
                    if (lib.getParam("title") === "查看") {
                        let listItems = detail.inventoryOrderItemDTOList;
                        listItems.map((item, index) => {
                            item.declareUnitQfy = parseInt(item.declareUnitQfy);
                            item.index = `${item.productId}.${index}`;
                            item.oldOrNew = item.isNew;
                            item.netweights = item.netweight;
                            item.netweight = (item.netweight * item.declareUnitQfy).toFixed(2);
                            item.firstUnitQfys = parseFloat((item.firstUnitQfy * item.declareUnitQfy).toFixed(4));
                            item.secondUnitQfys = parseFloat((item.secondUnitQfy * item.declareUnitQfy).toFixed(4));
                        });
                        this.setState({
                            activeKey: "2",
                            listItems,
                        });
                    }
                    if (detail.inveBusinessTypeDesc === "一线入境") {
                        this.setState({
                            relType: this.state.statusList[1].id,
                        });
                    } else {
                        this.setState({
                            relType: this.state.statusList[0].id,
                        });
                    }
                    this.setState({
                        detail,
                        channel: res.channel,
                        pickUpNo: detail.pickUpNo,
                        entryExitCustoms: detail.entryExitCustoms,
                        transportMode: detail.transportMode,
                        shipmentCountry: detail.shipmentCountry,
                        inAccountBook: detail.inAccountBook,
                        outAccountBook: detail.outAccountBook,
                        customsInvtType: detail.customsInvtType,
                        customsEntryNo: detail.customsEntryNo,
                    });
                }
            },
        });
    }

    renderForm(item, index, bool) {
        const { detail } = this.state;
        return (
            <FormItem {...{ labelCol: { span: 4 }, wrapperCol: { span: 4 } }} label={item.title} key={index}>
                {item.render && item.render ? item.render() : detail[item.dataIndex] ? detail[item.dataIndex] : ""}
            </FormItem>
        );
    }

    submitHandle() {
        let { listItems, id } = this.state;
        if (listItems.length === 0) {
            message.warning("请选择数据");
            return;
        }
        let flag = false;
        listItems.map(item => {
            item.isNew = item.oldOrNew;
            item.netweight = item.netweights;
            if (!item.declareUnitQfy) {
                flag = true;
                message.warning(`${item.goodsName}的申报单位数量为0`);
            }
        });
        if (flag) return;
        lib.request({
            url: "/ccs/invenorder/build-inventory-order-item",

            data: {
                invenOrderId: id,
                listOrderItems: listItems,
            },
            method: "POST",
            needMask: true,
            success: res => {
                if (res.errorMessage) {
                    message.warning(res.errorMessage);
                } else {
                    message.success("编辑成功");
                    lib.closePage();
                }
            },
        });
    }

    previewFunc(row) {
        let newWindow = window.open();
        let img = new Image();
        img.src = row.attachPath;
        newWindow.document.body.appendChild(img);
    }

    downloadFunc(row) {
        lib.request({
            url: "/ccs/invenorder/trace-log-download",
            data: {
                id: row.id,
            },
            needMask: true,
            success: res => {
                if (res) {
                    window.open(row.attachPath);
                }
            },
        });
    }

    deleteFunc(row, id) {
        Modal.confirm({
            cancelText: "取消",
            okText: "确定",
            title: "确认删除该附件吗？",
            onOk: () => {
                lib.request({
                    url: "/ccs/invenorder/delete-attach",
                    data: {
                        attachId: row.id,
                        orderId: id,
                    },
                    needMask: true,
                    success: res => {
                        if (res) {
                            message.success("删除成功");
                            this.loads(id);
                        }
                    },
                });
            },
        });
    }

    oldSelectChange(e) {
        this.setState(
            {
                oldOrNew: e,
            },
            () => {
                lib.request({
                    // url: "/ccs/customsBook/invenorder/findListBy",
                    url: "/ccs/customsGoods/findListBy",
                    data: {
                        oldOrNew: this.state.oldOrNew,
                        bookId: String(this.state.detail.areaBookId),
                    },
                    needMask: true,
                    success: res => {
                        if (res) {
                            let dataList = res;
                            dataList.map((item, index) => {
                                item.id = `${item.goodsSeqNo}.${item.productId}`;
                            });
                            this.setState({
                                dataList,
                                value: "",
                                nowItem: "",
                            });
                        }
                    },
                });
            },
        );
    }

    addHandle() {
        const { dataList, listItems, nowItem, value, detail } = this.state;
        if (!this.state.oldOrNew) {
            message.warning("请选择是否新品");
            return;
        }
        if (!nowItem) {
            message.warning("请选择料号");
            return;
        }
        let items,
            flag = false;
        dataList.map(item => {
            if (item.id === value) {
                items = item;
            }
        });
        listItems.map(item => {
            if (`${item.goodsSeqNo}${item.productId}` === `${items.goodsSeqNo}${items.productId}`) {
                flag = true;
            }
        });
        if (flag) {
            message.warning("请勿重复添加");
            return;
        }
        lib.request({
            url: "/ccs/customsGoods/findItemDetailBy",
            data: {
                oldOrNew: this.state.oldOrNew,
                productId: items.productId,
                bookId: String(this.state.detail.areaBookId),
                goodsSeqNo: items.goodsSeqNo,
            },
            needMask: true,
            success: res => {
                if (res) {
                    listItems.push(res.result);
                    listItems.map((item, index) => {
                        item.index = `${item.productId}.${index}`;
                        item.netweights = item.netweight;
                    });
                    if (res.result.oldOrNew !== "new") {
                        this.fetchRecordList(listItems[listItems.length - 1], detail).then(values => {
                            listItems[listItems.length - 1].recordList = values;
                            this.setState(
                                {
                                    listItems: [],
                                },
                                () => this.setState({ listItems }),
                            );
                        });
                    } else {
                        this.setState(
                            {
                                listItems: [],
                            },
                            () => this.setState({ listItems }),
                        );
                    }
                }
            },
        });
    }

    modalCancel() {
        this.setState({
            importVisible: false,
        });
    }

    modalOk() {
        let { relType, relNos, id, listItems } = this.state;
        if (relNos.trim() === "") {
            message.warning("请输入关联单证号");
            return;
        }
        relNos = relNos.split("\n");
        lib.request({
            url: "/ccs/invenorder/build-inventory-order-relation",
            data: {
                relType,
                relNos,
                invenOrderId: Number(lib.getParam("id")),
            },
            method: "POST",
            needMask: true,
            success: res => {
                if (!res.errorMessage) {
                    message.success("导入成功");
                    this.setState({
                        importVisible: false,
                        listItems: res.result,
                    });
                    this.loads(id);
                } else {
                    message.error(res.errorMessage);
                }
            },
        });
    }

    editModalOk() {
        let { editRelNo, editRow, id, listItems } = this.state;
        if (editRelNo) {
            lib.request({
                url: "/ccs/invenorder/update-inventory-order-relation",
                data: {
                    id: editRow.id,
                    relNo: editRelNo,
                },
                needMask: true,
                success: res => {
                    if (res.errorMessage) {
                        message.error(res.errorMessage);
                    } else {
                        this.setState({
                            editVisible: false,
                            listItems: res.result,
                        });
                        this.loads(id);
                    }
                },
            });
        } else {
            message.warning("请输入单证号");
        }
    }

    editModalCancel() {
        this.setState({
            editVisible: false,
        });
    }

    rejectModal() {
        return (
            <Modal
                cancelText="取消"
                okText="确定"
                title="审核驳回"
                open={this.state.refuseModalVisible}
                onOk={() => this.batchRefuse()}
                onCancel={() =>
                    this.setState({
                        refuseModalVisible: false,
                        auditId: "",
                    })
                }
                destroyOnClose={true}>
                <div style={{ padding: 10 }}>请输入驳回原因</div>
                <TextArea
                    onChange={e => {
                        this.setState({
                            reason: e.currentTarget.value,
                        });
                    }}
                />
            </Modal>
        );
    }
    batchRefuse() {
        let { reason } = this.state;
        lib.request({
            url: "/ccs/invenorder/reject",
            data: {
                id: lib.getParam("id"),
                reason,
            },
            needMask: true,
            success: res => {
                message.success("审核驳回");
                this.setState({
                    step: 2,
                    status: "error",
                    reason: "",
                    refuseModalVisible: false,
                });
                lib.closePage();
                this.loads(lib.getParam("id"));
            },
        });
    }

    submitHandle2() {
        let {
            id,
            entryExitCustoms,
            pickUpNo,
            transportMode,
            shipmentCountry,
            outAccountBook,
            inAccountBook,
            customsInvtType,
            customsEntryNo,
        } = this.state;
        lib.request({
            url: "/ccs/invenorder/editInventoryHead",
            data: {
                id,
                entryExitCustoms,
                pickUpNo,
                transportMode,
                shipmentCountry,
                outAccountBook,
                inAccountBook,
                customsInvtType,
                customsEntryNo,
            },
            needMask: true,
            success: res => {
                if (res.errorMessage) {
                    message.warning(res.errorMessage);
                } else {
                    message.success("编辑成功");
                    this.loads(id);
                }
            },
        });
    }

    render() {
        let {
            dataList,
            listItems,
            nowItem,
            value,
            detail,
            activeKey,
            id,
            type,
            importVisible,
            statusList,
            editVisible,
            editRow,
            relType,
            transportMode,
            transList,
            entryExitCustoms,
            outAccountBook,
            inAccountBook,
            shipmentCountry,
            countries,
            pickUpNo,
            customsInvtType,
            customsEntryNo,
            auditStatus,
            previewModalVisible,
            previewRow,
        } = this.state;
        let disabledBool = false;
        if (auditStatus === "null" || auditStatus === "AUDITED") {
            disabledBool = true;
        }
        let columns = [
            {
                title: "行号",
                width: 100,
                render: (text, record, index) => {
                    return index + 1;
                },
            },
            {
                title: "是否新品",
                dataIndex: "oldOrNew",
                width: 100,
                render: param => {
                    return param === "new" ? "是" : "否";
                },
            },
            {
                title: "商品sku",
                dataIndex: "skuId",
                width: 160,
            },
            {
                title: "商品料号",
                dataIndex: "productId",
                width: 160,
            },
            {
                title: "备案序号",
                dataIndex: "goodsSeqNo",
                width: 240,
                render: (text, record, index) => {
                    return record.oldOrNew === "new" ? (
                        ""
                    ) : (
                        <Select
                            value={{ value: text }}
                            labelInValue
                            style={{ width: 200 }}
                            onChange={e => {
                                listItems[index].goodsSeqNo = e.label;
                                lib.request({
                                    url: "/ccs/invenorder/getInventoryOrderItemByItemId",
                                    data: {
                                        bookItemId: e.value,
                                    },
                                    needMask: true,
                                    success: res => {
                                        let declareUnitQfy = parseInt(listItems[index].declareUnitQfy);
                                        listItems[index].goodsName = res.goodsName;
                                        listItems[index].hsCode = res.hsCode;
                                        listItems[index].goodsModel = res.goodsModel;
                                        listItems[index].unitDesc = res.unitDesc;
                                        listItems[index].firstUnitDesc = res.firstUnitDesc;
                                        listItems[index].firstUnitQfy = res.firstUnitQfy;
                                        listItems[index].firstUnitQfys = parseFloat(
                                            (res.firstUnitQfy * declareUnitQfy).toFixed(4),
                                        );
                                        listItems[index].secondUnitDesc = res.secondUnitDesc;
                                        listItems[index].secondUnitQfy = res.secondUnitQfy || 0;
                                        listItems[index].secondUnitQfys = parseFloat(
                                            ((res.secondUnitQfy || 0) * declareUnitQfy).toFixed(4),
                                        );
                                        listItems[index].netweight = res.netweight;
                                        listItems[index].netweights = res.netweight;
                                        this.setState({
                                            listItems,
                                        });
                                    },
                                });
                            }}>
                            {record.recordList?.map(item => (
                                <Option value={item.id} key={item.id}>
                                    {item.name}
                                </Option>
                            ))}
                        </Select>
                    );
                },
            },
            {
                title: "商品名称",
                dataIndex: "goodsName",
                width: 300,
            },
            {
                title: "商品编码",
                dataIndex: "hsCode",
                width: 130,
            },
            {
                title: "规格型号",
                dataIndex: "goodsModel",
                width: 130,
            },
            {
                title: "申报计量单位",
                dataIndex: "unitDesc",
                width: 120,
            },
            {
                title: "申报数量",
                dataIndex: "declareUnitQfy",
                width: 120,
                render: (item, record, inde) => {
                    return (
                        <InputNumber
                            value={item || 0}
                            min={0}
                            max={99999999}
                            onChange={e => {
                                e = parseInt(e) || 0;
                                listItems[inde].declareUnitQfy = e;
                                listItems[inde].declareTotalPrice = parseFloat(
                                    (listItems[inde].declarePrice * listItems[inde].declareUnitQfy).toFixed(2),
                                );
                                listItems[inde].netweight = parseFloat(
                                    (record.netweights * listItems[inde].declareUnitQfy).toFixed(2),
                                );
                                listItems[inde].firstUnitQfys = parseFloat(
                                    (record.firstUnitQfy * listItems[inde].declareUnitQfy).toFixed(4),
                                );
                                listItems[inde].secondUnitQfys = parseFloat(
                                    (record.secondUnitQfy * listItems[inde].declareUnitQfy).toFixed(4),
                                );
                                this.setState({
                                    listItems,
                                });
                            }}
                        />
                    );
                },
            },
            {
                title: "法定计量单位",
                dataIndex: "firstUnitDesc",
                width: 120,
            },
            {
                title: "法定数量（单）",
                dataIndex: "firstUnitQfy",
                width: 140,
                render: (text, record, index) => {
                    return (
                        <InputNumber
                            value={text || 0}
                            min={0}
                            max={99999999}
                            onChange={e => {
                                if (!e) e = 0;
                                if (String(e).endsWith(".")) return;
                                e = parseFloat(e.toFixed(4));
                                listItems[index].firstUnitQfy = e;
                                if (record.declareUnitQfy) {
                                    listItems[index].firstUnitQfys = parseFloat((e * record.declareUnitQfy).toFixed(4));
                                }
                                this.setState({
                                    listItems,
                                });
                            }}
                        />
                    );
                },
            },
            {
                title: "法定数量（总）",
                dataIndex: "firstUnitQfys",
                width: 140,
            },
            {
                title: "法定第二计量",
                dataIndex: "secondUnitDesc",
                width: 120,
            },
            {
                title: "第二法定数量（单）",
                dataIndex: "secondUnitQfy",
                width: 140,
                render: item => {
                    return item ? item : "";
                },
            },
            {
                title: "第二法定数量（总）",
                dataIndex: "secondUnitQfys",
                width: 140,
                render: item => {
                    return item ? item : "";
                },
            },
            {
                title: "净重",
                dataIndex: "netweight",
                width: 80,
                render: item => {
                    return item ? item : "";
                },
            },
            {
                title: "申报单价",
                dataIndex: "declarePrice",
                width: 140,
                render: (item, index, inde) => {
                    return (
                        <InputNumber
                            value={item || 0}
                            min={0}
                            max={99999999}
                            onChange={e => {
                                e = e || 0;
                                listItems[inde].declarePrice = parseFloat(e.toFixed(2));
                                listItems[inde].declareTotalPrice = parseFloat(
                                    ((listItems[inde].declareUnitQfy || 0) * listItems[inde].declarePrice).toFixed(2),
                                );
                                this.setState({
                                    listItems,
                                });
                            }}
                        />
                    );
                },
            },
            {
                title: "申报总价",
                dataIndex: "declareTotalPrice",
                width: 140,
            },
            {
                title: "原产国(地区)",
                dataIndex: "originCountryDesc",
                width: 160,
            },
            {
                title: "最终目的国(地区)",
                dataIndex: "destinationCountry",
                width: 200,
                render: (item, index, inde) => {
                    return (
                        <Select
                            style={{ width: "100%" }}
                            value={item}
                            showSearch
                            optionFilterProp="children"
                            onChange={e => {
                                listItems[inde].destinationCountry = e;
                                this.setState({
                                    listItems,
                                });
                            }}>
                            {this.state.countries.map(item => (
                                <Option value={item.id} key={item.id}>
                                    {item.name}
                                </Option>
                            ))}
                        </Select>
                    );
                },
            },
            {
                title: "商品条码",
                dataIndex: "goodsBar",
                width: 200,
            },
            {
                title: "币制",
                dataIndex: "currency",
                width: 260,
                render: (item, index, inde) => {
                    return (
                        <Select
                            style={{ width: "100%" }}
                            value={item}
                            showSearch
                            optionFilterProp="children"
                            onChange={e => {
                                listItems[inde].currency = e;
                                this.setState({
                                    listItems,
                                });
                            }}>
                            {this.state.currency.map(item => (
                                <Option value={item.id} key={item.id}>
                                    {item.name}
                                </Option>
                            ))}
                        </Select>
                    );
                },
            },
            {
                title: "征免方式",
                dataIndex: "avoidTaxMethod",
                width: 200,
                render: (item, index, inde) => {
                    return (
                        <Input
                            value={item}
                            onChange={e => {
                                listItems[inde].avoidTaxMethod = e.currentTarget.value;
                                this.setState({
                                    listItems,
                                });
                            }}></Input>
                    );
                },
            },
            {
                title: "操作",
                width: 120,
                fixed: "right",
                render: (text, row, index) => {
                    return (
                        <a
                            onClick={() => {
                                let modal = Modal.confirm({
                                    cancelText: "取消",
                                    okText: "确定",
                                    title: "提示",
                                    content: "确认删除吗？",
                                    onOk: () => {
                                        listItems.splice(index, 1);
                                        this.setState(
                                            {
                                                listItems: [],
                                            },
                                            () => this.setState({ listItems }),
                                        );
                                        modal.destroy();
                                        console.log(listItems);
                                    },
                                });
                            }}>
                            删除
                        </a>
                    );
                },
            },
        ];
        let thecolumns = [
            {
                title: "是否新品",
                dataIndex: "isNew",
                width: 100,
                render: param => {
                    return param === "new" ? "是" : "否";
                },
            },
            {
                title: "商品sku",
                dataIndex: "skuId",
                width: 160,
            },
            {
                title: "商品料号",
                dataIndex: "productId",
                width: 240,
            },
            {
                title: "备案序号",
                dataIndex: "goodsSeqNo",
                width: 240,
                render: (text, record, index) => {
                    return record.oldOrNew === "new" ? (
                        ""
                    ) : lib.getParam("auditStatus") === "AUDITING" ? (
                        <Select
                            value={{ value: text }}
                            labelInValue
                            style={{ width: 200 }}
                            onChange={e => {
                                listItems[index].goodsSeqNo = e.label;
                                lib.request({
                                    url: "/ccs/invenorder/getInventoryOrderItemByItemId",
                                    data: {
                                        bookItemId: e.value,
                                    },
                                    needMask: true,
                                    success: res => {
                                        let declareUnitQfy = parseInt(listItems[index].declareUnitQfy);
                                        listItems[index].goodsName = res.goodsName;
                                        listItems[index].hsCode = res.hsCode;
                                        listItems[index].goodsModel = res.goodsModel;
                                        listItems[index].unitDesc = res.unitDesc;
                                        listItems[index].firstUnitDesc = res.firstUnitDesc;
                                        listItems[index].firstUnitQfy = res.firstUnitQfy;
                                        listItems[index].firstUnitQfys = parseFloat(
                                            (res.firstUnitQfy * declareUnitQfy).toFixed(4),
                                        );
                                        listItems[index].secondUnitDesc = res.secondUnitDesc;
                                        listItems[index].secondUnitQfy = res.secondUnitQfy || 0;
                                        listItems[index].secondUnitQfys = parseFloat(
                                            ((res.secondUnitQfy || 0) * declareUnitQfy).toFixed(4),
                                        );
                                        listItems[index].netweight = res.netweight;
                                        listItems[index].netweights = res.netweight;
                                        this.setState({
                                            listItems,
                                        });
                                    },
                                });
                            }}>
                            {record.recordList?.map(item => (
                                <Option value={item.id} key={item.id}>
                                    {item.name}
                                </Option>
                            ))}
                        </Select>
                    ) : (
                        text
                    );
                },
            },
            {
                title: "商品名称",
                dataIndex: "goodsName",
                width: 300,
            },
            {
                title: "商品编码",
                dataIndex: "hsCode",
                width: 130,
            },
            {
                title: "规格型号",
                dataIndex: "goodsModel",
                width: 130,
            },
            {
                title: "申报计量单位",
                dataIndex: "unitDesc",
                width: 120,
            },
            {
                title: "申报数量",
                dataIndex: "declareUnitQfy",
                width: 120,
                render: (item, record, inde) => {
                    return (
                        <InputNumber
                            value={item || 0}
                            min={0}
                            max={99999999}
                            onChange={e => {
                                e = parseInt(e) || 0;
                                listItems[inde].declareUnitQfy = e;
                                listItems[inde].declareTotalPrice = parseFloat(
                                    (listItems[inde].declarePrice * listItems[inde].declareUnitQfy).toFixed(2),
                                );
                                listItems[inde].netweight = parseFloat(
                                    (record.netweights * listItems[inde].declareUnitQfy).toFixed(2),
                                );
                                listItems[inde].firstUnitQfys = parseFloat(
                                    (record.firstUnitQfy * listItems[inde].declareUnitQfy).toFixed(4),
                                );
                                listItems[inde].secondUnitQfys = parseFloat(
                                    (record.secondUnitQfy * listItems[inde].declareUnitQfy).toFixed(4),
                                );
                                this.setState({
                                    listItems,
                                });
                            }}
                        />
                    );
                },
            },
            {
                title: "法定计量单位",
                dataIndex: "firstUnitDesc",
                width: 120,
            },
            {
                title: "法定数量(单)",
                dataIndex: "firstUnitQfy",
                width: 140,
                render: (text, record, index) => {
                    return (
                        <InputNumber
                            value={text || 0}
                            min={0}
                            max={99999999}
                            onChange={e => {
                                if (!e) e = 0;
                                if (String(e).endsWith(".")) return;
                                e = parseFloat(e.toFixed(4));
                                listItems[index].firstUnitQfy = e;
                                if (record.declareUnitQfy) {
                                    listItems[index].firstUnitQfys = parseFloat((e * record.declareUnitQfy).toFixed(4));
                                }
                                this.setState({
                                    listItems,
                                });
                            }}
                        />
                    );
                },
            },
            {
                title: "法定数量(总)",
                dataIndex: "firstUnitQfys",
                width: 140,
            },
            {
                title: "法定第二计量",
                dataIndex: "secondUnitDesc",
                width: 120,
            },
            {
                title: "第二法定数量(单)",
                dataIndex: "secondUnitQfy",
                width: 140,
                render: item => {
                    return item ? item : "";
                },
            },
            {
                title: "第二法定数量(总)",
                dataIndex: "secondUnitQfy",
                width: 140,
                render: item => {
                    return item ? item : "";
                },
            },
            {
                title: "净重",
                dataIndex: "netweight",
                width: 80,
                render: item => {
                    return item ? item : "";
                },
            },
            {
                title: "申报单价",
                dataIndex: "declarePrice",
                width: 80,
            },
            {
                title: "申报总价",
                dataIndex: "declareTotalPrice",
                width: 80,
            },
            {
                title: "原产国(地区)",
                dataIndex: "originCountryDesc",
                width: 160,
            },
            {
                title: "最终目的国(地区)",
                dataIndex: "destinationCountryDesc",
                width: 160,
            },
            {
                title: "商品条码",
                dataIndex: "goodsBar",
                width: 200,
            },
            {
                title: "币制",
                dataIndex: "currencyDesc",
                width: 260,
            },
            {
                title: "征免方式",
                dataIndex: "avoidTaxMethod",
                width: 200,
            },
        ];
        let tableColumns = [
            {
                title: "理货编号",
                dataIndex: "tallyOrderNo",
            },
            {
                title: "出库单号",
                dataIndex: "outBoundNo",
            },
            {
                title: "操作",
                render: row => {
                    return (
                        <Space>
                            <span
                                className="link"
                                onClick={() => {
                                    lib.request({
                                        url: "/ccs/invenorder/getTallyReportDetailById",
                                        data: {
                                            id: row.id,
                                        },
                                        needMask: true,
                                        success: res => {
                                            this.setState({
                                                previewModalVisible: true,
                                                previewRow: res || [],
                                            });
                                        },
                                    });
                                }}>
                                预览
                            </span>
                        </Space>
                    );
                },
            },
        ];
        let previewColumns = [
            {
                title: "理货编号",
                dataIndex: "tallyOrderNo",
                width: 150,
            },
            {
                title: "出库单号",
                dataIndex: "outBoundNo",
                width: 180,
            },
            {
                title: "货品名称",
                dataIndex: "goodsName",
                width: 150,
            },
            {
                title: "SKU",
                dataIndex: "sku",
                width: 150,
            },
            {
                title: "料号",
                dataIndex: "productId",
                width: 100,
            },
            {
                title: "计划理货数量",
                dataIndex: "planTallyQty",
                width: 120,
            },
            {
                title: "实际理货数量",
                dataIndex: "actualTallyQty",
                width: 120,
            },
            {
                title: "备注",
                dataIndex: "remark",
                width: 120,
            },
        ];
        let forms = [
            {
                title: "预录入核注编号",
                dataIndex: "preNo",
                render: item => {
                    return item ? item : "";
                },
            },
            {
                title: "清关单号",
                dataIndex: "inveCustomsSn",
            },
            {
                title: "核注清单编号",
                dataIndex: "refHzInveNo",
            },
            {
                title: "进出标志",
                dataIndex: "inOrOutFlag",
            },
            {
                title: "区内账册编号",
                dataIndex: "areaBookNo",
            },
            {
                title: "业务类型",
                dataIndex: "inveBusinessTypeDesc",
            },
            {
                title: "运输方式",
                dataIndex: "transportModeName",
            },
            {
                title: "进出境关别",
                dataIndex: "entryExitCustoms",
            },
        ];
        if (detail) {
            if (detail.channel) {
                // erp传过来的单据
                forms[7].render = (text, record, index) => {
                    return (
                        <Input
                            style={{ width: 220 }}
                            value={entryExitCustoms || text}
                            onChange={e => {
                                this.setState({
                                    entryExitCustoms: e.currentTarget.value,
                                });
                            }}></Input>
                    );
                };
                if (detail.inveBusinessTypeDesc === "区间流转(出)" || detail.inveBusinessTypeDesc === "区内流转(出)") {
                    columns.splice(
                        8,
                        0,
                        {
                            title: "计划申报数量",
                            dataIndex: "planDeclareQty",
                            width: 160,
                        },
                        {
                            title: "出库单号",
                            dataIndex: "outBoundNo",
                            width: 160,
                        },
                        {
                            title: "实际理货数量",
                            dataIndex: "actualTallyQty",
                            width: 160,
                        },
                    );
                    thecolumns.splice(
                        8,
                        0,
                        {
                            title: "计划申报数量",
                            dataIndex: "planDeclareQty",
                            width: 160,
                        },
                        {
                            title: "出库单号",
                            dataIndex: "outBoundNo",
                            width: 160,
                        },
                        {
                            title: "实际理货数量",
                            dataIndex: "actualTallyQty",
                            width: 160,
                        },
                    );
                    columns[11].title = "实际申报数量";
                    thecolumns[11].title = "实际申报数量";
                }
            }
            if (detail.inveBusinessTypeDesc === "区间流转(出)" || detail.inveBusinessTypeDesc === "区内流转(出)") {
                forms = forms.concat([
                    {
                        title: "关联转入账册",
                        dataIndex: "inAccountBook",
                        render: (text, record, index) => {
                            return (
                                <Input
                                    style={{ width: 220 }}
                                    value={inAccountBook}
                                    onChange={e => {
                                        this.setState({
                                            inAccountBook: e.currentTarget.value,
                                        });
                                    }}></Input>
                            );
                        },
                    },
                    {
                        title: "预计出区日期",
                        dataIndex: "expectedOutAreaTime",
                    },
                ]);
                if (forms.filter(item => item.title === "关联核注清单编号").length === 0) {
                    forms.push({
                        title: "关联核注清单编号",
                        dataIndex: "pickUpNo",
                        render: (text, record, index) => {
                            return (
                                <Input
                                    value={pickUpNo}
                                    style={{ width: 220 }}
                                    onChange={e => {
                                        this.setState({
                                            pickUpNo: e.currentTarget.value,
                                        });
                                    }}
                                />
                            );
                        },
                    });
                }
            } else if (
                detail.inveBusinessTypeDesc === "区间流转(入)" ||
                detail.inveBusinessTypeDesc === "区内流转(入)"
            ) {
                forms.push({
                    title: "关联转出账册",
                    dataIndex: "outAccountBook",
                    render: (text, record, index) => {
                        return (
                            <Input
                                style={{ width: 220 }}
                                value={outAccountBook}
                                onChange={e => {
                                    this.setState({
                                        outAccountBook: e.currentTarget.value,
                                    });
                                }}></Input>
                        );
                    },
                });
            } else if (detail.inveBusinessTypeDesc === "一线入境") {
                forms = forms.concat([
                    {
                        title: "启运国",
                        dataIndex: "shipmentCountryName",
                    },
                    {
                        title: "预计到港日期",
                        dataIndex: "expectedToPortTime",
                    },
                    {
                        title: "区港联动",
                        dataIndex: "customsInvtType",
                        render: (text, record, index) => {
                            return (
                                <Switch
                                    disabled={disabledBool}
                                    checked={customsInvtType === "QUGANG"}
                                    onChange={e => {
                                        this.setState({
                                            customsInvtType: e ? "QUGANG" : "",
                                        });
                                    }}
                                />
                            );
                        },
                    },
                    {
                        title: "报关单号",
                        dataIndex: "customsEntryNo",
                        render: (text, record, index) => {
                            return (
                                <Input
                                    disabled={disabledBool}
                                    value={customsEntryNo}
                                    style={{ width: 220 }}
                                    onChange={e => {
                                        this.setState({
                                            customsEntryNo: e.currentTarget.value,
                                        });
                                    }}
                                />
                            );
                        },
                    },
                ]);
            }
        }

        let forms2 = [
            {
                title: "预录入核注编号",
                dataIndex: "preNo",
                render: item => {
                    return item ? item : "";
                },
            },
            {
                title: "清关单号",
                dataIndex: "inveCustomsSn",
            },
            {
                title: "核注清单编号",
                dataIndex: "refHzInveNo",
            },
            {
                title: "进出标志",
                dataIndex: "inOrOutFlag",
            },
            {
                title: "区内账册编号",
                dataIndex: "areaBookNo",
            },
            {
                title: "业务类型",
                dataIndex: "inveBusinessTypeDesc",
            },
        ];
        if (detail) {
            if (detail.inveBusinessTypeDesc !== "退货入区") {
                forms2 = forms2.concat([
                    {
                        title: "运输方式",
                        dataIndex: "transportMode",
                        render: (text, record, index) => {
                            return (
                                <Select
                                    style={{ width: 200 }}
                                    value={transportMode}
                                    onChange={transportMode => {
                                        this.setState({
                                            transportMode: String(transportMode),
                                        });
                                    }}>
                                    {transList &&
                                        transList.map((item, index) => {
                                            return (
                                                <Option value={item.value} key={index}>
                                                    {item.name}
                                                </Option>
                                            );
                                        })}
                                </Select>
                            );
                        },
                    },
                    {
                        title: "进出境关别",
                        dataIndex: "entryExitCustoms",
                        render: (text, record, index) => {
                            return (
                                <Input
                                    style={{ width: 220 }}
                                    value={entryExitCustoms || text}
                                    onChange={e => {
                                        this.setState({
                                            entryExitCustoms: e.currentTarget.value,
                                        });
                                    }}></Input>
                            );
                        },
                    },
                ]);
            }
            if (detail.inveBusinessTypeDesc === "一线入境") {
                forms2 = forms2.concat([
                    {
                        title: "启运国",
                        dataIndex: "shipmentCountry",
                        render: (text, record, index) => {
                            return (
                                <Select
                                    style={{ width: 200 }}
                                    value={shipmentCountry}
                                    onChange={shipmentCountry => {
                                        this.setState({
                                            shipmentCountry: String(shipmentCountry),
                                        });
                                    }}>
                                    {countries &&
                                        countries.map((item, index) => {
                                            return (
                                                <Option value={item.value} key={index}>
                                                    {item.name}
                                                </Option>
                                            );
                                        })}
                                </Select>
                            );
                        },
                    },
                    {
                        title: "区港联动",
                        dataIndex: "customsInvtType",
                        render: (text, record, index) => {
                            return (
                                <Switch
                                    disabled={disabledBool}
                                    checked={customsInvtType === "QUGANG"}
                                    onChange={e => {
                                        this.setState({
                                            customsInvtType: e ? "QUGANG" : "",
                                        });
                                    }}
                                />
                            );
                        },
                    },
                    {
                        title: "报关单号",
                        dataIndex: "customsEntryNo",
                        render: (text, record, index) => {
                            return (
                                <Input
                                    disabled={disabledBool}
                                    value={customsEntryNo}
                                    style={{ width: 220 }}
                                    onChange={e => {
                                        this.setState({
                                            customsEntryNo: e.currentTarget.value,
                                        });
                                    }}
                                />
                            );
                        },
                    },
                ]);
            }
            if (detail.inveBusinessTypeDesc === "区间流转(出)" || detail.inveBusinessTypeDesc === "区内流转(出)") {
                forms2.push({
                    title: "关联转入账册",
                    dataIndex: "inAccountBook",
                    render: (text, record, index) => {
                        return (
                            <Input
                                style={{ width: 220 }}
                                value={inAccountBook}
                                onChange={e => {
                                    this.setState({
                                        inAccountBook: e.currentTarget.value,
                                    });
                                }}></Input>
                        );
                    },
                });
                if (forms2.filter(item => item.title === "关联核注清单编号").length === 0) {
                    forms2.push({
                        title: "关联核注清单编号",
                        dataIndex: "pickUpNo",
                        render: (text, record, index) => {
                            return (
                                <Input
                                    value={pickUpNo}
                                    style={{ width: 220 }}
                                    onChange={e => {
                                        this.setState({
                                            pickUpNo: e.currentTarget.value,
                                        });
                                    }}
                                />
                            );
                        },
                    });
                }
            }
            if (detail.inveBusinessTypeDesc === "区间流转(入)" || detail.inveBusinessTypeDesc === "区内流转(入)") {
                forms2.push({
                    title: "关联转出账册",
                    dataIndex: "outAccountBook",
                    render: (text, record, index) => {
                        return (
                            <Input
                                style={{ width: 220 }}
                                value={outAccountBook}
                                onChange={e => {
                                    this.setState({
                                        outAccountBook: e.currentTarget.value,
                                    });
                                }}></Input>
                        );
                    },
                });
            }
        }

        const columns2 = [
            {
                title: "文件名称",
                dataIndex: "attachName",
            },
            {
                title: "操作",
                render: row => {
                    return (
                        <React.Fragment>
                            <Space>
                                {row.attachName.indexOf("jpg") !== -1 || row.attachName.indexOf("png") !== -1 ? (
                                    <a className="link" onClick={() => this.previewFunc(row)}>
                                        预览
                                    </a>
                                ) : row.attachName.indexOf("pdf") !== -1 ? (
                                    <a className="link" href={row.attachPath} target="_blank">
                                        预览
                                    </a>
                                ) : (
                                    <a
                                        className="link"
                                        href={`https://view.officeapps.live.com/op/view.aspx?src=${row.attachPath}`}
                                        target="_blank">
                                        预览
                                    </a>
                                )}
                                <a className="link" onClick={() => this.downloadFunc(row)}>
                                    下载
                                </a>
                                <a className="link" onClick={() => this.deleteFunc(row, id)}>
                                    删除
                                </a>
                            </Space>
                        </React.Fragment>
                    );
                },
            },
        ];
        const columns3 = [
            {
                title: "操作",
                dataIndex: "logName",
            },
            {
                title: "时间",
                dataIndex: "createTime",
                render: value => {
                    return new Date(value).format("yyyy-MM-dd hh:mm:ss");
                },
            },
            {
                title: "操作账号",
                dataIndex: "updateBy",
            },
            {
                title: "说明",
                dataIndex: "logDetail",
            },
        ];
        const columns4 = [
            {
                title: "运单号",
                dataIndex: "yundan",
            },
            {
                title: "报关单号",
                dataIndex: "baoguan",
            },
            {
                title: "操作",
                render: (item, row, index) => {
                    return (
                        type === "edit" && (
                            <React.Fragment>
                                <Space>
                                    <span
                                        className="link"
                                        onClick={() => {
                                            this.setState({
                                                editRow: row,
                                                editVisible: true,
                                                editRelNo: row.relNo,
                                            });
                                        }}>
                                        编辑
                                    </span>
                                    <span
                                        className="link"
                                        onClick={() => {
                                            const modal = Modal.confirm({
                                                cancelText: "取消",
                                                okText: "确定",
                                                title: "提示",
                                                content: "确认删除该条数据吗？",
                                                onOk: res => {
                                                    lib.request({
                                                        url: "/ccs/invenorder/delete-inventory-order-relation",
                                                        data: {
                                                            id: row.id,
                                                        },
                                                        needMask: true,
                                                        success: res => {
                                                            if (res) {
                                                                message.success("删除成功");
                                                                this.setState({
                                                                    listItems: res.result,
                                                                });
                                                                this.loads(this.state.id);
                                                                modal.destroy();
                                                            }
                                                        },
                                                    });
                                                },
                                                onCancel: res => {
                                                    modal.destroy();
                                                },
                                            });
                                        }}>
                                        删除
                                    </span>
                                </Space>
                            </React.Fragment>
                        )
                    );
                },
            },
        ];
        const formItemLayout = {
            labelCol: {
                xs: { span: 4 },
                sm: { span: 4 },
            },
            wrapperCol: {
                xs: { span: 20 },
                sm: { span: 16 },
            },
        };
        const props = {
            name: "file",
            accept: ".doc,.docx,.pdf,.jpg,.png,.xls,.xlsx",
            showUploadList: false,
            customRequest: info => {
                if (info.file) {
                    let arr = info.file.name.split(".");
                    let type = arr[arr.length - 1];
                    if (!["doc", "docx", "pdf", "jpg", "png", "xls", "xlsx"].includes(type)) {
                        message.warning("暂不支持此文件类型");
                        return;
                    }
                    let formData = new FormData();
                    formData.append("invenOrderId", this.state.id);
                    formData.append("file", info.file);
                    lib.request({
                        url: "/ccs/invenorder/upload-attach",
                        method: "POST",
                        data: formData,
                        needMask: true,
                        success: res => {
                            if (res) {
                                message.success("上传成功");
                                this.loads(id);
                            }
                        },
                    });
                }
            },
        };
        let showTab;
        if (detail.inventoryOrderItemDTOList && detail.inventoryOrderItemDTOList.length === 0) {
            showTab = 1;
        }
        if (detail.inventoryOrderItemDTOList && detail.inventoryOrderItemDTOList.length) {
            showTab = 2;
        }
        if (type === "edit") {
            showTab = 1;
        }
        if (lib.getParam("title") === "查看") {
            showTab = 2;
        }
        let disabled = false,
            showBtn = true;
        if (detail && detail.inveBusinessTypeDesc === "退货入区") {
            disabled = true;
        } else if (detail && detail.inveBusinessTypeDesc === "一线入境") {
            disabled = true;
        } else {
            showBtn = false;
        }
        return (
            <div className="base my-antd-des" style={{ padding: "10px", boxSizing: "border-box" }}>
                {this.rejectModal()}
                <Modal
                    title="编辑单证号"
                    open={editVisible}
                    onOk={() => this.editModalOk()}
                    onCancel={() => this.editModalCancel()}
                    destroyOnClose={true}>
                    <Input
                        defaultValue={editRow && editRow.relNo}
                        onChange={e =>
                            this.setState({
                                editRelNo: e.currentTarget.value,
                            })
                        }
                    />
                </Modal>
                {showTab === 2 && this.state.channel && lib.getParam("auditStatus") === "AUDITING" && (
                    <React.Fragment>
                        <Row>
                            <Col flex={6} offset={4}>
                                <Steps
                                    current={this.state.step}
                                    initial={0}
                                    style={{ width: "80%" }}
                                    status={this.state.status}>
                                    <Step title="已创建" description={lib.getParam("createTime")} />
                                    <Step title="审核中" />
                                    <Step title={lib.getParam("auditStatus") === "REJECT" ? "驳回" : "完成"} />
                                </Steps>
                            </Col>
                            <Col flex={1}>
                                {lib.getParam("auditStatus") == "AUDITING" ? (
                                    <Space style={{ float: "right", lineHeight: 5 }}>
                                        <Button
                                            type="primary"
                                            onClick={() => {
                                                lib.request({
                                                    url: "/ccs/invenorder/audit",
                                                    data: {
                                                        id: lib.getParam("id"),
                                                    },
                                                    needMask: true,
                                                    success: res => {
                                                        this.setState({
                                                            step: "3",
                                                        });
                                                        message.success("审核通过");
                                                        lib.closePage();
                                                    },
                                                });
                                            }}>
                                            审核通过
                                        </Button>
                                        <Button
                                            onClick={() => {
                                                this.setState({
                                                    refuseModalVisible: true,
                                                });
                                            }}>
                                            审核驳回
                                        </Button>
                                    </Space>
                                ) : null}
                            </Col>
                        </Row>
                    </React.Fragment>
                )}
                <Tabs
                    activeKey={activeKey}
                    onChange={activeKey => {
                        this.setState({ activeKey });
                    }}>
                    {showTab === 1 && (
                        <TabPane tab="清关单详情" key="1">
                            <div>
                                <Descriptions title="清关单详情">
                                    {forms2.map((item, index) => {
                                        return this.renderForm(item, index, true);
                                    })}
                                </Descriptions>
                                {detail && detail.inveBusinessTypeDesc !== "退货入区" && (
                                    <div style={{ marginBottom: "20px" }}>
                                        <Button type="primary" onClick={() => this.submitHandle2()}>
                                            确定
                                        </Button>
                                    </div>
                                )}
                            </div>
                            {detail && detail.inveBusinessTypeDesc !== "退货入区" && (
                                <Form layout="horizontal">
                                    <Row>
                                        <Col span={8}>
                                            <Form.Item {...formItemLayout} label="是否新品">
                                                <Select onChange={e => this.oldSelectChange(e)}>
                                                    <Option value="new">是</Option>
                                                    <Option value="old">否</Option>
                                                </Select>
                                            </Form.Item>
                                        </Col>
                                        <Col span={8}>
                                            {this.state.oldOrNew !== "old" ? (
                                                <Form.Item {...formItemLayout} label="料号">
                                                    <Select
                                                        showSearch
                                                        value={value}
                                                        onChange={e => {
                                                            this.setState({
                                                                nowItem: e.split(".")[1],
                                                                value: e,
                                                            });
                                                        }}>
                                                        {dataList.map((item, index) => {
                                                            return (
                                                                <Option value={item.id} key={index}>
                                                                    {item.recordProductName}
                                                                </Option>
                                                            );
                                                        })}
                                                    </Select>
                                                </Form.Item>
                                            ) : (
                                                <Form.Item {...formItemLayout} label="序号">
                                                    <Select
                                                        showSearch
                                                        value={value}
                                                        onChange={e => {
                                                            this.setState({
                                                                nowItem: e.split(".")[1],
                                                                value: e,
                                                            });
                                                        }}>
                                                        {dataList.map((item, index) => {
                                                            return (
                                                                <Option value={item.id} key={index}>
                                                                    {item.goodsSeqNo}-{item.productId}-
                                                                    {item.recordProductName}
                                                                </Option>
                                                            );
                                                        })}
                                                    </Select>
                                                </Form.Item>
                                            )}
                                        </Col>
                                        <Col span={4}>
                                            <Button type="primary" onClick={() => this.addHandle()}>
                                                新增
                                            </Button>
                                        </Col>
                                    </Row>
                                </Form>
                            )}
                            <Table
                                dataSource={listItems}
                                columns={columns}
                                scroll={{ x: "max-content", y: 500 }}
                                rowKey="index"
                                pagination={false}></Table>
                            <div className="button-div" style={{ padding: "20px", boxSizing: "border-box" }}>
                                <Button style={{ float: "right" }} type="primary" onClick={() => this.submitHandle()}>
                                    确认
                                </Button>
                            </div>
                        </TabPane>
                    )}

                    {showTab === 2 && (
                        <React.Fragment>
                            <TabPane tab={`清关单详情`} key="2">
                                <Descriptions title="清关单详情">
                                    {forms.map((item, index) => {
                                        return this.renderForm(item, index);
                                    })}
                                </Descriptions>
                                <div style={{ marginBottom: "20px" }}>
                                    <Button type="primary" onClick={() => this.submitHandle2()}>
                                        确定
                                    </Button>
                                </div>
                                {/* <div style={{ margin: "20px 0" }}></div> */}
                                <Table
                                    dataSource={detail.inventoryOrderItemDTOList}
                                    columns={thecolumns}
                                    scroll={{ x: "max-content", y: 500 }}
                                    rowKey="index"
                                    pagination={false}></Table>
                                <div className="button-div" style={{ padding: "20px", boxSizing: "border-box" }}>
                                    <Button
                                        style={{ float: "right" }}
                                        type="primary"
                                        onClick={() => this.submitHandle()}>
                                        确认
                                    </Button>
                                </div>
                                {!!detail?.inventoryOrderTallyReportVOList?.length && (
                                    <React.Fragment>
                                        <div
                                            className="ant-descriptions-title"
                                            style={{ width: "100%", margin: "10px 0" }}>
                                            相关文件
                                        </div>
                                        <Table
                                            dataSource={detail?.inventoryOrderTallyReportVOList}
                                            columns={tableColumns}
                                            pagination={false}
                                            rowKey="id"
                                        />
                                    </React.Fragment>
                                )}
                            </TabPane>
                        </React.Fragment>
                    )}

                    <TabPane tab="关联单证号" key="5">
                        <Descriptions title="关联单证号"></Descriptions>
                        <div style={{ padding: 20, boxSizing: "border-box" }}>
                            <div style={{ float: "right", paddingBottom: 20 }}>
                                {type === "edit" && showBtn && (
                                    <Button type="primary" onClick={() => this.setState({ importVisible: true })}>
                                        录入单证号
                                    </Button>
                                )}
                            </div>
                        </div>
                        <div style={{ padding: "0 20px" }}>
                            <Table
                                dataSource={detail.inventoryOrderRelationDTOList}
                                columns={columns4}
                                rowKey="id"
                                pagination={false}></Table>
                        </div>
                    </TabPane>

                    <TabPane tab="单据附件" key="3">
                        <Descriptions title="单据附件"></Descriptions>
                        <div style={{ padding: 20, boxSizing: "border-box" }}>
                            <div style={{ float: "right", paddingBottom: 20 }}>
                                <Upload {...props}>
                                    <Button type="primary">上传单据附件</Button>
                                </Upload>
                            </div>
                        </div>
                        <div style={{ padding: "0 20px" }}>
                            <Table
                                dataSource={detail.inventoryOrderAttachDTOList}
                                columns={columns2}
                                rowKey="id"
                                pagination={false}></Table>
                        </div>
                    </TabPane>
                    <TabPane tab="操作日志" key="4">
                        <Descriptions title="操作日志"></Descriptions>
                        <Table
                            dataSource={detail.inventoryOrderLogDTOList}
                            columns={columns3}
                            rowKey="createTime"
                            pagination={false}></Table>
                    </TabPane>
                </Tabs>
                <Modal
                    title="录入单证号"
                    open={importVisible}
                    onCancel={this.modalCancel.bind(this)}
                    onOk={this.modalOk.bind(this)}
                    destroyOnClose={true}>
                    <FormItem {...formItemLayout} label="单证类型">
                        <Select
                            style={{ width: 150 }}
                            disabled={disabled}
                            value={relType}
                            onChange={relType => this.setState({ relType })}>
                            {statusList.map((item, index) => {
                                return (
                                    <Option key={index} value={item.id}>
                                        {item.name}
                                    </Option>
                                );
                            })}
                        </Select>
                    </FormItem>
                    <TextArea onChange={e => this.setState({ relNos: e.currentTarget.value })} rows={20} />
                </Modal>
                {/* 预览文件弹窗 */}
                <Modal
                    open={previewModalVisible}
                    title="预览"
                    width="1000px"
                    onOk={() => this.setState({ previewModalVisible: false, previewRow: [] })}
                    onCancel={() => this.setState({ previewModalVisible: false, previewRow: [] })}>
                    <Table
                        dataSource={previewRow}
                        pagination={false}
                        columns={previewColumns}
                        scroll={{ x: "max-content" }}
                    />
                </Modal>
            </div>
        );
    }
}
export default App;
