import React from "react";
import { ConfigCenter, lib } from "react-single-app";
import { Button, Modal, Form, message, Space, Switch } from "antd";
import NewModal from "../components/NewModal";
class PaymentChannel extends ConfigCenter {
    constructor(props) {
        super(props);
        this.state.editDrawer = {
            show: false,
        };
        this.state.configList = [
            {
                type: "INPUT",
                labelName: "支付渠道名称",
                labelKey: "name",
                required: true,
                message: "请输入支付渠道名称",
                maxLength: 32,
                autocomplete: "off",
            },
            {
                type: "INPUT",
                labelName: "支付渠道标识",
                labelKey: "code",
                required: true,
                message: "请输入支付渠道标识",
                maxLength: 32,
                autocomplete: "off",
            },
            {
                type: "SELECT",
                labelName: "支付企业",
                labelKey: "payCompanyId",
                required: true,
                message: "请选择支付企业",
                list: [],
                ccs: "/ccs/company/listWithZFQY",
            },
        ];
        this.state.list = [];
        this.formRef = React.createRef();
    }
    componentDidMount() {
        this.getConfigList();
    }

    getConfigList() {
        let configList = this.state.configList;
        configList.map(item => {
            if (item.ccs) {
                lib.request({
                    url: item.ccs,
                    needMask: false,
                    success: res => {
                        item.list = res || [];
                        this.setState({ configList });
                    },
                });
            }
        });
        this.setState({ configList });
    }

    renderRightOperation() {
        return (
            <React.Fragment>
                <Button
                    type="primary"
                    onClick={() => {
                        this.setState({
                            visible: true,
                            modalTitle: "新增支付渠道",
                        });
                        this.getConfigList();
                    }}>
                    新增支付渠道
                </Button>
            </React.Fragment>
        );
    }

    handleOk(values, modalForm) {
        if (this.state.editRow) {
            values.id = this.state.editRow.id;
        }
        for (let i in values) {
            if (values[i] === null) {
                delete values[i];
            }
        }
        lib.request({
            url: "/ccs/pay/channel/upset",
            data: values,
            method: "POST",
            needMask: true,
            success: res => {
                if (res) {
                    this.setState({
                        visible: false,
                        editRow: null,
                    });
                    this.load(true);
                }
            },
        });
        // })
    }

    handleCancel() {
        this.setState({
            visible: false,
            editRow: null,
        });
    }

    renderModal() {
        return (
            <React.Fragment>
                <NewModal
                    {...{
                        configList: this.state.configList,
                        onOk: this.handleOk.bind(this),
                        onCancel: this.handleCancel.bind(this),
                        visible: this.state.visible,
                        form: this.formRef,
                        editRow: this.state.editRow,
                        title: this.state.modalTitle,
                        list: this.state.list,
                    }}
                />
            </React.Fragment>
        );
    }
    handleEnable(row, enable) {
        let data = JSON.parse(JSON.stringify(row));
        data.enable = enable;
        lib.request({
            url: "/ccs/pay/channel/upset",
            data,
            method: "POST",
            needMask: true,
            success: res => {
                if (res) {
                    this.load(true);
                    message.success(enable ? "启用成功" : "禁用成功");
                }
            },
        });
    }
    enableStatus(row) {
        return (
            <React.Fragment>
                {row.enable === 1 && (
                    <Switch
                        checked={row.enable}
                        onChange={() => {
                            Modal.confirm({
                                cancelText: "取消",
                                okText: "确定",
                                title: "提示",
                                content: "是否禁用?",
                                onOk: () => {
                                    this.handleEnable(row, 0);
                                },
                            });
                        }}></Switch>
                )}
                {row.enable === 0 && <Switch checked={row.enable} onChange={() => this.handleEnable(row, 1)}></Switch>}
            </React.Fragment>
        );
    }
    myOperation(row) {
        return (
            <Space>
                {/* {row.enable === 1 && <React.Fragment>
                <span className='link' onClick={() => }>禁用</span>
            </React.Fragment>} */}
                {row.enable === 0 && (
                    <Space>
                        <span
                            className="link"
                            onClick={() => {
                                row.payCompanyId = String(row.payCompanyId);
                                this.setState({
                                    editRow: row,
                                    visible: true,
                                    modalTitle: "编辑支付渠道",
                                });
                                this.getConfigList();
                            }}>
                            编辑
                        </span>
                    </Space>
                )}
            </Space>
        );
    }
}
export default PaymentChannel;
