import React, { useState, useEffect } from "react";
import "./shop-good.less";
import { ConfigCenter, lib, HOC } from "react-single-app";
import { Checkbox, Button, Modal, Drawer, Input, Select, Switch, message, Space } from "antd";
import NewModal from "../components/NewModal";

@HOC.mapAuthButtonsToState({ pagePath: "/enterprise-manage" })
class App extends ConfigCenter {
    constructor(props) {
        super(props);
        this.state.modalTitle = "申报配置";
        this.state.upUrl = "/ccs/company/upset";
        this.state.declareConfigList = [
            {
                type: "SELECT",
                labelName: "订单",
                labelKey: "customsOrder",
                allowClear: true,
                message: "请选择订单",
                list: [],
                ccs: "/ccs/declareWay/listByType",
            },
            {
                type: "SELECT",
                labelName: "运单",
                labelKey: "shipment",
                allowClear: true,
                message: "请选择运单",
                list: [],
                ccs: "/ccs/declareWay/listByType",
            },
            {
                type: "SELECT",
                labelName: "清单",
                labelKey: "inventory",
                allowClear: true,
                message: "请选择清单",
                list: [],
                ccs: "/ccs/declareWay/listByType",
            },
            {
                type: "SELECT",
                labelName: "撤单",
                labelKey: "inventoryCancel",
                allowClear: true,
                message: "请选择撤单",
                list: [],
                ccs: "/ccs/declareWay/listByType",
            },
            {
                type: "SELECT",
                labelName: "退货",
                labelKey: "inventoryRefund",
                allowClear: true,
                message: "请选择退货",
                list: [],
                ccs: "/ccs/declareWay/listByType",
            },
        ];
        this.state.buttons = [];
    }

    componentDidMount() {
        // lib.checkIsLogin()
        // 获取企业资质列表
        lib.request({
            url: "/ccs/company/listQualify",
            success: res => {
                if (res) {
                    this.setState({
                        qualifyList: res || [],
                    });
                }
            },
        });
        // 获取口岸列表
        lib.request({
            url: "/ccs/customs/listDistrict",
            success: res => {
                if (res) {
                    this.setState({
                        districtList: res || [],
                    });
                }
            },
        });
    }

    renderDistrictList(row) {
        return row.districtList.join(",");
    }

    renderRightOperation() {
        const { buttons } = this.state;
        return (
            <React.Fragment>
                {buttons.includes("add") && (
                    <Button
                        type="primary"
                        onClick={() =>
                            lib.openPage(`/new-enterprise?pageTitle=新增企业&title=新增企业`, () => {
                                this.load(true);
                            })
                        }>
                        新增企业
                    </Button>
                )}
            </React.Fragment>
        );
    }

    handleEnable(row, enable) {
        lib.request({
            url: "/ccs/company/updateEnable",
            data: {
                id: row.id,
                enable,
            },
            method: "POST",
            needMask: true,
            success: res => {
                if (res) {
                    message.success(`${enable ? "启用" : "禁用"}成功`);
                    this.load(true);
                }
            },
        });
    }

    qualifyList2String(row) {
        if (!this.state.qualifyList) return;
        let str = "";
        this.state.qualifyList.map(item => {
            row.qualifyList.map(ite => {
                if (ite === item.value) {
                    str += item.name + "、";
                }
            });
        });
        if (str) return str.slice(0, -1);
        return str;
    }

    districtList2String(row) {
        return row.districtList.join("、");
    }

    enableFunc(row) {
        Modal.confirm({
            cancelText: "取消",
            okText: "确定",
            title: "提示",
            content: "是否禁用?",
            onOk: () => {
                this.handleEnable(row, 0);
            },
        });
    }

    enableStatus(row) {
        return (
            <React.Fragment>
                {row.enable === 1 && <Switch checked={row.enable} onChange={() => this.enableFunc(row)}></Switch>}
                {row.enable === 0 && <Switch checked={row.enable} onChange={() => this.handleEnable(row, 1)}></Switch>}
            </React.Fragment>
        );
    }

    myOperation(row) {
        const { buttons } = this.state;
        return (
            <Space>
                {row.enable === 0 && buttons.includes("edit") && (
                    <React.Fragment>
                        <span
                            className="link"
                            onClick={() =>
                                lib.openPage(`/new-enterprise?pageTitle=编辑企业&theid=${row.id}`, () => {
                                    this.load(true);
                                })
                            }>
                            编辑
                        </span>
                    </React.Fragment>
                )}
                {buttons.includes("look") && (
                    <span
                        className="link"
                        onClick={() => lib.openPage(`/new-enterprise?pageTitle=查看企业&theid=${row.id}`)}>
                        查看
                    </span>
                )}
            </Space>
        );
    }

    declareConfig(row) {
        lib.request({
            url: "/ccs/declareWay/listByType",
            needMask: true,
            success: data => {
                let declareConfigList = this.state.declareConfigList;
                declareConfigList.forEach(item => {
                    item.list = [];
                    data.forEach(dataItem => {
                        if (dataItem.type === item.labelKey) {
                            item.list.push(dataItem);
                        }
                    });
                });
                this.setState({ declareConfigList: declareConfigList }, () => {
                    this.viewDeclareWay(row.id);
                });
            },
        });
    }

    viewDeclareWay(id) {
        lib.request({
            url: "/ccs/company/viewDeclareWay",
            data: { id: id },
            needMask: true,
            success: list => {
                let editRow = {};
                editRow.id = id;
                list &&
                    list.forEach(item => {
                        editRow[item.type] = item.declareCode;
                    });
                this.setState({ declareModalVisible: true, editRow: editRow, modalTitle: "申报配置" });
            },
        });
    }

    declareHandleOk(values) {
        let declareConfigList = [];
        Object.keys(values).forEach(item => {
            declareConfigList.push({ declareCode: values[item], type: `${item}` });
        });
        lib.request({
            url: "/ccs/company/updateDeclareWay",
            data: { companyId: this.state.editRow.id, declareConfigList: declareConfigList },
            method: "POST",
            needMask: true,
            success: res => {
                this.setState({
                    declareModalVisible: false,
                    editRow: null,
                });
                this.load(true);
            },
        });
    }

    declareHandleCancel() {
        this.setState({
            declareModalVisible: false,
        });
    }

    renderModal() {
        let declareModalProps = {
            title: this.state.modalTitle,
            onOk: this.declareHandleOk.bind(this),
            onCancel: this.declareHandleCancel.bind(this),
            configList: this.state.declareConfigList,
            visible: this.state.declareModalVisible,
            editRow: this.state.editRow,
            formItemLayout: {
                labelCol: { span: 8 },
                wrapperCol: { span: 14 },
            },
        };
        return <NewModal {...declareModalProps} />;
    }
}

export default App;
