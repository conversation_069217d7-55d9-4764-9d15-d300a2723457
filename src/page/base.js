import React from "react";
import { ConfigCenter, lib } from "react-single-app";
import { Modal, Table } from "antd";

class Base extends ConfigCenter {
    constructor(props) {
        super(props);
        this.state.data = {};
        this.state.startDate = null;
        this.state.columns = [
            {
                title: "序号",
                dataIndex: "gnum",
                width: 100,
            },
            {
                title: "商品编码",
                dataIndex: "gcode",
                width: 160,
            },
            {
                title: "完税总价（元）",
                dataIndex: "taxPrice",
                width: 120,
            },
            {
                title: "应征关税（元）",
                dataIndex: "customsTax",
                width: 120,
            },
            {
                title: "应征消费税（元）",
                dataIndex: "consumptionTax",
                width: 120,
            },
            {
                title: "应征增值税（元）",
                dataIndex: "valueAddedTax",
                width: 120,
            },
        ];
    }

    fetchBill(data) {
        lib.request({
            url: "/ccs/taxesCompany/sumSettledAmount",
            data: data,
            success: res => {
                if (res) {
                    this.setState({
                        data: res,
                    });
                }
            },
        });
    }

    renderTaxNo(row) {
        return (
            <span
                className={"link"}
                onClick={() => {
                    this.getTaxList(row);
                }}>
                {row.taxNo}
            </span>
        );
    }

    getTaxList(row) {
        lib.request({
            url: "/ccs/taxesCompany/findByTaxListIdAndInvtNo",
            data: { taxListId: row.id, invtNo: row.invtNo },
            success: res => {
                if (res) {
                    this.setState({
                        dataSource: res,
                        visible: true,
                    });
                }
            },
        });
    }

    modalClose() {
        this.setState({
            dataSource: null,
            visible: false,
        });
    }

    renderModal() {
        let { dataSource } = this.state;
        return (
            <Modal
                open={this.state.visible}
                title={"税单表体信息"}
                onCancel={() => this.modalClose()}
                onOk={() => this.modalClose()}
                width={890}>
                <Table columns={this.state.columns} dataSource={dataSource} />
            </Modal>
        );
    }

    renderLeftOperation() {
        const { data } = this.state;
        return (
            <pre style={{ lineHeight: 2.3 }}>
                当前已汇总金额：{data.settledAmount}(海关实扣) 当前未汇总金额：{data.unSettleAmount}(海关未扣)
            </pre>
        );
    }

    load(needMask) {
        let { searchConditions, pagination } = this.state;
        var data = {};
        Object.assign(data, pagination, searchConditions);
        this.fetchBill(data);
        lib.request({
            url: this.state.config.requestUrl,
            data: data,
            needMask: needMask,
            success: json => {
                this.setState({
                    pagination: json.page || {},
                    dataList: json.dataList || [],
                });
            },
        });
    }
}

export default Base;
