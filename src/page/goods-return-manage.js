import React, { useState, useEffect, useImperativeHandle, forwardRef, useRef, useCallback, useMemo } from "react";
// import BaseSticky from '../base/base-sticky.js';
import "./shop-good.less";
import { SearchList, lib, getConfigDataUtils, HOC, event } from "react-single-app";

import {
    Checkbox,
    Button,
    Modal,
    Drawer,
    Input,
    Select,
    Form,
    message,
    Tooltip,
    Tabs,
    Table,
    Space,
    Alert,
} from "antd";
import NewModal from "../components/NewModal";
import axios from "axios";
const { TabPane } = Tabs;
const { TextArea } = Input;
import "./goods-return-manage.less";
import moment from "moment";
const REFUNDLIST = {
    1: "已暂存",
    2: "申报中",
    3: "发送海关成功",
    4: "发送海关失败",
    100: "海关退单",
    120: "海关入库",
    399: "海关审结",
};

const statusDist = {
    INT: "初始化",
    AUDITING: "待总署审核",
    AUDIT_PASS: "审核通过",
    AUDIT_REJECT: "总署驳回",
    DECALRING_AUDITING: "待总署审核",
    FAIL: "申报失败",
};
const statusDist2 = ["初始化", "待退货", "退货完成", "退货关闭", "取消退货"];
@HOC.mapAuthButtonsToState({ buttonCodeArr: ["exportAuth"] })
class App extends SearchList {
    constructor(props) {
        super(props);
        this.state.modalTitle = "新增账册";
        this.state.detailUrl = "/ccs/refund/viewRefundOrder";
        this.state.tabsList = [];
        this.state.cancelDisabled = false;
        this.state.returnDisabled = false;
        this.state.deleteDisabled = false;
        this.state.buttonAuth = { exportAuth: false };
        this.state.detailInfo = {};
        this.state.importData = {};
        this.state.modalTitle = "批量手动操作";
        this.state.upUrl = "/ccs/refund/updRefundCheckStatus";
        this.state.configList = [
            {
                type: "SELECT",
                labelName: "手动操作原因",
                labelKey: "operateReason",
                list: [],
                ccs: "/ccs/inventroy-cancel/operateReasonEnums",
                required: true,
            },
            {
                type: "RADIO",
                labelName: "退货状态",
                labelKey: "refundCheckStatus",
                list: [],
                ccs: "/ccs/refund/listRefundCheckStatus",
                required: true,
                onChange: (e, form) => {
                    if (e.target.value === "AUDIT_REJECT") {
                        this.state.configList[2].hide = false;
                    } else {
                        this.state.configList[2].hide = true;
                    }
                    this.setState(this.state);
                },
            },
            {
                type: "SELECT",
                labelName: "手工回执",
                labelKey: "manualReceipt",
                list: [],
                ccs: "/ccs/refund/customsRefundOrderStatus",
                hide: true,
                required: true,
            },
        ];
        this.onSearchReset = this.onSearchReset.bind(this);
        this.tagSelectRef = React.createRef();
    }
    componentDidMount() {
        const beginCreateTime = moment().subtract(2, "month").format("YYYY-MM-DD");
        const endCreateTime = moment().format("YYYY-MM-DD");
        this.changeImmutable({
            beginCreateTime: Number(moment(beginCreateTime + " 00:00:00.000").format("x")),
            endCreateTime: Number(moment(endCreateTime + " 23:59:59.999").format("x")),
        });
        event.on("onSearchReset", this.onSearchReset);
    }
    componentWillUnmount() {
        event.off("onSearchReset", this.onSearchReset);
    }
    onSearchReset() {
        this.tagSelectRef.current.setActiveKey("-1");
        this.changeImmutable({ refundStatus: "" });
    }
    onSearch(search) {
        this.tagSelectRef.current.getDetail(search);
    }
    getConfig() {
        return axios.get(getConfigDataUtils.getDataUrlByDataId(515)).then(res => res.data.data);
    }
    getConfigList() {
        let { configList } = this.state;
        configList.map(item => {
            if (item.ccs) {
                lib.request({
                    url: item.ccs,
                    needMask: false,
                    success: res => {
                        item.lists = res;
                        item.list = res;
                        // console.log(res, "res");
                        this.setState({
                            configList,
                        });
                    },
                });
            }
        });
        this.setState({
            configList,
        });
    }

    // renderRefundStatus(row) {
    //   return statusDist2[row.refundStatus];
    // }

    renderRefundCheckStatus(row) {
        return statusDist[row.refundCheckStatus];
    }

    renderTooltip(row) {
        return (
            <Tooltip title={row.refundCustomCheckDetail}>
                <span>{row.refundCustomStatus}</span>
            </Tooltip>
        );
    }

    showTrackLog(row) {
        return (
            <a
                onClick={() => {
                    lib.openPage(`/declaration-manage-detail?orderId=${row.orderId}&page_title=申报单详情`, () =>
                        this.load(),
                    );
                }}>
                {row.refDeclareNo}
            </a>
        );
    }

    getCheckedRows() {
        let { selectedRows } = this.state;
        return selectedRows;
    }

    calcIds() {
        let list = this.getCheckedRows(),
            ids = "";
        list.map(item => {
            ids += item.id + ",";
        });
        return ids.slice(0, -1);
    }

    cancelFunc() {
        let ids = this.calcIds();
        if (ids == "") {
            message.warning("请选择数据");
            return;
        }
        this.setState({
            cancelVisible: true,
            ids,
        });
    }

    returnFunc() {
        let ids = this.calcIds();
        if (ids == "") {
            message.warning("请选择数据");
            return;
        }
        this.setState({
            submitVisible: true,
            ids,
        });
    }

    deleteFunc() {
        let ids = this.calcIds();
        if (ids == "") {
            message.warning("请选择数据");
            return;
        }
        this.setState({
            deleteVisible: true,
            ids,
        });
    }

    download(url, param) {
        var paramStr = "";
        for (var key in param) {
            if (param[key] != undefined) {
                paramStr += key + "=" + encodeURI(param[key]) + "&";
            }
        }
        var searchParam;
        if (url.indexOf("?") !== -1) {
            searchParam = lib.env.getHost() + url + "&" + paramStr;
        } else {
            searchParam = lib.env.getHost() + url + "?" + paramStr;
        }
        window.open(searchParam);
    }

    export(url) {
        var param = {};
        var strs = window.location.search.substring(1);
        var b = strs.split("&");
        for (var i = 0; i < b.length; i++) {
            var [key, value] = b[i].split("=");
            if (key != "config_id") {
                param[key] = value;
            }
        }
        var page = this.state.page;
        for (var key in page) {
            if (page[key].value !== "") {
                param[key] = page[key].value;
            }
        }
        this.download(url, param);
    }

    sync(title, url) {
        lib.request({
            url: url,
            method: "get",
            timeout: 50000,
            success: json => {
                lib.showModal({
                    content: json,
                    confirmText: "刷新页面",
                    confirmFn: function () {
                        location.reload();
                    },
                });
            },
        });
    }

    renderLeftOperation() {
        const { returnDisabled, cancelDisabled, deleteDisabled } = this.state;
        return (
            <Space>
                <Button
                    style={{ marginRight: 10 }}
                    type="primary"
                    disabled={returnDisabled}
                    onClick={() => this.returnFunc()}>
                    批量申报
                </Button>
                <Button style={{ marginRight: 10 }} disabled={cancelDisabled} onClick={() => this.cancelFunc()}>
                    批量取消
                </Button>
                <Button style={{ marginRight: 10 }} disabled={deleteDisabled} onClick={() => this.deleteFunc()}>
                    批量删除
                </Button>
                {this.state.buttons && this.state.buttons.includes("batch-manual-processing") && (
                    <Button
                        type="primary"
                        onClick={() => {
                            let list = this.getCheckedRows();
                            let ids = list.reduce((prev, curr) => [...prev, curr.id], []);
                            if (ids.length === 0) {
                                message.warning("请选择数据");
                                return;
                            }
                            this.setState({
                                visible: true,
                                ids: ids,
                            });
                            this.getConfigList();
                        }}>
                        批量手动操作
                    </Button>
                )}
                {/* <Button style={{ marginRight: 10 }} type='primary' onClick={() => this.export("/ccs/refund/exportEndorsement")}>导出核注</Button> */}
                {/* <Button style={{ marginRight: 10 }} className='import'>
                    批量导入运单
          <input type='file' onChange={this.importFunc.bind(this)} />
                </Button> */}
                <Button
                    onClick={() => {
                        const idList = this.getCheckedRows().map(item => item.id);
                        if (idList.length == 0) {
                            return message.error("请选择数据");
                        }
                        Modal.confirm({
                            title: "手动核注",
                            content: "手动核注前已核实跨关区退货单核注申报完成，当前确认手动核注吗？",
                            okText: "确定",
                            cancelText: "取消",
                            onOk: () => {
                                lib.request({
                                    url: "/ccs/refund/manual/endorsement",
                                    data: {
                                        idList: idList,
                                    },
                                    success: () => {
                                        message.success("操作成功");
                                        this.load();
                                    },
                                });
                            },
                        });
                    }}>
                    手动核注
                </Button>
            </Space>
        );
    }

    renderRightOperation() {
        return (
            <Space>
                <UpdateApplyCustomsDistrict
                    selectedRows={this.state.selectedRows}
                    load={() => {
                        this.load();
                        this.setState({
                            selectedRows: [],
                            selectedIds: [],
                        });
                    }}
                />
                <SelectImport load={this.load} />
            </Space>
        );
    }

    importFunc(e) {
        var input = e.currentTarget;
        var file = e.currentTarget.files[0];
        if (file) {
            var formData = new FormData();
            formData.append("file", file);
            lib.request({
                url: "/ccs/refund/pre-import",
                method: "POST",
                needMask: true,
                data: formData,
                success: res => {
                    input.value = "";
                    if (res.code < 0) {
                        Modal.error({
                            content: res.errosMessage,
                        });
                    } else {
                        this.setState({
                            importData: res,
                            importVisible: true,
                        });
                    }
                },
                fail: json => {
                    input.value = "";
                },
            });
        }
    }

    handleOk(values, modalForm) {
        lib.request({
            url: this.state.upUrl,
            data: Object.assign(values, { ids: this.state.ids }),
            method: "POST",
            needMask: true,
            success: res => {
                this.setState({
                    visible: false,
                    editRow: null,
                });
                this.load(true);
            },
        });
    }

    handleCancel() {
        this.setState({
            visible: false,
        });
    }

    tdClick(row) {
        let list = this.getCheckedRows();
        if (list && list.length) {
            let returnDisabled = false,
                deleteDisabled = false,
                cancelDisabled = false;
            list.map(item => {
                // 判断是否有数据无法申请退货
                if (item.refundStatus !== 0 && item.refundStatus !== 1) {
                    returnDisabled = true;
                } else if (item.refundStatus === 0 && item.refundCheckStatus !== "INT") {
                    returnDisabled = true;
                } else if (item.refundStatus === 1 && item.refundCheckStatus !== "AUDIT_REJECT") {
                    returnDisabled = true;
                }
                //判断是否有数据无法取消退货
                // if (item.refundStatus !== 0 && item.refundStatus !== 1) {
                //   cancelDisabled = true
                // } else if (item.refundStatus === 0 && item.refundCheckStatus !== "INT") {
                //   cancelDisabled = true
                // } else if (item.refundStatus === 1 && item.refundCheckStatus !== "AUDIT_REJECT") {
                //   cancelDisabled = true
                // }
                if (item.refundCheckStatus === "AUDIT_PASS") {
                    cancelDisabled = true;
                }
                //判断是否有数据无法删除退货
                if (
                    item.refundStatus !== 3 &&
                    item.refundCheckStatus !== "INT" &&
                    item.refundCheckStatus !== "AUDIT_REJECT"
                ) {
                    deleteDisabled = true;
                }
            });
            this.setState({
                returnDisabled,
                cancelDisabled,
                deleteDisabled,
            });
        } else {
            this.setState({
                returnDisabled: false,
                cancelDisabled: false,
                deleteDisabled: false,
            });
        }
    }

    cancelGoodsReturn() {
        if (!this.state.ids) {
            message.warning("请选择数据重试");
            return;
        }
        lib.request({
            url: "/ccs/refund/cancelRefund",
            data: {
                ids: this.state.ids,
            },
            needMask: true,
            success: res => {
                if (res) {
                    if (res.code < 0) {
                        message.warning(res.message);
                    } else {
                        this.setState({
                            cancelVisible: false,
                            CancelVisible: true,
                            ids: "",
                            detailInfo: res,
                        });
                    }
                }
            },
        });
    }

    submitGoodsReturn() {
        if (!this.state.ids) {
            message.warning("请选择数据重试");
            return;
        }
        lib.request({
            url: "/ccs/refund/declareRefundOrder",
            data: {
                declareIds: this.state.ids,
            },
            needMask: true,
            success: res => {
                message.success("申报成功");
                this.load(true);
                this.setState({
                    submitVisible: false,
                    ids: "",
                });
            },
            fail: () => {
                this.load();
                this.setState({
                    selectedRows: [],
                    selectedIdList: [],
                    submitVisible: false,
                });
            },
        });
    }

    deleteGoodsReturn() {
        if (!this.state.ids) {
            message.warning("请选择数据重试");
            return;
        }
        lib.request({
            url: "/ccs/refund/deleteRefundOrder",
            data: {
                declareIds: this.state.ids,
            },
            method: "post",
            needMask: true,
            success: res => {
                if (res) {
                    if (res.code < 0) {
                        message.warning(res.message);
                    } else {
                        this.setState({
                            deleteVisible: false,
                            modalVisible: true,
                            ids: "",
                            detailInfo: res,
                        });
                    }
                }
            },
        });
    }

    getItem(row) {
        for (let i in REFUNDLIST) {
            if (i == row.invenCustomStatus) {
                return REFUNDLIST[i];
            }
        }
    }

    textareaChange(e, key) {
        let state = this.state;
        state[key] = e.target.value;
        this.setState(state);
    }

    getRowValue(row, key) {
        if (key === "") return "";
        let value = row;
        let keys = key.split(".");
        keys.map(item => {
            value = value[item];
        });
        return value;
    }

    importMailNo() {
        let { importData } = this.state;
        if (importData.successNewCount === 0 && importData.successUpdateCount === 0) {
            message.warning("暂无有效数据");
            return;
        }
        let recordList = importData.successNewRecordList.concat(importData.successUpdateRecordList);
        lib.request({
            url: "/ccs/refund/import",
            data: {
                recordList,
            },
            method: "post",
            needMask: true,
            success: res => {
                if (res) {
                    this.load(true);
                    message.success("导入成功");
                    this.setState({
                        importVisible: false,
                    });
                }
            },
        });
    }

    renderModal() {
        let {
            detailInfo,
            cancelVisible,
            submitVisible,
            deleteVisible,
            CancelVisible,
            modalVisible,
            importData,
            importVisible,
            configList,
            visible,
        } = this.state;
        const columns1 = [
            {
                title: "申报单号",
                dataIndex: "declareNo",
            },
            {
                title: "清单编号",
                dataIndex: "inveNo",
            },
        ];
        const columns2 = [
            {
                title: "申报单号",
                dataIndex: "declareNo",
            },
            {
                title: "清单编号",
                dataIndex: "inveNo",
            },
            {
                title: "错误信息",
                dataIndex: "message",
            },
        ];
        const columns3 = [
            {
                title: "运单号",
                dataIndex: "mailNo",
            },
            {
                title: "退货单号",
                dataIndex: "refundMailNo",
            },
        ];
        const columns4 = [
            {
                title: "运单号",
                dataIndex: "mailNo",
            },
            {
                title: "退货单号",
                dataIndex: "refundMailNo",
            },
            {
                title: "错误信息",
                dataIndex: "errorMsg",
            },
        ];
        let props = {
            title: this.state.modalTitle,
            onOk: this.handleOk.bind(this),
            onCancel: this.handleCancel.bind(this),
            configList,
            visible,
            form: this.props.form,
            formItemLayout: {
                labelCol: { span: 8 },
                wrapperCol: { span: 14 },
            },
            modalStyle: { width: "800px" },
        };
        return (
            <React.Fragment>
                <Modal
                    cancelText="取消"
                    okText="确定"
                    title="取消退货"
                    open={cancelVisible}
                    onOk={() => this.cancelGoodsReturn()}
                    onCancel={() => this.setState({ cancelVisible: false, cancelText: "" })}>
                    确认取消退货？
                </Modal>
                <Modal
                    cancelText="取消"
                    okText="确定"
                    title="批量申报"
                    open={submitVisible}
                    onOk={() => this.submitGoodsReturn()}
                    onCancel={() => this.setState({ submitVisible: false, submitText: "" })}>
                    确认批量申报吗？
                </Modal>
                <Modal
                    cancelText="取消"
                    okText="确定"
                    title="删除退货"
                    open={deleteVisible}
                    onOk={() => this.deleteGoodsReturn()}
                    onCancel={() => this.setState({ deleteVisible: false, submitText: "" })}>
                    确认退货吗？
                </Modal>
                <Modal
                    title={`取消总数(${detailInfo.totalCount})`}
                    width={800}
                    open={CancelVisible}
                    onCancel={() => {
                        this.setState({ CancelVisible: false });
                        this.load(true);
                    }}
                    footer={
                        <Button
                            type="primary"
                            onClick={() => {
                                this.setState({
                                    CancelVisible: false,
                                    detailInfo: {},
                                });
                                this.load(true);
                            }}>
                            关闭
                        </Button>
                    }>
                    <Tabs defaultActiveKey="0">
                        <TabPane tab={`取消成功(${detailInfo.successCount})`} key="0">
                            <Table dataSource={detailInfo.successRecordList} columns={columns1} rowKey="idx"></Table>
                        </TabPane>
                        <TabPane tab={`取消失败(${detailInfo.failCount})`} key="1">
                            <Table dataSource={detailInfo.failRecordList} columns={columns2} rowKey="idx"></Table>
                        </TabPane>
                    </Tabs>
                </Modal>
                <Modal
                    title={`删除总数(${detailInfo.totalCount})`}
                    width={800}
                    open={modalVisible}
                    onCancel={() => {
                        this.setState({ modalVisible: false });
                        this.load(true);
                    }}
                    footer={
                        <Button
                            type="primary"
                            onClick={() => {
                                this.setState({
                                    modalVisible: false,
                                    detailInfo: {},
                                });
                                this.load(true);
                            }}>
                            关闭
                        </Button>
                    }>
                    <Tabs defaultActiveKey="0">
                        <TabPane tab={`删除成功(${detailInfo.successCount})`} key="0">
                            <Table dataSource={detailInfo.successRecordList} columns={columns1} rowKey="idx"></Table>
                        </TabPane>
                        <TabPane tab={`删除失败(${detailInfo.failCount})`} key="1">
                            <Table dataSource={detailInfo.failRecordList} columns={columns2} rowKey="idx"></Table>
                        </TabPane>
                    </Tabs>
                </Modal>
                <Modal
                    title={`导入总数(${importData.totalCount})`}
                    width={800}
                    open={importVisible}
                    onOk={() => this.importMailNo()}
                    onCancel={() => {
                        this.setState({
                            importVisible: false,
                            importData: {},
                        });
                        this.load(true);
                    }}>
                    <Tabs defaultActiveKey="0">
                        <TabPane tab={`新增(${importData.successNewCount})`} key="0">
                            <Table dataSource={importData.successNewRecordList} columns={columns3} rowKey="idx"></Table>
                        </TabPane>
                        <TabPane tab={`编辑(${importData.successUpdateCount})`} key="1">
                            <Table
                                dataSource={importData.successUpdateRecordList}
                                columns={columns3}
                                rowKey="idx"></Table>
                        </TabPane>
                        <TabPane tab={`校对出错(${importData.failCount})`} key="2">
                            <Table dataSource={importData.failRecordList} columns={columns4} rowKey="idx"></Table>
                        </TabPane>
                    </Tabs>
                </Modal>
                <NewModal {...props} />
            </React.Fragment>
        );
    }
    //  新增组件
    renderOperationTopView() {
        return <StorageTags ref={this.tagSelectRef} onChange={e => this.getValueChild(e)} />;
    }
    //接受子组件的点击参数
    getValueChild(value) {
        const { search } = this.state;
        if (value === "-1") {
            this.changeImmutable({ refundStatus: "" });
        } else {
            this.changeImmutable({ refundStatus: Number(value) });
            this.setState({
                refundStatus: value,
                search: { ...search, refundStatus: Number(value) },
            });
        }
    }
}
export default App;
const StorageTags = forwardRef(({ onChange }, ref) => {
    const [data, setDate] = useState();
    const [activeKey, setActiveKey] = useState();
    const { TabPane } = Tabs;
    function getDetail(search) {
        lib.request({
            url: "/ccs/refund/listRefundStatusCount",
            data: search,
            success: res => {
                setDate(res);
            },
        });
    }
    useImperativeHandle(ref, () => ({
        getDetail: getDetail,
        setActiveKey: setActiveKey,
    }));
    const handelTabsChange = e => {
        setActiveKey(e);
        onChange(e);
    };
    return (
        <Tabs defaultActiveKey={"-1"} activeKey={activeKey} onChange={handelTabsChange}>
            <TabPane tab="全部" key={"-1"}></TabPane>
            {data &&
                data.map(item => {
                    return <TabPane tab={`${item.value + " " + `(${item.num})`}`} key={item.id} />;
                })}
        </Tabs>
    );
});

const UpdateApplyCustomsDistrict = ({ selectedRows, load }) => {
    const refModal = useRef();
    const resultRef = useRef();
    const [configs, setConfigs] = useState([
        {
            type: "INPUT",
            labelName: "区内企业",
            labelKey: "areaCompanyName",
            disabled: true,
        },
        {
            type: "INPUT",
            labelName: "申报关区",
            labelKey: "declareCustomCodeDesc",
            disabled: true,
        },
        {
            type: "SELECT",
            labelName: "退货申报关区",
            labelKey: "refundCustomCode",
            required: true,
            list: [],
        },
    ]);
    useEffect(() => {
        lib.request({
            url: "/ccs/customs/listDistrict",
            success: res => {
                configs[2].list = res;
                setConfigs([...configs]);
            },
        });
    }, []);

    const [open, setOpen] = useState(false);

    const onOk = () => {
        const ids = selectedRows.map(item => item.id);
        lib.request({
            url: "/ccs/refund/modifyRefundCustomCode",
            methods: "post",
            data: {
                ids: ids.join(","),
            },
            success: data => {
                refModal.current.form.setFieldsValue(data);
                resultRef.current = data;
                setOpen(true);
            },
        });
    };

    const changeInfo = data => {
        const ids = selectedRows.map(item => item.id);

        lib.request({
            url: "/ccs/refund/updateRefundCustomCode",
            methods: "post",
            data: {
                ids: ids.join(","),
                ...data,
                declareCustomCode: resultRef.current.declareCustomCode,
            },
            success: res => {
                setOpen(false);
                load();
                message.success("修改成功");
            },
        });
    };

    const onCancel = () => {
        setOpen(false);
    };

    return (
        <>
            <Button
                onClick={() => {
                    if (selectedRows.length > 0) {
                        onOk();
                    } else {
                        message.warn("请选择数据做修改");
                    }
                }}>
                修改申报关区
            </Button>
            <NewModal
                title={"修改申报关区"}
                onOk={changeInfo}
                onCancel={onCancel}
                configList={configs}
                visible={open}
                ref={refModal}
                editRow={selectedRows[0] ? selectedRows[0] : {}}
            />
        </>
    );
};

const SelectImport = ({ selectedRows, load }) => {
    const refModal = useRef();
    const resultRef = useRef();
    const [configs, setConfigs] = useState([
        {
            type: "SELECT",
            labelName: "导入模版",
            labelKey: "code",
            required: true,
            list: [],
        },
    ]);
    useEffect(() => {
        lib.request({
            url: "/ccs/refund/part/import/type",
            success: res => {
                configs[0].list = res;
                setConfigs([...configs]);
            },
        });
    }, []);

    const [open, setOpen] = useState(false);

    const onOk = data => {
        // lib.openPage(`/excel/import-data?page_title=调整库存导入&code=REFUND_ORDER_PART`, () => this.load());
        onCancel();
        const url = `/excel/import-excel?page_title=部分退导入&api=${encodeURIComponent(
            "/ccs/refund/part",
        )}&funcCode=${encodeURIComponent(data.code)}`;
        lib.openPage(url, () => {
            load();
        });
    };
    const onCancel = () => {
        setOpen(false);
    };

    return (
        <>
            <Button
                onClick={() => {
                    setOpen(true);
                }}>
                部分退导入
            </Button>
            <NewModal
                title={"部分退导入"}
                onOk={onOk}
                onCancel={onCancel}
                configList={configs}
                visible={open}
                formItemLayout={{ style: { marginTop: "10px" } }}
                header={
                    <Alert
                        message=""
                        description={
                            <>
                                <p style={{ margin: "10px", color: "red" }}>1.平台销退单请确保【质检通过】</p>
                                <p style={{ margin: "10px", color: "red" }}>
                                    2.导入失败，提示清单不存在时请检查表格内容是否有误
                                </p>
                                <p style={{ margin: "10px", color: "red" }}>
                                    3.导入完成请检查部分退清单的逆向申报数量是否准确
                                </p>{" "}
                            </>
                        }
                        type="info"
                    />
                }
                ref={refModal}
            />
        </>
    );
};
