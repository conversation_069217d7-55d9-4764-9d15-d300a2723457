import React from "react";
import { Modal, Table } from "antd";
export default ({ open, onClose, data }) => {
    const columns = [
        {
            title: "行号",
            render: (_, row, index) => index + 1,
        },
        {
            title: "服务商运输作业单号",
            dataIndex: "servTransportNo",
            key: "servTransportNo",
        },
        {
            title: "外部单号",
            dataIndex: "outOrderNo",
            key: "outOrderNo",
        },
        {
            title: "运输编号",
            dataIndex: "transportTimesNo",
            key: "transportTimesNo",
        },
        {
            title: "作业单状态",
            dataIndex: "transportOrderStatus",
            key: "transportOrderStatus",
        },
    ];
    return (
        <Modal
            title="运输流水"
            open={open}
            width={900}
            onCancel={() => {
                onClose && onClose();
            }}
            onOk={() => {
                onClose && onClose();
            }}>
            <Table columns={columns} dataSource={data} />
        </Modal>
    );
};
