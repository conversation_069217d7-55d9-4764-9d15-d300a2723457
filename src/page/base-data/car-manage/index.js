import { SearchList } from "@dt/components";
import React, { useRef, useState } from "react";
import { getConfigDataUtils, lib, hooks } from "react-single-app";
import { Switch, message, Button } from "antd";
import axios from "axios";
import TableModal from "./table-modal";
const { useGetAuthButtons } = hooks;
export default () => {
    const [tableOpen, setTableOpen] = useState(false);
    const [dataSource, setDataSource] = useState([]);
    const SearchListRef = useRef();
    const [buttons] = useGetAuthButtons({ systemCode: "CCS_ADMIN", pagePath: "/car-manage" });
    const getConfig = async () => {
        let url = getConfigDataUtils.getDataUrlByDataId(839);
        const res = await axios.get(url);
        return res.data.data;
    };
    return (
        <SearchList
            ref={SearchListRef}
            searchConditionConfig={{
                size: "middle",
            }}
            headerMode={"sticky"}
            scrollMode="tableScroll"
            getConfig={getConfig}
            tableCustomFun={{
                statusFn: (row, index) => {
                    return (
                        <>
                            <Switch
                                // 1: 启用，0:关闭
                                checked={row.enable == 1}
                                checkedChildren={"已启用"}
                                unCheckedChildren={"已禁用"}
                                onChange={e => {
                                    lib.request({
                                        url: "/ccs/vehicleResource/enable",
                                        data: {
                                            ids: String(row.id),
                                            enable: e ? 1 : 0,
                                        },
                                        success: () => {
                                            message.success("修改成功");
                                            SearchListRef.current.load();
                                        },
                                    });
                                }}
                            />
                        </>
                    );
                },
                transportFn: (row, index) => {
                    return (
                        <>
                            <Button
                                type="link"
                                onClick={() => {
                                    console.log(row.fLowResList);
                                    setDataSource(row.fLowResList);
                                    setTableOpen(true);
                                }}>
                                {row.transportTimes}
                            </Button>
                        </>
                    );
                },
            }}
            renderModal={() => (
                <>
                    <TableModal
                        open={tableOpen}
                        onClose={() => {
                            setTableOpen(false);
                            setDataSource([]);
                        }}
                        data={dataSource}
                    />
                </>
            )}
            import={true}
            importAuth={() => {
                return buttons.includes("import");
            }}
        />
    );
};
