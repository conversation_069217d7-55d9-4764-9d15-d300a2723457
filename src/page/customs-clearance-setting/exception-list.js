import React from "react";
import { Config<PERSON><PERSON><PERSON><PERSON>, SearchList, getConfigDataUtils } from "react-single-app";
import axios from "axios";
import { Button, Space, Modal, message } from "antd";

export default class ExceptionList extends SearchList {
    constructor(props) {
        super(props);
    }

    getConfig() {
        return axios.get(getConfigDataUtils.getDataUrlByDataId(528)).then(res => res.data.data);
    }

    editHandle(row) {
        this.setState({
            showModal: true,
            rowData: row,
            type: "edit",
        });
    }

    addHandle() {
        this.setState({
            showModal: true,
            type: "add",
        });
    }

    myOperation(row) {
        return (
            <Space>
                <span className="link" onClick={() => this.editHandle(row)}>
                    编辑
                </span>
            </Space>
        );
    }

    renderLeftOperation() {
        return (
            <Space>
                <Button
                    onClick={() => {
                        this.editHandle();
                    }}
                    type="primary">
                    新增
                </Button>
            </Space>
        );
    }

    renderModal() {
        return (
            <React.Fragment>
                {
                    <EditModal
                        rowData={this.state.rowData}
                        type={this.state.type}
                        dialogClose={ok => {
                            this.setState({
                                rowData: null,
                                showModal: false,
                            });
                            if (ok) {
                                this.load();
                            }
                        }}
                        showModal={this.state.showModal}
                    />
                }
            </React.Fragment>
        );
    }
}

function EditModal({ rowData, showModal, type, dialogClose }) {
    const ref = React.useRef();
    const handleCancel = () => {
        dialogClose(false);
    };
    const handleOk = () => {
        ref.current.submitForm();
    };
    const beforeSubmit = values => {
        if (rowData) values.id = rowData.id;
        return values;
    };
    const onConfigLoadSuccess = config => {
        config.baseInfo.children.map(item => {
            if (item.name === "useTagList") {
                ref.current.initSelect(item, { type: 1 });
            } else {
                ref.current.initSelect(item);
            }
        });
        if (rowData) {
            ref.current.setMergeDetail(rowData);
        }
    };
    const onSubmitSuccess = () => {
        message.success(type === "edit" ? "编辑异常成功" : "添加异常成功");
        dialogClose(true);
    };
    return (
        (<Modal
            destroyOnClose
            onCancel={handleCancel}
            onOk={handleOk}
            title={type === "edit" ? "编辑异常" : "添加异常"}
            open={showModal}>
            <ConfigFormCenter
                ref={ref}
                confData={data}
                disableEdit={type !== "add"}
                submitUrl={type !== "add" ? "/ccs/exception/upsetException" : "/ccs//exception/upsetException"}
                beforeSubmit={beforeSubmit}
                onConfigLoadSuccess={onConfigLoadSuccess}
                onSubmitSuccess={onSubmitSuccess}
            />
        </Modal>)
    );
}

const data = {
    baseInfo: {
        children: [
            {
                label: "异常名称",
                editEnable: true,
                name: "exceptionName",
                type: "textInput",
                rules: [
                    {
                        required: true,
                    },
                ],
            },
            {
                label: "异常描述",
                editEnable: true,
                name: "exceptionDescribe",
                type: "textarea",
                rules: [
                    {
                        required: true,
                    },
                ],
            },
            {
                label: "异常分类",
                editEnable: true,
                name: "exceptionClassify",
                type: "single-select",
                from: "/ccs/exception/exceptionType",
                rules: [
                    {
                        required: true,
                        message: "请选择异常分类",
                    },
                ],
            },
            {
                label: "处理建议",
                editEnable: true,
                name: "handlePropose",
                type: "textarea",
                rules: [
                    {
                        required: true,
                    },
                ],
            },
            {
                label: "是否支持重推",
                editEnable: true,
                name: "repushEnable",
                type: "single-select",
                list: [
                    { name: "是", id: 1 },
                    { name: "否", id: 0 },
                ],
                rules: [
                    {
                        required: true,
                        message: "请选择是否支持重推",
                    },
                ],
            },
            {
                label: "用途标签",
                editEnable: true,
                name: "useTagList",
                type: "single-select",
                customConfig: { mode: "multiple" },
                from: "/ccs/sys/common/enum/list",
            },
        ],
        label: "",
        name: "baseInfo",
        isGroup: true,
        className: "baseInfo",
    },
};
