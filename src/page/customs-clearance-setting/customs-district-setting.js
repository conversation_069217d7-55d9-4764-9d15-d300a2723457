import React from "react";
import { Config<PERSON><PERSON><PERSON><PERSON>, SearchList, getConfigDataUtils } from "react-single-app";
import axios from "axios";
import { Button, Space, Modal } from "antd";

export default class CustomsDistrictSetting extends SearchList {
    constructor(props) {
        super(props);
    }

    getConfig() {
        return axios.get(getConfigDataUtils.getDataUrlByDataId(386)).then(res => res.data.data);
    }
    editHandle(row) {
        this.setState({
            showModal: true,
            rowData: row,
        });
    }
    myOperation(row) {
        return (
            <Space>
                <span className="link" onClick={() => this.editHandle(row)}>
                    编辑
                </span>
            </Space>
        );
    }

    renderLeftOperation() {
        return (
            <Space>
                <Button
                    onClick={() => {
                        this.editHandle();
                    }}
                    type="primary">
                    新增关区
                </Button>
            </Space>
        );
    }

    renderModal() {
        return (
            <React.Fragment>
                {
                    <EditModal
                        rowData={this.state.rowData}
                        dialogClose={ok => {
                            this.setState({ showModal: false });
                            if (ok) {
                                this.load();
                            }
                        }}
                        showModal={this.state.showModal}
                    />
                }
            </React.Fragment>
        );
    }
}

function EditModal({ rowData, showModal, dialogClose }) {
    const ref = React.useRef();
    const handleCancel = () => {
        dialogClose(false);
    };
    const handleOk = () => {
        ref.current.submitForm();
    };
    const beforeSubmit = values => {
        values.id = rowData.id;
        return values;
    };
    const onConfigLoadSuccess = () => {
        if (rowData) {
            ref.current.setMergeDetail(rowData);
        }
    };
    const onSubmitSuccess = () => {
        dialogClose(true);
    };
    return (
        (<Modal
            destroyOnClose
            onCancel={handleCancel}
            onOk={handleOk}
            title={rowData ? "新增关区" : "编辑关区"}
            open={showModal}>
            <ConfigFormCenter
                ref={ref}
                confData={data}
                submitUrl={"http://rap2api.taobao.org/app/mock/295480/base/declare/way/submit"}
                beforeSubmit={beforeSubmit}
                onConfigLoadSuccess={onConfigLoadSuccess}
                onSubmitSuccess={onSubmitSuccess}
            />
        </Modal>)
    );
}

const data = {
    baseInfo: {
        children: [
            {
                label: "关区名称",
                name: "name",
                editEnable: false,
                type: "textInput",
                rules: [
                    {
                        required: true,
                        message: "请输入关区名称!",
                    },
                ],
            },
            {
                label: "关区代码",
                editEnable: false,
                name: "code",
                type: "textInput",
                rules: [
                    {
                        required: true,
                        message: "请输入关区代码!",
                    },
                ],
            },
            {
                label: "口岸",
                editEnable: true,
                name: "type",
                wrapperCol: { span: 16 },
                type: "single-select",
                list: [
                    { name: "支付单", id: "payment" },
                    { name: "订单", id: "customsOrder" },
                    {
                        name: "运单",
                        id: "shipment",
                    },
                    { name: "清单", id: "inventory" },
                    { name: "清单取消", id: "inventoryCancel" },
                    {
                        name: "清单退货",
                        id: "inventoryRefund",
                    },
                ],
                rules: [
                    {
                        required: true,
                        message: "请选择口岸!",
                    },
                ],
            },
        ],
        label: "",
        name: "baseInfo",
        isGroup: true,
        className: "baseInfo",
    },
};
