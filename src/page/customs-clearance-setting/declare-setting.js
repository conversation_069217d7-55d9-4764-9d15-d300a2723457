import React, { useEffect } from "react";
import { ConfigFormCenter, lib, SearchList, getConfigDataUtils } from "react-single-app";
import axios from "axios";
import { Button, Space, Modal, Switch, message } from "antd";

export default class extends SearchList {
    constructor(props) {
        super(props);
    }

    getConfig() {
        return axios.get(getConfigDataUtils.getDataUrlByDataId(385)).then(res => res.data.data);
    }

    editHandle(row) {
        this.setState({
            showModal: true,
            rowData: row,
        });
    }

    handleEnable(row, checked) {
        Modal.confirm({
            cancelText: "取消",
            okText: "确定",
            title: "提示",
            content: `是否${row.enable ? "禁用" : "启用"}`,
            onOk: () => {
                let data = { id: row.id, enable: checked };
                lib.request({
                    url: "/ccs/declareWay/changeEnable",
                    data: data,
                    method: "POST",
                    needMask: true,
                    success: res => {
                        message.success(`${row.enable ? "禁用" : "启用"}成功`);
                        this.load();
                    },
                });
            },
        });
    }

    myOperation(row) {
        return (
            <Space>
                {row.enable === 0 && (
                    <span className="link" onClick={() => this.editHandle(row)}>
                        编辑
                    </span>
                )}
                <span className="link" onClick={() => this.deleteHandle(row)}>
                    删除
                </span>
            </Space>
        );
    }

    enableFunc(row) {
        return (
            <React.Fragment>
                <Switch checked={row.enable} onChange={checked => this.handleEnable(row, checked)} />
            </React.Fragment>
        );
    }

    deleteHandle({ id }) {
        let modal = Modal.confirm({
            title: "提示",
            content: "确定删除吗？",
            onOk: () => {
                lib.request({
                    url: "/ccs/declareWay/delete",
                    data: { id },
                    needMask: true,
                    success: res => {
                        message.success("删除成功");
                        this.load();
                        modal.destroy();
                    },
                });
            },
            onCancel: () => modal.destroy(),
        });
    }

    renderLeftOperation() {
        return (
            <Space>
                <Button
                    onClick={() => {
                        this.editHandle();
                    }}
                    type="primary">
                    新增申报方式
                </Button>
            </Space>
        );
    }

    renderModal() {
        return (
            <React.Fragment>
                {
                    <EditModal
                        rowData={this.state.rowData}
                        dialogClose={ok => {
                            this.setState({ showModal: false });
                            if (ok) {
                                this.load();
                            }
                        }}
                        showModal={this.state.showModal}
                    />
                }
            </React.Fragment>
        );
    }
}

function EditModal({ rowData, showModal, dialogClose }) {
    const ref = React.useRef();
    const handleCancel = () => {
        dialogClose(false);
    };
    const handleOk = () => {
        ref.current.submitForm();
    };
    const beforeSubmit = values => {
        if (rowData) {
            values.id = rowData.id;
        }
        return values;
    };
    const onConfigLoadSuccess = config => {
        config.baseInfo.children.map(item => {
            if (item.type === "single-select" && !item.fromParam) {
                ref.current.initSelect(item);
            }
        });
        if (rowData) {
            ref.current.setMergeDetail(rowData);
        }
    };
    const onSubmitSuccess = () => {
        dialogClose(true);
    };
    return (
        (<Modal
            destroyOnClose
            onCancel={handleCancel}
            onOk={handleOk}
            title={rowData ? "编辑申报方式" : "新增申报方式"}
            open={showModal}>
            <ConfigFormCenter
                ref={ref}
                confData={data}
                submitUrl={"/ccs/declareWay/saveOrUpdate"}
                beforeSubmit={beforeSubmit}
                onConfigLoadSuccess={onConfigLoadSuccess}
                onSubmitSuccess={onSubmitSuccess}
            />
        </Modal>)
    );
}

const data = {
    baseInfo: {
        children: [
            {
                label: "申报方式",
                name: "name",
                editEnable: false,
                type: "textInput",
                rules: [
                    {
                        required: true,
                        message: "传输节点!",
                    },
                ],
            },
            {
                label: "code",
                editEnable: false,
                name: "code",
                type: "textInput",
                rules: [
                    {
                        required: true,
                        message: "请输入code!",
                    },
                ],
            },
            {
                label: "申报项",
                editEnable: true,
                name: "type",
                wrapperCol: 16,
                type: "single-select",
                from: "/ccs/declareWay/listDeclareEnum",
            },
            {
                label: "传输节点",
                editEnable: true,
                name: "customsTransferNode",
                wrapperCol: 16,
                type: "single-select",
                rules: [
                    {
                        required: true,
                        message: "请输入code!",
                    },
                ],
                list: [
                    {
                        id: 2,
                        name: "二级节点",
                    },
                    {
                        id: 3,
                        name: "三级节点",
                    },
                ],
            },
            {
                label: "实现方式",
                name: "declareImpl",
                editEnable: false,
                type: "textInput",
                rules: [
                    {
                        required: true,
                        message: "请输入实现方式!",
                    },
                ],
            },
        ],
        label: "",
        name: "baseInfo",
        isGroup: true,
        className: "baseInfo",
    },
};
