import React, { useState, useEffect } from "react";
import { Config<PERSON>orm<PERSON>enter, SearchList, getConfigDataUtils, lib, FormList } from "react-single-app";
import axios from "axios";
import { Button, Space, Modal, message } from "antd";
import "./receipt-mapping-setting.less";
import DrawerForm from "../../components/drawer-form";
import NewModal from "../../components/NewModal";

export default class ReceiptMappingSetting extends SearchList {
    constructor(props) {
        super(props);
    }

    getConfig() {
        return axios.get(getConfigDataUtils.getDataUrlByDataId(525)).then(res => res.data.data);
    }

    addHandle() {
        this.setState({
            showModal: true,
            rowData: {},
            type: "add",
            modalTitle: "新增映射",
        });
    }

    editHandle(row) {
        this.setState({
            showModal: true,
            rowData: row,
            type: "edit",
            modalTitle: "编辑映射",
        });
    }

    deleteHandle(row) {
        let modal = Modal.confirm({
            title: "提示",
            content: "确定删除吗？",
            onOk: () => {
                lib.request({
                    url: "/ccs/receiptMapping/delete",
                    data: { id: row.id },
                    needMask: true,
                    success: res => {
                        message.success("删除成功");
                        this.load();
                        modal.destroy();
                    },
                });
            },
            onCancel: () => modal.destroy(),
        });
    }

    addMappingHandle(row) {
        this.setState({
            showModal: true,
            rowData: row,
            type: "addMapping",
            modalTitle: "添加映射",
        });
    }

    myOperation(row) {
        return (
            <Space>
                {row.mapStatus === 1 && (
                    <span className="link" onClick={() => this.editHandle(row)}>
                        编辑
                    </span>
                )}
                {row.mapStatus === 0 && (
                    <span className="link" onClick={() => this.addMappingHandle(row)}>
                        添加映射
                    </span>
                )}
            </Space>
        );
    }

    renderLeftOperation() {
        return (
            <Space>
                <Button
                    onClick={() => {
                        this.addHandle();
                    }}
                    type="primary">
                    新增
                </Button>
                <WaitMappingInfo />
            </Space>
        );
    }

    renderModal() {
        return (
            <React.Fragment>
                {
                    <EditModal
                        rowData={this.state.rowData}
                        type={this.state.type}
                        title={this.state.modalTitle}
                        dialogClose={ok => {
                            this.setState({
                                rowData: {},
                                showModal: false,
                            });
                            if (ok) {
                                this.load();
                            }
                        }}
                        showModal={this.state.showModal}
                    />
                }
            </React.Fragment>
        );
    }
}

function EditModal({ rowData, showModal, type, dialogClose, title }) {
    const ref = React.useRef();
    const [config, setConfig] = useState(CONFIG_DATA);
    const [groups, setGroups] = useState(GroupList);

    const handleCancel = () => {
        dialogClose(false);
    };
    const handleOk = data => {
        const regexCheckList = [];
        const names = GroupList.map(item => item.name);
        for (let i in data) {
            if (data[i]) {
                names.map((name, index) => {
                    if (i.indexOf(name) > -1) {
                        let regIndex = null;
                        regexCheckList.some((item, index) => {
                            if (item.index == i.split(name)[1]) {
                                regIndex = index;
                            }
                        });
                        if (regIndex === 0 || regIndex > 0) {
                            regexCheckList[regIndex][name] = data[i];
                        } else {
                            regexCheckList.push({ [name]: data[i], index: i.split(name)[1] });
                        }
                    }
                });
            }
        }
        regexCheckList.forEach(item => {
            delete item.index;
        });
        const submitData = {
            action: data.action,
            customsStatusCode: data.customsStatusCode,
            detailCode: data.detailCode,
            status: data.status,
            receiptExplain: data.receiptExplain,
            note: data.note,
            exceptionFlag: data.exceptionFlag && JSON.parse(data.exceptionFlag),
            remark: data.remark,
            finalReceiptFlag: data.finalReceiptFlag && JSON.parse(data.finalReceiptFlag),
            regexCheckList: regexCheckList,
            id: rowData.id,
            exceptionId: data.exceptionId,
        };
        lib.request({
            url: type !== "add" ? "/ccs/receiptMapping/edit" : "/ccs/receiptMapping/save",
            data: submitData,
            success: data => {
                message.success(type === "edit" ? "编辑成功" : "新增成功");
                dialogClose(true);
            },
        });
        // ref.current.submitForm()
    };
    const beforeSubmit = values => {
        if (rowData) {
            values.id = rowData.id;
        }
        return values;
    };
    const onConfigLoadSuccess = config => {
        config.baseInfo.children.map(item => {
            ref.current.initSelect(item);
        });
        if (rowData) {
            let config = ref.current.config;
            config.baseInfo.children.map(item => {
                if (item.name === "exceptionId") {
                    item.hidden = !rowData.exceptionFlag;
                }
                return item;
            });
        }
    };

    const onSubmitSuccess = () => {
        dialogClose(true);
    };

    const beforeSetDetail = values => {
        const { regexCheckList } = { ...values };
        const obj = {};
        if (Array.isArray(regexCheckList)) {
            regexCheckList?.map((item, index) => {
                obj[GroupList[0].name + (index + 1)] = item[GroupList[0].name];
                obj[GroupList[1].name + (index + 1)] = item[GroupList[1].name];
            });
            // delete values.regexCheckList
        }
        return { ...values, ...obj };
    };

    const onSinglesSelectChange = desc => {
        switch (desc.name) {
            case "exceptionFlag":
                config.baseInfo.children.map(item => {
                    if (item.name === "exceptionId") {
                        item.hidden = desc.value.value;
                    }
                    return item;
                });
                ref.current.changeConfig(config);
                break;
            default:
                break;
        }
    };

    useEffect(() => {
        setSelectList();
        getGroupData();
    }, []);

    useEffect(() => {
        changeConfig();
    }, [showModal, type]);

    const changeConfig = () => {
        config.map(item => {
            if ((type !== "add" ? ["code", "action", "customsStatusCode"] : ["code"]).includes(item.name)) {
                item.disabled = true;
            } else {
                item.disabled = false;
            }
        });
        setConfig([...config]);
    };

    const setSelectList = () => {
        const arr = [];
        CONFIG_DATA.map((item, index) => {
            if (item.from) {
                arr.push(
                    new Promise((resolve, reject) => {
                        lib.request({
                            url: item.from,
                            success: data => {
                                resolve({ data, index });
                            },
                            fail: () => {
                                reject();
                            },
                        });
                    }),
                );
            }
            if (item.name === "exceptionFlag") {
                item.onChange = data => {
                    if (data) {
                        config[index + 1].hidden = false;
                        config[index + 1].rules = [
                            {
                                required: true,
                                message: "请选择异常名称",
                            },
                        ];
                        setConfig([...config]);
                    } else {
                        config[index + 1].hidden = true;
                        config[index + 1].rules = null;
                        setConfig([...config]);
                    }
                };
            }
        });
        Promise.all(arr).then(res => {
            res.map((item, index) => {
                config[item.index].list = item.data;
            });
            setConfig([...config]);
        });
    };

    const getGroupData = () => {
        const arr = [];
        GroupList.map((item, index) => {
            if (item.from) {
                arr.push(
                    new Promise((resolve, reject) => {
                        lib.request({
                            url: item.from,
                            success: data => {
                                resolve({ data, index });
                            },
                            fail: () => {
                                reject();
                            },
                        });
                    }),
                );
            }
        });
        Promise.all(arr).then(res => {
            res.map((item, index) => {
                groups[item.index].list = item.data;
            });
            setGroups([...groups]);
        });
    };

    useEffect(() => {
        if (rowData) {
            config.map((item, index) => {
                if (item.name === "exceptionFlag") {
                    rowData && console.log(rowData[item.name]);
                    if (rowData && rowData[item.name]) {
                        config[index + 1].hidden = false;
                        config[index + 1].rules = [
                            {
                                required: true,
                                message: "请选择异常名称",
                            },
                        ];
                    } else {
                        config[index + 1].hidden = true;
                        config[index + 1].rules = null;
                    }
                }
            });
            setConfig([...config]);
        }
    }, [rowData]);

    return (
        <>
            <DrawerForm
                onClose={handleCancel}
                open={showModal}
                onOk={handleOk}
                title={title}
                configData={config}
                beforeSetDetail={beforeSetDetail}
                beforeSubmit={beforeSubmit}
                onConfigLoadSuccess={onConfigLoadSuccess}
                onSubmitSuccess={onSubmitSuccess}
                onSinglesSelectChange={onSinglesSelectChange}
                disableEdit={type !== "add"}
                submitUrl={type !== "add" ? "/ccs/receiptMapping/edit" : "/ccs/receiptMapping/save"}
                formProps={{ layout: "vertical" }}
                groups={groups}
                editRow={rowData}
                allowAdd={true}
            />
        </>
    );
}

const CONFIG_DATA = [
    {
        label: "code",
        name: "code",
        editEnable: false,
        type: "input",
    },
    {
        label: "申报项",
        name: "action",
        editEnable: false,
        type: "select",
        from: "/ccs/route/listAction",
        list: [],
        rules: [
            {
                required: true,
                message: "请选择申报项",
            },
        ],
        // customConfig: { style: { width: '30%' } },
    },
    {
        label: "海关状态码",
        editEnable: false,
        name: "customsStatusCode",
        type: "select",
        from: "/ccs/singleInvtOrder/listCustomsStatus",
        list: [],
        customConfig: { together: true, showSearch: true, style: { width: "30%" } },
    },
    {
        label: "详细状态码",
        editEnable: false,
        name: "detailCode",
        type: "input",
        // customConfig: { style: { width: '30%' } },
    },
    {
        label: "海关回执",
        editEnable: false,
        name: "note",
        type: "textarea",
    },
    {
        label: "映射说明",
        editEnable: true,
        name: "receiptExplain",
        type: "textarea",
        rules: [
            {
                required: true,
            },
        ],
    },
    {
        label: "申报项状态",
        editEnable: true,
        name: "status",
        type: "select",
        from: "/ccs/receiptMapping/declareItemStatus",
        customConfig: { showSearch: true },
        rules: [
            {
                required: true,
                message: "请选择申报项状态",
            },
        ],
        list: [],
    },
    {
        label: "是否异常",
        editEnable: true,
        name: "exceptionFlag",
        type: "select",
        rules: [
            {
                required: true,
                message: "请选择是否异常",
            },
        ],
        list: [
            { name: "否", id: false },
            { name: "是", id: true },
        ],
    },
    {
        label: "异常名称",
        editEnable: true,
        name: "exceptionId",
        type: "select",
        hidden: true,
        list: [],
        from: "/ccs/exception/exceptionName",
    },
    {
        label: "是否终态回执",
        editEnable: true,
        name: "finalReceiptFlag",
        type: "select",
        list: [
            { name: "否", id: false },
            { name: "是", id: true },
        ],
    },

    {
        label: "备注",
        editEnable: true,
        name: "remark",
        type: "textarea",
    },
];

const GroupList = [
    {
        label: "正则表达式",
        editEnable: true,
        name: "regex",
        type: "input",
        delMain: true,
        rules: [
            {
                validator(_, value) {
                    let reg = new RegExp(
                        "[`~!%@#$^&*()=|{}':;',\\[\\]<>《》/?~！@#￥……&*（）|{}【】‘；：”“'\"。，、？.·-]",
                    );
                    if (reg.test(value)) {
                        return Promise.reject("不允许出现符号");
                    }
                    return Promise.resolve();
                },
            },
        ],
    },
    {
        label: "code",
        editEnable: true,
        name: "mappingCode",
        type: "select",
        from: "/ccs/receiptMapping/listCode",
        list: [],
        rules: [{ required: true, message: `请输入code` }],
    },
];

/**
 * 待出来回执映射
 * @returns {JSX.Element}
 * @constructor
 */
function WaitMappingInfo() {
    const [waitMapping, setWaitMapping] = useState(1);
    useEffect(() => {
        lib.request({
            url: "/ccs/receiptMapping/findPendingCount",
            success: res => {
                setWaitMapping(res);
            },
        });
    }, []);

    return <span>待处理映射：{waitMapping}</span>;
}
