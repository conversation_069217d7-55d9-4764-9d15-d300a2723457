import React, { useState, useEffect } from "react";
import { ConfigCenter, Uploader, lib, event } from "react-single-app";
import { Checkbox, Button, Modal, Drawer, Input, Select, Form, message, Tooltip } from "antd";
class App extends ConfigCenter {
    componentDidMount() {}

    renderRightOperation() {
        //渲染自己的按钮
        return <React.Fragment>{/* <Button onClick={() => this.exportFunc()}>导出</Button> */}</React.Fragment>;
    }

    // 导出数据
    exportFunc() {
        var data = {};
        data.currentPage = this.state.pagination.currentPage;
        data.pageSize = this.state.pagination.pageSize;
        var page = this.state.page;
        for (var key in this.state.page) {
            if (page[key].value !== "") {
                data[key] = page[key].value;
            }
        }
        data.bookItemId = lib.getParam("bookItemId");
        lib.request({
            url: "/ccs/itemRegionLog/exportExcelByDownLoadCenter",
            data,
            needMask: true,
            success: res => {
                if (res) {
                    let url = `/download-center/${new Date().getTime()}?page_title=下载中心&config_id=1593580312171226&refresh_event=${new Date().getTime()}`;
                    window.indexProps.history.push(url);
                    event.emit("add-page", {
                        url,
                    });
                }
            },
        });
    }
}
export default App;
