import React, { Fragment } from "react";

import { <PERSON><PERSON><PERSON>, getConfigDataUtils, lib } from "react-single-app";
import axios from "axios";
import { <PERSON><PERSON>, But<PERSON>, message, Space } from "antd";
import moment from "moment";
export default class implementMange extends SearchList {
    constructor(props) {
        super(props);
    }
    componentDidMount() {
        let toTime = moment().format("YYYY-MM-DD"),
            fromTime = moment().subtract(1, "months").format("YYYY-MM-DD");
        this.changeImmutable({
            createTimeFrom: moment(fromTime + " 00:00:00.000").format("x"),
            createTimeTo: moment(toTime + " 23:59:59.999").format("x"),
        });
    }
    getConfig() {
        return axios.get(getConfigDataUtils.getDataUrlByDataId(459)).then(res => res.data.data);
    }
    handleOk() {
        this.setState({
            isvisible: false,
        });
    }
    handleCancel() {
        this.setState({
            isvisible: false,
        });
    }
    handleCopy(requestData) {
        let transfer = document.createElement("input");
        document.body.appendChild(transfer);
        transfer.value = requestData; // 这里表示想要复制的内容
        transfer.focus();
        transfer.select();
        if (document.execCommand("copy")) {
            document.execCommand("copy");
        }
        transfer.blur();
        message.success("复制成功");
        document.body.removeChild(transfer);
    }

    renderModal() {
        let requestData = this.state.requestData;
        return (
            <Fragment>
                {
                    this.state.isvisible && (
                        <Modal
                            open={this.state.isvisible}
                            title="原始报文"
                            onCancel={() => this.handleCancel()}
                            footer={[
                                <Button onClick={() => this.handleCancel()}>取消</Button>,
                                <Button onClick={() => this.handleCopy(requestData)}>复制报文</Button>,
                            ]}>
                            <p>{this.state.requestData}</p>
                        </Modal>
                    )
                    // <div>234</div>
                }
            </Fragment>
        );
    }
    getOperation(row) {
        return (
            <div>
                <a
                    onClick={() => {
                        this.setState({
                            isvisible: true,
                            requestData: row.requestData,
                        });
                    }}>
                    {" "}
                    查看报文
                </a>
            </div>
        );
    }

    activeDataFun(row) {
        return (
            <div>
                <a
                    onClick={() => {
                        this.setState({
                            isvisible: true,
                            requestData: row.sendRecordJson,
                        });
                    }}>
                    {" "}
                    查看报文
                </a>
            </div>
        );
    }

    batchNeglectCull() {
        let { selectedRows } = this.state;
        let idList = selectedRows.reduce((prev, curr) => [...prev, curr.id], []);
        if (idList.length === 0) {
            message.warning("请选择数据");
            return;
        }
        lib.request({
            url: "/ccs/message/task/retry",
            data: { ids: idList.join(",") },
            success: res => {
                message.success("重试成功");
            },
        });
    }

    renderLeftOperation() {
        return (
            <Space>
                <Button
                    onClick={() => {
                        this.batchNeglectCull();
                    }}
                    type="primary">
                    批量重试
                </Button>
            </Space>
        );
    }
}
