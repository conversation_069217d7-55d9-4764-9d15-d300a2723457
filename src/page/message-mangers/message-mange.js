import React, { Fragment } from "react";

import { SearchList, getConfigDataUtils, lib } from "react-single-app";
import axios from "axios";
import { <PERSON><PERSON>, <PERSON><PERSON>, message } from "antd";
import moment from "moment";
import NewModal from "../../components/NewModal";
export default class MessageMange extends SearchList {
    constructor() {
        super();
        this.state.addConfig = [
            {
                type: "SELECT",
                labelName: "消息类型",
                list: [],
                ccs: "/ccs/message/listMessageType",
                labelKey: "messageType",
                required: true,
                onChange: data => {
                    lib.request({
                        url: "/ccs/message/listContentType",
                        data: {
                            messageType: data,
                        },
                        success: data => {
                            this.state.addConfig[1].list = data;
                            this.setState({
                                addConfig: this.state.addConfig,
                            });
                            this.newModalRef.setFormValue([
                                {
                                    name: "messageContentType",
                                    value: "",
                                },
                            ]);
                        },
                    });
                },
            },
            {
                type: "SELECT",
                labelName: "消息内容",
                list: [],
                labelKey: "messageContentType",
                required: true,
            },
            {
                type: "TEXTAREA",
                labelName: "业务编码",
                labelKey: "businessCodes",
                required: true,
            },
        ];
    }

    getConfig() {
        return axios.get(getConfigDataUtils.getDataUrlByDataId(508)).then(res => res.data.data);
    }
    componentDidMount() {
        let toTime = moment().format("YYYY-MM-DD"),
            fromTime = moment().subtract(1, "months").format("YYYY-MM-DD");
        this.changeImmutable({
            createTimeFrom: moment(fromTime + " 00:00:00.000").format("x"),
            createTimeTo: moment(toTime + " 23:59:59.999").format("x"),
        });

        this.getSelectData();
    }
    handleOk() {
        this.setState({
            isvisible: false,
        });
    }
    handleCancel() {
        this.setState({
            isvisible: false,
        });
    }
    handleCopy(requestData) {
        let transfer = document.createElement("input");
        document.body.appendChild(transfer);
        transfer.value = requestData; // 这里表示想要复制的内容
        transfer.focus();
        transfer.select();
        if (document.execCommand("copy")) {
            document.execCommand("copy");
        }
        transfer.blur();
        message.success("复制成功");
        document.body.removeChild(transfer);
    }

    addMsg() {
        this.setState({
            addBol: true,
        });
    }

    getSelectData() {
        const { addConfig } = this.state;
        const arr = [];
        addConfig.map((item, index) => {
            if (item.ccs) arr.push({ url: item.ccs, index });
        });
        const promise = Promise.all(
            arr.map(item => {
                return new Promise((resolve, reject) => {
                    lib.request({
                        url: item.url,
                        success(data) {
                            resolve({ data: data, index: item.index });
                        },
                    });
                });
            }),
        );
        promise.then(res => {
            res.forEach((item, index) => {
                addConfig[index].list = item.data;
            });
            this.setState({
                addConfig,
            });
        });
    }

    renderLeftOperation() {
        return (
            <>
                <Button onClick={() => this.batchRetry()}>批量重试</Button>
                <Button type="primary" onClick={() => this.addMsg()}>
                    新增消息
                </Button>
            </>
        );
    }

    addOK(data) {
        const arr = data.businessCodes.split("\n");
        data.businessCodes = arr
            .map(item => {
                return item.replace(/(^\s+)|(\s+$)/g, "");
            })
            .join(",");
        lib.request({
            url: "/ccs/message/createMessageBatch",
            data,
            needMask: true,
            success: res => {
                message.success("新增成功");
                this.load();
                this.setState({ addBol: false });
            },
        });
    }

    renderModal() {
        let requestData = this.state.requestData;
        return (
            <Fragment>
                {this.state.isvisible && (
                    <Modal
                        open={this.state.isvisible}
                        title="原始报文"
                        onCancel={() => this.handleCancel()}
                        footer={[
                            <Button onClick={() => this.handleCancel()}>取消</Button>,
                            <Button onClick={() => this.handleCopy(requestData)}>复制报文</Button>,
                        ]}>
                        <p>{this.state.requestData}</p>
                    </Modal>
                )}
                <NewModal
                    visible={this.state.addBol}
                    configList={this.state.addConfig}
                    ref={ref => (this.newModalRef = ref)}
                    title={"新增消息"}
                    okText={"保存"}
                    onOk={this.addOK.bind(this)}
                    onCancel={() => {
                        this.setState({ addBol: false });
                    }}
                />
            </Fragment>
        );
    }
    requestDataFun(row) {
        return (
            <div>
                <a
                    onClick={() => {
                        this.setState({
                            isvisible: true,
                            requestData: row.requestData,
                        });
                    }}>
                    {" "}
                    查看报文
                </a>
            </div>
        );
    }
    activeDataFun(row) {
        return (
            <div>
                <a
                    onClick={() => {
                        this.setState({
                            isvisible: true,
                            requestData: row.activeData,
                        });
                    }}>
                    {" "}
                    查看报文
                </a>
            </div>
        );
    }

    batchRetry() {
        let { selectedRows } = this.state;
        let idList = selectedRows.reduce((prev, curr) => [...prev, curr.id], []);
        if (idList.length === 0) {
            message.warning("请选择数据");
            return;
        }
        lib.request({
            url: "/ccs/message/retry",
            data: { idList: idList },
            success: res => {
                message.success("重试成功");
            },
        });
    }
}
