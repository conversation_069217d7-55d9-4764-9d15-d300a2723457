import React from "react";
import { ConfigCenter, lib } from "react-single-app";
class App extends ConfigCenter {
    constructor(props) {
        super(props);
        this.state.data = {};
        this.state.startDate = null;
    }
    componentDidMount() {}
    fetchBill(data) {
        lib.request({
            url: "/ccs/taxesTenant/sumSettledAmount",
            data: data,
            needMask: true,
            success: res => {
                console.log(res);
                if (res) {
                    this.setState({
                        data: res,
                    });
                }
            },
        });
    }
    load(needMask) {
        let { searchConditions, pagination } = this.state;
        var data = {};
        Object.assign(data, pagination, searchConditions);
        this.fetchBill(data);
        lib.request({
            url: this.state.config.requestUrl,
            data: data,
            needMask: needMask,
            success: json => {
                this.setState({
                    pagination: json.page || {},
                    dataList: json.dataList || [],
                });
            },
        });
    }
    renderLeftOperation() {
        const { data } = this.state;
        return (
            <pre style={{ lineHeight: 2.3, float: "left" }}>
                当前已汇总金额：{data.settledAmount}(海关实扣) 当前未汇总金额：{data.unSettleAmount}(海关未扣)
            </pre>
        );
    }
}
export default App;
