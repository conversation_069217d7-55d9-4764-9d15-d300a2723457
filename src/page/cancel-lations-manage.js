import React, { useState, useEffect } from "react";
import "./shop-good.less";
import { Button, Modal, Form, Tooltip, message, Tabs, Table, Checkbox, Space } from "antd";
import NewModal from "../components/NewModal";
import { ConfigCenter, HOC, lib, SearchList, getConfigDataUtils } from "react-single-app";
import axios from "axios";
import moment from "moment/moment";
const TabPane = Tabs.TabPane;

@HOC.mapAuthButtonsToState({ buttonCodeArr: ["exportAuth"] })
class App extends SearchList {
    constructor(props) {
        super(props);
        this.state.statusDist = [];
        this.state.disabled = false;
        this.state.deleteVisible = false;
        this.state.cancelVisible = false;
        this.state.buttonAuth = { exportAuth: false };
        this.state.deleteData = {
            failRecordList: [],
            successRecordList: [],
        };
        this.state.importList = {
            failRecordList: [],
            successRecordList: [],
        };
        this.state.modalTitle = "批量手动操作";
        this.state.upUrl = "/ccs/inventory-cancel/manualOperation";
        this.state.configList = [
            {
                type: "SELECT",
                labelName: "手动操作原因",
                labelKey: "cancelReason",
                list: [],
                ccs: "/ccs/inventroy-cancel/operateReasonEnums",
                required: true,
            },
            {
                type: "RADIO",
                labelName: "撤单状态",
                labelKey: "status",
                list: [
                    { name: "初始化", value: "INT" },
                    { name: "待总署审核", value: "AUDITING" },
                    {
                        name: "审核通过",
                        value: "AUDIT_PASS",
                    },
                    { name: "撤单失败", value: "CANCEL_FAIL" },
                ],
                required: true,
                onChange: (e, form) => {
                    if (e.target.value === "CANCEL_FAIL") {
                        this.state.configList[2].hide = false;
                    } else {
                        this.state.configList[2].hide = true;
                    }
                    this.setState(this.state);
                },
            },
            {
                type: "SELECT",
                labelName: "手工回执",
                labelKey: "manualReceipt",
                list: [],
                ccs: "/ccs/inventroy-cancel/customsLogisticsStatus",
                hide: true,
                required: true,
            },
        ];
    }
    getConfig() {
        let url = getConfigDataUtils.getDataUrlByDataId(594);
        return axios.get(url).then(res => res.data.data);
    }
    componentDidMount() {
        const beginCreateTime = moment().subtract(2, "month").format("YYYY-MM-DD");
        const endCreateTime = moment().format("YYYY-MM-DD");
        this.changeImmutable({
            beginCreateTime: Number(moment(beginCreateTime + " 00:00:00.000").format("x")),
            endCreateTime: Number(moment(endCreateTime + " 23:59:59.999").format("x")),
        });
        lib.request({
            url: "/ccs/inventroy-cancel/list-status",
            success: res => {
                this.setState({
                    statusDist: res,
                });
            },
        });
        this.getConfigList();
    }

    getConfigList() {
        let { configList } = this.state;
        configList.map(item => {
            if (item.ccs) {
                lib.request({
                    url: item.ccs,
                    needMask: false,
                    success: res => {
                        item.lists = res;
                        item.list = res;
                        // console.log(res, "res");
                        this.setState({
                            configList,
                        });
                    },
                });
            }
        });
        this.setState({
            configList,
        });
    }

    renderStatus(row) {
        let name = "";
        this.state.statusDist.map(item => {
            if (row.status === item.id) {
                name = item.name;
            }
        });
        return name;
    }

    showTrackLog(row) {
        return (
            <a
                onClick={() => {
                    lib.openPage(`/declaration-manage-detail?orderId=${row.orderId}&page_title=申报单详情`, () =>
                        this.load(),
                    );
                }}>
                {row.refOrderSn}
            </a>
        );
    }
    renderDetail(row) {
        return (
            <Tooltip title={row.customsCheckDetail}>
                <span>{row.customsStatus}</span>
            </Tooltip>
        );
    }
    getCheckedRows() {
        return this.state.selectedRows;
    }

    calcIds() {
        let list = this.getCheckedRows(),
            ids = "";
        list.map(item => {
            ids += item.id + ",";
        });
        return ids.slice(0, -1);
    }
    renderLeftOperation() {
        let { cancelDisabled } = this.state;
        return (
            <Space>
                <Button
                    type="primary"
                    // disabled={this.state.disabled}
                    onClick={() => this.decalres()}>
                    批量申报
                </Button>
                <Button onClick={() => this.cancels()}>批量取消</Button>

                <Button onClick={() => this.deletes()}>批量删除</Button>
                <Button onClick={() => this.auditHandle()}>手动审核</Button>
                {this.state.buttons && this.state.buttons.includes("batch-manual-processing") && (
                    <Button
                        type="primary"
                        onClick={() => {
                            let ids = this.calcIds();
                            if (ids == "") {
                                message.warning("请选择数据");
                                return;
                            }
                            this.setState({
                                visible: true,
                                ids: ids,
                            });
                            this.getConfigList();
                        }}>
                        批量手动操作
                    </Button>
                )}

                {/* <a className="import ant-btn">
                    <input type='file' onChange={(e) => this.importFunc(e)} />
                    批量导入
                </a> */}
            </Space>
        );
    }

    // 手动审核
    auditHandle() {
        let list = this.getCheckedRows()
            .filter(item => item.checked && item.status === "AUDITING")
            .reduce((prev, curr) => [...prev, curr.id], []);
        if (list.length == 0) {
            message.warning("请选择待总署审核状态的数据");
            return;
        }
        let modal = Modal.confirm({
            title: "提示",
            content: "请确认完成手动审核",
            onOk: () => {
                lib.request({
                    url: "/ccs/inventory-cancel/manualReview",
                    data: {
                        ids: list.join(","),
                    },
                    needMask: true,
                    success: res => {
                        message.success("手动审核成功");
                        this.load();
                        modal.destroy();
                    },
                });
            },
            onCancel: () => modal.destroy(),
        });
    }

    // 导入预览
    importFunc(e) {
        var file = e.currentTarget.files[0];
        if (file) {
            var formData = new FormData();
            formData.append("file", file);
            e.currentTarget.value = "";
            lib.request({
                url: "/ccs/inventroy-cancel/pre-import",
                method: "POST",
                needMask: true,
                data: formData,
                success: res => {
                    this.setState({
                        importList: res,
                        visible: true,
                    });
                },
            });
        }
    }

    tdClick() {
        this.setState(this.state, () => {
            let list = this.state.dataList.filter(item => item.checked),
                cancelDisabled = false,
                disabled = false;
            list.map(item => {
                if (item.status !== "INT" && item.status !== "FAIL") {
                    disabled = true;
                }
                if (item.status !== "INT" && item.status !== "AUDIT_REJECT") {
                    cancelDisabled = true;
                }
            });
            this.setState({
                disabled,
                cancelDisabled,
            });
        });
    }

    decalres() {
        let list = this.getCheckedRows();
        if (list && list.length) {
            let ids = "";
            list.map(item => {
                ids += item.id + ",";
            });
            lib.request({
                url: "/ccs/inventroy-cancel/do-declare",
                data: {
                    ids: ids.slice(0, -1),
                },
                needMask: true,
                success: res => {
                    // if (res.errorMessage) {
                    //     message.error(res.errorMessage);
                    // } else {
                    message.success("申报成功");
                    this.load(true);
                    // }
                },
            });
        } else {
            message.warning("请选择数据");
        }
    }

    deletes() {
        let list = this.getCheckedRows();
        if (list && list.length) {
            let modal = Modal.confirm({
                cancelText: "取消",
                okText: "确定",
                title: "确认删除吗？",
                onOk: () => {
                    let ids = "";
                    list.map(item => {
                        ids += item.id + ",";
                    });
                    lib.request({
                        url: "/ccs/inventroy-cancel/delete",
                        data: {
                            ids: ids.slice(0, -1),
                        },
                        needMask: true,
                        success: res => {
                            if (res) {
                                // message.success("删除成功")
                                // this.load()
                                this.setState({
                                    deleteVisible: true,
                                    deleteData: res,
                                });
                            }
                            modal.destroy();
                        },
                    });
                },
            });
        } else {
            message.warning("请选择数据");
        }
    }

    cancels() {
        let list = this.getCheckedRows();
        if (list && list.length) {
            let modal = Modal.confirm({
                cancelText: "取消",
                okText: "确定",
                title: "确认取消撤单吗？",
                onOk: () => {
                    let ids = "";
                    list.map(item => {
                        ids += item.id + ",";
                    });
                    lib.request({
                        url: "/ccs/inventroy-cancel/cancel",
                        data: {
                            ids: ids.slice(0, -1),
                        },
                        needMask: true,
                        success: res => {
                            this.setState({
                                cancelVisible: true,
                                deleteData: res,
                            });
                            modal.destroy();
                        },
                    });
                },
            });
        } else {
            message.warning("请选择数据");
        }
    }

    modalOk() {
        if (this.state.importList.successRecordList.length) {
            lib.request({
                url: "/ccs/inventroy-cancel/submit/import",
                method: "POST",
                data: {
                    recordList: this.state.importList.successRecordList,
                },
                needMask: true,
                success: res => {
                    if (res) {
                        this.setState({
                            visible: false,
                        });
                        message.success("导入成功");
                        this.load(true);
                    }
                },
            });
        } else {
            message.warning("校验成功数据为0，请重新导入");
        }
    }

    afterClose() {
        this.setState({
            importList: {
                successRecordList: [],
                failRecordList: [],
            },
        });
    }

    handleOk(values, modalForm) {
        lib.request({
            url: this.state.upUrl,
            data: Object.assign(values, { ids: this.state.ids }),
            method: "POST",
            needMask: true,
            success: res => {
                this.setState({
                    visible: false,
                    editRow: null,
                });
                this.load(true);
            },
        });
    }

    handleCancel() {
        this.setState({
            visible: false,
        });
    }

    renderModal() {
        const { deleteVisible, cancelVisible, deleteData, configList, visible } = this.state;

        const columns3 = [
            {
                title: "申报单号",
                dataIndex: "declareNo",
            },
            {
                title: "清单编号",
                dataIndex: "inveNo",
            },
        ];
        const columns4 = [
            {
                title: "申报单号",
                dataIndex: "declareNo",
            },
            {
                title: "清单编号",
                dataIndex: "inveNo",
            },
            {
                title: "错误信息",
                dataIndex: "message",
            },
        ];
        let props = {
            title: this.state.modalTitle,
            onOk: this.handleOk.bind(this),
            onCancel: this.handleCancel.bind(this),
            configList,
            visible,
            form: this.props.form,
            formItemLayout: {
                labelCol: { span: 8 },
                wrapperCol: { span: 14 },
            },
            modalStyle: { width: "800px" },
        };
        return (
            <React.Fragment>
                <Modal
                    title={`取消总数${deleteData.totalCount}`}
                    width={800}
                    open={cancelVisible}
                    onCancel={() => {
                        this.setState({
                            cancelVisible: false,
                            cancelDisabled: false,
                            // deleteData: {}
                        });
                        this.load(true);
                    }}
                    footer={
                        <Button
                            type="primary"
                            onClick={() => {
                                this.setState({
                                    cancelVisible: false,
                                    cancelDisabled: false,
                                    // deleteData: {}
                                });
                                this.load(true);
                            }}>
                            关闭
                        </Button>
                    }>
                    <Tabs defaultActiveKey="0">
                        <TabPane tab={`取消成功(${deleteData.successCount})`} key="0">
                            <Table dataSource={deleteData.successRecordList} columns={columns3} rowKey="idx"></Table>
                        </TabPane>
                        <TabPane tab={`取消失败(${deleteData.failCount})`} key="1">
                            <Table dataSource={deleteData.failRecordList} columns={columns4} rowKey="idx"></Table>
                        </TabPane>
                    </Tabs>
                </Modal>
                <Modal
                    title={`删除总数${deleteData.totalCount}`}
                    width={800}
                    open={deleteVisible}
                    onCancel={() => {
                        this.setState({
                            deleteVisible: false,
                            cancelDisabled: false,
                            // deleteData: {}
                        });
                        this.load(true);
                    }}
                    footer={
                        <Button
                            type="primary"
                            onClick={() => {
                                this.setState({
                                    deleteVisible: false,
                                    cancelDisabled: false,
                                    // deleteData: {}
                                });
                                this.load(true);
                            }}>
                            关闭
                        </Button>
                    }>
                    <Tabs defaultActiveKey="0">
                        <TabPane tab={`删除成功(${deleteData.successCount})`} key="0">
                            <Table dataSource={deleteData.successRecordList} columns={columns3} rowKey="idx"></Table>
                        </TabPane>
                        <TabPane tab={`删除失败(${deleteData.failCount})`} key="1">
                            <Table dataSource={deleteData.failRecordList} columns={columns4} rowKey="idx"></Table>
                        </TabPane>
                    </Tabs>
                </Modal>
                <NewModal {...props} />
            </React.Fragment>
        );
    }
}

export default App;
