import React, { useEffect } from "react";
import ReactDOM from "react-dom";
import { App, ConfigCenter, Uploader, Outlet, lib, ImportExcel, DownloadCenter, hooks } from "react-single-app";
import { ConfigProvider } from "antd";
import { Spa, SpaConfigProvider } from "@dt/components";
import { request } from "@dt/networks";

import zhCN from "antd/lib/locale/zh_CN";
import "./index.less";
import moment from "moment";
import Base from "./page/base";
import DeclareRoute from "./page/declare-route";
import HsCode from "./page/hs-code";
import HsCodeDetail from "./page/hs-code-detail";
import paymentChannel from "./page/payment-channel";
import BooksInventory from "./page/access-management/book-inventory/books-inventory";
import BooksInventoryHK from "./page/books-inventory-hk";
import outboundDetailManage from "./page/outbound-detail-manage";
import checkDetailManage from "./page/check-detail-mange";
// import declarationManage from "./page/declaration-manage";
import declarationManage from "./page/customs-clearance-system/declaration-manage";
import ExpressManage from "./page/express-manage";
import BooksManage from "./page/access-management/books-manage/books-manage";
import TenantTaxesManage from "./page/tenant-taxes-manage";
import EnterpriseManage from "./page/enterprise-manage";
import NewEnterprise from "./page/new-enterprise";
import MerchantsManage from "./page/merchants-manage";
import AnomalyMange from "./page/anomaly-manage";
import TenantTaxesRecharge from "./page/tenant-taxes-recharge";
import TaxAccountManage from "./page/tax-account-manage";
import TaxAccountRecharge from "./page/tax-account-recharge";
import CollectionChannelsManage from "./page/collection-channels-manage";
import DeclareOutboundManage from "./page/declare-outbound-manage";
import DeclareOutboundDetail from "./page/cross_border_entrance/declare-outbound-detail";
import CustomsClearanceManage from "./page/customs-clearance-manage/customs-clearance-manage";
import CustomsClearanceDetail from "./page/customs-clearance-detail";
// import CustomsClearanceDetail2 from "./page/customs-clearance-manage/customs-clearance-detail2";
import NuclearNoteManage from "./page/cross_border_entrance/nuclear-note-manage/nuclear-note-manage";
import NuclearNoteDetail from "./page/cross_border_entrance/nuclear-note-detail";
import NuclearNoteManageHK from "./page/nuclear-note-manage-hk";
import NuclearReleaseManage from "./page/cross_border_entrance/nuclear-release-manage/nuclear-release-manage";
import DeclarePath from "./page/clearance-management/declare-path";
import tenantTaxesBill from "./page/tenant-taxes-bill";
import GoodsRecord from "./page/access-management/goods-record/goods-record";
import GoodsReturnManage from "./page/goods-return-manage";
import OrderDeclaration from "./page/order-declaration";
import WaybillDeclaration from "./page/waybill-declaration";
import PaymentDeclaration from "./page/payment-declaration";
import CancelOrderManage from "./page/bonded-sales/cancel-order-manage/cancel-order-manage";
import CancelLationsManage from "./page/cancel-lations-manage";
import ListDeclareManage from "./page/list-declare-manage";
import CustomsDeclarationCertificate from "./page/customs-declaration-certificate";
import OrderCount from "./page/order-count";
import TestChart from "./page/test-chart";
import GoodsRecordDetails from "./page/access-management/goods-record/goods-record-details";
import TrackLog from "./page/track-log";
import MariasStock from "./page/access-management/marias-stock/marias-stock";
import MariasStockDetail from "./page/access-management/marias-stock/marias-stock-detail";
// 准入管理
import CarryoverBookManage from "./page/access-management/carryover-book-manage";
import ProcessTradeBook from "./page/access-management/process-trade-book";
import ProcessTradeBookDetail from "./page/access-management/process-trade-book-detail";

import SalesManage from "./page/access-management/sales-manage";

import WarehouseAddressManage from "./page/access-management/warehouse-address-manage";
// 京东业务
import RecordManage from "./jd-business/record-manage";
import RecordDetail from "./jd-business/record-detail";
import RecordRefresh from "./jd-business/record-refresh";
import ProcurementManage from "./jd-business/procurement-manage";
import AddProcurement from "./jd-business/add-procurement";
import ProcurementDetail from "./jd-business/procurement-detail";
import LadingBillManage from "./jd-business/lading-bill-manage";
import LadingBillDetail from "./jd-business/lading-bill-detail";
import AddLadingBill from "./jd-business/lading-bill-detail";

// 跨境进口
import LoadingInfomation from "./cross-border/loading-infomation";
import LoadingInfomationDetail from "./cross-border/loading-infomation-detail";
import { NuclearReleaseManageDetail } from "./page/cross_border_entrance";
import RideHailingManage from "./page/access-management/ride-hailing-manage";
import RideHailingDetail from "./page/access-management/ride-hailing-detail";
import RetrieveAuthorization from "./page/cross-border-import/retrieve-authorization";
import CustomsClearanceDetail2 from "./page/cross_border_entrance/customs-clearance-detail";
import DeclarationFormManage from "./page/cross_border_entrance/declaration-form-manage";
import delarationformDetail from "./page/cross_border_entrance/declaration-form-detail";

// 清关申报
import CustomsClearanceChange from "./customs-clearance/customs-clearance-change";
import BooksInventoryDetail from "./page/access-management/book-inventory/books-inventory-detail";
import ShiftManage from "./page/cross-border-import/shift-manage/shift-manage";
import ShiftOrderDetail from "./page/cross-border-import/shift-manage/shift-order-detail";

import config from "../site-config.json";
//申报管理
import DeclareSetting from "./page/customs-clearance-setting/declare-setting";
//import CustomsDistrictSetting from "./page/customs-clearance-setting/customs-district-setting";

// 系统运维
import SubscriptionMange from "./page/message-mangers/subscription-mange";
import ImplementMange from "./page/message-mangers/implement-mange";

import EntityWarehouse from "./page/access-management/entity-warehouse/entity-warehouse";
import CollaborateOrderManage from "./page/cross_border_entrance/collaborate-order/collaborate-order-manage";
import CollaborateOrderDetail from "./page/cross_border_entrance/collaborate-order/collaborate-order-detail";
import MessageMange from "./page/message-mangers/message-mange";
import ReceiptMappingSetting from "./page/customs-clearance-setting/receipt-mapping-setting";
import ExceptionList from "./page/customs-clearance-setting/exception-list";
import { DeclarationManageDetail } from "./page/customs-clearance-business/declaration-manage-detail";
import CullList from "./page/customs-clearancep-services/cull-list";
import CustomsCallback from "./page/system-setting/customs-callback";
import FilingLibrary from "./page/access-management/filing-library/filing-library"; // 备案库
import EndangeredIngredients from "./page/access-management/filing-library/endangered-ingredients";
import AddFilingLibrary from "./page/access-management/filing-library/component/add-filing-library";
import EditFilingLibrary from "./page/access-management/filing-library/component/edit-filing-library";
import TaxAccountPaymentLetterRecharge from "./page/tax-account-payment-letter-recharge";
// 个人工作台
import MailConfiguration from "./page/work-overview/mail-configuration";
import MyMailItems from "./page/work-overview/my-mail-items";
import WorkOverview from "./page/work-overview/work-overview";
import MailDetail from "./page/workbench/mail-pool/mail-detail";
import Workbench from "./page/workbench";
import MailPoolSetting from "./page/workbench/mail-pool";
import DataDictionary from "./page/data-dictionary/index";
import DataDictionaryDetails from "./page/data-dictionary/data-dictionary-details";
import axios from "axios";
import tsTest from "./page/tsTest";

// 数据看板
import AbnormalStatistics from "./page/data-board/abnormal-statistics/abnormal-statistics";
import AllLinkMonitoring from "./page/data-board/all-link-monitoring/all-link-monitoring";
import OrderQuery from "./page/data-board/order-query";
import ItemInventoryFlow from "./page/data-board/Item-inventory-flow/index";
import MaterialInventorySummary from "./page/data-board/material-inventory-summary/material-inventory-summary";
import ToBOverdue from "./page/data-board/tob-overdue/index";

// 京东业务
import NewPurchaseManage from "./jd-business/new-purchase-manage";
import PurchaseDetail from "./jd-business/purchase-detail";
import NewLabBillManage from "./jd-business/new-lad-bill-manage";
import CreateLabBill from "./jd-business/create-lad-bill";
import LabBillDetail from "./jd-business/lad-bill-detail";
import MailFilter from "./page/work-overview/css/mail-filter";
import FacilitatorManage from "./jd-business/facilitator-manage";
import WarehouseAllocation from "./jd-business/facilitator-manage/warehouseAllocation";

// 准入管理
import PhysicalAccountBookDiff from "./page/access-management/physical-account-book-diff";
import TransportWorkOrder from "./page/cross_border_entrance/transport-work-order";
import TransportWorkOrderDetail from "./page/cross_border_entrance/transport-work-order/detail";
import SalesDetail from "./page/access-management/sales-manage/detail";

// 分类监管
import CustomsAccess from "./page/class-supervise/customs-access";
import CustomsAccessDetail from "./page/class-supervise/customs-access-detail";
import InventoryManage from "./page/class-supervise/inventory-manage";
import MergeRelation from "./page/class-supervise/merge-relation";
import NonGuaranteedRelease from "./page/class-supervise/non-guaranteed-release";
import NonGuaranteedDetail from "./page/class-supervise/non-guaranteed-detail";
import MergeRelationDetail from "./page/class-supervise/merge-relation-detail";
import InventoryManageDetail from "./page/class-supervise/inventory-detail";
import GuanqiTransmission from "./page/class-supervise/guanqi-transmission";

// 基础数据
import PortMangage from "./page/port-manage";
import CarManage from "./page/base-data/car-manage";

// 进出对账
import OutboundReconciliation from "./page/in-and-out-reconciliate/outbound-reconciliation";
import ReconciliationOrder from "./page/in-and-out-reconciliate/reconciliation-order";
import embedChatbot from "./gtp-iframe";

moment.locale("zh-cn");
var pageMap = {
    "config-center": ConfigCenter,
    base: Base,
    "payment-channel": paymentChannel,
    "declare-route": DeclareRoute,
    "declare-path": DeclarePath,
    "hs-code": HsCode,
    "hs-code-detail": HsCodeDetail,
    "books-inventory": BooksInventory,
    "books-inventory-detail": BooksInventoryDetail,
    "books-inventory-hk": BooksInventoryHK,
    "outbound-detail-manage": outboundDetailManage,
    "check-detail-manage": checkDetailManage,
    "declaration-manage": declarationManage,
    "express-manage": ExpressManage,
    "books-manage": BooksManage,
    "tenant-taxes-manage": TenantTaxesManage,
    "enterprise-manage": EnterpriseManage,
    "new-enterprise": NewEnterprise,
    "merchants-manage": MerchantsManage,
    "anomaly-manage": AnomalyMange,
    "tenant-taxes-recharge": TenantTaxesRecharge,
    "tax-account-manage": TaxAccountManage,
    "tax-account-recharge": TaxAccountRecharge,
    "tax-account-payment-letter-recharge": TaxAccountPaymentLetterRecharge,
    "collection-channels-manage": CollectionChannelsManage,
    "declare-outbound-manage": DeclareOutboundManage,
    "declare-outbound-detail": DeclareOutboundDetail,
    "customs-clearance-manage": CustomsClearanceManage,
    "customs-clearance-detail": CustomsClearanceDetail,
    "customs-clearance-detail2": CustomsClearanceDetail2,
    "nuclear-note-manage": NuclearNoteManage,
    "nuclear-note-detail": NuclearNoteDetail,
    "nuclear-note-manage-hk": NuclearNoteManageHK,
    "nuclear-release-manage": NuclearReleaseManage,
    "customs-declaration-certificate": CustomsDeclarationCertificate,
    "order-count": OrderCount,
    "test-chart": TestChart,

    // 准入管理
    "carryover-book-manage": CarryoverBookManage,
    "warehouse-address-manage": WarehouseAddressManage,
    //新用户中心
    "download-center": DownloadCenter,
    "import-excel": ImportExcel,

    "tenant-taxes-bill": tenantTaxesBill,
    "goods-record": GoodsRecord,
    "goods-record-details": GoodsRecordDetails,
    "goods-return-manage": GoodsReturnManage,
    "order-declaration": OrderDeclaration,
    "waybill-declaration": WaybillDeclaration,
    "payment-declaration": PaymentDeclaration,
    "cancel-order-manage": CancelOrderManage,
    "cancel-lations-manage": CancelLationsManage,
    "list-declare-manage": ListDeclareManage,
    "record-manage": RecordManage,
    "record-detail": RecordDetail,
    "procurement-manage": ProcurementManage,
    "lading-bill-manage": LadingBillManage,
    "lading-bill-detail": LadingBillDetail,
    "add-lading-bill": AddLadingBill,
    "add-procurement": AddProcurement,
    "procurement-detail": ProcurementDetail,

    tsTest: tsTest,
    // 准入管理
    "physical-account-book-diff": PhysicalAccountBookDiff,
    "process-trade-book": ProcessTradeBook,
    "process-trade-book-detail": ProcessTradeBookDetail,

    "sales-manage": SalesManage,
    "sales-detail": SalesDetail,

    // 跨境进口
    "loading-infomation": LoadingInfomation,
    "loading-infomation-detail": LoadingInfomationDetail,
    "nuclear-release-manage-detail": NuclearReleaseManageDetail,
    "transport-work-order": TransportWorkOrder,
    "transport-work-order-detail": TransportWorkOrderDetail,
    "declaration-form-manage": DeclarationFormManage,
    "declaration-form-detail": delarationformDetail,
    // "declaration-form-detail"

    // 清单申报
    "customs-clearance-change": CustomsClearanceChange,
    "track-log": TrackLog,
    "record-refresh": RecordRefresh,
    "declare-setting": DeclareSetting,
    //"customs-district-setting":CustomsDistrictSetting
    "shift-manage": ShiftManage,
    "shift-detail": ShiftOrderDetail,
    // 系统运维
    "subscription-mange": SubscriptionMange,
    "implement-mange": ImplementMange,
    "entity-warehouse": EntityWarehouse,
    "collaborate-order-manage": CollaborateOrderManage,
    "collaborate-order-detail": CollaborateOrderDetail,
    "message-mange": MessageMange,
    "receipt-mapping-setting": ReceiptMappingSetting,
    "exception-list": ExceptionList,
    "declaration-manage-detail": DeclarationManageDetail,
    "cull-list": CullList,
    // 数据字典
    "data-dictionary": DataDictionary,
    "data-dictionary-details": DataDictionaryDetails,
    "customs-callback": CustomsCallback,
    "filing-library": FilingLibrary,
    "endangered-ingredients": EndangeredIngredients,
    "add-filing-library": AddFilingLibrary,
    "edit-filing-library": EditFilingLibrary,
    "marias-stock": MariasStock,
    "marias-stock-detail": MariasStockDetail,
    // 个人工作台
    "mail-configuration": MailConfiguration,
    "my-mail-items": MyMailItems,
    "work-overview": WorkOverview,
    "mail-detail": MailDetail,
    workbench: Workbench,
    "mail-pool": MailPoolSetting,

    // 数据看板
    "abnormal-statistics": AbnormalStatistics,
    "all-link-monitoring": AllLinkMonitoring,
    "order-query": OrderQuery,
    "material-inventory-summary": MaterialInventorySummary,
    "tob-overdue": ToBOverdue,

    "item-inventory-flow": ItemInventoryFlow,
    // 京东业务
    "new-purchase-manage": NewPurchaseManage,
    "purchase-detail": PurchaseDetail,
    "new-lab-bill-manage": NewLabBillManage,
    "create-lad-bill": CreateLabBill,
    "lad-bill-detail": LabBillDetail,
    "mail-filter": MailFilter,
    "facilitator-manage": FacilitatorManage,
    "warehouse-allocation": WarehouseAllocation,

    // 分类监管
    "customs-access": CustomsAccess,
    "customs-access-detail": CustomsAccessDetail,
    "inventory-manage": InventoryManage,
    "merge-relation": MergeRelation,
    "non-guaranteed-release": NonGuaranteedRelease,
    "non-guaranteed-detail": NonGuaranteedDetail,
    "merge-relation-detail": MergeRelationDetail,
    "inventory-detail": InventoryManageDetail,
    "guanqi-transmission": GuanqiTransmission,

    // 基础数据
    "port-manage": PortMangage,
    "car-manage": CarManage,
    //跨境进口
    "ride-hailing-manage": RideHailingManage,
    "ride-hailing-detail": RideHailingDetail,
    "retrieve-authorization": RetrieveAuthorization,

    // 进出对账
    "outbound-reconciliation": OutboundReconciliation,
    "reconciliation-order": ReconciliationOrder,
};
let siteConfig;
if (config.usrVar === "true") {
    siteConfig = config;
} else {
    siteConfig = config.defaultConfig;
}
lib.setConfig(
    {
        webToken: "admin",
        systemCode: "CCS_ADMIN",
        env: BUILD_ENV,
        configList: "undefined" == typeof config_list ? [] : config_list,
        ...siteConfig,
    },
    {
        platformCode: "DT",
        systemCode: "CCS_ADMIN",
        env: BUILD_ENV,
    },
);
const isDev = process.env.NODE_ENV === "development"; // 是否是开发模式
if (isDev) {
    console.log("本地开发环境");
    lib.getAxios().interceptors.response.use(res => {
        let data = res.data;
        if (data.code === -1001) {
            console.log("为登");
            lib.localDevelopDebug();
        }
        return res;
    });
    lib.getAxios().interceptors.request.use(res => {
        res.headers.accesstoken = lib.getCookie("token");
        return res;
    });
}
let [clientId, clientSecret] = ["9E514E70AD7D485986D687F64616C662", "33F14542BB274284B63147E6C8F3DF9E"];
let baseURL = "https://danding-gateway.yang800.com";
if (["integration", "dev", "test", "pre"].includes(lib.config?.env)) {
    clientId = "96A63530DA0C49BB9FABB66ED40FB3C7";
    clientSecret = "F6A99B36E4D24817AB037237454893D9";
    let gatewayMap = {
        integration: `https://danding-gateway-integration.yang800.com.cn`,
        dev: `https://danding-gateway-dev.yang800.com.cn`,
        test: `https://danding-gateway-test.yang800.com.cn`,
        pre: `https://danding-gateway-pre.yang800.com`,
    };
    baseURL = gatewayMap[lib.config.env];
} else {
    if (lib.config.env === "jz") {
        clientId = "9E514E70AD7D485986D687F64616C662";
        clientSecret = "33F14542BB274284B63147E6C8F3DF9E";
    }
    let gatewayMap = {
        "www.yang800.com": "https://danding-gateway.yang800.com",
        "www.dataeta.com": "https://danding-gateway.dataeta.com",
        "www.yangmaigu.com": "https://danding-gateway.yangmaigu.com",
        "jieztech.com": "https://soul-gateway.jieztech.com",
        "yccp.yang800.com": "https://danding-gateway.yang800.com",
    };
    const hostname = location.hostname;
    if (gatewayMap[location.hostname]) {
        baseURL = gatewayMap[location.hostname];
    } else {
        const regex = /([a-zA-Z0-9-]+\.[a-zA-Z]{2,})$/;
        const match = hostname.match(regex);
        const rootHost = match ? match[0] : hostname;
        baseURL = `https://gateway.${rootHost}`;
    }
}
request.useConfig({
    gayWayConfig: {
        clientId: clientId,
        clientSecret: clientSecret,
        webToken: lib.config.webToken,
        env: lib.config.env,
        systemCode: lib.config.systemCode,
    },
    baseURL: baseURL,
    showErrorMessage: true,
});
function Admin() {
    const [sassInfo] = hooks.useSaasInfo();
    const [buttons] = hooks.useGetAuthButtons({ systemCode: "CCS_ADMIN", pagePath: "/workbench" });
    useEffect(() => {
        if (sassInfo.tenantId === 1001) {
            let botSrc;
            if (["integration", "dev", "test", "pre"].includes(BUILD_ENV)) {
                let gptMap = {
                    integration: `https://ai.yang800.com.cn/chat/share?shareId=4ci266irh8fh8hesjfabr2m1`,
                    dev: `https://ai.yang800.com.cn/chat/share?shareId=4ci266irh8fh8hesjfabr2m1`,
                    test: `https://ai.yang800.com.cn/chat/share?shareId=4ci266irh8fh8hesjfabr2m1`,
                    pre: `https://ai.yang800.com.cn/chat/share?shareId=4ci266irh8fh8hesjfabr2m1`,
                };
                botSrc = gptMap[lib.config.env];
            } else {
                botSrc = "https://ai.yang800.com/chat/share?shareId=gv0s9fkgivfbtvye5lsywqzz";
            }
            if (buttons?.includes("AI-button")) {
                embedChatbot(botSrc);
            }
        }
    }, [sassInfo, buttons]);
    return (
        <ConfigProvider locale={zhCN}>
            <SpaConfigProvider spa={new Spa({ _openPage: lib.openPage.bind(lib), request: request })}>
                <App pageMap={pageMap} configList={json_data} />
            </SpaConfigProvider>
        </ConfigProvider>
    );
}

ReactDOM.render(<Admin />, document.getElementById("root"));
