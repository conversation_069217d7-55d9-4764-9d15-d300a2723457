import { message } from "antd";
import { lib } from "react-single-app";
function handleCopy(requestData) {
    let transfer = document.createElement("input");
    document.body.appendChild(transfer);
    transfer.value = requestData; // 这里表示想要复制的内容
    transfer.focus();
    transfer.select();
    if (document.execCommand("copy")) {
        document.execCommand("copy");
    }
    transfer.blur();
    message.success("复制成功");
    document.body.removeChild(transfer);
}

function batchUpdateConfigsList(data) {
    const arr = [];
    data.map((item, index) => {
        if (item.from) {
            arr.push(
                new Promise((resolve, reject) => {
                    lib.request({
                        url: item.from,
                        data: item.fromParams,
                        success: data => {
                            resolve({
                                data: data,
                                index: index,
                            });
                        },
                    });
                }),
            );
        }
    });
    return Promise.all(arr);
}

function simpleHash(str) {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
        let char = str.charCodeAt(i);
        hash = (hash << 5) - hash + char;
        hash = hash & hash; // Convert to 32bit integer
    }
    return hash;
}

function textToRGB(text) {
    const hash = simpleHash(text);
    // 将哈希值映射到0-255的范围（每个颜色分量）
    const r = (hash & 0xff0000) >> 16;
    const g = (hash & 0x00ff00) >> 8;
    const b = hash & 0x0000ff;
    return `rgb(${r}, ${g}, ${b})`;
}

export { handleCopy, batchUpdateConfigsList, textToRGB };
