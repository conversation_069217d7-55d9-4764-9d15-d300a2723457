// declare module "react-single-app"{
//     export const lib: ILib
//     export const event: IEvent
//     export class SearchList<P,T> extends ISearchList<P,T>{}
//     export const getConfigDataUtils: IgetConfigDataUtils
//     export class App extends React.Component<any,any>{}
//     export const ConfigFormCenter: React.FC<ConfigFormCenterProps>
//     export const UploadFile: any
//     type TEnv = "integration" | 'test' | 'dev' | 'pre' | 'online'
//     interface IOpenPageV2Params {
//         url: string,
//         refreshFn?: ()=>{},
//         openByWindow?: boolean,
//         pageTitleCanEmpty?: boolean
//         params?: DDYObject
//     }

//     interface ILibRequest {
//         url: string
//         needMask?: boolean
//         methods?: string,
//         data: any,
//         success: (data:any) => void,
//         fail: (code:number,msg:string,data:any) => void
//     }

//     interface ILibConfig {
//         webToken: "admin" | "user"
//         env: TEnv
//         hostLogin: string
//         loadUrlList: string[]
//         openByWindow?: boolean
//         pageTitleCanEmpty?: boolean
//         systemCode: string
//         platformCode: string
//     }
//     interface ILibenvConfig {
//         platformCode?: string;
//         systemCode: string;
//         envCode: any;
//         site_config?: {};
//         // systemCode: "SMS_ADMIN",
//         // platformCode: "sms",
//     }

//     interface IEvent {
//         on: (name: string, fn: () => void) => void
//         off: (name: string, fn: () => void) => void
//         emit: (name: string, entry: any, once?: boolean) => void
//         map: {}
//     }

//     interface ILib {
//         config: ILibConfig,
//         getOtherSystemPage: (url: string,systemCode: string) => string,
//         getDevHost: () => string,
//         openPageV2: (params:IOpenPageV2Params) => void,
//         openPage: (url: string, refreshFn?: ()=> void, openByWindow?: boolean, pageTitleCanEmpty?: boolean, params?: DDYObject) => void,
//         closePage: (url?: string) => void,
//         getParam: (key:string | number,url?:string) => string | number,
//         request: (requestObj:ILibRequest,showErrorMessage?:boolean) => void,
//         wait: (time: number) => void,
//         waitEnd: () => void,
//         setConfig: (config: Partial<ILibConfig>, envConfig: Partial<ILibenvConfig>) => void,
//         isType: (obj: any, type: string) => boolean,
//         clone: (parent:DDYObject) => DDYObject,
//         getCookie: (key: string) => string,
//         setCookie: (key: string, val: string, time?: number) => void,
//         localDevelopDebug: () => void
//     }

//     interface ClassSearchListProps {
//         name: string,
//     }

//     interface ClassSearchListStatePagination {
//         currentPage: number
//         pageSize: number
//         totalPage: number
//     }

//     interface ClassSearchListStateConfig extends DDYObject {

//     }

//     interface ClassSearchListStateTable {
//         panel: React.RefObject<any>,
//         height: number
//     }

//     interface ClassSearchListState {
//         pagination: ClassSearchListStatePagination
//         search: DDYObject
//         dataList: any[]
//         config: ClassSearchListStateConfig
//         table: ClassSearchListStateTable
//         selectedRows: any[]
//         selectedIdList: any[]
//         sorter: any[],
//         defaultSearch: DDYObject,
//         buttons: string[]
//         _loading: boolean
//         detail: any
//     }

//     class ISearchList<P,S > extends React.Component<P & ClassSearchListProps,S & ClassSearchListState> {
//         constructor(props: P & ClassSearchListProps)
//         searchRef: React.RefObject<any>
//         loadConfig: () => void
//         resetSize: () => void
//         componentWillUnmount: () => void
//         load: (_?: any, toTop?: boolean) => void
//         configLoadDefaultParams: () => DDYObject
//         setDetail: (detail: any) => void
//         renderDeleteRow: (params:{api: string, id: string, title: string}) => React.FunctionComponentElement<any>
//         renderTable: () =>  React.FunctionComponentElement<any>
//         handleTableChange: (pagination: ClassSearchListStatePagination,filters: ()=>void,sorter:{order:any}) => void
//         renderPagination: () => React.FunctionComponentElement<any>
//         setTableConfigureStorage: (tableFieldList:any) => void
//         renderOperation: () => React.FunctionComponentElement<any>
//         importAuth: () => boolean
//         exportAuth: () => boolean
//         saveTableFieldListConfig: (config:ClassSearchListStateConfig) => void
//         changeImmutable: (params: DDYObject) => void
//         getSearchCondition: () => DDYObject
//         renderSearchTopView?: () =>  React.FunctionComponentElement<any>
//         renderModal?: () => React.FunctionComponentElement<any>
//         onSearch?: (search:DDYObject) => void
//         renderOperationTopView?: () => React.FunctionComponentElement<any>
//         renderTableTopView?: () => React.FunctionComponentElement<any>
//         renderDetail?: () =>  React.FunctionComponentElement<any> |  React.ComponentElement<any,any>
//     }

//     interface IgetConfigDataUtils {
//         getDataUrlByDataId: (id:number) => string;
//     }

//     interface DDYObject {
//         [index : string]: any
//     }

//     interface ConfigFormCenterProps{
//         confData: any[]

//         disableEdit: boolean // 是否禁用编辑
//         formProps?: {
//             labelWrap?: boolean
//             layout?: 'horizontal' | 'vertical'
//             formItemLayout?: {
//                 labelCol: {span: number},
//                 wrapperCol: {span: number}
//             }

//         }
//         configUrl: string
//         onConfigLoadSuccess: (list: any[]) => void  // configUrl数据加载成功后的处理
//         onSinglesSelectChange: (desc: any) => void
//         onInitSelectSuccess: (item: any) => void    // 下拉数据获取之后的处理
//         confirmContent?: any[]
//         beforeSubmit?:  (values: any) => boolean     // 提交之前的自定义验证
//         submitUrl?: string                          // 提交的地址
//         onSubmitSuccess?: (data: any) => void        // 提交成功之后的处理
//         beforeSetDetail?: (data: any) => void        // 设置详情detail之前的处理函数，如果设置，会覆盖接口的数据
//         onSetDetailFail?: () => void                // 获取详情数据失败的处理函数
//         ref: {
//             current?:{

//             }
//         }
//     }

// }
