server {
    listen 80;
    server_name localhost;
    # compression
    gzip on;
    gzip_min_length   2k;
    gzip_buffers      4 16k;
    gzip_http_version 1.0;
    gzip_comp_level   3;
    gzip_types        text/plain application/javascript application/x-javascript text/css application/xml text/javascript application/x-httpd-php image/jpeg image/gif image/png;
    gzip_vary on;

    client_max_body_size 0;

    set $taEnabled ${TERMINUS_TA_ENABLE};
    set $taUrl ${TERMINUS_TA_URL};
    set $collectorUrl ${TERMINUS_TA_COLLECTOR_URL};
    set $terminusKey ${TERMINUS_KEY};
    location / {
        root   /usr/share/nginx/html;
        index  index.htm;
        try_files $uri $uri/ /index.htm;
    }
    location /ta {
        default_type application/javascript;
        return 200 'window._taConfig={enabled:$taEnabled,ak:"$terminusKey",url:"$taUrl",collectorUrl:"$collectorUrl"}';
    }
}
